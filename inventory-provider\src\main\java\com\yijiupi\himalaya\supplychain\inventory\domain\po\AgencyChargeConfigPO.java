package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class AgencyChargeConfigPO implements Serializable {
    /**
     * 编号
     */
    private Long id;

    /**
     * 经销商id
     */
    private Long agencyId;

    /**
     * 经销商名称
     */
    private String agencyname;

    /**
     * 是否收取仓配费用
     */
    private Boolean isgetwarehousecharge;

    /**
     * 业务类型 0=正常业务 1=贷款业务
     */
    private Byte bussinesstype;

    /**
     * 状态 0=停用 1=启用
     */
    private Byte status;

    /**
     * 服务商id
     */
    private Long facilitatorId;

    /**
     * 服务商名称
     */
    private String facilitatorname;

    /**
     * 创建人
     */
    private Long createuser;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 最后更新人
     */
    private Long lastupdateuser;

    /**
     * 最后更新时间
     */
    private Date lastupdatetime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(Long agencyId) {
        this.agencyId = agencyId;
    }

    public String getAgencyname() {
        return agencyname;
    }

    public void setAgencyname(String agencyname) {
        this.agencyname = agencyname == null ? null : agencyname.trim();
    }

    public Boolean getIsgetwarehousecharge() {
        return isgetwarehousecharge;
    }

    public void setIsgetwarehousecharge(Boolean isgetwarehousecharge) {
        this.isgetwarehousecharge = isgetwarehousecharge;
    }

    public Byte getBussinesstype() {
        return bussinesstype;
    }

    public void setBussinesstype(Byte bussinesstype) {
        this.bussinesstype = bussinesstype;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Long getFacilitatorId() {
        return facilitatorId;
    }

    public void setFacilitatorId(Long facilitatorId) {
        this.facilitatorId = facilitatorId;
    }

    public String getFacilitatorname() {
        return facilitatorname;
    }

    public void setFacilitatorname(String facilitatorname) {
        this.facilitatorname = facilitatorname == null ? null : facilitatorname.trim();
    }

    public Long getCreateuser() {
        return createuser;
    }

    public void setCreateuser(Long createuser) {
        this.createuser = createuser;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Long getLastupdateuser() {
        return lastupdateuser;
    }

    public void setLastupdateuser(Long lastupdateuser) {
        this.lastupdateuser = lastupdateuser;
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

}
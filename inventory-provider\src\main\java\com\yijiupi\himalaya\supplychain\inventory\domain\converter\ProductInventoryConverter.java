package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockApplyPreemptDTO;

/**
 * 产品库存转换类
 * 
 * <AUTHOR>
 *
 */
public class ProductInventoryConverter {

    private ProductInventoryConverter() {}

    /**
     * 将产品库存PO对象转换为分页列表展示DTO对象
     */
    public static WarehouseStoreDTO convertToDTO(ProductInventoryPO po) {
        WarehouseStoreDTO warehouseStoreDTO = new WarehouseStoreDTO();
        warehouseStoreDTO.setId(po.getId());
        warehouseStoreDTO.setCityId(po.getCityId());
        warehouseStoreDTO.setProductSpecId(po.getProductSpecificationId());
        warehouseStoreDTO.setWarehouseId(po.getWarehouseId());
        warehouseStoreDTO.setOwnerId(po.getOwnerId());
        warehouseStoreDTO.setOwnerType(po.getOwnerType());
        return warehouseStoreDTO;
    }

    /**
     * 创建不存在库存记录的仓库ID集合的库存DTO对象
     */
    public static WarehouseStoreDTO convertNoExitInventoryToDTO(Integer warehouseId) {
        WarehouseStoreDTO warehouseStoreDTO = new WarehouseStoreDTO();
        warehouseStoreDTO.setId(UUIDUtil.getUUID());
        warehouseStoreDTO.setWarehouseId(warehouseId);
        return warehouseStoreDTO;
    }

    /**
     *
     */
    public static List<ProductInventoryInfoDTO> productInventoryPOList2DTOList(List<ProductInventoryPO> poList) {
        ArrayList<ProductInventoryInfoDTO> dtoList = new ArrayList<>();
        for (ProductInventoryPO productInventoryPO : poList) {
            ProductInventoryInfoDTO productInventoryInfoDTO = productInventoryPO2DTO(productInventoryPO);
            dtoList.add(productInventoryInfoDTO);
        }
        return dtoList;
    }

    public static ProductInventoryInfoDTO productInventoryPO2DTO(ProductInventoryPO po) {
        ProductInventoryInfoDTO dto = new ProductInventoryInfoDTO();
        dto.setId(po.getId());
        dto.setCityId(po.getCityId());
        dto.setOwnerId(po.getOwnerId());
        dto.setOwnerType(po.getOwnerType());
        dto.setProductSkuId(po.getProductSkuId());
        dto.setTotalCountMinUnit(po.getTotalCountMinUnit());
        dto.setWarehouseId(po.getWarehouseId());
        dto.setChannel(po.getChannel());
        dto.setSecOwnerId(po.getSecOwnerId());
        dto.setPackageName(po.getPackageName());
        dto.setUnitName(po.getUnitName());
        dto.setPackageQuantity(po.getPackageQuantity());
        dto.setSource(po.getSource());
        dto.setSpecificationName(po.getSpecificationName());
        dto.setOwnerName(po.getOwnerName());
        return dto;
    }

    public static ProductInventoryDTO productInventoryPO2ProductInventoryDTO(ProductInventoryPO po) {
        if (po == null) {
            return null;
        }
        ProductInventoryDTO dto = new ProductInventoryDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    public static List<ProductInventoryDTO> productInventoryPO2ProductInventoryDTO(List<ProductInventoryPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<ProductInventoryDTO> dtoList = new ArrayList<>();
        for (ProductInventoryPO po : poList) {
            ProductInventoryDTO dto = productInventoryPO2ProductInventoryDTO(po);
            if (dto != null) {
                dtoList.add(dto);
            }

        }
        return dtoList;
    }

    public static List<WarehouseStoreDTO> outStockApplyPreempts2WarehouseStoreDTOS(
        List<OutStockApplyPreemptDTO> outStockApplyPreemptDTOS, Integer orgId, Integer warehouseId) {
        if (CollectionUtils.isEmpty(outStockApplyPreemptDTOS)) {
            return null;
        }
        List<WarehouseStoreDTO> warehouseStoreDTOS = new ArrayList<>();
        outStockApplyPreemptDTOS.forEach(item -> {
            WarehouseStoreDTO warehouseStore = new WarehouseStoreDTO();
            warehouseStore.setCityId(item.getOrgId());
            warehouseStore.setWarehouseId(item.getWarehouseId());
            warehouseStore.setWarehouseTotalCount(item.getUnitTotalCount());
            warehouseStore.setProductSkuId(item.getProductSkuId());
            warehouseStore.setProductSpecId(item.getProductSpecificationId());
            warehouseStore.setOwnerId(item.getOwnerId());
            warehouseStore.setSecOwnerId(item.getSecOwnerId());

            warehouseStoreDTOS.add(warehouseStore);
        });
        return warehouseStoreDTOS;
    }
}

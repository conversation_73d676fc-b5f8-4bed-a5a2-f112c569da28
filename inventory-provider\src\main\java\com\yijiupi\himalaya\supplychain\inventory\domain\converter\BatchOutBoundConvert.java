package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.DeliveryMode;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.pushorder.enums.pushCapabilityTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2022/3/10
 */
public class BatchOutBoundConvert {

    public static List<InventoryDeliveryJiupiOrder>
        orderDTOS2InventoryDeliveryJiupiOrderS(List<OrderDTO> orderDTOList) {
        List<InventoryDeliveryJiupiOrder> deliveryJiupiOrderS = new ArrayList<>();
        for (OrderDTO orderDTO : orderDTOList) {
            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
            inventoryDeliveryJiupiOrder.setOrderId(orderDTO.getId());
            inventoryDeliveryJiupiOrder.setOrderNo(orderDTO.getRefOrderNo());
            inventoryDeliveryJiupiOrder.setOmsOrderId(StringUtils.isNumeric(orderDTO.getBusinessId())
                ? Long.valueOf(orderDTO.getBusinessId()) : orderDTO.getId());
            inventoryDeliveryJiupiOrder.setRelationOrderId(orderDTO.getRelationOrderId());
            inventoryDeliveryJiupiOrder.setCityId(orderDTO.getOrgId());
            inventoryDeliveryJiupiOrder.setFromCityId(orderDTO.getFromCityId());
            inventoryDeliveryJiupiOrder.setWarehouseId(orderDTO.getWarehouseId());
            inventoryDeliveryJiupiOrder.setFromWarehouseId(
                null != orderDTO.getFromWarehouseId() ? orderDTO.getFromWarehouseId() : orderDTO.getWarehouseId());
            inventoryDeliveryJiupiOrder.setItems(orderItemDTOList2InventoryDeliveryJiupiOrderItem(orderDTO.getItems()));

            int orderType = orderDTO.getOrderType() == null ? 0 : orderDTO.getOrderType();
            // jiuPiEventType转换
            inventoryDeliveryJiupiOrder.setJiupiEventType(getJiuPiEventType(orderDTO.getBusinessType(),
                orderDTO.getOrderType().intValue(), orderDTO.getRegistrationPromotion(), orderDTO.getCapabilityType()));

            inventoryDeliveryJiupiOrder.setJiupiOrderType(orderType);
            inventoryDeliveryJiupiOrder.setOrderType(orderType);
            // 记录订单能力类型
            inventoryDeliveryJiupiOrder.setCapabilityType(orderDTO.getCapabilityType());
            // 记录配送方式
            inventoryDeliveryJiupiOrder.setDeliveryMode(
                null != orderDTO.getDeliveryMode() ? Integer.valueOf(orderDTO.getDeliveryMode()) : DeliveryMode.酒批配送);

            deliveryJiupiOrderS.add(inventoryDeliveryJiupiOrder);
        }
        return deliveryJiupiOrderS;
    }

    private static List<InventoryDeliveryJiupiOrderItem>
        orderItemDTOList2InventoryDeliveryJiupiOrderItem(List<OrderItemDTO> orderItemDTOList) {
        ArrayList<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            BigDecimal saleSpecQuantity = orderItemDTO.getSaleSpecQuantity();
            if (CollectionUtils.isNotEmpty(orderItemDTO.getItemDetailList())) {
                orderItemDTO.getItemDetailList().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem =
                        new InventoryDeliveryJiupiOrderItem();
                    inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItemDTO.getSkuId());
                    inventoryDeliveryJiupiOrderItem.setTakeCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItemDTO.getId());
                    inventoryDeliveryJiupiOrderItem
                        .setOmsOrderItemId(StringUtils.isNumeric(orderItemDTO.getBusinessItemId())
                            ? Long.valueOf(orderItemDTO.getBusinessItemId()) : orderItemDTO.getId());
                    inventoryDeliveryJiupiOrderItem.setOrderItemDetailId(detail.getId());
                    inventoryDeliveryJiupiOrderItem.setRelationOrderItemId(orderItemDTO.getRelationOrderItemId());
                    inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(detail.getProductSpecificationId());
                    inventoryDeliveryJiupiOrderItem.setOwnerId(detail.getOwnerId());
                    inventoryDeliveryJiupiOrderItem.setSecOwnerId(detail.getSecOwnerId());
                    inventoryDeliveryJiupiOrderItem.setLocationId(detail.getLocationId());
                    inventoryDeliveryJiupiOrderItem.setLocationName(detail.getLocationName());
                    inventoryDeliveryJiupiOrderItem.setDeliverCount(detail.getUnitTotalCount().abs());
                    inventoryDeliveryJiupiOrderItem.setBuyCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setSaleSpecQuantity(saleSpecQuantity);
                    inventoryDeliveryJiupiOrderItem.setIsAdvent(orderItemDTO.getIsAdvent());
                    inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
                });
            } else {
                InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
                inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItemDTO.getSkuId());
                inventoryDeliveryJiupiOrderItem.setTakeCount(orderItemDTO.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItemDTO.getId());
                inventoryDeliveryJiupiOrderItem
                    .setOmsOrderItemId(StringUtils.isNumeric(orderItemDTO.getBusinessItemId())
                        ? Long.valueOf(orderItemDTO.getBusinessItemId()) : orderItemDTO.getId());
                inventoryDeliveryJiupiOrderItem.setRelationOrderItemId(orderItemDTO.getRelationOrderItemId());
                inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(orderItemDTO.getProductSpecificationId());
                inventoryDeliveryJiupiOrderItem.setOwnerId(orderItemDTO.getOwnerId());
                inventoryDeliveryJiupiOrderItem.setSecOwnerId(orderItemDTO.getSecOwnerId());
                inventoryDeliveryJiupiOrderItem.setLocationId(orderItemDTO.getLocationId());
                inventoryDeliveryJiupiOrderItem.setLocationName(orderItemDTO.getLocationName());
                inventoryDeliveryJiupiOrderItem.setDeliverCount(orderItemDTO.getUnitTotalCount().abs());
                inventoryDeliveryJiupiOrderItem.setBuyCount(orderItemDTO.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setSaleSpecQuantity(saleSpecQuantity);
                inventoryDeliveryJiupiOrderItem.setIsAdvent(orderItemDTO.getIsAdvent());
                inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
            }
        }
        return inventoryDeliveryJiupiOrderItems;
    }

    private static Integer getJiuPiEventType(Byte orderBusinessType, Integer OrderType, Boolean registrationPromotion,
        Byte capabilityType) {
        Integer jiupiEvenType = JiupiEventType.仓库发货扣仓库库存.getType();
        if (orderBusinessType != null) {
            boolean isReturnOrder = orderBusinessType == InStockOrderBusinessType.退货订单.getType()
                || (orderBusinessType == InStockOrderBusinessType.酒批业务退货单.getType()
                    && JiupiOrderTypeEnum.ORDER_TYPE_RTURNOFFLINE != OrderType);
            if (orderBusinessType == InStockOrderBusinessType.部分配送订单.getType()) {
                jiupiEvenType = JiupiEventType.部分配送返仓库库存.getType();
            } else if (orderBusinessType == InStockOrderBusinessType.延迟配送订单.getType()) {
                jiupiEvenType = JiupiEventType.订单延迟配送.getType();
            } else if (orderBusinessType == InStockOrderBusinessType.配送失败订单.getType()) {
                jiupiEvenType = JiupiEventType.配送失败返仓库库存.getType();
            } else if (isReturnOrder) {
                jiupiEvenType = JiupiEventType.退货单返仓库库存.getType();
            } else if (JiupiOrderTypeEnum.ORDER_TYPE_OEFLINE == OrderType) {
                jiupiEvenType = JiupiEventType.易款便利线下单扣仓库库存.getType();
            } else if (JiupiOrderTypeEnum.ORDER_TYPE_RTURNOFFLINE == OrderType) {
                jiupiEvenType = JiupiEventType.易款便利线下退货单返仓库库存.getType();
            }

        }
        if (registrationPromotion != null && registrationPromotion) {
            jiupiEvenType = JiupiEventType.注册有礼订单扣仓库库存.getType();
        }

        if (null != capabilityType) {
            if (capabilityType == pushCapabilityTypeEnum.调拨强制完成.getType()) {
                jiupiEvenType = JiupiEventType.供应链调拨.getType();
            } else if (capabilityType == pushCapabilityTypeEnum.调拨强制取消.getType()) {
                jiupiEvenType = JiupiEventType.供应链调拨.getType();
            }
        }
        return jiupiEvenType;
    }

    public static InventoryDeliveryJiupiOrder
        outStockOrderDTO2InventoryDeliveryJiupiOrder(OutStockOrderDTO outStockOrderDTO) {
        if (outStockOrderDTO == null) {
            return null;
        }
        InventoryDeliveryJiupiOrder deliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
        deliveryJiupiOrder.setOrderId(Long.valueOf(outStockOrderDTO.getId()));
        deliveryJiupiOrder.setOrderNo(outStockOrderDTO.getRefOrderNo());
        deliveryJiupiOrder.setOmsOrderId(outStockOrderDTO.getBusinessId() == null
            ? Long.valueOf(outStockOrderDTO.getId()) : Long.valueOf(outStockOrderDTO.getBusinessId()));
        deliveryJiupiOrder.setCityId(outStockOrderDTO.getOrgId());
        deliveryJiupiOrder.setFromCityId(outStockOrderDTO.getFromCityId());
        deliveryJiupiOrder.setWarehouseId(outStockOrderDTO.getWarehouseId());
        deliveryJiupiOrder.setItems(
            outStockOrderItemDetailDTOS2InventoryDeliveryJiupiOrderItems(outStockOrderDTO.getOutStockOrderItemDTOS()));

        int orderType = outStockOrderDTO.getOrderType() == null ? 0 : outStockOrderDTO.getOrderType();
        // jiuPiEventType转换
        deliveryJiupiOrder
            .setJiupiEventType(getJiuPiEventType(outStockOrderDTO.getBusinessType(), orderType, null, null));

        deliveryJiupiOrder.setJiupiOrderType(orderType);

        return deliveryJiupiOrder;
    }

    private static List<InventoryDeliveryJiupiOrderItem>
        outStockOrderItemDetailDTOS2InventoryDeliveryJiupiOrderItems(List<OutStockOrderItemDTO> outStockOrderItemDTOS) {
        List<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
        for (OutStockOrderItemDTO item : outStockOrderItemDTOS) {
            if (item.getOutStockOrderItemDetailDTOS() == null
                || CollectionUtils.isEmpty(item.getOutStockOrderItemDetailDTOS())) {
                InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
                inventoryDeliveryJiupiOrderItem.setProductSkuId(item.getSkuId());
                inventoryDeliveryJiupiOrderItem.setTakeCount(item.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setOrderItem_Id(Long.valueOf(item.getId()));
                inventoryDeliveryJiupiOrderItem.setOmsOrderItemId(item.getBusinessItemId() == null
                    ? Long.valueOf(item.getId()) : Long.valueOf(item.getBusinessItemId()));
                inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(item.getProductSpecificationId());
                inventoryDeliveryJiupiOrderItem.setOwnerId(item.getOwnerId());
                inventoryDeliveryJiupiOrderItem.setSecOwnerId(item.getSecOwnerId());
                inventoryDeliveryJiupiOrderItem.setDeliverCount(item.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setBuyCount(item.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
            } else {
                item.getOutStockOrderItemDetailDTOS().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem =
                        new InventoryDeliveryJiupiOrderItem();
                    inventoryDeliveryJiupiOrderItem.setProductSkuId(item.getSkuId());
                    inventoryDeliveryJiupiOrderItem.setTakeCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setOrderItem_Id(Long.valueOf(item.getId()));
                    inventoryDeliveryJiupiOrderItem.setOmsOrderItemId(item.getBusinessItemId() == null
                        ? Long.valueOf(item.getId()) : Long.valueOf(item.getBusinessItemId()));
                    inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(item.getProductSpecificationId());
                    inventoryDeliveryJiupiOrderItem.setOwnerId(detail.getOwnerId());
                    inventoryDeliveryJiupiOrderItem.setSecOwnerId(detail.getSecOwnerId());
                    inventoryDeliveryJiupiOrderItem.setLocationId(detail.getLocationId());
                    inventoryDeliveryJiupiOrderItem.setLocationName(detail.getLocationName());
                    inventoryDeliveryJiupiOrderItem.setDeliverCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setBuyCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
                });
            }
        }
        return inventoryDeliveryJiupiOrderItems;
    }

    public static List<InventoryDeliveryJiupiOrder>
        outStockOrderDTOS2InventoryDeliveryJiupiOrders(List<OutStockOrderDTO> outStockOrderDTOS) {
        if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
            return null;
        }
        List<InventoryDeliveryJiupiOrder> jiupiOrders = new ArrayList<>();
        outStockOrderDTOS.forEach(order -> jiupiOrders.add(outStockOrderDTO2InventoryDeliveryJiupiOrder(order)));
        return jiupiOrders;
    }
}

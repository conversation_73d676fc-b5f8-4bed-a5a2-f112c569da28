package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.attribute;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeDicBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchAttributeDicService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 2018/4/9
 */
@Service
public class BatchAttributeDicServiceImpl implements IBatchAttributeDicService {

    @Autowired
    private BatchAttributeDicBL batchAttributeDicBL;

    /**
     * 新增
     *
     * @param batchAttributeDicDTO
     */
    @Override
    public void addBatchAttributeDic(BatchAttributeDicDTO batchAttributeDicDTO) {
        AssertUtils.notNull(batchAttributeDicDTO.getAttributeName(), "属性名称不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getAttributeType(), "属性类型不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getEnable(), "状态不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getCreateUser(), "创建人不能为空");
        batchAttributeDicBL.addBatchAttributeDic(batchAttributeDicDTO);
    }

    /**
     * 停用,启用
     *
     * @param batchAttributeDicDTO
     */
    @Override
    public void updateBatchAttributeDicState(BatchAttributeDicDTO batchAttributeDicDTO) {
        AssertUtils.notNull(batchAttributeDicDTO.getId(), "主键不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getEnable(), "状态不能为空");
        batchAttributeDicBL.updateState(batchAttributeDicDTO);
    }

    /**
     * 修改
     *
     * @param batchAttributeDicDTO
     */
    @Override
    public void updateBatchAttributeDic(BatchAttributeDicDTO batchAttributeDicDTO) {
        AssertUtils.notNull(batchAttributeDicDTO.getId(), "主键不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getEnable(), "状态不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getAttributeName(), "属性名称不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getAttributeType(), "属性类型不能为空");
        AssertUtils.notNull(batchAttributeDicDTO.getEnable(), "状态不能为空");
        batchAttributeDicBL.updateBatchAttributeDic(batchAttributeDicDTO);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public int deleteById(Long id) {
        AssertUtils.notNull(id, "主键不能为空");
        return batchAttributeDicBL.deleteById(id);
    }

    /**
     * 列表(分页)
     *
     * @param batchAttributeDicQueryDTO
     * @return
     */
    @Override
    public PageList<BatchAttributeDicDTO>
        findBatchAttributeDicList(BatchAttributeDicQueryDTO batchAttributeDicQueryDTO) {
        return batchAttributeDicBL.findBatchAttributeDicList(batchAttributeDicQueryDTO);
    }
}

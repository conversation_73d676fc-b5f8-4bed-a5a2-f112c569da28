package com.yijiupi.himalaya.supplychain.inventory.domain.message;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.MessageApplyMapper;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.StockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryOrderDTO;

/**
 * 幂等消费者.
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class IdempotenceConsumer {

    private static final Logger LOG = LoggerFactory.getLogger(IdempotenceConsumer.class);

    @Autowired
    private MessageApplyMapper messageApplyMapper;

    public void apply(String messageId, Runnable call) {
        int count = messageApplyMapper.countMessageApply(messageId);
        if (count > 0) {
            LOG.warn("消息重复投递: {}", messageId);
            return;
        }
        messageApplyMapper.insertMessageApply(messageId);
        try {
            call.run();
        } catch (Exception e) {
            LOG.error("消息消费异常: " + messageId, e);
            throw e;
        }
    }

    public void apply(List<String> messageIds, Runnable call) {
        if (messageIds.size() > 0) {
            messageApplyMapper.insertMessageApplyBatch(messageIds);
            try {
                call.run();
            } catch (Exception e) {
                LOG.error("消息消费异常: " + messageIds, e);
                throw e;
            }
        }
    }

    /**
     * 获取未处理的订单编号集合
     */
    public List<String> getNoProcessMessageIds(List<String> messageIds) {
        // 查询出已经处理过的订单编号,然后过滤掉.
        List<String> processedMessageIds = messageApplyMapper.getProcessedMessage(messageIds);
        return messageIds.stream().filter(p -> !processedMessageIds.contains(p)).collect(Collectors.toList());
    }

    /**
     * 批量获取未处理过的订单，并将已处理过的订单移除
     *
     * @param deliveryOrders
     * @param messageType
     * @param opType
     * @return
     */
    public List<String> getNoProcessOrderNos(Integer warehouseId, List<InventoryDeliveryJiupiOrder> deliveryOrders,
        String messageType, String opType) {
        List<String> internalDeliverOrderNos = deliveryOrders.stream()
            .map(p -> p.getOrderInventoryIdentityKey(warehouseId, messageType)).distinct().collect(Collectors.toList());
        List<String> noProcessOrderIds = getNoProcessMessageIds(internalDeliverOrderNos);

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("%s:%s", opType, JSON.toJSONString(deliveryOrders)));
        if (noProcessOrderIds.size() != internalDeliverOrderNos.size()) {
            List<String> processedOrderNos = deliveryOrders.stream()
                .filter(p -> !noProcessOrderIds.contains(p.getOrderInventoryIdentityKey(warehouseId, messageType)))
                .map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
            stringBuilder
                .append(String.format("%n，仓库：%s,内配订单：%s已经被处理过，跳过…", warehouseId, String.join(",", processedOrderNos)));
            deliveryOrders.removeIf(p -> processedOrderNos.contains(p.getOrderNo()));
        }
        LOG.info(stringBuilder.toString());
        return noProcessOrderIds;
    }

    /**
     * 延迟配送及召回，需要清除原来所有的Id
     */
    public void deleteAllKey(Integer warehouseId, List<String> orderNos) {
        List<String> lstDeleteIds = new ArrayList<>();
        orderNos.forEach(p -> {
            lstDeleteIds.add(OrderDeliveryOpType.IN_STOCK_ORDER_TYPE + "" + warehouseId + p);
            lstDeleteIds.add(OrderDeliveryOpType.ORDER_OUT_OF_STOCK_MESSAGE_TYPE + "" + warehouseId + p);
            lstDeleteIds.add(OrderDeliveryOpType.ORDER_DELIVER_MESSAGE_TYPE + "" + warehouseId + p);
            lstDeleteIds.add(OrderDeliveryOpType.ORDER_DELIVERY_DELAY_TYPE + "" + warehouseId + p);
            lstDeleteIds.add(OrderDeliveryOpType.ZITI_OUT_STOCK_ORDER_TYPE + "" + warehouseId + p);
        });
        if (lstDeleteIds.size() > 0) {
            List<String> processedMessage = messageApplyMapper.getProcessedMessage(lstDeleteIds);
            if (CollectionUtils.isNotEmpty(processedMessage)) {
                messageApplyMapper.deleteMessagesBatch(lstDeleteIds);
            }
        }
    }

    /**
     * 批量获取未处理过的内配订单，并将已处理过的内配订单移除
     */
    public List<String> getNoProcessInternalDeliveryOrderNos(Integer warehouseId,
        List<InventoryOrderDTO> internalDeliveryOrders, String messageType, String opType) {
        List<String> internalDeliverOrderNos = internalDeliveryOrders.stream()
            .map(p -> p.getOrderInventoryIdentityKey(warehouseId, messageType)).distinct().collect(Collectors.toList());
        List<String> noProcessOrderIds = getNoProcessMessageIds(internalDeliverOrderNos);

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("%s:%s", opType, JSON.toJSONString(internalDeliveryOrders)));
        if (noProcessOrderIds.size() != internalDeliverOrderNos.size()) {
            List<String> processedOrderNos = internalDeliveryOrders.stream()
                .filter(p -> !noProcessOrderIds.contains(p.getOrderInventoryIdentityKey(warehouseId, messageType)))
                .map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
            stringBuilder
                .append(String.format("%n，仓库：%s,内配订单：%s已经被处理过，跳过…", warehouseId, String.join(",", processedOrderNos)));
            internalDeliveryOrders.removeIf(p -> processedOrderNos.contains(p.getRefOrderNo()));
        }
        LOG.info(stringBuilder.toString());
        return noProcessOrderIds;
    }

    /**
     * 获取未处理过的订单，并将已处理过的订单移除
     *
     * @param stockOrderInventoryDTO
     * @param messageType
     * @param opType
     * @return
     */
    public List<String> getNoProcessStockOrderInventoryOrderNo(Integer warehouseId,
        StockOrderInventoryDTO stockOrderInventoryDTO, String messageType, String opType) {
        String internalOrderNo = messageType + "" + warehouseId + stockOrderInventoryDTO.getRefOrderNo();
        List<String> noProcessOrderIds = getNoProcessMessageIds(Collections.singletonList(internalOrderNo));

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("%s:%s", opType, JSON.toJSONString(stockOrderInventoryDTO)));
        if (CollectionUtils.isEmpty(noProcessOrderIds)) {
            stringBuilder.append(
                String.format("%n，仓库：%s,二次分拣订单：%s已经被处理过，跳过…", warehouseId, stockOrderInventoryDTO.getRefOrderNo()));
        }
        LOG.info(stringBuilder.toString());
        return noProcessOrderIds;
    }

    /**
     * 根据操作类型清除原来对应的Id
     */
    public void deleteKeyByMessageType(Integer warehouseId, List<String> orderNos, String messageType) {
        List<String> lstDeleteIds = new ArrayList<>();
        orderNos.forEach(p -> {
            lstDeleteIds.add(messageType + "" + warehouseId + p);
        });

        if (lstDeleteIds.size() > 0) {
            List<String> processedMessage = messageApplyMapper.getProcessedMessage(lstDeleteIds);
            if (CollectionUtils.isNotEmpty(processedMessage)) {
                messageApplyMapper.deleteMessagesBatch(lstDeleteIds);
            }
        }
    }
}

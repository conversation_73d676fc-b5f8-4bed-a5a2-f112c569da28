package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo;

import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryTransferDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/13
 */
public class PickUpCompleteTransferBO {
    /**
     * 移库参数
     */
    private List<BatchInventoryTransferDTO> transferDTOList;
    /**
     * 直接更新批次库存参数
     */
    private List<ProductStoreBatchDTO> updateStoreBatchList;

    public PickUpCompleteTransferBO() {}

    public PickUpCompleteTransferBO(List<BatchInventoryTransferDTO> transferDTOList) {
        this.transferDTOList = transferDTOList;
    }

    public PickUpCompleteTransferBO(List<BatchInventoryTransferDTO> transferDTOList,
        List<ProductStoreBatchDTO> updateStoreBatchList) {
        this.transferDTOList = transferDTOList;
        this.updateStoreBatchList = updateStoreBatchList;
    }

    /**
     * 获取 移库参数
     *
     * @return transferDTOList 移库参数
     */
    public List<BatchInventoryTransferDTO> getTransferDTOList() {
        return this.transferDTOList;
    }

    /**
     * 设置 移库参数
     *
     * @param transferDTOList 移库参数
     */
    public void setTransferDTOList(List<BatchInventoryTransferDTO> transferDTOList) {
        this.transferDTOList = transferDTOList;
    }

    /**
     * 获取 直接更新批次库存参数
     *
     * @return updateStoreBatchList 直接更新批次库存参数
     */
    public List<ProductStoreBatchDTO> getUpdateStoreBatchList() {
        return this.updateStoreBatchList;
    }

    /**
     * 设置 直接更新批次库存参数
     *
     * @param updateStoreBatchList 直接更新批次库存参数
     */
    public void setUpdateStoreBatchList(List<ProductStoreBatchDTO> updateStoreBatchList) {
        this.updateStoreBatchList = updateStoreBatchList;
    }
}

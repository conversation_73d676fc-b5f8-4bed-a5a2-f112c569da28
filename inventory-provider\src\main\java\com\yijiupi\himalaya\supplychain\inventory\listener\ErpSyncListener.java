package com.yijiupi.himalaya.supplychain.inventory.listener;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.framework.businessaudit.BusinessAuditEntry;
import com.yijiupi.himalaya.supplychain.instockorder.constant.InStockOrderConstants;
import com.yijiupi.himalaya.supplychain.instockorder.dto.communal.InStockOrderDispatchDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.aspect.InventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryErpBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.dto.WareHoseInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpProductStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStoreOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IErpStoreOrderService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.CheckResultItemDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.enums.StoreCheckResultTypeEnum;
import com.yijiupi.himalaya.supplychain.storecheck.domain.so.CheckResultItemQuerySO;
import com.yijiupi.himalaya.supplychain.storecheck.service.StoreCheckResultService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * ERP同步消息监听. Created by Lifeng on 2017/6/12.
 */
@Component
public class ErpSyncListener {

    private static final Logger LOG = LoggerFactory.getLogger(ErpSyncListener.class);

    @Autowired
    private InventoryErpBL inventoryErpBL;
    @Autowired
    private IdempotenceConsumer idempotenceConsumer;
    @Reference
    private IErpStoreOrderService iErpStoreOrderService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;

    @Reference
    private IProductSkuService iProductSkuService;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Autowired
    private InventorySendFaildMQ inventorySendFaildMQ;

    @Reference
    private StoreCheckResultService storeCheckResultService;

    @Reference(timeout = 300000)
    private OwnerService ownerService;

    @Reference(timeout = 300000)
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @RabbitListener(
        queues = {"${mq.supplychain.inventory.syncInventory}", "${mq.supplychain.storecheckResult.directPass}"})
    public void syncApply(ErpStoreOrderDTO storeOrder, Message message) {
        String messageId = message.getMessageProperties().getMessageId();
        idempotenceConsumer.apply("ErpSync:" + messageId, () -> {
            processErpMsg(storeOrder);
        });
        try {
        } catch (Exception e) {
            LOG.error("ERP消息处理失败，错误信息：" + e.getMessage(), e);
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(storeOrder), "processErpMsg", e);
        }
    }

    public void processErpMsg(ErpStoreOrderDTO storeOrder) {
        Integer warehouseId = CollectionUtils.isEmpty(storeOrder.getProductSkuList()) ? null
            : Integer.valueOf(storeOrder.getProductSkuList().get(0).getWarehouseId());
        if (warehouseId == null) {
            LOG.error("ERP 消息，没有仓库Id ！ 参数信息：{}", JSON.toJSONString(storeOrder));
            return;
        }
        if (storeOrder.getCityId() == null) {
            Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
            Assert.notNull(warehouse, "仓库信息不存在！");
            storeOrder.setCityId(warehouse.getCityId().toString());
        }
        // 转化供应商
        conversionSupplier(storeOrder);
        // 替换[描述敏感字符]
        storeOrder.setDescription(StringUtils.replace(storeOrder.getDescription(), "采购", ""));
        Boolean isOpenStock = warehouseConfigService.isOpenLocationStock(warehouseId);
        boolean isOpenSCMV3 = isOpenStock != null && isOpenStock;
        String syncData = JSON.toJSONString(storeOrder);
        LOG.info(String.format("[V%s-%s]ERP同步消息：%s", isOpenSCMV3 ? 3 : 2, isOpenStock, syncData));
        // 添加审计日志
        if (storeOrder.getErpEventType().equals(ERPEventType.单据审核.getType())) {
            BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "ErpStoreOrder");
            builder.businessId(storeOrder.getOrderId());
            if (storeOrder.getErpType() != null) {
                builder.addFeature("OrderType", String.valueOf(storeOrder.getErpType()));
            }
            builder.content("盘点单审核").done();
        } else if (storeOrder.getErpEventType().equals(ERPEventType.单据反审核.getType())) {
            BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "ErpStoreOrder");
            builder.businessId(storeOrder.getOrderId());
            if (storeOrder.getErpType() != null) {
                builder.addFeature("OrderType", String.valueOf(storeOrder.getErpType()));
            }
            builder.content("盘点单反审核").done();
        }

        // V3盘盈盘亏逻辑
        if (isOpenSCMV3) {
            // ERP盘点单审核逻辑
            // if (storeOrder.getErpType().equals(ERPType.盘点单.getType())) {
            // if (storeOrder.getErpEventType().equals(ERPEventType.单据审核.getType())) {
            // LOG.info("ERP同步消息-》盘点单单据审核：{}", syncData);
            // // 处理库存总数相同的移库操作
            // inventoryErpBL.processStockOrder(storeOrder.getOrderId(), null, false, false);
            // return;
            // }
            // }
            // 盘盈盘亏单逻辑
            if (storeOrder.getErpType().equals(ERPType.库存盘点单.getType())) {
                Map<Long,
                    List<Long>> skuAndSecOwnerMap = storeOrder.getProductSkuList().stream()
                        .collect(Collectors.groupingBy(p -> Long.valueOf(p.getProductSkuId()),
                            Collectors.mapping(ErpProductStoreDTO::getSecOwnerId, Collectors.toList())));
                List<String> specAndOwnerAndSecOwnerList = storeOrder.getProductSkuList().stream()
                    .filter(p -> p.getProductSpecificationId() != null).map(p -> String.format("%s-%s-%s-%s",
                        p.getWarehouseId(), p.getProductSpecificationId(), p.getOwnerId(), p.getSecOwnerId()))
                    .distinct().collect(Collectors.toList());
                if (storeOrder.getErpEventType().equals(ERPEventType.单据审核.getType())) {
                    LOG.info("ERP同步消息-》盘盈盘亏单据审核：{}", syncData);
                    // 处理库存总数不相同的移库操作
                    inventoryErpBL.processStockOrder(storeOrder.getParentId(), skuAndSecOwnerMap, true, false,
                        storeOrder.getOrderId(), specAndOwnerAndSecOwnerList);
                    return;
                } else if (storeOrder.getErpEventType().equals(ERPEventType.单据反审核.getType())) {
                    LOG.info("ERP同步消息-》盘盈盘亏单据反审核：{}", syncData);
                    // 处理库存总数不相同的移库操作
                    inventoryErpBL.processStockOrder(storeOrder.getParentId(), skuAndSecOwnerMap, true, true,
                        storeOrder.getOrderId(), specAndOwnerAndSecOwnerList);
                    return;
                }
            }
        }
        // 获取ownerId, ProductSpecificationId
        genProductSpecificationIdBySkuId(storeOrder, warehouseId);
        // 处理未开启货位库存盘盈盘亏库龄管控产品
        if (!isOpenSCMV3) {
            fillStoreCheckInfo(storeOrder);
        }
        // 生成入库单据
        List<StockOrderStoreDTO> stockOrderStoreDTOS = iErpStoreOrderService.processErpStoreOrder(storeOrder);
        // LOG.info("ERP->生成出/入库单返回参数：{}", JSON.toJSONString(stockOrderStoreDTOS));
        if (CollectionUtils.isEmpty(stockOrderStoreDTOS)) {
            LOG.warn("SKU库存为空: 忽略: {}", JSON.toJSONString(stockOrderStoreDTOS));
            return;
        }
        // if (ERPType.库存盘点单.getType().equals(storeOrder.getErpType()) ||
        // ERPType.盘点单.getType().equals(storeOrder.getErpType())) {
        // 盘点单不需要计算 ： 暂时此入库全部不计算
        stockOrderStoreDTOS.stream().filter(Objects::nonNull).forEach(order -> order.setAllocationCalculation(false));
        // }
        // 未开启货位库存：盘亏单拆分关于销售库存处理
        splitDisposedProductCount(isOpenSCMV3, stockOrderStoreDTOS);

        boolean isUpdateProductStore = !storeOrder.getNotChangeStock();
        boolean isUpdateBatchStore = storeOrder.getUpdateBatchStore() == null || storeOrder.getUpdateBatchStore();
        boolean isUpdateSellStore = storeOrder.getUpdateSellStore() == null || storeOrder.getUpdateSellStore();
        // Integer toSubcategory = null;
        // if (ERPType.处理品转入.getType().equals(storeOrder.getErpType())) {
        // toSubcategory = LocationAreaEnum.暂存区.getType();
        // }
        for (StockOrderStoreDTO stockOrderStoreDTO : stockOrderStoreDTOS) {
            // if (storeOrder.getErpType().equals(ERPType.库存盘点单.getType())) {
            // Integer cityId = Integer.valueOf(stockOrderStoreDTO.getCityId());
            // Boolean canProcessByApi = inventoryErpBL.canProcessByApi(cityId);
            // //酒批产品，调交易API校正销售库存
            // //非交易产品，通过MQ增量更新
            // inventoryErpBL.applyErpOrder(stockOrderStoreDTO, false, false, isUpdateProductStore, true,
            // !canProcessByApi);
            // if (canProcessByApi) {
            // List<Long> productSkuIds = stockOrderStoreDTO.getProductSkuList().stream().filter(p ->
            // StringUtils.isNotEmpty(p.getProductSkuId())).map(p ->
            // Long.valueOf(p.getProductSkuId())).collect(Collectors.toList());
            // inventoryErpBL.processSellInventoryMsgByApi(cityId, warehouseId, productSkuIds);
            // }
            // } else {
            // inventoryErpBL.applyErpOrder(stockOrderStoreDTO, false, false, isUpdateProductStore, true, true);
            // }
            inventoryErpBL.applyErpOrder(stockOrderStoreDTO, false, false, isUpdateProductStore, isUpdateBatchStore,
                isUpdateSellStore);
        }
        // if (storeOrder.getErpType().equals(ERPType.兑奖单.getType())) {
        // LOG.info(String.format("兑奖到货消息：%s", JSON.toJSONString(storeOrder)));
        // if (!CollectionUtils.isEmpty(storeOrder.getProductSkuList())
        // && storeOrder.getProductSkuList().get(0).getTotalStoreCountMinUnit().compareTo(BigDecimal.ZERO) > 0) {
        // processAwardOrderMessage(storeOrder);
        // }
        // }
        boolean isNeedUpdateStore = ERPType.采购入库单.getType().equals(storeOrder.getErpType())
            && (ERPEventType.单据录入.getType().equals(storeOrder.getErpEventType())
                || ERPEventType.单据审核.getType().equals(storeOrder.getErpEventType()))
            && isUpdateProductStore;
        if (isNeedUpdateStore) {
            InStockOrderDispatchDTO dispatchDTO = new InStockOrderDispatchDTO();
            dispatchDTO.setOrgId(
                StringUtils.isNumeric(storeOrder.getCityId()) ? Integer.valueOf(storeOrder.getCityId()) : null);
            dispatchDTO.setRefOrderNOList(Lists.newArrayList(storeOrder.getOrderId()));
            dispatchDTO.setWarehouseId(warehouseId);
            inventoryChangeEventFireBL.dispathRatioEvent(dispatchDTO);
        }
        LOG.info("ERP同步消息处理成功: {}", storeOrder.getOrderId());
    }
    //
    // /**
    // * 处理兑奖单兑入事件，给TMS发消息，生成兑奖配送单
    // *
    // * @param storeDTO
    // */
    // private void processAwardOrderMessage(ErpStoreOrderDTO storeDTO) {
    // List<InventoryChangeMessageDTO> lstMessage = new ArrayList<>();
    // storeDTO.getProductSkuList().forEach(erpProductStoreDTO -> {
    // //兑奖兑入单，发消息到TMS，生成对应的
    // if (erpProductStoreDTO != null && erpProductStoreDTO.getTotalStoreCountMinUnit().compareTo(BigDecimal.ZERO) > 0)
    // {
    // if (!CollectionUtils.isEmpty(erpProductStoreDTO.getLstReletedInfo())) {
    // InventoryChangeMessageDTO messageDTO = new InventoryChangeMessageDTO();
    // messageDTO.setProductSkuId(Long.valueOf(erpProductStoreDTO.getProductSkuId()));
    // messageDTO.setWarehouseId(Integer.valueOf(erpProductStoreDTO.getWarehouseId()));
    // messageDTO.setCount(erpProductStoreDTO.getTotalStoreCountMinUnit());
    // messageDTO.setLstOrderNo(erpProductStoreDTO.getLstReletedInfo());
    // LOG.info(String.format("兑奖入库消息：%s", JSON.toJSONString(messageDTO)));
    // lstMessage.add(messageDTO);
    // } else {
    // LOG.info(String.format("产品没有关联单号，暂不兑出！%s", JSON.toJSONString(erpProductStoreDTO)));
    // }
    // }
    // });
    // if (!CollectionUtils.isEmpty(lstMessage)) {
    // inventoryChangeEventFireBL.awardOrderInventoryChangeEvent(lstMessage);
    // }
    // }

    /**
     * 根据SkuId获取ProductSpecificationId
     *
     * @param storeOrder
     */
    private void genProductSpecificationIdBySkuId(ErpStoreOrderDTO storeOrder, Integer warehouseId) {
        if (storeOrder == null) {
            return;
        }
        List<ErpProductStoreDTO> itemList = storeOrder.getProductSkuList();
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<Long> skuIds = itemList.stream().filter(d -> d.getProductSkuId() != null)
            .map(item -> Long.valueOf(item.getProductSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        Map<Long, ProductSkuInfoReturnDTO> productSkuInfoMap = iProductSkuService.getProductInfoBySkuId(skuIds);
        if (CollectionUtils.isEmpty(productSkuInfoMap)) {
            return;
        }
        itemList.stream().filter(d -> d.getProductSkuId() != null).forEach(d -> {
            ProductSkuInfoReturnDTO skuInfoReturnDTO = productSkuInfoMap.get(Long.valueOf(d.getProductSkuId()));
            if (skuInfoReturnDTO != null) {
                if (d.getProductSpecificationId() == null) {
                    d.setProductSpecificationId(skuInfoReturnDTO.getProductSpecificationId());
                }
                if (d.getOwnerId() == null) {
                    d.setOwnerId(skuInfoReturnDTO.getCompanyId());
                }
                if (d.getSecOwnerId() == null) {
                    d.setSecOwnerId(skuInfoReturnDTO.getSecOwnerId());
                }
            }
        });
    }

    /**
     * 填充盘盈盘亏信息
     * 
     * @param storeOrder
     */
    private void fillStoreCheckInfo(ErpStoreOrderDTO storeOrder) {
        // 不是盘点单则退出
        boolean canProcess = validateForStoreCheck(storeOrder);
        if (!canProcess) {
            return;
        }
        Integer cityId = storeOrder.getCityId() == null ? null : Integer.valueOf(storeOrder.getCityId());
        List<ErpProductStoreDTO> productSkuList = storeOrder.getProductSkuList();
        // 处理库龄管理产品
        processStoreAgeControlProducts(cityId, storeOrder.getOrderId(), productSkuList);
    }

    /**
     * 处理库龄管理产品:盘盈单据回写生产日期和批次日期
     */
    private void processStoreAgeControlProducts(Integer cityId, String reportItemNo,
        List<ErpProductStoreDTO> productSkuList) {
        if (StringUtils.isBlank(reportItemNo)) {
            LOG.info("ERP同步消息 orderId 为空，无法填充盘盈盘亏单信息！");
            return;
        }
        CheckResultItemQuerySO querySO = new CheckResultItemQuerySO();
        querySO.setOrgId(cityId);
        querySO.setReportItemNo(reportItemNo);
        CheckResultItemDTO checkResultItemDTO = storeCheckResultService.selectByReportItemNo(querySO);
        if (checkResultItemDTO == null) {
            LOG.info("ERP同步消息 orderId:{} 没有找到对应盘盈盘亏单信息！", reportItemNo);
            return;
        }
        for (ErpProductStoreDTO storeDTO : productSkuList) {
            if (storeDTO == null) {
                continue;
            }
            // 盘盈-填充盘盈单据中库龄管控入库时间、生产日期
            if (Objects.equals(StoreCheckResultTypeEnum.盘盈单.getType(), checkResultItemDTO.getReporttype())) {
                storeDTO.setBatchTime(checkResultItemDTO.getBatchInStockTime());
                storeDTO.setProductionDate(checkResultItemDTO.getBatchProductionDate());
            }
            storeDTO.setProductSkuId(checkResultItemDTO.getProductskuId().toString());
            storeDTO.setProductName(checkResultItemDTO.getProductname());
            storeDTO.setCategoryName(checkResultItemDTO.getCategoryName());
            storeDTO.setPackageName(checkResultItemDTO.getCategoryName());
            storeDTO.setProductBrand(checkResultItemDTO.getProductBrand());
            storeDTO.setSpecName(checkResultItemDTO.getSpecname());
            storeDTO.setSpecQuantity(checkResultItemDTO.getSpecquantity().toString());
            storeDTO.setUnitName(checkResultItemDTO.getUnitname());
        }
    }

    private boolean validateForStoreCheck(ErpStoreOrderDTO storeOrder) {
        // 不是盘点单则退出
        if (storeOrder == null || !ERPType.库存盘点单.getType().equals(storeOrder.getErpType())) {
            return false;
        }
        List<ErpProductStoreDTO> productSkuList = storeOrder.getProductSkuList();
        if (CollectionUtils.isEmpty(productSkuList)) {
            LOG.info("ERP同步消息 orderId ：{} 产品信息项为空！", storeOrder.getOrderId());
            return false;
        }
        return true;
    }

    private void conversionSupplier(ErpStoreOrderDTO storeOrder) {
        if (storeOrder == null || CollectionUtils.isEmpty(storeOrder.getProductSkuList())) {
            return;
        }
        List<ErpProductStoreDTO> needConvertList = storeOrder.getProductSkuList().stream()
            .filter(it -> it != null && StringUtils.isNotBlank(it.getRefSecOwnerId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needConvertList)) {
            return;
        }
        String fixedId = InStockOrderConstants.NEW_FIXED_SUPPLIER_ID.toString();
        List<ErpProductStoreDTO> fixedList = needConvertList.stream()
            .filter(it -> Objects.equals(fixedId, it.getRefSecOwnerId())).collect(Collectors.toList());
        List<ErpProductStoreDTO> nonFixedList = needConvertList.stream()
            .filter(it -> !Objects.equals(fixedId, it.getRefSecOwnerId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(fixedList)) {
            fixedList.stream().filter(Objects::nonNull)
                .forEach(it -> it.setSecOwnerId(Long.valueOf(it.getRefSecOwnerId())));
        }
        if (!CollectionUtils.isEmpty(nonFixedList)) {
            List<String> supplierIdList =
                nonFixedList.stream().map(ErpProductStoreDTO::getRefSecOwnerId).distinct().collect(Collectors.toList());
            Map<String, Long> wmsOwnerIdMap = ownerService.getOwnerIdMap(supplierIdList);
            if (wmsOwnerIdMap == null || wmsOwnerIdMap.isEmpty()) {
                throw new BusinessException(
                    String.format("同步单据[%s]供应商[%s]不存在！", storeOrder.getOrderId(), JSON.toJSONString(supplierIdList)));
            }
            Set<String> existSuppliers = wmsOwnerIdMap.keySet();
            if (!existSuppliers.containsAll(supplierIdList)) {
                supplierIdList.removeAll(existSuppliers);
                throw new BusinessException(
                    String.format("同步单据[%s]供应商[%s]不存在！", storeOrder.getOrderId(), JSON.toJSONString(supplierIdList)));
            }
            nonFixedList.forEach(productStoreDTO -> productStoreDTO
                .setSecOwnerId(wmsOwnerIdMap.get(productStoreDTO.getRefSecOwnerId())));
        }
    }

    /**
     * 未开启货位库存【盘亏】处理：产品总库存 - 产品残次品 = 【正品数量】 对比 【变更数量(盘亏数量)】 1、 正品数量 >= 变更数量：处理[变更数量]等量库存及销售库存 2、0 < 正品数量 <
     * 变更数量：处理[变更数量]等量库存及[正品数量]销售库存变动消息 3、【正品数量】 <= 0 ：即都是残次品，只处理[变更数量]等量库存，不发送销售库存变动消息
     */
    private void splitDisposedProductCount(boolean isOpenSCMV3, List<StockOrderStoreDTO> orderStoreDTOS) {
        if (isOpenSCMV3 || CollectionUtils.isEmpty(orderStoreDTOS)) {
            return;
        }
        // 盘亏：增量数据小于 0 ;反审核可能是大于 0 , 对于反审核不做处理
        Predicate<StockOrderStoreItemDTO> splitSkuPredicate = (sku -> {
            if (sku != null && sku.getTotalStoreCountMinUnit() != null
                && sku.getTotalStoreCountMinUnit().compareTo(BigDecimal.ZERO) < 0) {
                return true;
            }
            return false;
        });
        Predicate<StockOrderStoreDTO> splitPredicate = (order -> {
            if (order == null) {
                return false;
            }
            // 库存盘点审核为负数则经行处理，正数不处理
            if (ERPType.库存盘点单.getType().equals(order.getErpType())) {
                List<StockOrderStoreItemDTO> productSkuList = order.getProductSkuList();
                if (CollectionUtils.isEmpty(productSkuList)) {
                    return false;
                }
                // 盘亏单且数量小于 0
                return productSkuList.stream().anyMatch(splitSkuPredicate);
            }
            return false;
        });
        List<StockOrderStoreDTO> needSplitDisposedOrders =
            orderStoreDTOS.stream().filter(splitPredicate).collect(Collectors.toList());
        // LOG.info("splitDisposedProductCount - 未开启货位库存盘亏单需要处理残次品数据：{}", JSON.toJSONString(needSplitDisposedOrders));
        if (CollectionUtils.isEmpty(needSplitDisposedOrders)) {
            return;
        }
        // 需要拆分sku集合
        Map<Integer, List<Long>> needSplitProductSku =
            getNeedSplitProductSku(needSplitDisposedOrders, splitSkuPredicate);
        LOG.info("splitDisposedProductCount - 未开启货位库存盘亏单需要处理残次品产品信息：{}", JSON.toJSONString(needSplitProductSku));
        if (needSplitProductSku.isEmpty()) {
            return;
        }
        // 查询产品当前残次品库存
        Map<String, WarehouseStoreDTO> secOwnerDisposedProductInventory =
            getSecOwnerDisposedProductInventory(needSplitProductSku);
        LOG.info("splitDisposedProductCount - 残次品信息：{}", JSON.toJSONString(secOwnerDisposedProductInventory));
        if (secOwnerDisposedProductInventory.isEmpty()) {
            // 没有找到残次品数量不做拆分
            return;
        }
        // 查询产品当前总库存
        Map<String, ProductInventoryDTO> secOwnerProductInventory = getSecOwnerProductInventory(needSplitProductSku);
        LOG.info("splitDisposedProductCount - 产品当前总库存信息：{}", JSON.toJSONString(secOwnerProductInventory));
        // 拆分
        needSplitDisposedOrders.forEach(order -> {
            List<StockOrderStoreItemDTO> needSplitProducts =
                order.getProductSkuList().stream().filter(splitSkuPredicate).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needSplitProducts)) {
                return;
            }
            List<StockOrderStoreItemDTO> productSkuList = order.getProductSkuList();
            for (StockOrderStoreItemDTO needSplitProduct : needSplitProducts) {
                // 匹配 KEY
                String checkDisposedMatchKey = getCheckDisposedMatchKey(order.getWarehouseId(),
                    Long.valueOf(needSplitProduct.getProductSkuId()), needSplitProduct.getSecOwnerId());
                // 残次品数量
                // BigDecimal disposedCount = getSecOwnerDisposedProductInventory(checkDisposedMatchKey,
                // secOwnerDisposedProductInventory);
                // 当前产品库存数量
                BigDecimal inventoryCount =
                    getSecOwnerProductInventory(checkDisposedMatchKey, secOwnerProductInventory);
                // 拆分
                // StockOrderStoreItemDTO storeItemDTO = processSplitCheck(needSplitProduct, inventoryCount,
                // disposedCount);
                BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
                ArrayList<Integer> subCategeoryList = new ArrayList<>();
                subCategeoryList.add(LocationEnum.残次品位.getType());
                subCategeoryList.add(LocationAreaEnum.残次品区.getType());
                batchInventoryQueryDTO.setSubCategoryList(subCategeoryList);
                batchInventoryQueryDTO.setProductSkuId(Long.valueOf(needSplitProduct.getProductSkuId()));
                batchInventoryQueryDTO.setWarehouseId(order.getWarehouseId());
                // PageList<BatchInventoryDTO> batchInventoryListBatchNew =
                // iBatchInventoryQueryService.findBatchInventoryListBatchNew(batchInventoryQueryDTO);

                PageList<BatchInventoryDTO> batchInventoryListBatchNew =
                    iBatchInventoryQueryService.findBatchInventoryList(batchInventoryQueryDTO);

                List<BatchInventoryDTO> dataList = batchInventoryListBatchNew.getDataList();

                List<BatchInventoryDTO> batchInventoryDTOList =
                    dataList.stream().filter(batchInventoryDTO -> Objects.equals(batchInventoryDTO.getSecOwnerId(),
                        needSplitProduct.getSecOwnerId())).collect(Collectors.toList());

                List<StockOrderStoreItemDTO> storeItemDTOList =
                    processSplitCheck(needSplitProduct, inventoryCount, batchInventoryDTOList);

                if (storeItemDTOList != null) {
                    for (StockOrderStoreItemDTO stockOrderStoreItemDTO : storeItemDTOList) {
                        if (stockOrderStoreItemDTO != null) {
                            LOG.info("盘点盘亏单 {} 产品 {} 残次品货架 {} 残次品数量{} ", order.getErpOrderId(),
                                needSplitProduct.getProductSkuId(), stockOrderStoreItemDTO.getLocationName(),
                                needSplitProduct.getTotalStoreCountMinUnit());
                            productSkuList.add(stockOrderStoreItemDTO);
                        }
                    }
                }

                LOG.info("正品数量:{} ", needSplitProduct.getTotalStoreCountMinUnit());

                /*if (storeItemDTO != null) {
                    LOG.info("盘点盘亏单 {} 产品 {} 库存数量： {} 残次品数量 : {} 拆分后原item数量： {} 新item数量：{} ", order.getErpOrderId(), needSplitProduct.getProductSkuId(), inventoryCount, disposedCount, needSplitProduct.getTotalStoreCountMinUnit(), storeItemDTO.getTotalStoreCountMinUnit());
                    productSkuList.add(storeItemDTO);
                }*/
            }
        });
    }

    /**
     * 根据产品当前总库库存、残次品库存计算盘亏单销售库存变更信息 1、总库存 = 0, 残次品有值/无值：即残次品货位与正常货位一正一负抵消，不做拆分处理,仓库库存变动与销售库存变动一致 2、总库存 > 0, 残次品 <= 0
     * ：即残次品货位库存与正品货位库存抵消后总库存大于 0 , 不做拆分处理,仓库库存变动与销售库存变动一致 3、总库存 > 0, 残次品 > 0 ：总库存 - 残次品数量 = 正品数量 对比 【变更数量】 a) 正品数量 <=0
     * : 即正品所有货位库存总和是负库存，此时仓库库存都是残次品，不发送销售库存变动消息只处理仓库库存 b) 正品数量 > 0 且 正品数量 >= 变更数量，不做拆分,仓库库存变动与销售库存变动一致 c) 正品数量 > 0 且
     * 正品数量 < 变更数量 ： 仓库库存按【变更数量】处理，销售库存变更数量 = 正品数量 4、总库存 < 0 ：不做拆分处理,仓库库存变动与销售库存变动一致
     *
     * @param needSplitProduct 盘亏单产品信息
     * @param inventoryCount 产品当前库存
     * @param disposedCount 产品残次品库存
     * @return
     */
    public List<StockOrderStoreItemDTO> processSplitCheck(StockOrderStoreItemDTO needSplitProduct,
        BigDecimal inventoryCount, List<BatchInventoryDTO> batchInventoryDTOList) {
        // 算出残次品数量.
        BigDecimal disposedCount = batchInventoryDTOList.stream()
            .filter(batchInventoryDTO -> batchInventoryDTO != null && batchInventoryDTO.getStoreTotalCount() != null)
            .map(batchInventoryDTO -> batchInventoryDTO.getStoreTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalStoreCountMinUnit =
            ObjectUtils.defaultIfNull(needSplitProduct.getTotalStoreCountMinUnit(), BigDecimal.ZERO).abs();

        if (disposedCount.compareTo(BigDecimal.ZERO) <= 0) {
            // 2、总库存大于 0, 残次品 <= 0
            return null;
        } else {
            // 3、总库存大于 0, 残次品大于 0
            // 正品数量
            BigDecimal normalCount = inventoryCount.subtract(disposedCount);
            if (normalCount.compareTo(BigDecimal.ZERO) > 0 && normalCount.compareTo(totalStoreCountMinUnit) >= 0) {
                // 正品数量 > 0 且 正品数量 >= 变更数量，不做拆分,仓库库存变动与销售库存变动一致
                // needSplitProduct.setTotalStoreCountMinUnit(needSplitProduct.getTotalStoreCountMinUnit());
                needSplitProduct.setTotalStoreCountMinUnit(totalStoreCountMinUnit.negate());
                return null;
            } else {
                // 需要把货位id以及货位name 赋值上
                ArrayList<StockOrderStoreItemDTO> list = new ArrayList<>();

                BigDecimal surplusProduct = null;
                if (normalCount.compareTo(BigDecimal.ZERO) < 0) {
                    surplusProduct = totalStoreCountMinUnit;
                } else {
                    surplusProduct = totalStoreCountMinUnit.subtract(normalCount);
                }

                boolean flag = true;
                for (BatchInventoryDTO batchInventoryDTO : batchInventoryDTOList) {
                    if (flag) {
                        if (batchInventoryDTO.getStoreTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                            // 变更数量
                            StockOrderStoreItemDTO storeItemDTO = new StockOrderStoreItemDTO();
                            BeanUtils.copyProperties(needSplitProduct, storeItemDTO);
                            storeItemDTO.setLocationId(batchInventoryDTO.getLocationId());
                            storeItemDTO.setLocationName(batchInventoryDTO.getLocationName());
                            // 当前货位的数量
                            BigDecimal batchCount = batchInventoryDTO.getStoreTotalCount();
                            // 当变更<=当前货位库存时
                            if (surplusProduct.compareTo(batchCount) <= 0) {
                                // 直接把数据set进去
                                storeItemDTO.setTotalStoreCountMinUnit(surplusProduct.negate());
                                flag = false;
                            } else {
                                // 1.把当前货位数量值 set进去
                                storeItemDTO.setTotalStoreCountMinUnit(batchCount.negate());
                                // 2.把数量减去
                                surplusProduct = surplusProduct.subtract(batchCount);
                            }
                            LOG.info("需要变更数量: {} 正品数量: {} 残次品总数: {}  货位数量: {} 剩余数量： {} 货位信息：{}", totalStoreCountMinUnit,
                                normalCount, disposedCount, batchCount, surplusProduct,
                                batchInventoryDTO.getLocationName());
                            list.add(storeItemDTO);
                        }
                    }
                }
                // 修改原始项,剩余余量只处理库存
                // 当正品数小于0,并且分完的情况下, 需要把正品库存置为0
                if (normalCount.compareTo(BigDecimal.ZERO) < 0 && !flag) {
                    needSplitProduct.setTotalStoreCountMinUnit(new BigDecimal(0));
                } else if (flag) {
                    // 当没有分完的清空下,需要把变更数减去残次品数 为正品数的变更值
                    needSplitProduct
                        .setTotalStoreCountMinUnit((totalStoreCountMinUnit.subtract(disposedCount)).negate());
                } else {
                    // 当正品数大于等于0 ,并且残次品分完的情况下,正品也应该全部分完
                    needSplitProduct.setTotalStoreCountMinUnit(normalCount.negate());
                }
                return list;
            }
        }

    }

    /**
     * 按仓库维度找出需要拆分的SKU集合
     */
    private Map<Integer, List<Long>> getNeedSplitProductSku(List<StockOrderStoreDTO> needSplitDisposedOrders,
        Predicate<StockOrderStoreItemDTO> splitSkuPredicate) {
        Map<Integer,
            List<List<Long>>> warehouseSkuMap =
                needSplitDisposedOrders.stream()
                    .collect(Collectors.groupingBy(order -> order.getWarehouseId(),
                        Collectors.mapping(order -> order.getProductSkuList().stream().filter(splitSkuPredicate)
                            .map(storeItem -> Long.valueOf(storeItem.getProductSkuId())).collect(Collectors.toList()),
                            Collectors.toList())));
        if (warehouseSkuMap == null || warehouseSkuMap.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Integer, List<Long>> warehouseSkuIdMap = new HashMap<>(16);
        warehouseSkuMap.forEach((warehouseId, skuList) -> {
            List<Long> skuIdList = skuList.stream().filter(skuIds -> !CollectionUtils.isEmpty(skuIds))
                .flatMap(skuIds -> skuIds.stream()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(skuIdList)) {
                warehouseSkuIdMap.put(warehouseId, skuIdList);
            }
        });
        return warehouseSkuIdMap;
    }

    /**
     * 获取产品+二级货主仓库库存
     */
    private Map<String, ProductInventoryDTO> getSecOwnerProductInventory(Map<Integer, List<Long>> warehouseSkuMap) {
        // 结果集合
        Map<String, ProductInventoryDTO> result = new HashMap<>(16);
        // 按仓库维度查询库存
        warehouseSkuMap.forEach((warehouseId, skuIds) -> {
            Map<Long, List<ProductInventoryDTO>> productStoreMap =
                iWarehouseInventoryQueryService.findProductInventoryIncludeSupplierBySkuIds(skuIds, warehouseId);
            if (productStoreMap.isEmpty()) {
                return;
            }
            productStoreMap.forEach((skuId, productInventories) -> {
                Map<String,
                    ProductInventoryDTO> inventoryRS = productInventories.stream()
                        .collect(Collectors.toMap(
                            inventory -> getCheckDisposedMatchKey(inventory.getWarehouseId(),
                                inventory.getProductSkuId(), inventory.getSecOwnerId()),
                            Function.identity(), (v1, v2) -> v1));
                result.putAll(inventoryRS);
            });

        });
        return result;
    }

    private BigDecimal getSecOwnerProductInventory(String ownerKey,
        Map<String, ProductInventoryDTO> productInventoryMap) {
        ProductInventoryDTO productInventoryDTO = productInventoryMap.get(ownerKey);
        if (productInventoryDTO == null) {
            return BigDecimal.ZERO;
        }
        return ObjectUtils.defaultIfNull(productInventoryDTO.getTotalCountMinUnit(), BigDecimal.ZERO);
    }

    /**
     * 获取产品+二级货主仓库残次品库存
     */
    private Map<String, WarehouseStoreDTO>
        getSecOwnerDisposedProductInventory(Map<Integer, List<Long>> warehouseSkuMap) {
        // 结果集合
        Map<String, WarehouseStoreDTO> result = new HashMap<>(16);
        // 按仓库维度查询库存
        warehouseSkuMap.forEach((warehouseId, skuIds) -> {
            WareHoseInventoryQueryDTO queryDTO = new WareHoseInventoryQueryDTO();
            queryDTO.setWarehouseId(warehouseId);
            queryDTO.setProductSkuIds(skuIds);
            List<WarehouseStoreDTO> disposedProductInventories =
                iWarehouseInventoryQueryService.getDisposedProductInventories(queryDTO);
            if (CollectionUtils.isEmpty(disposedProductInventories)) {
                return;
            }
            Map<String,
                WarehouseStoreDTO> disposedRS = disposedProductInventories.stream().filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                        disposedInventory -> getCheckDisposedMatchKey(disposedInventory.getWarehouseId(),
                            disposedInventory.getProductSkuId(), disposedInventory.getSecOwnerId()),
                        Function.identity(), (v1, v2) -> v1));
            result.putAll(disposedRS);
        });
        return result;
    }

    private BigDecimal getSecOwnerDisposedProductInventory(String ownerKey,
        Map<String, WarehouseStoreDTO> disposedStoreMap) {
        WarehouseStoreDTO warehouseStoreDTO = disposedStoreMap.get(ownerKey);
        if (warehouseStoreDTO == null) {
            return BigDecimal.ZERO;
        }
        return ObjectUtils.defaultIfNull(warehouseStoreDTO.getWarehouseTotalCount(), BigDecimal.ZERO);
    }

    private String getCheckDisposedMatchKey(Integer warehouseId, Long skuId, Long secOwnerId) {
        return String.format("%s-%s-%s", warehouseId, skuId, secOwnerId);
    }
}

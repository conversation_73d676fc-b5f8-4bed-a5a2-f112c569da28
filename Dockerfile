FROM 197.255.20.20:50000/yjp/himalaya

# 作者信息 
MAINTAINER <EMAIL>

# 将tar包解压到容器中的/root/目录
ADD *.tar /root/

# 将服务目录名字统一改成app
RUN find . -maxdepth 1 -name "himalaya*" -exec mv {} app \;

# 挂载卷 
VOLUME ["/root/app/config", "/root/logs"]

# 对外暴露的端口号，可配置多项
EXPOSE 20005

# 主类名
ENV APP_MAINCLASS com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp

# 切换到工作空间
WORKDIR /root/

ENTRYPOINT ["sh","run.sh"]

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.ProcessLocationInventoryTypeConstants;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProcessLocationInventoryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Service
public class ProcessDefectiveToOrdinaryLocationInventoryBL extends ProcessLocationInventoryBaseBL {

    @Override
    protected boolean doSupport(ProcessLocationInventoryBO bo) {
        if (Objects.isNull(bo.getProcessType())) {
            return Boolean.FALSE;
        }
        if (ProcessLocationInventoryTypeConstants.DEFECTIVE_TO_NORMAL.equals(bo.getProcessType())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    protected ProcessLocationInventoryBO rebuildParam(ProcessLocationInventoryBO bo) {
        return bo;
    }

    @Override
    protected void processSelfLogical(ProcessLocationInventoryBO bo, List<ProductStoreBatchPO> lstUpdatePO) {
        super.processSelfLogical(bo, lstUpdatePO);
    }

    // 残次品转整平，批次库存的生产日期都被清空，所以按正常生产日期没查到后，再清空生产日期查询条件，再查一下
    @Override
    protected List<ProductStoreBatchPO> getProductStoreBatchPOS(
        ProductInventoryChangeRecordPO productInventoryChangeRecordPO, Integer locationType,
        List<Integer> exSubcategory) {
        List<ProductStoreBatchPO> productStoreBatchPOS =
            super.getProductStoreBatchPOS(productInventoryChangeRecordPO, locationType, exSubcategory);
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)) {
            return productStoreBatchPOS;
        }

        ProductInventoryChangeRecordPO changeRecordPO = new ProductInventoryChangeRecordPO();
        BeanUtils.copyProperties(productInventoryChangeRecordPO, changeRecordPO);
        return super.getProductStoreBatchPOS(changeRecordPO, locationType, exSubcategory);
    }

    @Override
    protected List<ProductStoreBatchPO> getFirstPriorityProductStoreBatch(ProcessLocationInventoryBO bo,
        List<ProductStoreBatchPO> productStoreBatchPOS) {
        List<ProductStoreBatchPO> filterStoreBatchList =
            super.getFirstPriorityProductStoreBatch(bo, productStoreBatchPOS);
        if (CollectionUtils.isNotEmpty(filterStoreBatchList)) {
            return filterStoreBatchList;
        }

        ProductInventoryChangeRecordPO changeRecordPO = new ProductInventoryChangeRecordPO();
        BeanUtils.copyProperties(bo.getProductInventoryChangeRecordPO(), changeRecordPO);

        ProcessLocationInventoryBO copyBO = new ProcessLocationInventoryBO();
        BeanUtils.copyProperties(bo, copyBO);
        copyBO.setProductInventoryChangeRecordPO(changeRecordPO);
        return super.getFirstPriorityProductStoreBatch(copyBO, productStoreBatchPOS);
    }
}

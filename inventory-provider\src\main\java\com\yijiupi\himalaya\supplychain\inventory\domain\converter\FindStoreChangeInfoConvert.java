package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangeInfoDTO;

@Component
public class FindStoreChangeInfoConvert extends ConvertUtils<ProductStoreChangeRecordDTO, FindStoreChangeInfoDTO> {

    @Override
    public FindStoreChangeInfoDTO convert(ProductStoreChangeRecordDTO m) {
        FindStoreChangeInfoDTO findStoreChangeInfoVO = new FindStoreChangeInfoDTO();
        if (m != null) {
            findStoreChangeInfoVO.setOrderId(null);
            findStoreChangeInfoVO.setOrderNo(m.getOrderNo());
            if (m.getAddStoreCountDTO() != null) {
                findStoreChangeInfoVO.setChangeCount(m.getAddStoreCountDTO().getTotalCountMinUnit());
                findStoreChangeInfoVO.setChangeCountMax(m.getAddStoreCountDTO().getStoreCountMaxUnit());
                findStoreChangeInfoVO.setChangeCountMin(m.getAddStoreCountDTO().getStoreCountMinUnit());
            }
            if (m.getSourceStoreCountDTO() != null) {
                findStoreChangeInfoVO.setSourceStoreCountMax(m.getSourceStoreCountDTO().getStoreCountMaxUnit());
                findStoreChangeInfoVO.setSourceStoreCountMin(m.getSourceStoreCountDTO().getStoreCountMinUnit());
            }
            if (m.getNewStoreCountDTO() != null) {
                findStoreChangeInfoVO.setNewStoreCountMax(m.getNewStoreCountDTO().getStoreCountMaxUnit());
                findStoreChangeInfoVO.setNewStoreCountMin(m.getNewStoreCountDTO().getStoreCountMinUnit());
            }
            findStoreChangeInfoVO.setPackageName(m.getMaxUnit());
            findStoreChangeInfoVO.setUnitName(m.getMinUnit());
            findStoreChangeInfoVO.setOrderTypeName(null);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            findStoreChangeInfoVO.setCreateTime(sdf.format(m.getCreateTime()));
            findStoreChangeInfoVO.setDescription(m.getDes());
        }
        return findStoreChangeInfoVO;
    }

    public List<FindStoreChangeInfoDTO> convert(List<ProductStoreChangeRecordDTO> mList,
        Map<String, String> mapOrderType, Map<String, String> mapJiupiEventType) {
        List<FindStoreChangeInfoDTO> nList = null;
        if (mList != null) {
            nList = new ArrayList<>(mList.size());
            for (ProductStoreChangeRecordDTO m : mList) {
                FindStoreChangeInfoDTO vo = convert(m);
                if (m.getOrderType() != null) {
                    vo.setOrderTypeName(mapOrderType.get(m.getOrderType().toString()));
                }
                if (m.getJiupiEventType() != null && mapJiupiEventType != null
                    && mapJiupiEventType.containsKey(m.getJiupiEventType().toString())) {
                    vo.setJiupiEventTypeName(mapJiupiEventType.get(m.getJiupiEventType().toString()));
                }
                nList.add(vo);
            }
        }
        return nList;
    }

    @Override
    public ProductStoreChangeRecordDTO reverseConvert(FindStoreChangeInfoDTO n) {
        return null;
    }
}

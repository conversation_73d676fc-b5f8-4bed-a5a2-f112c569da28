package com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.TrainsImportInStockDTOConverter;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.instockbatchnotify.TrainsImportInStockDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStockOrderRecordDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.enums.StoreCheckTypeConstants;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/3/16
 */
@Service
public class TrainsImportInStockBL {

    @Autowired
    private InventoryOrderCenterBL inventoryOrderCenterBL;
    @Autowired
    private TrainsImportInStockDTOConverter inStockDTOConverter;

    private static final Logger LOG = LoggerFactory.getLogger(TrainsImportInStockBL.class);

    /**
     * 通知订单中台盘亏正向出库、盘盈反审核出库
     * 
     * @param erpStoreOrderRecord
     * @param erpEventType
     */
    public void notifyOrderCenterIn(List<ErpStockOrderRecordDTO> erpStoreOrderRecord, Integer erpEventType) {
        if (CollectionUtils.isEmpty(erpStoreOrderRecord)) {
            return;
        }

        List<ErpStockOrderRecordDTO> needSyncList = erpStoreOrderRecord.stream()
            .filter(m -> needNotifyOrderCenterIn(m.getReforderid(), erpEventType)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needSyncList)) {
            LOG.warn("入库单同步，订单类型不匹配，不通知订单中台， {} ;  erpEventType : {}", JSON.toJSONString(erpStoreOrderRecord),
                erpEventType);
            return;
        }

        try {
            TrainsImportInStockDTO inStockDTO = inStockDTOConverter.convert(needSyncList);
            inventoryOrderCenterBL.batchImportInStock(inStockDTO);
        } catch (Exception e) {
            LOG.warn("入库单同步，订单类型不匹配，不通知订单中台， {}", JSON.toJSONString(needSyncList));
        }
    }

    private boolean needNotifyOrderCenterIn(String orderNo, Integer erpEventType) {
        if (StringUtils.isEmpty(orderNo)) {
            return Boolean.FALSE;
        }

        if (orderNo.contains(StoreCheckTypeConstants.STORE_CHECK_RESULT_LACK)
            && ERPEventType.单据反审核.getType().intValue() == erpEventType) {
            return Boolean.TRUE;
        }

        if (orderNo.contains(StoreCheckTypeConstants.STORE_CHECK_RESULT_MORE)
            && ERPEventType.单据审核.getType().intValue() == erpEventType) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}

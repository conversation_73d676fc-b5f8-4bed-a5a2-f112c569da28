package com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.ordercenter.sdk.serviceability.ServiceAbilityClient;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryResultDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.*;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterResponseDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderApiResult;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.instockbatchnotify.TrainsImportInStockDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.stocknotify.OrderCenterTrainsImportOutStockDTO;

/**
 * <AUTHOR>
 * @title: OrderCenterBL
 * @description:
 * @date 2023-03-16 13:56
 */
@Service
public class InventoryOrderCenterBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryOrderCenterBL.class);

    @Autowired
    private ServiceAbilityClient serviceAbilityClient;

    @Value("${ordercenter.sdk.timeout}")
    private Integer timeout;

    /**
     * 批次导入入库通知中台地址
     */
    private static final String BATCH_IN_STOCK_NOTIFY_URL = "/yijiupi/InStockService/batchImportInStock";

    /**
     * 中台待发货数据查询
     */
    private static final String FIND_WAIT_DELIVERY_QUERY_URL =
        "/saleinventory/SaleInventoryWaitDeliveryService/findWaitDeliveryByQuery";

    /**
     * 出库单通知订单中台
     */
    public void batchImportOutStock(OrderCenterTrainsImportOutStockDTO orderCenterTrainsImportOutStockDTO) {
        LOG.info("OrderCenterBL.batchImportOutStock,outStockList:{}",
            JSON.toJSONString(orderCenterTrainsImportOutStockDTO));

        try {
            OrderCenterResponseDTO<Void> orderResult = serviceAbilityClient
                .invoke("/yijiupi/OutStockService/batchImportOutStock", Void.class, orderCenterTrainsImportOutStockDTO);
            if (orderResult.getCode() != 200) {
                LOG.error("获取取货单列表接口报错{}", orderResult.getMsg());
            }

        } catch (Exception e) {
            LOG.warn("OrderCenterBL.batchImportOutStock 调用中台异常", e);
        }
    }

    /**
     * 入库单通知订单中台
     */
    public void batchImportInStock(TrainsImportInStockDTO trainsImportInStockDTO) {
        LOG.info("OrderCenterBL.batchImportInStock,inStockList:{}", JSON.toJSONString(trainsImportInStockDTO));

        try {
            OrderCenterResponseDTO<Void> orderResult =
                serviceAbilityClient.invoke(BATCH_IN_STOCK_NOTIFY_URL, timeout, Void.class, trainsImportInStockDTO);
            if (orderResult.getCode() != 200) {
                LOG.error("获取取货单列表接口报错{}", orderResult.getMsg());
            }

        } catch (Exception e) {
            LOG.warn("OrderCenterBL.batchImportInStock 调用中台异常", e);
        }
    }

    /**
     * 查询销售库存
     */
    /**
     * 查询销售库存
     */
    public List<OrderCenterSaleInventoryQueryResultDTO>
        findSaleInventoryList(List<OrderCenterSaleInventoryQueryDTO> queryDTOList) {
        LOG.info("findSaleInventoryList，入参queryQuery:{}", JSON.toJSONString(queryDTOList));
        if (CollectionUtils.isEmpty(queryDTOList)) {
            return new ArrayList<>();
        }

        try {
            OrderApiResult<List<OrderCenterSaleInventoryQueryResultDTO>> result =
                serviceAbilityClient.invoke("/saleinventory/SaleInventoryService/findSaleInventoryByKeys",
                    new TypeToken<OrderApiResult<List<OrderCenterSaleInventoryQueryResultDTO>>>() {}.getType(),
                    queryDTOList);
            if (result.getCode() != 200) {
                LOG.error("查询销售库存接口报错{}", result.getMsg());
            }

            LOG.info("查询销售库存findSaleInventoryList，查询结果:{}", JSON.toJSONString(result.getData()));
            return result.getData();

        } catch (Exception e) {
            LOG.warn("findSaleInventoryList查询销售库存", e);
            return new ArrayList<>();
        }
    }

    public List<SaleInventoryInfoDTO> findSaleInventoryByCityIdAndProductSpecId(SaleInventoryQueryParam param) {
        String url = "/saleinventory/SaleInventoryService/findSaleInventoryByCityIdAndProductSpecId";
        LOG.info("findSaleInventoryByCityIdAndProductSpecId, 入参: {}", JSON.toJSONString(param));
        if (null == param) {
            return new ArrayList<>();
        }
        try {
            Type type = new TypeToken<OrderApiResult<List<SaleInventoryInfoDTO>>>() {}.getType();
            OrderApiResult<List<SaleInventoryInfoDTO>> result = serviceAbilityClient.invoke(url, type, param);
            if (result.getCode() != 200) {
                LOG.error("findSaleInventoryByCityIdAndProductSpecId 报错: {}", result.getMsg());
            }
            LOG.info("findSaleInventoryByCityIdAndProductSpecId 查询结果: {}", JSON.toJSONString(result.getData()));
            return result.getData();
        } catch (Exception e) {
            LOG.warn("findSaleInventoryByCityIdAndProductSpecId 出现异常", e);
            return new ArrayList<>();
        }
    }

    public List<SaleInventoryInfoDTO> findInventoryByProductOwners(ProductOwnerInventoryQueryParam param) {
        String url = "/saleinventory/SaleInventoryService/findInventoryByProductOwners";
        LOG.info("findInventoryByProductOwners, 入参: {}", JSON.toJSONString(param));
        if (null == param) {
            return new ArrayList<>();
        }
        Object[] payload =
            Lists.newArrayList(param.getCityId(), param.getWarehouseId(), param.getProductOwnerInfo()).toArray();
        try {
            Type type = new TypeToken<OrderApiResult<List<SaleInventoryInfoDTO>>>() {}.getType();
            OrderApiResult<List<SaleInventoryInfoDTO>> result = serviceAbilityClient.invoke(url, type, payload);
            if (result.getCode() != 200) {
                LOG.error("findInventoryByProductOwners 报错: {}", result.getMsg());
            }
            LOG.info("findInventoryByProductOwners 查询结果: {}", JSON.toJSONString(result.getData()));
            return result.getData();
        } catch (Exception e) {
            LOG.warn("findInventoryByProductOwners 出现异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 中台待发货数据查询
     */
    public List<SaleInventoryWaitDeliveryDTO> findWaitDeliveryByQuery(SaleInventoryWaitDeliveryQueryDTO queryDTO) {
        LOG.info("中台待发货数据查询 入参: {}", JSON.toJSONString(queryDTO));
        if (queryDTO == null) {
            return Collections.emptyList();
        }

        try {
            OrderApiResult<List<SaleInventoryWaitDeliveryDTO>> orderDetailResult =
                serviceAbilityClient.invoke(FIND_WAIT_DELIVERY_QUERY_URL,
                    new TypeToken<OrderApiResult<List<SaleInventoryWaitDeliveryDTO>>>() {}.getType(), queryDTO);
            if (orderDetailResult.getCode() != 200) {
                LOG.warn("中台待发货数据查询接口报错{}", orderDetailResult.getMsg());
            }
            LOG.info("中台待发货数据查询 查询结果: {}", JSON.toJSONString(orderDetailResult.getData()));
            return orderDetailResult.getData();
        } catch (Exception e) {
            LOG.warn("中台待发货数据查询失败, 入参: " + JSON.toJSONString(queryDTO), e);
            return Collections.emptyList();
        }
    }

}

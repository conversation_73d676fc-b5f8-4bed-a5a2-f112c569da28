package com.yijiupi.himalaya.supplychain.batchinventory.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.base.enums.MallAppType;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dubbop.dto.SMSMessageDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OutStockAlarmEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUserQueryDTO;
import com.yijiupi.himalaya.supplychain.user.enums.AdminUserRoleType;
import com.yijiupi.himalaya.supplychain.user.enums.OrgType;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 临期过期产品短信提醒 获取每一个久批仓库的临期产品数量、过期产品数量，并发送给每一个仓库的仓管
 *
 * <AUTHOR>
 * @since 2021/10/12 10:59
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class CheckByOutStockConfigService {

    @Reference(timeout = 60000)
    private IWarehouseQueryService warehouseQueryService;

    @Reference(timeout = 120000)
    private IInStockConfigService iInStockConfigService;

    @Reference(timeout = 60000)
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Reference(timeout = 120000)
    private IAdminUserQueryService iAdminUserQueryService;

    @Autowired
    private SendMsgService sendMsgService;

    private static final Logger logger = LoggerFactory.getLogger(CheckByOutStockConfigService.class);

    /**
     * 每天执行一次
     */
    @XxlJob("checkProductByOutStockConfig")
    public void checkByOutStockConfig() {
        logger.info("[临期过期产品数量短信通知]开始");

        // 已启用仓库
        // 城市仓库((byte) 0),
        // 集货点((byte) 5),
        // 店仓合一((byte) 6),
        // 前置仓((byte) 8)
        List<Warehouse> warehouseList = warehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0, 5, 6, 8));
        if (CollectionUtils.isEmpty(warehouseList)) {
            logger.info("[临期过期产品数量短信通知]没有查询到任何仓库");
            return;
        }
        for (Warehouse warehouseElem : warehouseList) {
            if (warehouseElem.getCityId() == null || warehouseElem.getCityId() < 100
                    || warehouseElem.getCityId() > 900) {
                continue;
            }
            try {
                checkWarehouseByOutStockConfig(warehouseElem);
            } catch (Exception ex) {
                logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，获取仓库的临期产品数量、过期产品数量异常", warehouseElem.getName(),
                        warehouseElem.getId(), ex);
            }
        }
        logger.info("[临期过期产品数量短信通知]结束");
    }

    /**
     * 获取单个仓库的临期产品数量、过期产品数量，并发提醒短信给仓库管理员
     */
    private void checkWarehouseByOutStockConfig(Warehouse warehouseElem) {
        // 1.查询仓库所有sku（有库存、有生产日期）
        List<Long> skuIdList = iBatchInventoryQueryService.listSkuIdByBatchInventory(warehouseElem.getId());
        if (CollectionUtils.isEmpty(skuIdList)) {
            logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，没有有库存有生产日期的产品", warehouseElem.getName(), warehouseElem.getId());
            return;
        }
        logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，有库存有生产日期的产品个数：{}", warehouseElem.getName(), warehouseElem.getId(),
                skuIdList.size());

        // 2.查询仓库所有仓库管理员
        AdminUserQueryDTO warehouseAdminQuery = new AdminUserQueryDTO();
        warehouseAdminQuery.setOrgId(warehouseElem.getId());
        warehouseAdminQuery.setOrgType(OrgType.仓库.getType());
        warehouseAdminQuery.setUserRole(AdminUserRoleType.仓库管理员.getRole());
        List<AdminUser> warehouseAdminList =
                iAdminUserQueryService.findEffectiveAdminUserByOrgIdAndOrgType(warehouseAdminQuery);
        if (CollectionUtils.isEmpty(warehouseAdminList)) {
            logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，查询不到仓库管理员", warehouseElem.getName(), warehouseElem.getId());
            return;
        }

        // 3.获取仓管的电话号码
        warehouseAdminList = warehouseAdminList.stream().filter(admin -> Objects.nonNull(admin.getMobileNo()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseAdminList)) {
            logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，获取不到仓库管理员的电话号码", warehouseElem.getName(), warehouseElem.getId());
            return;
        }

        // 4.根据出库策略，查询仓库的过期产品数量、临期产品数量
        long banProductCount = 0;
        long remainProductCount = 0;
        List<List<Long>> skuIdsPart = Lists.partition(skuIdList, 1000);
        for (List<Long> skuIdListElem : skuIdsPart) {
            List<OutStockConfigCheckDTO> checkDTOList = new ArrayList<>();
            skuIdListElem.forEach(skuIdElem -> {
                OutStockConfigCheckDTO checkElem = new OutStockConfigCheckDTO();
                checkElem.setSkuId(skuIdElem);
                checkElem.setChannel(ProductChannelType.JIUPI);
                checkElem.setSource(ProductSourceType.易酒批);
                checkDTOList.add(checkElem);
            });

            List<OutStockConfigCheckResultDTO> checkResults =
                    iInStockConfigService.checkByOutStockConfig(checkDTOList, warehouseElem.getId());
            banProductCount += checkResults.stream()
                    .filter(elem -> Objects.equals(elem.getAlarm(), OutStockAlarmEnum.出库禁止.getType())).count();
            remainProductCount += checkResults.stream()
                    .filter(elem -> Objects.equals(elem.getAlarm(), OutStockAlarmEnum.出库提醒.getType())).count();
        }

        if (banProductCount == 0 && remainProductCount == 0) {
            logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，仓库无临期产品、无过期产品", warehouseElem.getName(), warehouseElem.getId());
            return;
        }
        logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，临期产品数量：{}，过期产品数量：{}", warehouseElem.getName(), warehouseElem.getId(),
                remainProductCount, banProductCount);

        // warehouseAdminList = warehouseAdminList.stream().filter(adminUser ->
        // Objects.equals(adminUser.getMobileNo(),"13407127672")).collect(Collectors.toList());

        // 5.发送过期产品数量、临期产品数量的短信给仓库管理员
        SMSMessageDTO msg = new SMSMessageDTO();
        msg.setContent(String.format("【临过期提醒】%s有%s个产品临过期,有%s个产品已经过期，请通过供应链客户端库存查询-临过期报表查询详情并立即处理！",
                warehouseElem.getName(), remainProductCount, banProductCount));
        msg.setMobileList(warehouseAdminList.stream().map(AdminUser::getMobileNo).collect(Collectors.toList()));
        msg.setMallAppType(MallAppType.酒批);
        sendMsgService.sendMessage(msg);

        logger.info("[临期过期产品数量短信通知]仓库名：{}，仓库ID：{}，短信service已调用，仓库管理员：{}", warehouseElem.getName(),
                warehouseElem.getId(), JSON.toJSONString(warehouseAdminList));
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSecOwnerIdQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSpecAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;

@Service
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public class WarehouseInventoryAtomicBL {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseInventoryAtomicBL.class);

    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;

    @DistributeLock(conditions = "#warehouseId", sleepMills = 3000, key = "insertInventoryPOList",
        lockType = DistributeLock.LockType.WAITLOCK, expireMills = 30000)
    public void insertInventoryPOList(Integer warehouseId, List<ProductInventoryPO> insertProductInventoryPOS) {
        Long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(insertProductInventoryPOS)) {
            return;
        }

        // 查询库存
        List<ProductInventoryPO> insertList = new ArrayList<>();
        ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO = new ProductSecOwnerIdQueryDTO();
        productSecOwnerIdQueryDTO.setWarehouseId(warehouseId);
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIds = new ArrayList<>();

        insertProductInventoryPOS.stream().filter(StreamUtils.distinctByKey(ProductInventoryPO::getSkuSign))
            .forEach(inventory -> {
                ProductSpecAndOwnerIdDTO productSpecAndOwnerIdDTO = new ProductSpecAndOwnerIdDTO();
                productSpecAndOwnerIdDTO.setProductSpecId(inventory.getProductSpecificationId());
                productSpecAndOwnerIdDTO.setOwnerId(inventory.getOwnerId());
                productSpecAndOwnerIdDTO.setSecOwnerId(inventory.getSecOwnerId());
                specAndOwnerIds.add(productSpecAndOwnerIdDTO);
            });
        productSecOwnerIdQueryDTO.setSpecAndOwnerIds(specAndOwnerIds);

        List<ProductInventoryPO> productInventoryList =
            productInventoryPOMapper.listProductStore(productSecOwnerIdQueryDTO);

        // 判断库存是否存在
        if (CollectionUtils.isNotEmpty(productInventoryList)) {
            // 存在则替换id
            productInventoryList.forEach(inventory -> {
                insertProductInventoryPOS.stream().filter(i -> inventory.getSkuSign().equals(i.getSkuSign()))
                    .forEach(i -> {
                        LOG.info("创建仓库库存记录修改storeId，原参数：{}，数据库数据：{}", JSON.toJSONString(i),
                            JSON.toJSONString(inventory));
                        BeanUtils.copyProperties(inventory, i);
                    });
            });

            // 不存在则新增
            List<String> existedSkuSignList =
                productInventoryList.stream().map(ProductInventoryPO::getSkuSign).collect(Collectors.toList());
            List<ProductInventoryPO> productInventoryPOS = insertProductInventoryPOS.stream()
                .filter(i -> !existedSkuSignList.contains(i.getSkuSign())).collect(Collectors.toList());
            insertList.addAll(productInventoryPOS);
        } else {
            // 不存在则新增
            insertList.addAll(insertProductInventoryPOS);
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            LOG.info("创建仓库库存记录:{}", JSON.toJSONString(insertList));
            productInventoryPOMapper.insertInventoryPOList(insertList);
        }
        Long endTime = System.currentTimeMillis();
        LOG.info("创建仓库库存记录耗时：{}", endTime - startTime);
    }
}

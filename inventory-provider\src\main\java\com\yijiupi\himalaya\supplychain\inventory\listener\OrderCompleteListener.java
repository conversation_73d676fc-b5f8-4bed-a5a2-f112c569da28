package com.yijiupi.himalaya.supplychain.inventory.listener;

import org.springframework.stereotype.Component;

/**
 * 供应链同步消息监听.
 *
 * <AUTHOR> 2018/1/12
 */
@Component
public class OrderCompleteListener {
    // private static final Logger LOG = LoggerFactory.getLogger(OrderCompleteListener.class);
    //
    // @Autowired
    // private IdempotenceConsumer idempotenceConsumer;
    // @Autowired
    // private WarehouseChangListBOConverter warehouseChangListBOConverter;
    // @Autowired
    // private WarehouseInventoryManageBL warehouseInventoryManageBL;
    // @Autowired
    // private ProductSkuZhaoShangBL productSkuZhaoShangBL;
    // @Reference
    // private IOutStockCommQueryService iOutStockCommQueryService;
    //
    // //合作商/经销商取货
    // public static final Integer PICKUP_PARTNER = 1;
    // public static final Integer BUS_CITYID = 898;
    // //jiupiOrderType=10 合作商订单
    // public static final Integer JIUPIORDERTYPE_PARTNERTOJIUPI = 10;
    // //jiupiOrderType=12 经销商订单
    // public static final Integer JIUPIORDERTYPE_SHOP = 12;
    // //jiupiOrderType=2 经销商直配订单
    // public static final Integer ORDER_TYPE_DIRECT_DISTRIBUTION = 2;
    //
    // @RabbitListener(queues = "${mq.supplychain.jiupiOrder.complete}")
    // public void syncApply(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder, Message message) {
    // //为了后面判断channel
    // inventoryDeliveryJiupiOrder.setJiupiOrderType(inventoryDeliveryJiupiOrder.getOrderType());
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrder);
    // LOG.info("订单完成同步消息监听：{}", syncData);
    // if (isProcessPartnerDeliveryInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("订单完成同步消息：{},合作商订单不需要操作发货中库存数量.", syncData);
    // } else if (isProcessAgencyPickInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("订单完成同步消息：{},经销商提货订单不需要操作发货中库存数量.", syncData);
    // } else if (isProcessAgencyDeliveryInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("订单完成同步消息：{},经销商直配订单不需要操作发货中库存数量.", syncData);
    // } else {
    // if (isSelfDelivery(inventoryDeliveryJiupiOrder)) {
    // LOG.info(String.format("自提订单不需要处理已出库数量！%s", JSON.toJSONString(inventoryDeliveryJiupiOrder)));
    // return;
    // }
    // idempotenceConsumer.apply("orderDeliveryCompleteListener:" + inventoryDeliveryJiupiOrder.getOmsOrderId(), () -> {
    // if (CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("订单项项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // //经销商自己配送订单，来源和发货城市Id更新为一致
    // if (JIUPIORDERTYPE_SHOP.equals(inventoryDeliveryJiupiOrder.getJiupiOrderType())
    // && !Objects.equals(inventoryDeliveryJiupiOrder.getCityId(), inventoryDeliveryJiupiOrder.getFromCityId())) {
    // inventoryDeliveryJiupiOrder.setFromCityId(inventoryDeliveryJiupiOrder.getCityId());
    // }
    // InventoryDeliveryJiupiOrder order =
    // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createDeliveryDetailBOList(order);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("供应链订单完成确认成功监听: {}", inventoryDeliveryJiupiOrder.getOmsOrderId());
    // });
    // }
    // }
    //
    // @RabbitListener(queues = "${mq.supplychain.ykorders.complete}")
    // public void syncYKOrder(NewOrderCompleteMqDTO inventoryDeliveryJiupiOrder, Message message) {
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrder);
    // LOG.info("易款订单完成同步消息监听：{}", syncData);
    // if (inventoryDeliveryJiupiOrder == null || CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("易款订单完成，项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // //退货单不处理已发货未完成数量
    // if (Objects.equals(StoreOrderType.易款便利线下退货单,
    // ObjectUtils.defaultIfNull(inventoryDeliveryJiupiOrder.getOrderType(), StoreOrderType.订单).intValue())
    // || OrderConstant.ORDER_TYPE_RETURN.equals(inventoryDeliveryJiupiOrder.getOrderType())
    // || OrderConstant.ORDER_TYPE_RETURN_WY.equals(inventoryDeliveryJiupiOrder.getOrderType())
    // || OrderConstant.ORDER_TYPE_RETURN_ZL.equals(inventoryDeliveryJiupiOrder.getOrderType())) {
    // LOG.warn("易款退货单不处理已发货未完成数量: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // idempotenceConsumer.apply("ykOrderDeliveryCompleteListener:" + inventoryDeliveryJiupiOrder.getOmsOrderId(), () ->
    // {
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createCompleteOrderDeliveryBOList(inventoryDeliveryJiupiOrder);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("易款订单完成确认成功监听: {}", inventoryDeliveryJiupiOrder.getOmsOrderId());
    // });
    // }
    //
    // @RabbitListener(queues = "${mq.supplychain.nporders.complete}")
    // public void syncNPOrder(NewOrderCompleteMqDTO inventoryDeliveryJiupiOrder, Message message) {
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrder);
    // LOG.info("内配订单完成同步消息监听：{}", syncData);
    // if (inventoryDeliveryJiupiOrder == null || CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("内配订单完成，项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // //订单直发不需要发货数量记录
    // if (Objects.equals(inventoryDeliveryJiupiOrder.getPickType(), OrderPickTypeConstant.PICKTYPE_LOGIC)) {
    // LOG.warn("内配订单完成-快递直发: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // }
    // idempotenceConsumer.apply("npOrderDeliveryCompleteListener:" + inventoryDeliveryJiupiOrder.getOmsOrderId(), () ->
    // {
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createCompleteOrderDeliveryBOList(inventoryDeliveryJiupiOrder);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("内配订单完成确认成功监听: {}", inventoryDeliveryJiupiOrder.getOmsOrderId());
    // });
    // }
    //
    // @RabbitListener(queues = "${mq.supplychain.npreturnordes.complete}")
    // public void syncNPReturnOrder(NewOrderCompleteMqDTO inventoryDeliveryJiupiOrder, Message message) {
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrder);
    // LOG.info("内配退货单完成同步消息监听：{}", syncData);
    // if (inventoryDeliveryJiupiOrder == null || CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("内配订退货单完成，项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // idempotenceConsumer.apply("npOrderDeliveryCompleteListener:" + inventoryDeliveryJiupiOrder.getOmsOrderId(), () ->
    // {
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createCompleteOrderDeliveryBOList(inventoryDeliveryJiupiOrder);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("内配退货单完成确认成功监听: {}", inventoryDeliveryJiupiOrder.getOmsOrderId());
    // });
    // }
    //
    // @RabbitListener(queues = "${mq.supplychain.easyGoWMSOrderDelivery.complete}")
    // public void easyGoOrderComplete(OrderStockInMainMqDTO easyGoOrder, Message message) {
    // String syncData = JSON.toJSONString(easyGoOrder);
    //// LOG.info("EasyGo订单完成同步消息监听：{}", syncData);
    // List<OrderStockInMqDTO> orderList = easyGoOrder.getOrderList();
    // if (!CollectionUtils.isEmpty(orderList)) {
    // orderList.forEach(inventoryDeliveryJiupiOrder -> {
    // idempotenceConsumer.apply("easyGoOrderDeliveryCompleteListener:" + inventoryDeliveryJiupiOrder.getOrderId(), ()
    // -> {
    // if (CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("EasyGo订单项项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // OrderStockInMqDTO order = productSkuZhaoShangBL.processEasyGoOrderItemProductSkuId(inventoryDeliveryJiupiOrder);
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createEasyGoDeliveryBOList(order);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("EasyGo订单完成确认成功监听,OrderId: {},OrderNo:{}", inventoryDeliveryJiupiOrder.getOrderId(),
    // inventoryDeliveryJiupiOrder.getOrderNo());
    // });
    // });
    // }
    // }
    //
    // /**
    // * 共享仓配订单完成消息
    // */
    // @RabbitListener(queues = "${mq.supplychain.distributtionOrder.complete}")
    // public void distributtionOrderComplete(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder, Message message)
    // {
    // //为了后面判断channel
    // inventoryDeliveryJiupiOrder.setJiupiOrderType(inventoryDeliveryJiupiOrder.getOrderType());
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrder);
    // String orderType = inventoryDeliveryJiupiOrder.getJiupiOrderType().equals(JiupiOrderTypeEnum.ORDER_TYPE_GROUP) ?
    // "团购直营订单" : "共享仓配订单";
    // LOG.info(orderType + "完成同步消息监听：{}", syncData);
    // if (isProcessPartnerDeliveryInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info(orderType + "完成同步消息：{},合作商订单不需要操作发货中库存数量.", syncData);
    // } else if (isProcessAgencyPickInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info(orderType + "完成同步消息：{},经销商提货订单不需要操作发货中库存数量.", syncData);
    // } else if (isProcessAgencyDeliveryInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("订单完成同步消息：{},经销商直配订单不需要操作发货中库存数量.", syncData);
    // } else {
    // idempotenceConsumer.apply("orderDeliveryCompleteListener:" + inventoryDeliveryJiupiOrder.getOrderId(), () -> {
    // if (CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("订单项项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // InventoryDeliveryJiupiOrder order =
    // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createDeliveryDetailBOList(order);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info(orderType + "完成确认成功监听: {}", inventoryDeliveryJiupiOrder.getOrderId());
    // });
    // }
    // }
    //
    // /**
    // * 兑奖配送订单完成消息
    // */
    // @RabbitListener(queues = "${mq.supplychain.awardOrderDelivery.complete}")
    // public void awardOrderComplete(ErpAwardDeliveryDTO erpAwardDeliveryDTO, Message message) {
    // String syncData = JSON.toJSONString(erpAwardDeliveryDTO);
    //// LOG.info("兑奖配送订单完成同步消息监听：{}", syncData);
    // idempotenceConsumer.apply("orderDeliveryCompleteListener:" + erpAwardDeliveryDTO.getId(), () -> {
    // if (CollectionUtils.isEmpty(erpAwardDeliveryDTO.getItemList())) {
    // LOG.warn("订单项项为空: 忽略: {}", JSON.toJSONString(erpAwardDeliveryDTO));
    // return;
    // }
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createAwardDeliveryBOList(erpAwardDeliveryDTO);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("兑奖配送订单完成确认成功监听: {}", erpAwardDeliveryDTO.getId());
    // });
    // }
    //
    // /**
    // * 招商订单完成消息
    // */
    // @RabbitListener(queues = "${mq.supplychain.jiupiOrder.wmsBusComplete}")
    // public void zhaoShangOrderComplete(BusOrderCompleteDTO busOrderCompleteDTO, Message message) {
    // if (busOrderCompleteDTO == null ||
    // !(busOrderCompleteDTO.getJiupiOrderType().equals(JiupiOrderTypeEnum.ORDER_TYPE_BUS)
    // && busOrderCompleteDTO.getCityId().equals(BUS_CITYID)
    // && busOrderCompleteDTO.getPickupWarehouseId().toString().startsWith(BUS_CITYID.toString()))) {
    // return;
    // }
    // String syncData = JSON.toJSONString(busOrderCompleteDTO);
    //// LOG.info("招商订单完成同步消息监听：{}", syncData);
    // String orderNo = busOrderCompleteDTO.getOrderNo();
    // idempotenceConsumer.apply("busOrderDeliveryCompleteListener:" + orderNo, () -> {
    // OutStockOrderDTO outStockOrderDTO = iOutStockCommQueryService.getOutStockOrderByRefOrderNo(orderNo,
    // busOrderCompleteDTO.getCityId());
    // if (outStockOrderDTO == null || outStockOrderDTO.getRefOrderNo() == null) {
    // LOG.info("招商订单信息不存在！" + syncData);
    // return;
    // }
    // //直接将延迟配送的入库单进行出库扣库存
    // InventoryDeliveryJiupiOrder deliveryOrder =
    // OrderDTOConvert.outStockOrderDTO2InventoryDeliveryJiupiOrder(outStockOrderDTO);
    // if (deliveryOrder == null) {
    // return;
    // }
    // InventoryDeliveryJiupiOrder order = productSkuZhaoShangBL.processOrderItemProductSkuId(deliveryOrder);
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.processZhaoShangOrderItemToBO(order);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("招商订单完成确认成功监听: {}", orderNo);
    // });
    // }
    //
    // //是否自提
    // private boolean isSelfDelivery(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
    // return
    // Objects.equals(DeliveryOrderConstant.DELIVERY_MODE_USERSELF.intValue(),inventoryDeliveryJiupiOrder.getDeliveryMode());
    // }
    //
    // //是否操作发货中库存
    // private boolean isProcessPartnerDeliveryInventory(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
    // return JIUPIORDERTYPE_PARTNERTOJIUPI.equals(inventoryDeliveryJiupiOrder.getJiupiOrderType())//合作商订单
    // && (inventoryDeliveryJiupiOrder.getPickupType() != null
    // && PICKUP_PARTNER.equals(inventoryDeliveryJiupiOrder.getPickupType()));//合作商收款
    // }
    //
    // //经销商配送
    // private boolean isProcessAgencyPickInventory(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
    // return Objects.equals(JIUPIORDERTYPE_SHOP, inventoryDeliveryJiupiOrder.getJiupiOrderType())
    // && Objects.equals(DELIVERY_MODE_PARTER, inventoryDeliveryJiupiOrder.getDeliveryMode());
    // }
    //
    // //是否操作发货中库存
    // private boolean isProcessAgencyDeliveryInventory(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
    // return ORDER_TYPE_DIRECT_DISTRIBUTION.equals(inventoryDeliveryJiupiOrder.getOrderType());//经销商直配订单
    // }
    //
    // /**
    // * 酒批订单配送失败，财务收款发消息给运营平台
    // */
    // @RabbitListener(queues = "${mq.supplychain.jiupiOrder.deliveryFailed}")
    // public void returnOrderSyncApply(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder, Message message) {
    // //为了后面判断channel
    // inventoryDeliveryJiupiOrder.setJiupiOrderType(inventoryDeliveryJiupiOrder.getOrderType());
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrder);
    // LOG.info("配送失败完成同步消息监听：{}", syncData);
    // if (isProcessPartnerDeliveryInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("配送失败完成同步消息：{},合作商订单不需要操作发货中库存数量.", syncData);
    // } else if (isProcessAgencyPickInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("配送失败完成同步消息：{},经销商提货订单不需要操作发货中库存数量.", syncData);
    // } else if (isProcessAgencyDeliveryInventory(inventoryDeliveryJiupiOrder)) {
    // LOG.info("订单完成同步消息：{},经销商直配订单不需要操作发货中库存数量.", syncData);
    // } else {
    // idempotenceConsumer.apply("deliveryFailedCompleteListener:" + inventoryDeliveryJiupiOrder.getOrderId(), () -> {
    // if (CollectionUtils.isEmpty(inventoryDeliveryJiupiOrder.getItems())) {
    // LOG.warn("订单项项为空: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    // return;
    // }
    // InventoryDeliveryJiupiOrder order =
    // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);
    // //配送失败数量需要用到发货数量
    // order.getItems().stream().flatMap(item -> item.getSecOwnerDetail().stream()).forEach(detail ->
    // detail.setCount(detail.getDispatchCount()));
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createDeliveryDetailBOList(order);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("配送失败完成确认成功监听: {}", inventoryDeliveryJiupiOrder.getOrderId());
    // });
    // }
    // }
    //
    // /**
    // * 调拨出库单完成
    // */
    // @RabbitListener(queues = "${mq.supplychain.allotOrders.complete}")
    // public void syncAllotOrder(List<NewOrderCompleteMqDTO> inventoryDeliveryJiupiOrders, Message message) {
    // String syncData = JSON.toJSONString(inventoryDeliveryJiupiOrders);
    // LOG.info("调拨出库单完成同步消息监听：{}", syncData);
    // if (inventoryDeliveryJiupiOrders == null) {
    // LOG.warn("调拨出库单完成，单为空: 忽略");
    // return;
    // }
    // List<String> noProcessOrderIds = idempotenceConsumer.getNoProcessCompleteOrderIds(inventoryDeliveryJiupiOrders,
    // "allotOrderDeliveryCompleteListener", "调拨出库完成");
    //// if (Objects.equals(inventoryDeliveryJiupiOrder.getPickType(), OrderPickTypeConstant.PICKTYPE_LOGIC)) {
    //// LOG.warn("调拨出库单完成: 忽略: {}", JSON.toJSONString(inventoryDeliveryJiupiOrder));
    //// }
    // idempotenceConsumer.apply(noProcessOrderIds, () -> {
    // List<WarehouseInventoryChangeBO> deliveryBOList =
    // warehouseChangListBOConverter.createCompleteOrderDeliveryBOList(inventoryDeliveryJiupiOrders);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("调拨出库单完成确认成功监听: {}",
    // JSON.toJSONString(inventoryDeliveryJiupiOrders.stream().map(NewOrderCompleteMqDTO::getOrderId).collect(Collectors.toList())));
    // });
    // }

}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.BatchAttributeRedisConstants;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.ConditionStateEnum;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchNOProcessBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchNOProcessItemBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchNOProcessResultBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchAttributeInfoConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchNOProcessConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeInfoMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeInfoPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.RedisUtil;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryManageService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStockAgeStrategyService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;

/**
 * <AUTHOR> 2018/4/10
 */
@Service
public class BatchAttributeInfoBL {

    private static final Logger LOG = LoggerFactory.getLogger(BatchAttributeInfoBL.class);

    @Autowired
    private BatchAttributeInfoMapper batchAttributeInfoMapper;
    @Autowired
    private ProductStoreMapper productStoreMapper;
    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private Gson gson;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    @Reference
    private IInStockQueryService inStockQueryService;

    @Reference
    private IWarehouseInventoryManageService iWarehouseInventoryManageService;

    @Reference
    private IStockAgeStrategyService iStockAgeStrategyService;

    @Autowired
    private RedisUtil<String> redisUtil;

    @Autowired
    @Qualifier(value = "batchInventoryTaskExecutor")
    private ExecutorService batchInventoryTaskExecutor;

    /**
     * 只获取批次编号不插入数据库
     *
     * @param productStoreId
     * @param productionDate
     * @return
     */
    public String getBatchAttributeInfoNo(String productStoreId, Integer ownerType, Date productionDate,
        Date batchTime) {
        return getProductBatchAttributeInfoNo(productStoreId, ownerType, productionDate, batchTime, new ArrayList<>(),
            false);
    }

    public String getBatchAttributeInfoNoBySku(Integer warehouseId, Long productSkuId, Integer channel,
        Date productionDate, Date batchTime) {
        ProductInventoryPO inventoryPO = productStoreMapper.findInventoryPOBySkuId(warehouseId, productSkuId, channel);
        // 如果该产品之前没入过库
        if (inventoryPO == null) {
            return null;
        }
        return getProductBatchAttributeInfoNo(inventoryPO.getId(), inventoryPO.getOwnerType(), productionDate,
            batchTime, new ArrayList<>(), Boolean.FALSE);
    }

    /**
     * 新增
     *
     * @param productInventoryChangeRecordPO
     */
    public String addBatchAttributeInfo(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
        String attributeList = productInventoryChangeRecordPO.getAttributeList();
        List<BatchAttributeInfoPO> batchAttributeInfoPOS = new ArrayList<>();
        // 反序列化.
        if (StringUtils.isNotEmpty(attributeList)) {
            ErpAttributeInfoDTO erpAttributeInfoDTO =
                gson.fromJson(attributeList, new TypeToken<ErpAttributeInfoDTO>() {}.getType());
            List<BatchAttributeInfoDTO> batchAttributeInfoDTOS = erpAttributeInfoDTO.getItems();
            // 反序列化.
            batchAttributeInfoPOS = BatchAttributeInfoConvert.BatchAttributeInfoDTOS2POS(batchAttributeInfoDTOS);
        }
        productInventoryChangeRecordPO
            .setProductionDate(formatDate(productInventoryChangeRecordPO.getProductionDate()));

        String batchAttributeInfoNo = getProductBatchAttributeInfoNo(productInventoryChangeRecordPO.getProductStoreId(),
            productInventoryChangeRecordPO.getOwnerType(), productInventoryChangeRecordPO.getProductionDate(),
            productInventoryChangeRecordPO.getBatchTime(), batchAttributeInfoPOS, true);
        return batchAttributeInfoNo;
    }

    public String getProductBatchAttributeInfoNo(String productStoreId, Integer ownerType, Date productionDate,
        Date batchTime, List<BatchAttributeInfoPO> batchAttributeInfoPOS, boolean isAddToDB) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuffer stringBuffer = new StringBuffer();
        String batchTimeStr = null;
        String productionDateStr = null;
        stringBuffer.append(productStoreId);
        if (batchAttributeInfoPOS == null) {
            batchAttributeInfoPOS = new ArrayList<>();
        }
        // 如果批属性不存在
        if (CollectionUtils.isEmpty(batchAttributeInfoPOS)) {
            // 去查库存 ownertype 如果是经销商,保存批次时间.
            ProductInventoryPO inventoryPO = productStoreMapper.findInventoryPO(productStoreId);
            AssertUtils.notNull(inventoryPO, "库存查询失败 productStoreId:" + productStoreId);
            // 查询是否库龄管控产品
            boolean storeControlProduct = iStockAgeStrategyService.isProductStockAgeControlsBySpec(
                inventoryPO.getCityId(), inventoryPO.getWarehouseId(), inventoryPO.getProductSpecificationId(),
                inventoryPO.getOwnerId(), inventoryPO.getSecOwnerId());
            // 批次时间【生产日期】
            batchTime = formatDate(batchTime);
            batchTimeStr = batchTime == null ? "" : simpleDateFormat.format(batchTime);
            // 生产日期
            productionDate = formatDate(productionDate);
            productionDateStr = productionDate == null ? "" : simpleDateFormat.format(productionDate);
            // 货主类型
            if (ownerType == null) {
                ownerType = inventoryPO.getOwnerType();
            }
            boolean isDealer = Objects.equals(OwnerTypeConst.入驻商, ownerType);
            // [入驻商]或者[库龄管控产品]拼接批次时间
            if (storeControlProduct || isDealer) {
                // 拼接-批次时间【生产日期】
                stringBuffer.append(batchTimeStr);
            }
            // 拼接-生产日期
            stringBuffer.append(productionDateStr);
            if (isAddToDB) {
                // [入驻商]或者[库龄管控产品]拼接批次时间
                boolean isProcessBatchTime =
                    (storeControlProduct || isDealer) && org.apache.commons.lang3.StringUtils.isNotBlank(batchTimeStr);
                if (isProcessBatchTime) {
                    BatchAttributeInfoPO batchTimeAttributeInfoPO = getBatchAttributeInfoPOByDesc(batchTimeStr, "批次时间");
                    // 添加 批次时间 属性
                    batchAttributeInfoPOS.add(batchTimeAttributeInfoPO);
                }
                BatchAttributeInfoPO productionDateAttributeInfoPO =
                    getBatchAttributeInfoPOByDesc(productionDateStr, "生产日期");
                // 添加 生产日期 属性
                batchAttributeInfoPOS.add(productionDateAttributeInfoPO);
            }
        } else {
            for (BatchAttributeInfoPO batchAttributeInfoPO : batchAttributeInfoPOS) {
                stringBuffer.append(batchAttributeInfoPO.getAttributeValueId())
                    .append(batchAttributeInfoPO.getAttributeValueName());
            }
        }

        String batchAttributeInfoNo = "PSX" + stringBuffer.toString().hashCode();

        if (isAddToDB) {
            // 去查batchattributeinfo是否已经存在该batchAttributeInfoNo;如果存在直接返回.
            if (CollectionUtils.isNotEmpty(batchAttributeInfoPOS)) {
                batchAttributeInfoPOS.forEach(n -> {
                    n.setBatchAttributeInfoNo(batchAttributeInfoNo);
                });
                saveBatchAttrInfo(Collections.singletonMap(batchAttributeInfoNo, batchAttributeInfoPOS));
            }
        }
        return batchAttributeInfoNo;
    }

    private BatchAttributeInfoPO getBatchAttributeInfoPOByDesc(String productionDateStr, String Desc) {
        BatchAttributeInfoPO batchAttributeInfoPO = new BatchAttributeInfoPO();
        batchAttributeInfoPO.setAttributeId(0L);
        batchAttributeInfoPO.setAttributeName(Desc);
        batchAttributeInfoPO.setAttributeValueId("0");
        batchAttributeInfoPO.setAttributeValueName(productionDateStr);
        batchAttributeInfoPO.setRemark("");
        return batchAttributeInfoPO;
    }

    public Date formatDate(Date batchTime) {
        if (batchTime != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formatDate = simpleDateFormat.format(batchTime);
            try {
                batchTime = simpleDateFormat.parse(formatDate);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return batchTime;
    }

    /**
     * 根据批属性编号查找详情
     *
     * @param batchAttributeInfoNo
     * @return
     */
    public List<BatchAttributeInfoDTO> findAttributeInfoByNo(String batchAttributeInfoNo) {
        return batchAttributeInfoMapper.findAttributeInfoByNo(batchAttributeInfoNo);
    }

    public String getProductBatchAttributeInfoNo(String productStoreId, Date productionDate, Date batchTime,
        Integer ownerType) {
        // SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // StringBuffer stringBuffer = new StringBuffer();
        // stringBuffer.append(productStoreId);
        // String batchTimeStr = null;
        // String productionDateStr = null;
        // if (Objects.equals(OwnerTypeConst.入驻商, ownerType)) {
        // batchTimeStr = batchTime == null ? "" : simpleDateFormat.format(batchTime);
        // stringBuffer.append(batchTimeStr);
        // }
        //
        // productionDateStr = productionDate == null ? "" : simpleDateFormat.format(productionDate);
        // stringBuffer.append(productionDateStr);
        //
        //
        // String batchAttributeInfoNo = "PSX" + String.valueOf(stringBuffer.toString().hashCode());

        // return batchAttributeInfoNo;
        throw new BusinessValidateException("getProductBatchAttributeInfoNo [生成批次号] 方法已经作废！");
    }

    /**
     * 同步批次号
     *
     * @param ownerType
     */
    public void processBatchNo(Integer ownerType) {
        throw new BusinessValidateException("processBatchNo[同步批次号] 方法已经作废！");

        // List<ProductStoreBatchNoDTO> productStoreBatchNoDTOList =
        // productStoreBatchMapper.findProductStoreBatchByOwnerType(ownerType);
        // if (CollectionUtils.isEmpty(productStoreBatchNoDTOList)) {
        // LOG.info("批次号对象为空");
        // return;
        // }
        // List<ProductStoreBatchNoUpdateDTO> list = productStoreBatchNoDTOList.stream().map(p -> {
        // ProductStoreBatchNoUpdateDTO dto = new ProductStoreBatchNoUpdateDTO();
        // dto.setBatchId(p.getBatchId());
        // dto.setBatchNo(this.getProductBatchAttributeInfoNo(p.getProductStoreId(), p.getProductionDate(),
        // p.getBatchTime(), ownerType));
        // return dto;
        // }).collect(Collectors.toList());
        // LOG.info(String.format("批次对象(%s): %s", list.size(), JSON.toJSONString(list)));
        // productStoreBatchMapper.updateProductStoreBatchNo(list);
    }

    /**
     * 根据仓库产品信息获取入库批次编号
     */
    public List<BatchNOQueryResultDTO> genOrderBatchInfoNO(BatchNOQueryDTO queryDTO) {
        // 1、查询产品是否有库存记录
        // 2、没有库存记录创建一条库存记录数为0的记录
        // 3、创建产品入库批次编码
        LOG.info("获取批次编号产品信息参数：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "创建批次编码信息不能为空");
        AssertUtils.notNull(queryDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(queryDTO.getAddItemDTOList(), "获取批次编号产品信息不能为空");
        List<BatchNOQueryItemDTO> addItemDTOList = queryDTO.getAddItemDTOList();
        Optional<BatchNOQueryItemDTO> hasEmptyInStockTimeOpt =
            addItemDTOList.stream().filter(e -> e != null && e.getInStockTime() == null).findAny();

        Optional<BatchNOQueryItemDTO> hasEmptySkuIdOpt =
            addItemDTOList.stream().filter(e -> e != null && e.getSkuId() == null).findAny();
        if (hasEmptyInStockTimeOpt.isPresent()) {
            throw new BusinessValidateException(
                String.format("产品[%s]入库时间为空，获取批次编码失败！", hasEmptyInStockTimeOpt.get().getSkuId()));
        }
        if (hasEmptySkuIdOpt.isPresent()) {
            throw new BusinessValidateException("批次编号产品信息存在SkuID为空信息！");
        }
        List<Long> productSkuIds = addItemDTOList.stream().filter(e -> e != null).map(e -> e.getSkuId()).distinct()
            .collect(Collectors.toList());
        LOG.info("获取批次编号产品SkuId集合：{}", JSON.toJSONString(productSkuIds));
        List<ProductSkuDTO> skuDTOS = iProductSkuQueryService.findBySku(productSkuIds);
        AssertUtils.notEmpty(skuDTOS, "产品SKU信息不存在，获取批次编码失败！");
        // 批次编号处理BO
        List<BatchNOProcessBO> batchNOProcessBOList =
            BatchNOProcessConvert.createBatchInfoNOProcessBO(queryDTO, skuDTOS);
        if (CollectionUtils.isEmpty(batchNOProcessBOList)) {
            LOG.info("生成批次编号BO集合为空，创建批次编号失败！");
            return new ArrayList<>();
        }
        List<ProductInventoryDTO> productInventoryDTOList = queryProductInventory(batchNOProcessBOList);
        if (CollectionUtils.isEmpty(productInventoryDTOList)) {
            LOG.info("生成批次编号时产品对应库存信息没有找到，创建批次编号失败！");
            return new ArrayList<>();
        }
        Map<String, List<BatchAttributeInfoPO>> allBatchNOMap = new HashMap<>(16);
        List<BatchNOQueryResultDTO> resultDTOList = new ArrayList<>();
        for (BatchNOProcessBO bo : batchNOProcessBOList) {
            if (bo == null) {
                continue;
            }
            Optional<ProductInventoryDTO> inventoryAnyOpt = productInventoryDTOList.stream()
                .filter(e -> e != null && Objects.equals(e.getProductSpecificationId(), bo.getProductSpecificationId())
                    && Objects.equals(e.getOwnerId(), bo.getOwnerId())
                    && Objects.equals(e.getSecOwnerId(), bo.getSecOwnerId()))
                .findAny();
            if (!inventoryAnyOpt.isPresent()) {
                LOG.info("生成批次编号时没有找到产品[{}]对应库存信息，跳过此产品！", bo.getProductSkuId());
                continue;
            }
            Pair<String, List<BatchAttributeInfoPO>> productBatchPair =
                createProductBatchInfoNo(inventoryAnyOpt.get(), bo);
            String batchNo = productBatchPair.getLeft();
            BatchNOQueryResultDTO resultDTO = new BatchNOQueryResultDTO();
            resultDTO.setSkuId(bo.getProductSkuId());
            resultDTO.setBatchInfoNO(batchNo);
            resultDTO.setBatchTime(bo.getBatchTime());
            resultDTO.setProductionDate(bo.getProductionDate());
            // 添加结果集
            resultDTOList.add(resultDTO);
            // 记录持久化批次信息
            if (!allBatchNOMap.containsKey(batchNo)) {
                allBatchNOMap.put(batchNo, productBatchPair.getRight());
            }
        }
        // 保存批次信息
        saveBatchAttrInfo(allBatchNOMap);
        return resultDTOList;
    }

    public Pair<String, List<BatchAttributeInfoPO>> createProductBatchInfoNo(ProductInventoryDTO inventoryDTO,
        BatchNOProcessBO processBO) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuffer stringBuffer = new StringBuffer();
        String batchTimeStr = null;
        String productionDateStr = null;
        List<BatchAttributeInfoPO> batchAttributeInfoPOS = new ArrayList();
        stringBuffer.append(inventoryDTO.getId());
        // 批次时间【生产日期】
        Date batchTime = formatDate(processBO.getBatchTime());
        batchTimeStr = batchTime == null ? "" : simpleDateFormat.format(batchTime);
        // 生产日期
        Date productionDate = formatDate(processBO.getProductionDate());
        productionDateStr = productionDate == null ? "" : simpleDateFormat.format(productionDate);
        // 货主类型
        Integer ownerType = inventoryDTO.getOwnerType();
        boolean isDealer = Objects.equals(OwnerTypeConst.入驻商, ownerType);
        // 查询是否库龄管控产品
        boolean storeControlProduct = iStockAgeStrategyService.isProductStockAgeControlsBySpec(inventoryDTO.getCityId(),
            inventoryDTO.getWarehouseId(), inventoryDTO.getProductSpecificationId(), inventoryDTO.getOwnerId(),
            inventoryDTO.getSecOwnerId());
        // [入驻商]或者[库龄管控]产品需要拼接批次时间
        boolean isProcessBatchTime =
            (isDealer || storeControlProduct) && org.apache.commons.lang3.StringUtils.isNotBlank(batchTimeStr);
        if (isProcessBatchTime) {
            // 拼接 - 批次时间【入库日期】
            stringBuffer.append(batchTimeStr);
            // 记录批次时间
            BatchAttributeInfoPO batchOwnerAttributeInfoPO = getBatchAttributeInfoPOByDesc(batchTimeStr, "批次时间");
            // 添加 入驻商-入库时间 属性
            batchAttributeInfoPOS.add(batchOwnerAttributeInfoPO);
        }
        // 拼接 - 生产日期
        stringBuffer.append(productionDateStr);
        BatchAttributeInfoPO productionDateAttributeInfoPO = getBatchAttributeInfoPOByDesc(productionDateStr, "生产日期");
        // 添加 生产日期 属性
        batchAttributeInfoPOS.add(productionDateAttributeInfoPO);
        // 扩展 - 批属性值
        List<BatchNOProcessItemBO> processItemBOList = processBO.getProcessItemBOList();
        if (CollectionUtils.isNotEmpty(processItemBOList)) {
            // 扩展批属性
            String extBatchAttrStr = processItemBOList.stream()
                .filter(item -> item != null && Objects.equals(ConditionStateEnum.是.getType(), item.getIsCalculation())
                    && item.getDicId() != null)
                .map(item -> (item.getAttributeValueId() != null && item.getAttributeValueId() != 0L)
                    ? item.getAttributeValueId().toString()
                    : org.apache.commons.lang3.StringUtils.trimToEmpty(item.getAttributeValueName()))
                .distinct().collect(Collectors.joining(""));
            // 添加 扩展批属性 属性
            stringBuffer.append(extBatchAttrStr);
            // 扩展批次属性
            List<BatchAttributeInfoPO> extBatchInfoPOList =
                processItemBOList.stream().filter(Objects::nonNull).map(e -> {
                    BatchAttributeInfoPO infoPO = new BatchAttributeInfoPO();
                    infoPO.setAttributeId(e.getDicId());
                    infoPO.setAttributeName(org.apache.commons.lang3.StringUtils.trimToEmpty(e.getAttributeName()));
                    infoPO.setAttributeValueId(
                        e.getAttributeValueId() != null ? e.getAttributeValueId().toString() : "0");
                    infoPO.setAttributeValueName(
                        org.apache.commons.lang3.StringUtils.trimToEmpty(e.getAttributeValueName()));
                    return infoPO;
                }).collect(Collectors.toList());
            batchAttributeInfoPOS.addAll(extBatchInfoPOList);
        }
        // 生成批次编码
        String batchAttributeInfoNo = "PSX" + stringBuffer.toString().hashCode();
        LOG.info(String.format("StoreId：%s,生产日期：%s,入库时间：%s, 是否库龄管控产品:%s, 是否入驻商：%s 编号：%s", inventoryDTO.getId(),
            productionDateStr, batchTimeStr, storeControlProduct, isDealer, batchAttributeInfoNo));
        // 赋值
        for (BatchAttributeInfoPO po : batchAttributeInfoPOS) {
            po.setBatchAttributeInfoNo(batchAttributeInfoNo);
        }
        return new ImmutablePair<String, List<BatchAttributeInfoPO>>(batchAttributeInfoNo, batchAttributeInfoPOS);
    }

    public void saveBatchAttrInfo(Map<String, List<BatchAttributeInfoPO>> batchAttrMap) {
        if (batchAttrMap == null || batchAttrMap.isEmpty()) {
            return;
        }
        List<String> batchNOList = batchAttrMap.keySet().stream().collect(Collectors.toList());
        List<BatchAttributeInfoPO> batchAttributeInfoPOList = batchAttrMap.values().stream()
            .filter(e -> CollectionUtils.isNotEmpty(e)).flatMap(e -> e.stream()).collect(Collectors.toList());
        List<String> existBatchNoList = getExistBatchNoList(batchNOList);
        if (CollectionUtils.isNotEmpty(existBatchNoList)) {
            // 移除已经存在的批次编号
            batchAttributeInfoPOList.removeIf(e -> e != null && existBatchNoList.contains(e.getBatchAttributeInfoNo()));
        }
        if (CollectionUtils.isNotEmpty(batchAttributeInfoPOList)) {
            List<String> newBatchNOList = batchAttributeInfoPOList.stream()
                .map(BatchAttributeInfoPO::getBatchAttributeInfoNo).collect(Collectors.toList());
            // 存入缓存
            redisUtil.setList(BatchAttributeRedisConstants.BATCH_NO_LIST_REDIS_KEY, newBatchNOList, 3,
                TimeUnit.SECONDS);
            batchAttributeInfoPOList.forEach(info -> {
                info.setAttributeName(info.getAttributeName() == null ? "" : info.getAttributeName());
                info.setAttributeValueId(info.getAttributeValueId() == null ? "0" : info.getAttributeValueId());
                info.setAttributeValueName(info.getAttributeValueName() == null ? "" : info.getAttributeValueName());
                info.setRemark(info.getRemark() == null ? "" : info.getRemark());
            });
            // 持久化数据库
            Lists.partition(batchAttributeInfoPOList, 200).forEach(part -> {
                part.forEach(p -> p.setId(UUIDGenerator.getUUID(p.getClass().getName())));
                batchAttributeInfoMapper.insertList(part);
            });
        }
    }

    /**
     * 获取已经存在的批次编号
     */
    private List<String> getExistBatchNoList(List<String> newBatchNOList) {
        // redis + 数据库排重
        List<String> cacheBatchNoList = redisUtil.getList(BatchAttributeRedisConstants.BATCH_NO_LIST_REDIS_KEY, 0, -1);
        // LOG.info("getExistBatchNoList - Redis 获取批次编号信息：{}", JSON.toJSONString(cacheBatchNoList));
        List<BatchAttributeInfoPO> existInfoList = new ArrayList<>(newBatchNOList.size());
        // 创建任务集合：大批量的产品首次过来需要创建库存,逻辑比较复杂然后耗时严重,此处用连接池处理
        List<FutureTask<List<BatchAttributeInfoPO>>> taskList = new ArrayList<FutureTask<List<BatchAttributeInfoPO>>>();
        Lists.partition(newBatchNOList, 500).forEach(part -> {
            FutureTask<List<BatchAttributeInfoPO>> ft =
                new FutureTask<List<BatchAttributeInfoPO>>(new Callable<List<BatchAttributeInfoPO>>() {
                    @Override
                    public List<BatchAttributeInfoPO> call() throws Exception {
                        List<BatchAttributeInfoPO> exists =
                            batchAttributeInfoMapper.findByBatchAttributeInfoNoList(part);
                        if (CollectionUtils.isNotEmpty(exists)) {
                            return exists;
                        }
                        return Collections.emptyList();
                    }
                });
            taskList.add(ft);
            // 提交给线程池执行任务
            batchInventoryTaskExecutor.submit(ft);
        });
        taskList.forEach(task -> {
            try {
                if (task != null) {
                    // FutureTask的get方法会自动阻塞,直到获取计算结果为止
                    List<BatchAttributeInfoPO> taskResult = task.get();
                    if (CollectionUtils.isNotEmpty(taskResult)) {
                        existInfoList.addAll(task.get());
                    }
                }
            } catch (Exception e) {
                throw new BusinessValidateException("获取批次编号信息失败!", e);
            }
        });
        List<String> existBatchNoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cacheBatchNoList)) {
            existBatchNoList.addAll(cacheBatchNoList);
        }
        if (CollectionUtils.isNotEmpty(existInfoList)) {
            existBatchNoList.addAll(existInfoList.stream().filter(Objects::nonNull)
                .map(e -> e.getBatchAttributeInfoNo()).collect(Collectors.toList()));
        }
        return existBatchNoList;
    }

    // 查询仓库库存
    private List<ProductInventoryDTO> queryProductInventory(List<BatchNOProcessBO> batchNOProcessBOList) {
        Map<String, List<BatchNOProcessBO>> processGroupMap = batchNOProcessBOList.stream().filter(e -> e != null)
            .collect(Collectors.groupingBy(e -> e.getWarehouseIdAndChannelAndOwnerId()));
        List<ProductInventoryDTO> productInventoryDTOList = new ArrayList<>();
        // 创建任务集合：大批量的产品首次过来需要创建库存,逻辑比较复杂然后耗时严重,此处用连接池处理
        List<FutureTask<List<ProductInventoryDTO>>> taskList = new ArrayList<FutureTask<List<ProductInventoryDTO>>>();
        for (Map.Entry<String, List<BatchNOProcessBO>> entry : processGroupMap.entrySet()) {
            FutureTask<List<ProductInventoryDTO>> ft =
                new FutureTask<List<ProductInventoryDTO>>(new Callable<List<ProductInventoryDTO>>() {
                    @Override
                    public List<ProductInventoryDTO> call() throws Exception {
                        // 集合数据
                        List<BatchNOProcessBO> value = entry.getValue();
                        // 移除规格为空的数据
                        value.removeIf(e -> e != null && e.getProductSpecificationId() == null);
                        if (CollectionUtils.isEmpty(value)) {
                            return Collections.emptyList();
                        }
                        BatchNOProcessBO processBO = value.get(0);
                        Long ownerId = processBO.getOwnerId();
                        Integer warehouseId = processBO.getWarehouseId();
                        Integer cityId = processBO.getCityId();
                        Integer channel =
                            processBO.getChannel() == null ? ProductChannelType.JIUPI : processBO.getChannel();
                        Long secOwnerId = processBO.getSecOwnerId();
                        // 默认创建库存
                        Boolean createInventory = ObjectUtils.defaultIfNull(processBO.getCreateInventory(), true);
                        Map<Long,
                            Long> specSkuMap = value.stream().filter(e -> e != null)
                                .collect(Collectors.toMap(e -> e.getProductSpecificationId(), e -> e.getProductSkuId(),
                                    (v1, v2) -> v1 != null ? v1 : v2));
                        // 查询库存信息，当产品库存不存在时创建一条为0库存记录
                        ProductInventoryQueryDTO inventoryQueryDTO = new ProductInventoryQueryDTO();
                        inventoryQueryDTO.setSpecSkuMap(specSkuMap);
                        inventoryQueryDTO.setCityId(cityId);
                        inventoryQueryDTO.setWarehouseId(warehouseId);
                        inventoryQueryDTO.setOwnerId(ownerId);
                        inventoryQueryDTO.setChannel(channel);
                        inventoryQueryDTO.setSecOwnerId(secOwnerId);
                        inventoryQueryDTO.setCreateInventory(createInventory);
                        List<ProductInventoryDTO> productInventories = iWarehouseInventoryManageService
                            .getLongProductInventoryPOMapAndCreateNoExits(inventoryQueryDTO);
                        if (CollectionUtils.isNotEmpty(productInventories)) {
                            return productInventories;
                        } else {
                            return Collections.emptyList();
                        }
                    }
                });
            taskList.add(ft);
            // 提交给线程池执行任务
            batchInventoryTaskExecutor.submit(ft);
        }
        taskList.forEach(task -> {
            try {
                if (task != null) {
                    // FutureTask的get方法会自动阻塞,直到获取计算结果为止
                    List<ProductInventoryDTO> taskResult = task.get();
                    if (CollectionUtils.isNotEmpty(taskResult)) {
                        productInventoryDTOList.addAll(task.get());
                    }
                }
            } catch (Exception e) {
                throw new BusinessValidateException("生成批次号失败!", e);
            }
        });
        return productInventoryDTOList;
    }

    /**
     * 保存批属性并且生成批次编号
     */
    public List<BatchAttributeValueSaveReturnDTO> saveBatchAttributeDicInfo(BatchAttributeValueSaveDTO saveDTO) {
        LOG.info("保存批属性并且生成批次编号参数：{}", JSON.toJSONString(saveDTO));
        AssertUtils.notNull(saveDTO, "参数不能为空");
        AssertUtils.notNull(saveDTO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(saveDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(saveDTO.getItemList(), "批属性信息不能为空");
        // 明细项
        List<BatchAttributeValueSaveItemDTO> itemList = saveDTO.getItemList();
        itemList.stream().filter(Objects::nonNull).forEach(item -> {
            AssertUtils.isTrue(item.getProductSkuId() != null || item.getProductSpecificationId() != null,
                "产品SkuId与规格ID不能同时为空！");
            // 明细项详情
            List<BatchAttributeValueSaveItemDetailDTO> itemDetailList = item.getItemDetailList();
            if (CollectionUtils.isNotEmpty(itemDetailList)) {
                itemDetailList.stream().filter(Objects::nonNull).forEach(detail -> {
                    AssertUtils.notNull(detail.getDicId(), "字典属性值ID不能为空");
                    AssertUtils.notNull(detail.getAttributeName(), "字典属性值名称不能为空");
                    AssertUtils.isTrue(detail.getAttributeValueId() != null || detail.getAttributeValueName() != null,
                        "属性值ID与属性值名称不能同时为空！");
                    AssertUtils.notNull(detail.getIsCalculation(), "是否参与批属性计算标识不能为空");
                });
            }
        });
        // 回填产品信息
        backFillProductInfo(itemList);
        // 1、查询库存信息
        List<BatchNOProcessBO> batchNOProcessBOS = BatchNOProcessConvert
            .convertBatchSaveItemTOBatchProcessBO(saveDTO.getCityId(), saveDTO.getWarehouseId(), itemList);
        if (CollectionUtils.isEmpty(batchNOProcessBOS)) {
            LOG.info("生成批次编号BO集合为空，创建批次编号失败！");
            return Collections.emptyList();
        }
        // 设置是否创建库存
        batchNOProcessBOS.stream().filter(Objects::nonNull)
            .forEach(bo -> bo.setCreateInventory(ObjectUtils.defaultIfNull(saveDTO.getCreateInventory(), true)));
        List<ProductInventoryDTO> productInventoryDTOList = queryProductInventory(batchNOProcessBOS);
        if (CollectionUtils.isEmpty(productInventoryDTOList)) {
            LOG.info("生成批次编号时产品对应库存信息没有找到，创建批次编号失败！");
            return Collections.emptyList();
        }
        // 2、生成批次编号
        Map<String, List<BatchAttributeInfoPO>> allBatchNOMap = new HashMap<>(16);
        List<BatchAttributeValueSaveReturnDTO> returnDTOList = new ArrayList<>();
        // 异步结果集合
        List<BatchNOProcessResultBO> taskResultList = new ArrayList<>(batchNOProcessBOS.size());
        // 创建任务集合：大批量的产品首次过来需要创建库存,逻辑比较复杂然后耗时严重,此处用连接池处理
        List<FutureTask<List<BatchNOProcessResultBO>>> taskList =
            new ArrayList<FutureTask<List<BatchNOProcessResultBO>>>();
        batchNOProcessBOS.stream().filter(Objects::nonNull)
            .collect(Collectors.groupingBy(BatchNOProcessBO::getWarehouseIdAndChannelAndOwnerId))
            .forEach((invKey, batchBOList) -> {
                if (CollectionUtils.isNotEmpty(batchBOList)) {
                    FutureTask<List<BatchNOProcessResultBO>> ft =
                        new FutureTask<List<BatchNOProcessResultBO>>(new Callable<List<BatchNOProcessResultBO>>() {
                            @Override
                            public List<BatchNOProcessResultBO> call() throws Exception {
                                List<BatchNOProcessResultBO> subRS = new ArrayList<>(batchBOList.size());
                                for (BatchNOProcessBO bo : batchBOList) {
                                    if (bo == null) {
                                        continue;
                                    }
                                    Optional<ProductInventoryDTO> inventoryAnyOpt = productInventoryDTOList.stream()
                                        .filter(e -> e != null
                                            && Objects.equals(e.getProductSpecificationId(),
                                                bo.getProductSpecificationId())
                                            && Objects.equals(e.getOwnerId(), bo.getOwnerId())
                                            && Objects.equals(e.getSecOwnerId(), bo.getSecOwnerId()))
                                        .findAny();
                                    if (!inventoryAnyOpt.isPresent()) {
                                        LOG.info("生成批次编号时没有找到产品[{}]对应库存信息，跳过此产品！规格ID:{}, OwnerId:{}, SecOwnerId:{}",
                                            bo.getProductSkuId(), bo.getProductSpecificationId(), bo.getOwnerId(),
                                            bo.getSecOwnerId());
                                        continue;
                                    }
                                    Pair<String, List<BatchAttributeInfoPO>> productBatchPair =
                                        createProductBatchInfoNo(inventoryAnyOpt.get(), bo);
                                    String batchNo = productBatchPair.getLeft();
                                    BatchNOProcessResultBO resultBO = new BatchNOProcessResultBO();
                                    resultBO.setCityId(saveDTO.getCityId());
                                    resultBO.setWarehouseId(saveDTO.getWarehouseId());
                                    resultBO.setRefInfoId(bo.getRefInfoId());
                                    resultBO.setProductSkuId(bo.getProductSkuId());
                                    resultBO.setBatchAttributeInfoNo(batchNo);
                                    resultBO.setBatchInfoList(productBatchPair.getRight());
                                    subRS.add(resultBO);
                                }
                                return subRS;
                            }
                        });
                    taskList.add(ft);
                    // 提交给线程池执行任务
                    batchInventoryTaskExecutor.submit(ft);
                }
            });
        taskList.forEach(task -> {
            try {
                if (task != null) {
                    List<BatchNOProcessResultBO> taskResult = task.get();
                    if (CollectionUtils.isNotEmpty(taskResult)) {
                        taskResultList.addAll(task.get());
                    }
                }
            } catch (Exception e) {
                throw new BusinessValidateException("生成批次编号信息失败!", e);
            }
        });
        if (CollectionUtils.isNotEmpty(taskResultList)) {
            for (BatchNOProcessResultBO resultBO : taskResultList) {
                if (resultBO == null) {
                    continue;
                }
                // 记录返回值
                BatchAttributeValueSaveReturnDTO returnDTO = new BatchAttributeValueSaveReturnDTO();
                BeanUtils.copyProperties(resultBO, returnDTO);
                returnDTOList.add(returnDTO);
                // 记录持久化批次信息
                if (!allBatchNOMap.containsKey(resultBO.getBatchAttributeInfoNo())) {
                    allBatchNOMap.put(resultBO.getBatchAttributeInfoNo(), resultBO.getBatchInfoList());
                }
            }
        }
        // 保存批次信息
        saveBatchAttrInfo(allBatchNOMap);
        // 返回批次编号
        return returnDTOList;
    }

    /**
     * 有skuId没有规格id的进行产品信息回填
     */
    private void backFillProductInfo(List<BatchAttributeValueSaveItemDTO> itemList) {
        Predicate<BatchAttributeValueSaveItemDTO> hasSkuPredicate = (input -> {
            if (input != null && input.getProductSkuId() != null && input.getProductSpecificationId() == null) {
                return true;
            }
            return false;
        });
        List<Long> productskuIdList = itemList.stream().filter(hasSkuPredicate).map(e -> e.getProductSkuId()).distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productskuIdList)) {
            Map<Long, ProductSkuDTO> skuMap = iProductSkuQueryService.findBySkuWithMap(productskuIdList);
            itemList.stream().filter(hasSkuPredicate).forEach(item -> {
                ProductSkuDTO skuDTO = skuMap.get(item.getProductSkuId());
                if (skuDTO != null) {
                    item.setProductSpecificationId(skuDTO.getProductSpecificationId());
                    item.setOwnerId(skuDTO.getCompany_Id());
                    // 如果有二级货主以传递过来为准
                    item.setSecOwnerId(item.getSecOwnerId() == null ? skuDTO.getSecOwnerId() : item.getSecOwnerId());
                } else {
                    LOG.warn("backFillProductInfo - 没有找到 SKU [{}] 相关产品信息！", item.getProductSkuId());
                }
            });
        }
    }

}

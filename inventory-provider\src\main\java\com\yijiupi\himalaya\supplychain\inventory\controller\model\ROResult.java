package com.yijiupi.himalaya.supplychain.inventory.controller.model;

public class ROResult<T> extends BaseResult {

    private T data;

    public ROResult() {
        super.setResult("success");
        super.setMessage(WebConstants.RESULT_SUCCESS);
    }

    public ROResult(T data) {
        super.setResult("success");
        this.data = data;
        super.setMessage(WebConstants.RESULT_SUCCESS);
        super.setSuccess(true);
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public void getSuccess(Integer totalCount) {
        super.setTotalCount(totalCount);
        super.setResult("success");
        super.setMessage(WebConstants.RESULT_SUCCESS);
    }

    public static <T> ROResult<T> success(T data) {
        ROResult<T> result = new ROResult<>();
        result.setData(data);
        result.setResult(WebConstants.RESULT_SUCCESS);
        return result;
    }

    public static <T> ROResult<T> getResult(T data) {
        return new ROResult<>(data);
    }

}

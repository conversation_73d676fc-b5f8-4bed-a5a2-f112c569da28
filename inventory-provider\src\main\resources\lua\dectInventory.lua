local pk = KEYS[1]
local len = #ARGV
local temp
local avai
local error = 0
local deductTable = {}
local deductResult = {error}
for i = 1, len, 4 do
    local keys = ARGV[i];
    local name = ARGV[i + 1];
    local cnt = tonumber(ARGV[i + 2]);
    local mod = tonumber(ARGV[i + 3]);
    temp = cnt
    if (mod == 1) then
        for key in string.gmatch(keys, "([^,]+)") do
            avai = tonumber(redis.call('hget', pk, key)) or 0
            if (avai < cnt) then
                error = 1
                table.insert(deductResult, name ..key..'库存不足:' .. avai .. '<' .. cnt)
                break;
            end
        end
    elseif (mod == 2) then
        for key in string.gmatch(keys, "([^,]+)") do
            avai = tonumber(redis.call('hget', pk, key)) or 0
            if (avai < temp) then
                temp = avai
            end
        end
        if (temp < 0) then
            temp = 0
        end
    end
    if (error == 0) then
        temp = 0 - temp;
        for key in string.gmatch(keys, "([^,]+)") do
            table.insert(deductResult, key..','..name..','..temp..','..mod);
            table.insert(deductTable, { key, temp })
        end
    end
end

if (error == 0) then
    for _, v in pairs(deductTable) do
        redis.call('hincrby', pk, v[1], v[2])
    end
else
    deductResult[1] = 1
end
return table.concat(deductResult,",")



-- [库存A,库存B 扣除数 模式(1:必须扣, 2:尽量扣)] 参数三个一组, 每组返回一个数量
-- 必须扣逻辑: 库存A, 库存B必须扣除指定数量, 否则库存不足, 返回成功扣除数
-- 尽量扣逻辑: 库存A, 库存B取库存余量最小值扣除, 返回成功扣除数
-- 返回操作的结果，error 正确的扣除信息或错误信息




package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.AbstractBaseTest;
import com.yijiupi.himalaya.supplychain.inventory.controller.BatchInventoryController;
import com.yijiupi.himalaya.supplychain.inventory.controller.StoreInfoController;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.BaseResult;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.FindStorePageDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProductInventoryPOMapperTest extends AbstractBaseTest {

    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;

    // 根据主键查找产品库存 1/18通过
    // @Test
    // public void selectByPrimaryKey() {
    // ProductInventoryPO po = productInventoryPOMapper.selectByPrimaryKey("a75cca414e6d4caa8a8429dbde69143a");
    // }

    // 根据主键集合查找产品库存集合 1/18通过
    // @Test
    // public void selectInventoryListByPrimaryKey() {
    // List<ProductInventoryPO> list =
    // productInventoryPOMapper.selectInventoryListByPrimaryKey(Collections.singleton("a75cca414e6d4caa8a8429dbde69143a"));
    // }

    // 通过产品SkuId和cityId集合查询库存信息 1/18通过
    // @Test
    // public void getProductInventoryBySkuIdCityId() {
    // ArrayList<ProductSkuForInventoryDTO> list = new ArrayList<>();
    // ProductSkuForInventoryDTO dto1 = new ProductSkuForInventoryDTO();
    // dto1.setCityId(100);
    // dto1.setProductSkuId(10000000008878L);
    // ProductSkuForInventoryDTO dto2 = new ProductSkuForInventoryDTO();
    // dto2.setCityId(999);
    // dto2.setProductSkuId(99900050779542L);
    // dto2.setChannel(0);
    // dto2.setSecOwnerId(0);
    // list.add(dto1);
    // list.add(dto2);
    //
    // List<ProductInventoryPO> productInventoryBySkuIdCityId =
    // productInventoryPOMapper.getProductInventoryBySkuIdCityId(dto2);
    // }

    // 通过skuid和仓库id查询出记录 todo
    // @Test
    // public void findProductInventoryByProductSkuIdWarehouseId() {
    //// List<ProductInventoryPO> list =
    // productInventoryPOMapper.findProductInventoryByProductSkuIdWarehouseId(Collections.singletonList(99900050779542L),
    // 9991, 0);
    // }

    // 批量修改库存数量 1/18通过
    @Test
    public void increaseWarehouseCountBatchById() {
        List<ProductInventoryPO> lstTmp = new ArrayList<>();
        ProductInventoryPO productInventoryPO = new ProductInventoryPO();
        productInventoryPO.setId("06f636b33e954106ab7ed7d76998352c");
        productInventoryPO.setChangeCount(BigDecimal.valueOf(100));
        productInventoryPO.setLastUpdateTime(new Date());
        lstTmp.add(productInventoryPO);
        productInventoryPOMapper.increaseWarehouseCountBatchById(lstTmp);
    }

    // 查询某城市下所有sku库存 todo
    // @Test
    // public void listProductSkuInventoyByCity() {
    //// productInventoryPOMapper.listProductSkuInventoyByCity()
    // }

    // 库存报表 1/18通过
    @Test
    public void findStoreReportPageByAuth() {
        StockReportSO so = new StockReportSO();
        // so.setSecOwnerId(1);
        PagerCondition pager = new PagerCondition();
        pager.setCurrentPage(1);
        pager.setPageSize(20);
        so.setWarehouseIds(Arrays.asList(9981));
        PageResult<InventoryReportDTO> list =
            productInventoryPOMapper.findStoreReportPageByAuth(so, pager.getCurrentPage(), pager.getPageSize());
        System.out.println(1);
    }

    @Autowired
    private BatchInventoryController batchInventoryController;
    @Test
    public void findBatchInventoryInfoTest() {
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(9981);
        batchInventoryQueryDTO.setWarehouseAllocationType(1);
        batchInventoryQueryDTO.setQueryType(2);
        BaseResult batchInventoryInfo = batchInventoryController.findBatchInventoryInfo(batchInventoryQueryDTO);
        Assert.assertNotNull(batchInventoryInfo);
    }


    @Autowired
    private StoreInfoController storeInfoController;
    @Test
    public void findStorePageTest() {
        FindStorePageDTO findStorePageDTO = new FindStorePageDTO();
        findStorePageDTO.setWarehouseId(9981);
        findStorePageDTO.setCityId(998);
        findStorePageDTO.setWarehouseAllocationType(1);
        BaseResult storePage = storeInfoController.findStorePage(findStorePageDTO);
        Assert.assertNotNull(storePage);
    }

    @Test
    public void aaa() {
        FindStorePageDTO findStorePageDTO = new FindStorePageDTO();
        findStorePageDTO.setWarehouseId(9981);
        findStorePageDTO.setCityId(998);
        findStorePageDTO.setWarehouseAllocationType(1);
        BaseResult storePageInfo = storeInfoController.findStorePageInfo(findStorePageDTO);
        Assert.assertNotNull(storePageInfo);
    }

    // 通过仓库id,查询该仓库下的所有库存信息 todo
    // @Test
    // public void findProductInventoryByWarehouseId() {
    // List<ProductInventoryPO> list = productInventoryPOMapper.findProductInventoryByWarehouseId(9991, 0);
    // }

    // 查询仓库库存(分页) 1/18通过
    // @Test
    // public void getListWarehouseInventory() {
    // WarehouseInventoryQueryDTO dto = new WarehouseInventoryQueryDTO();
    // dto.setSecOwnerId(1);
    // PageResult<WarehouseInventoryDTO> list = productInventoryPOMapper.getListWarehouseInventory(dto, 1, 20);
    // }
}
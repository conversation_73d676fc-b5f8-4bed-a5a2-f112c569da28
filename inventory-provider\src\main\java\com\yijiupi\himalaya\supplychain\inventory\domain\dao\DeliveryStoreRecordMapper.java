package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.DeliveryStoreRecordDTO;

/**
 * 发货库存记录
 *
 * <AUTHOR> 2018/1/2
 */
public interface DeliveryStoreRecordMapper {
    /**
     * 发货中的库存记录.
     * 
     * @param poList
     */
    void insertOrUpdateDeliveryRecordBatch(@Param("poList") List<ProductInventoryPO> poList);

    /**
     * 批量更新已发货总数量
     * 
     * @param list
     */
    void updateDeliveryStoreRecordBatch(@Param("list") List<DeliveryStoreRecordDTO> list);
}

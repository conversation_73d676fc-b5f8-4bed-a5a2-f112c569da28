package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;

/**
 * <AUTHOR>
 */
public interface ProductChargeConfigPOMapper {
    /**
     * 产品标准费率新增
     * 
     * @param po
     * @return
     */
    int insertProductChargeConfig(@Param("po") ProductChargeConfigPO po);

    /**
     * 产品标准费率批量新增
     * 
     * @param list
     * @return
     */
    void saveProductChargeConfig(List<ProductChargeConfigPO> list);

    /**
     * 产品标准费率明细查询
     * 
     * @param productCountQuery
     * @return
     */
    ProductChargeConfigDTO selectProductChargeConfigById(ProductCountQuery productCountQuery);

    /**
     * 产品标准费率明细查询
     * 
     * @param productCountQuery
     * @return
     */
    PageResult<ProductChargeConfigDTO> selectProductChargeConfigList(ProductCountQuery productCountQuery);

    /**
     * 根据产品skuid集合查询标准费率
     * 
     * @param list
     * @return
     */
    @MapKey("productSpecificationId")
    Map<Long, ProductChargeConfigDTO> selectProductChargeList(ProductChargeQuery productChargeQuery);

    /**
     * 根据产品skuid集合查询标准费率
     * 
     * @param list
     * @return
     */
    @MapKey("productSpecificationId")
    Map<Long, ProductChargeConfigDTO> selectProductCharge(ProductChargeQuery productChargeQuery);

    /**
     * 产品标准费率修改
     * 
     * @param po
     * @return
     */
    int updateProductChargeConfig(@Param("po") ProductChargeConfigPO po);

    /**
     * 根据产品规格参数id查询个数
     * 
     * @param
     * @return
     */
    int selectCountByProductId(ProductCountQuery productCountQuery);

    /**
     * 获取经销商 的托管产品
     * 
     * @param dealerProductQuery
     * @return
     */
    PageResult<DealerProductDTO> selectDealerProductList(DealerProductQuery dealerProductQuery);

    /**
     * 申请入库的产品 经销商仓配服务
     * 
     * @param productInStockQuery
     * @return
     */
    PageResult<ProductStoreStockDTO> selectProductInStockList(ProductInStockQuery productInStockQuery);

    /**
     * 申请入库产品详情
     * 
     * @param dealerProductDetailQuery
     * @return
     */
    ProductStoreStockDTO selectDealerProductDetail(DealerProductDetailQuery dealerProductDetailQuery);

    /**
     * 启用停用经销商收费
     * 
     * @param po
     * @return
     */
    int updateProductConfigStatus(@Param("po") ProductChargeConfigPO po);

    /**
     * 查询产品
     * 
     * @param productProDetailQuery
     * @return
     */
    PageResult<ProductProDetailDTO> findProductProList(ProductProDetailQuery productProDetailQuery);
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductChargeConfigPOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="UnloadingCharge" property="unloadingCharge" jdbcType="DECIMAL"/>
        <result column="SortingCharge" property="sortingCharge" jdbcType="DECIMAL"/>
        <result column="CustodianCharge" property="custodianCharge" jdbcType="DECIMAL"/>
        <result column="LoadingCharge" property="loadingCharge" jdbcType="DECIMAL"/>
        <result column="TransportCharge" property="transportCharge" jdbcType="DECIMAL"/>
        <result column="LandingCharge" property="landingCharge" jdbcType="DECIMAL"/>
        <result column="CreateUser" property="createUser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="ProductBrand" property="productBrand" jdbcType="VARCHAR"/>
        <result column="BusinessCity" property="businessCity" jdbcType="VARCHAR"/>
        <result column="WarehouseName" property="warehouseName" jdbcType="VARCHAR"/>
        <result column="SpecificationName" property="specificationName" jdbcType="VARCHAR"/>
        <result column="FirstInStockTime" property="firstInStockTime" jdbcType="TIMESTAMP"/>
        <result column="Dealer_Id" property="dealerId" jdbcType="BIGINT"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, ProductSpecification_Id, UnloadingCharge, SortingCharge, CustodianCharge, LoadingCharge,
        TransportCharge, LandingCharge, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime,
        ProductName, ProductBrand, BusinessCity, WarehouseName, SpecificationName, FirstInStockTime,
        Dealer_Id, Status, Warehouse_Id, City_Id
    </sql>
    <insert id="saveProductChargeConfig" parameterType="list">
        insert into
        productchargeconfig (
        Id,
        ProductSpecification_Id,
        ProductSku_Id,
        UnloadingCharge,
        SortingCharge,
        CustodianCharge,
        LoadingCharge,
        TransportCharge,
        LandingCharge,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        ProductName,
        ProductBrand,
        BusinessCity,
        WarehouseName,
        SpecificationName,
        FirstInStockTime,
        Dealer_Id,
        Status,
        Warehouse_Id,
        City_Id,
        Facilitator_Id,
        DealerName,
        MobileNo
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.productSkuId,jdbcType=BIGINT},
            #{item.unloadingCharge,jdbcType=DECIMAL},
            #{item.sortingCharge,jdbcType=DECIMAL},
            #{item.custodianCharge,jdbcType=DECIMAL},
            #{item.loadingCharge,jdbcType=DECIMAL},
            #{item.transportCharge,jdbcType=DECIMAL},
            #{item.landingCharge,jdbcType=DECIMAL},
            #{item.createUser,jdbcType=BIGINT},
            now(),
            #{item.lastUpdateUser,jdbcType=BIGINT},
            now(),
            #{item.productName,jdbcType=VARCHAR},
            #{item.productBrand,jdbcType=VARCHAR},
            #{item.businessCity,jdbcType=VARCHAR},
            #{item.warehouseName,jdbcType=VARCHAR},
            #{item.specificationName,jdbcType=VARCHAR},
            #{item.firstInStockTime,jdbcType=TIMESTAMP},
            #{item.dealerId,jdbcType=BIGINT},
            #{item.status,jdbcType=TINYINT},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.cityId,jdbcType=INTEGER},
            #{item.facilitatorId,jdbcType=BIGINT},
            #{item.dealerName,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


    <insert id="insertProductChargeConfig"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO">
        insert into
        productchargeconfig (
        Id,
        ProductSpecification_Id,
        ProductSku_Id,
        UnloadingCharge,
        SortingCharge,
        CustodianCharge,
        LoadingCharge,
        TransportCharge,
        LandingCharge,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        ProductName,
        ProductBrand,
        BusinessCity,
        WarehouseName,
        SpecificationName,
        FirstInStockTime,
        Dealer_Id,
        Status,
        Warehouse_Id,
        City_Id,
        Facilitator_Id,
        DealerName,
        MobileNo
        )
        values
        (
        #{po.id,jdbcType=BIGINT},
        #{po.productSpecificationId,jdbcType=BIGINT},
        #{po.productSkuId,jdbcType=BIGINT},
        #{po.unloadingCharge,jdbcType=DECIMAL},
        #{po.sortingCharge,jdbcType=DECIMAL},
        #{po.custodianCharge,jdbcType=DECIMAL},
        #{po.loadingCharge,jdbcType=DECIMAL},
        #{po.transportCharge,jdbcType=DECIMAL},
        #{po.landingCharge,jdbcType=DECIMAL},
        #{po.createUser,jdbcType=BIGINT},
        now(),
        #{po.lastUpdateUser,jdbcType=BIGINT},
        now(),
        #{po.productName,jdbcType=VARCHAR},
        #{po.productBrand,jdbcType=VARCHAR},
        #{po.businessCity,jdbcType=VARCHAR},
        #{po.warehouseName,jdbcType=VARCHAR},
        #{po.specificationName,jdbcType=VARCHAR},
        #{po.firstInStockTime,jdbcType=TIMESTAMP},
        #{po.dealerId,jdbcType=BIGINT},
        #{po.status,jdbcType=TINYINT},
        #{po.warehouseId,jdbcType=INTEGER},
        #{po.cityId,jdbcType=INTEGER},
        #{po.facilitatorId,jdbcType=BIGINT},
        #{po.dealerName,jdbcType=VARCHAR},
        #{po.mobileNo,jdbcType=VARCHAR}
        )
    </insert>


    <select id="selectProductChargeConfigById"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductChargeConfigDTO">
        select
        pcc.Id as id,
        pcc.ProductSpecification_Id as productSpecificationId ,
        pcc.ProductSku_Id as productSkuId ,
        pcc.UnloadingCharge as unloadingCharge,
        pcc.SortingCharge as sortingCharge ,
        pcc.CustodianCharge as custodianCharge,
        pcc.LoadingCharge as loadingCharge,
        pcc.TransportCharge as transportCharge,
        pcc.LandingCharge as landingCharge,
        pcc.CreateUser as createUser,
        pcc.CreateTime as createTime,
        pcc.LastUpdateUser as lastUpdateUser,
        pcc.LastUpdateTime as lastUpdateTime,
        pcc.ProductName as productName,
        pcc.ProductBrand as productBrand,
        pcc.BusinessCity as businessCity,
        pcc.WarehouseName as warehouseName ,
        pcc.SpecificationName as specificationName,
        pcc.FirstInStockTime as firstInStockTime,
        pcc.Dealer_Id as dealerId,
        pcc.Status as status,
        pcc.Warehouse_Id as warehouseId,
        pcc.City_Id as cityId,
        pcc.Facilitator_Id as facilitatorId,
        pcc.DealerName as dealerName
        from productchargeconfig pcc
        where pcc.ProductSpecification_Id = #{productSpecificationId}
        and pcc.Facilitator_Id=#{facilitatorId}
        <if test="dealerId != null">
            and Dealer_Id = #{dealerId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectProductChargeConfigList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductChargeConfigDTO">
        select
        pcc.Id as id,
        pcc.ProductSpecification_Id as productSpecificationId ,
        pcc.ProductSku_Id as productSkuId ,
        pcc.UnloadingCharge as unloadingCharge,
        pcc.SortingCharge as sortingCharge ,
        pcc.CustodianCharge as custodianCharge,
        pcc.LoadingCharge as loadingCharge,
        pcc.TransportCharge as transportCharge,
        pcc.LandingCharge as landingCharge,
        pcc.CreateUser as createUser,
        pcc.CreateTime as createTime,
        pcc.LastUpdateUser as lastUpdateUser,
        pcc.LastUpdateTime as lastUpdateTime,
        pcc.ProductName as productName,
        pcc.ProductBrand as productBrand,
        pcc.BusinessCity as businessCity,
        pcc.WarehouseName as warehouseName ,
        pcc.SpecificationName as specificationName,
        pcc.FirstInStockTime as firstInStockTime,
        pcc.Dealer_Id as dealerId,
        pcc.Status as status,
        pcc.Warehouse_Id as warehouseId,
        pcc.City_Id as cityId,
        pcc.Facilitator_Id as facilitatorId,
        pcc.DealerName as dealerName
        from productchargeconfig pcc
        where pcc.Facilitator_Id=#{facilitatorId}
        <if test="dealerId != null">
            and Dealer_Id = #{dealerId,jdbcType=BIGINT}
        </if>
        <if test="productSkuIdList != null">
            and pcc.ProductSku_Id in
            <foreach collection="productSkuIdList" index="index" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>


    </select>


    <select id="selectProductChargeList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductChargeConfigDTO">
        select
        pcc.Id as id,
        pcc.ProductSpecification_Id as productSpecificationId ,
        pcc.ProductSku_Id as productSkuId ,
        pcc.UnloadingCharge as unloadingCharge,
        pcc.SortingCharge as sortingCharge ,
        pcc.CustodianCharge as custodianCharge,
        pcc.LoadingCharge as loadingCharge,
        pcc.TransportCharge as transportCharge,
        pcc.LandingCharge as landingCharge,
        pcc.CreateUser as createUser,
        pcc.CreateTime as createTime,
        pcc.LastUpdateUser as lastUpdateUser,
        pcc.LastUpdateTime as lastUpdateTime,
        pcc.ProductName as productName,
        pcc.ProductBrand as productBrand,
        pcc.BusinessCity as businessCity,
        pcc.WarehouseName as warehouseName ,
        pcc.SpecificationName as specificationName,
        pcc.FirstInStockTime as firstInStockTime,
        pcc.Dealer_Id as dealerId,
        pcc.Status as status,
        pcc.Warehouse_Id as warehouseId,
        pcc.City_Id as cityId,
        pcc.Facilitator_Id as facilitatorId,
        pcc.DealerName as dealerName
        from productchargeconfig pcc
        <where>
            <if test="facilitatorIdList != null">
                and pcc.Facilitator_Id in
                <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dealerId != null">
                and pcc.Dealer_Id = #{dealerId,jdbcType=BIGINT}
            </if>
            <if test="productSpecificationIdList != null and productSpecificationIdList.size >0">
                and pcc.ProductSpecification_Id in
                <foreach collection="productSpecificationIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>

            </if>
        </where>


    </select>


    <select id="selectProductCharge" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductChargeConfigDTO">
        select
        pcc.Id as id,
        pcc.ProductSpecification_Id as productSpecificationId ,
        pcc.ProductSku_Id as productSkuId ,
        pcc.UnloadingCharge as unloadingCharge,
        pcc.SortingCharge as sortingCharge ,
        pcc.CustodianCharge as custodianCharge,
        pcc.LoadingCharge as loadingCharge,
        pcc.TransportCharge as transportCharge,
        pcc.LandingCharge as landingCharge,
        pcc.CreateUser as createUser,
        pcc.CreateTime as createTime,
        pcc.LastUpdateUser as lastUpdateUser,
        pcc.LastUpdateTime as lastUpdateTime,
        pcc.ProductName as productName,
        pcc.ProductBrand as productBrand,
        pcc.BusinessCity as businessCity,
        pcc.WarehouseName as warehouseName ,
        pcc.SpecificationName as specificationName,
        pcc.FirstInStockTime as firstInStockTime,
        pcc.Dealer_Id as dealerId,
        pcc.Status as status,
        pcc.Warehouse_Id as warehouseId,
        pcc.City_Id as cityId,
        pcc.Facilitator_Id as facilitatorId,
        pcc.DealerName as dealerName
        from productchargeconfig pcc
        where pcc.Dealer_Id is not null
        <if test="facilitatorIdList != null">
            and pcc.Facilitator_Id in
            <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="productSpecificationIdList != null and productSpecificationIdList.size >0">
            and pcc.ProductSpecification_Id in
            <foreach collection="productSpecificationIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>


    </select>


    <update id="updateProductChargeConfig"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO">
        update productchargeconfig
        <set>

            <if test="po.unloadingCharge != null">
                UnloadingCharge = #{po.unloadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="po.sortingCharge != null">
                SortingCharge = #{po.sortingCharge,jdbcType=DECIMAL},
            </if>
            <if test="po.custodianCharge != null">
                CustodianCharge = #{po.custodianCharge,jdbcType=DECIMAL},
            </if>
            <if test="po.loadingCharge != null">
                LoadingCharge = #{po.loadingCharge,jdbcType=DECIMAL},
            </if>
            <if test="po.transportCharge != null">
                TransportCharge = #{po.transportCharge,jdbcType=DECIMAL},
            </if>
            <if test="po.landingCharge != null">
                LandingCharge = #{po.landingCharge,jdbcType=DECIMAL},
            </if>
            <if test="po.lastUpdateUser != null">
                LastUpdateUser = #{po.lastUpdateUser,jdbcType=BIGINT},
            </if>
            <if test="po.lastUpdateTime != null">
                LastUpdateTime = #{po.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="po.productName != null">
                ProductName = #{po.productName,jdbcType=VARCHAR},
            </if>
            <if test="po.productBrand != null">
                ProductBrand = #{po.productBrand,jdbcType=VARCHAR},
            </if>

            <if test="po.specificationName != null">
                SpecificationName = #{po.specificationName,jdbcType=VARCHAR},
            </if>
            <if test="po.dealerId != null">
                Dealer_Id = #{po.dealerId,jdbcType=BIGINT},
            </if>
            <if test="po.status != null">
                Status = #{po.status,jdbcType=TINYINT}
            </if>


        </set>
        where ProductSpecification_Id = #{po.productSpecificationId}
        and Facilitator_Id=#{po.facilitatorId}

    </update>


    <select id="selectCountByProductId" resultType="java.lang.Integer">
        select
        count(1) as count
        from productchargeconfig
        where ProductSpecification_Id = #{productSpecificationId}
        and Facilitator_Id=#{facilitatorId}
        <if test="dealerId != null">
            and Dealer_Id = #{dealerId,jdbcType=BIGINT}
        </if>

    </select>


    <select id="selectDealerProductList" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.DealerProductDTO">
        select
        distinct
        psk.ProductSpecification_Id as productSpecificationId,
        psk.Name as productName,
        psk.specificationName as specificationName,
        psk.packageName as packageName,
        psk.unitName as unitName,
        psk.packageQuantity as packageQuantity,
        ps.TotalCount_MinUnit as totalCount,
        ps.Warehouse_Id as warehouseId,
        wh.City_Id as facilitatorId,
        ps.Owner_Id as dealerId,
        ps.CreateTime as firstInStockTime
        from productsku psk
        inner join productstore ps on
        psk.ProductSpecification_Id= ps.ProductSpecification_Id
        inner join warehouse wh on
        wh.Id=ps.Warehouse_Id
        where ps.Owner_Id = #{dealerId,jdbcType=BIGINT}
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="productName != null">
            and psk.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
        </if>
        and ps.Warehouse_Id in(
        select wh.Id from warehouse wh
        <where>
            <if test="facilitatorIdList != null">
                and wh.City_Id in
                <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        )
        order by ps.CreateTime desc

    </select>


    <select id="selectProductInStockList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreStockDTO">
        select
        distinct
        pc.Dealer_Id as dealerId,
        pc.ProductSpecification_Id as productSpecificationId,
        pc.ProductSku_Id as productSkuId ,
        pc.ProductName as productName,
        pc.ProductBrand as productBrand,
        pc.BusinessCity as businessCity,
        pc.Warehouse_Id as warehouseId,
        pc.WarehouseName as warehouseName,
        pc.SpecificationName as specificationName,
        pc.MobileNo as mobileNo,
        pc.UnloadingCharge as unloadingCharge ,
        pc.SortingCharge as sortingCharge,
        pc.CustodianCharge as custodianCharge,
        pc.LoadingCharge as loadingCharge,
        pc.TransportCharge as transportCharge,
        pc.LandingCharge as landingCharge,
        pc.Status as status,
        pc.DealerName as dealerName,
        pc.Facilitator_Id as facilitatorId,
        pc.FirstInStockTime as firstInStockTime
        from productchargeconfig pc
        <where>
            <if test="facilitatorIdList != null">
                and pc.Facilitator_Id in
                <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dealerName != null and dealerName != ''">
                and pc.DealerName like concat('%',#{dealerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productName != null and productName != '' ">
                and pc.ProductName like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="productBrand != null and productBrand != ''">
                and pc.ProductBrand like concat('%',#{productBrand,jdbcType=VARCHAR},'%')
            </if>

            <if test="warehouseId != null">
                and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="mobileNo != null and mobileNo != ''">
                and pc.MobileNo like concat('%',#{mobileNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="startTime != null">
                and TO_DAYS(pc.LastUpdateTime)<![CDATA[ >= ]]> TO_DAYS(#{startTime})
            </if>
            <if test="endTime != null">
                and TO_DAYS(pc.LastUpdateTime) <![CDATA[ <= ]]> TO_DAYS(#{endTime})
            </if>

        </where>

    </select>


    <select id="selectDealerProductDetail"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreStockDTO">
        select
        pc.Dealer_Id as dealerId,
        pc.ProductSpecification_Id as productSpecificationId,
        pc.ProductSku_Id as productSkuId ,
        pc.ProductName as productName,
        pc.ProductBrand as productBrand,
        pc.BusinessCity as businessCity,
        pc.Warehouse_Id as warehouseId,
        pc.WarehouseName as warehouseName,
        pc.SpecificationName as specificationName,
        pc.MobileNo as mobileNo,
        pc.UnloadingCharge as unloadingCharge ,
        pc.SortingCharge as sortingCharge,
        pc.CustodianCharge as custodianCharge,
        pc.LoadingCharge as loadingCharge,
        pc.TransportCharge as transportCharge,
        pc.LandingCharge as landingCharge,
        pc.Status as status,
        pc.DealerName as dealerName,
        pc.Facilitator_Id as facilitatorId,
        pc.FirstInStockTime as firstInStockTime
        from productchargeconfig pc
        <where>
            <if test="facilitatorId != null">
                and pc.Facilitator_Id=#{facilitatorId,jdbcType=BIGINT}
            </if>
            <if test="dealerId != null">
                and pc.Dealer_Id = #{dealerId,jdbcType=BIGINT}
            </if>
            <if test="productSpecificationId != null">
                and pc.ProductSpecification_Id = #{productSpecificationId,jdbcType=BIGINT}
            </if>

        </where>
    </select>


    <update id="updateProductConfigStatus"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO">
        update productchargeconfig
        set
        Status = #{po.status,jdbcType=TINYINT},
        LastUpdateUser = #{po.lastUpdateUser,jdbcType=BIGINT},
        LastUpdateTime = now()
        where Facilitator_Id=#{po.facilitatorId}
        and ProductSpecification_Id=#{po.productSpecificationId,jdbcType=BIGINT}
        <if test="po.dealerId != null">
            and Dealer_Id = #{po.dealerId}
        </if>
    </update>

    <select id="findProductProList" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductProDetailDTO">
        select
        distinct
        ps.Name as productName,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.specificationName as specificationName,
        ps.productBrand as productBrand
        from productsku ps
        <where>
            <if test="productName != null">
                and ps.Name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
        </where>
        limit 0,10
    </select>


</mapper>
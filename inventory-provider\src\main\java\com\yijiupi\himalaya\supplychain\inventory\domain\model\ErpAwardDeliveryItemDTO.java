package com.yijiupi.himalaya.supplychain.inventory.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by 余明 on 2019-01-14.
 */
public class ErpAwardDeliveryItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /// <summary>
    /// 小单位数量
    /// </summary>
    private BigDecimal minUnitTotalCount;
    /// <summary>
    /// 总金额 0？
    /// </summary>
    private BigDecimal totalAmount;
    /// <summary>
    /// skuid
    /// </summary>
    private Long productId;
    private Long specificationId;
    /// <summary>
    /// 包装数量
    /// </summary>
    private BigDecimal specQuantity;
    /// <summary>;
    /// 大单位数量
    /// </summary>
    private String packageName;
    /// <summary>
    /// 小单位数量
    /// </summary>
    private String unitName;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 二级货主id
     */
    private Long secOwnerId;

    public Long getSpecificationId() {
        return specificationId;
    }

    public void setSpecificationId(Long specificationId) {
        this.specificationId = specificationId;
    }

    public BigDecimal getMinUnitTotalCount() {
        return minUnitTotalCount;
    }

    public void setMinUnitTotalCount(BigDecimal minUnitTotalCount) {
        this.minUnitTotalCount = minUnitTotalCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public BigDecimal getSpecQuantity() {
        return specQuantity;
    }

    public void setSpecQuantity(BigDecimal specQuantity) {
        this.specQuantity = specQuantity;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}

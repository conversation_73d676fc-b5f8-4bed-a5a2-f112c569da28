package com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved. 如果重构需要特殊逻辑的单独创建扩展类处理
 * 
 * <AUTHOR>
 * @date 2023/9/20
 */
public abstract class InStockHandleProductDateBaseBL {

    @Reference
    protected IBatchInventoryQueryService iBatchInventoryQueryService;

    protected static final Logger LOG = LoggerFactory.getLogger(InventoryOrderBizBL.class);

    // 这里有三种情况：1、退货入库单；2、不带前缀的入库单；3、带前缀的入库单
    protected List<OrderDTO> support(List<OrderDTO> orderList) {
        orderList = orderList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return doSupport(orderList);
    }

    protected abstract List<OrderDTO> doSupport(List<OrderDTO> orderList);

    protected List<OrderDTO> fillProductDate(List<OrderDTO> orderList) {
        List<OrderDTO> supportOrderList = support(orderList);
        if (CollectionUtils.isEmpty(supportOrderList)) {
            return Collections.emptyList();
        }

        // key 是入参的订单号，value是对应原单的订单号
        Map<String, String> orderNoMap = getOrderNoMap(supportOrderList);

        List<ProductStoreBatchChangeRecordDTO> changeRecordDTOList =
            getProductStoreBatchChangeRecordDTOS(supportOrderList, orderNoMap);
        if (CollectionUtils.isEmpty(changeRecordDTOList)) {
            return orderList;
        }

        return handleProductDate(supportOrderList, changeRecordDTOList, orderNoMap);
    }

    // 这里有三种情况：1、退货入库单；2、不带前缀的入库单；3、带前缀的入库单
    private List<OrderDTO> handleProductDate(List<OrderDTO> orderList,
        List<ProductStoreBatchChangeRecordDTO> changeRecordDTOList, Map<String, String> orderNoMap) {
        if (CollectionUtils.isEmpty(changeRecordDTOList)) {
            return orderList;
        }
        // 按规格id + 一级货主id + 二级货主id +订单号 进行分组
        Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdOrderNoMap =
            changeRecordDTOList.stream().filter(p -> null != p && null != p.getProductionDate())
                .collect(Collectors.groupingBy(it -> it.getProductSpecificationId() + "_" + it.getOwnerId() + "_"
                    + it.getSecOwnerId() + "_" + it.getOrderNo()));
        if (MapUtils.isEmpty(ownerIdAndSecOwnerIdOrderNoMap) || ownerIdAndSecOwnerIdOrderNoMap.size() == 0) {
            return orderList;
        }

        orderList.forEach(order -> {
            List<OrderItemDTO> itemList = order.getItems();
            itemList.stream().filter(Objects::nonNull).forEach(d -> {
                setProductionDate(ownerIdAndSecOwnerIdOrderNoMap, order, d, orderNoMap);
            });
        });

        return orderList;
    }

    protected void setProductionDate(Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdOrderNoMap,
        OrderDTO order, OrderItemDTO d, Map<String, String> orderNoMap) {
        List<ProductStoreBatchChangeRecordDTO> orderStoreChangeRecordDTOList =
            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecificationId() + "_" + d.getOwnerId() + "_"
                + d.getSecOwnerId() + "_" + order.getRefOrderNo());
        List<ProductStoreBatchChangeRecordDTO> replaceOrderStorechangeRecordDTOList =
            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecificationId() + "_" + d.getOwnerId() + "_"
                + d.getSecOwnerId() + "_" + orderNoMap.get(order.getRefOrderNo()));
        LOG.info("查询原单生产日期并赋值，获取orderStoreChangeRecordDTOList：{},获取replaceOrderStorechangeRecordDTOList：{}",
            JSON.toJSONString(orderStoreChangeRecordDTOList), JSON.toJSONString(replaceOrderStorechangeRecordDTOList));
        if (CollectionUtils.isNotEmpty(orderStoreChangeRecordDTOList)) {
            d.setProductionDate(orderStoreChangeRecordDTOList.get(0).getProductionDate());
        } else if (CollectionUtils.isEmpty(orderStoreChangeRecordDTOList)
            && CollectionUtils.isNotEmpty(replaceOrderStorechangeRecordDTOList)
            && !order.getRefOrderNo().matches("^[0-9]+$")) {
            d.setProductionDate(replaceOrderStorechangeRecordDTOList.get(0).getProductionDate());
        }
    }

    /**
     * key 是入参的订单号，value是对应原单的订单号
     */
    protected abstract Map<String, String> getOrderNoMap(List<OrderDTO> orderList);

    private List<ProductStoreBatchChangeRecordDTO> getProductStoreBatchChangeRecordDTOS(List<OrderDTO> orderList,
        Map<String, String> orderNoMap) {
        Map<Integer, ProductStoreBatchChangeRecordQueryDTO> map = convertQueryDTO(orderList, orderNoMap);

        List<ProductStoreBatchChangeRecordDTO> recordList = new ArrayList<>();
        for (Map.Entry<Integer, ProductStoreBatchChangeRecordQueryDTO> entry : map.entrySet()) {
            LOG.info("查询原单生产日期并赋值，查询批次库存变更记录-入参：{}", JSON.toJSONString(entry.getValue()));
            List<ProductStoreBatchChangeRecordDTO> changeRecordList =
                iBatchInventoryQueryService.selectProductStoreBatchChangeRecords(entry.getValue());
            LOG.info("查询原单生产日期并赋值，查询批次库存变更记录-结果：{}", JSON.toJSONString(changeRecordList));
            recordList.addAll(changeRecordList);
        }

        return recordList;
    }

    private Map<Integer, ProductStoreBatchChangeRecordQueryDTO> convertQueryDTO(List<OrderDTO> orderList,
        Map<String, String> orderNoMap) {
        Map<Integer, ProductStoreBatchChangeRecordQueryDTO> warehouseOrderMap = new HashMap<>();

        orderList.stream().collect(Collectors.groupingBy(OrderDTO::getWarehouseId)).forEach((warehouseId, orders) -> {
            // 获取单号
            List<String> orderNos = orders.stream().filter(o -> o != null && !StringUtils.isBlank(o.getRefOrderNo()))
                .map(OrderDTO::getRefOrderNo).distinct().collect(Collectors.toList());
            // 含前缀订单去掉前缀
            // List<String> replaceOrderNos = orders.stream()
            // .filter(
            // o -> o != null && !StringUtils.isBlank(o.getRefOrderNo()) && !o.getRefOrderNo().matches("^[0-9]+$"))
            // .map(o -> o.getRefOrderNo().replaceAll("[a-zA-Z]", "")).distinct().collect(Collectors.toList());
            List<String> replaceOrderNos =
                orderNos.stream().filter(orderNoMap::containsKey).map(orderNoMap::get).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(replaceOrderNos)) {
                orderNos.addAll(replaceOrderNos);
            }
            ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO =
                new ProductStoreBatchChangeRecordQueryDTO();
            productStoreBatchChangeRecordQueryDTO.setCityId(orders.get(0).getOrgId());
            productStoreBatchChangeRecordQueryDTO.setWarehouseId(warehouseId);
            productStoreBatchChangeRecordQueryDTO
                .setOrderNos(orderNos.stream().distinct().collect(Collectors.toList()));
            warehouseOrderMap.put(warehouseId, productStoreBatchChangeRecordQueryDTO);
        });

        return warehouseOrderMap;
    }

    protected boolean isNotNumberOrder(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return Boolean.FALSE;
        }
        return !orderNo.matches("^[0-9]+$");
    }

    protected boolean isReturnOrder(Byte orderType, Byte businessType) {
        if (Objects.nonNull(businessType)) {
            if (InStockOrderBusinessType.酒批业务退货单.getType() == businessType) {
                return Boolean.TRUE;
            }
            if (InStockOrderBusinessType.退货订单.getType() == businessType) {
                return Boolean.TRUE;
            }
            if (InStockOrderBusinessType.订单退货.getType() == businessType) {
                return Boolean.TRUE;
            }
            if (InStockOrderBusinessType.无忧退货订单.getType() == businessType) {
                return Boolean.TRUE;
            }
            if (InStockOrderBusinessType.质量问题退货订单.getType() == businessType) {
                return Boolean.TRUE;
            }
        }
        if (OrderConstant.ORDER_TYPE_RETURN.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_RETURN_WY.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_RETURN_ZL.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_SUPPLIER_RETURN.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_DYY_JIUPI_RETURN.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_DYY_JIUPI_CUSTOMER_RETURN_OLD.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_DYY_JIUPI_CUSTOMER_RETURN_NEW.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_SHOP_DYY_CUSTOMER_RETURN_OLD.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_SHOP_DYY_CUSTOMER_RETURN_NEW.equals(orderType)) {
            return Boolean.TRUE;
        }
        if (OrderConstant.ORDER_TYPE_RETURN_OF_REF.equals(orderType)) {
            return Boolean.TRUE;
        }
        // TODO 无单退货怎么办？
        // if (OrderConstant.ORDER_TYPE_RETURN_OF_NO_REF.equals(orderType)) {
        //
        // }

        return Boolean.FALSE;
    }

}

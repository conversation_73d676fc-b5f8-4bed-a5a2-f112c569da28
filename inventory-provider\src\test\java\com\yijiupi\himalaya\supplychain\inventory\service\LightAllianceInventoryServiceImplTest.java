package com.yijiupi.himalaya.supplychain.inventory.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.biz.LightAllianceInventoryServiceImpl;

/**
 * <AUTHOR> 2017/12/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class LightAllianceInventoryServiceImplTest {

    @Autowired
    private LightAllianceInventoryServiceImpl lightAllianceInventoryService;

    @Test
    public void getProductInventoryMap() {
        // LightAllianceProductInventoryDTO productInventoryMap =
        // lightAllianceInventoryService.getProductInventoryMap(123L, 7001);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.DealerChargeConfigBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.service.IDealerChargeConfigService;

/**
 * 仓配经销商信息
 * 
 * @author: lidengfeng
 * @date 2018/11/28 20:02
 */
@Service
public class DealerChargeConfigServiceImpl implements IDealerChargeConfigService {

    @Autowired
    private DealerChargeConfigBL dealerChargeConfigBL;

    /**
     * 新增或修改经销商费用
     * 
     * @param dto
     * @return: void
     */
    @Override
    public void saveOrUpdateDealerChargeConfig(DealerChargeConfigDTO dto) {
        dealerChargeConfigBL.saveOrUpdateDealerChargeConfig(dto);
    }

    /**
     * 查询经销商是否配置
     * 
     * @param
     * @return
     */
    @Override
    public Boolean selectCountByDealerId(DealerCountQuery dealerCountQuery) {
        AssertUtils.notNull(dealerCountQuery.getDealerId(), "经销商id不能为空");
        AssertUtils.notNull(dealerCountQuery.getFacilitatorId(), "服务商id不能为空");
        return dealerChargeConfigBL.selectCountByDealerId(dealerCountQuery);
    }

    /**
     * 根据服务商id查询经销商费用配置
     * 
     * @param
     * @return
     */
    @Override
    public PageList<DealerChargeConfigDTO> selectDealerChargeList(DealerChargeConfigQuery dealerChargeConfigQuery) {
        AssertUtils.notNull(dealerChargeConfigQuery.getFacilitatorId(), "服务商id不能为空");
        return dealerChargeConfigBL.selectDealerChargeList(dealerChargeConfigQuery);
    }

    /**
     * 根据服务商id集合查询经销商费用配置
     * 
     * @param
     * @return
     */
    @Override
    public PageList<DealerChargeConfigDTO> selectDealerList(DealerChargeConfigQuery dealerChargeConfigQuery) {
        AssertUtils.notNull(dealerChargeConfigQuery.getFacilitatorIdList(), "服务商id集合不能为空");
        return dealerChargeConfigBL.selectDealerList(dealerChargeConfigQuery);
    }

    /**
     * 启用停用经销商费用配置
     * 
     * @param dto
     */
    @Override
    public void updateDealerConfigStatus(DealerChargeConfigDTO dto) {
        dealerChargeConfigBL.updateDealerConfigStatus(dto);

    }

    /**
     * 根据经销商id，城市id查询仓库信息 仓配服务信息
     * 
     * @param dealerWarehouseQuery
     * @return
     */
    @Override
    public PageList<DealerWarehouseDTO> selectDealerWarehouseList(DealerWarehouseQuery dealerWarehouseQuery) {
        AssertUtils.notNull(dealerWarehouseQuery.getDealerId(), "经销商id不能为空");
        return dealerChargeConfigBL.selectDealerWarehouseList(dealerWarehouseQuery);
    }

    /**
     * 经销商费用配置明细查询
     * 
     * @param
     * @return
     */
    @Override
    public DealerChargeConfigDTO selectDealerChargeConfigById(DealerCountQuery dealerCountQuery) {
        AssertUtils.notNull(dealerCountQuery.getDealerId(), "经销商id不能为空");
        AssertUtils.notNull(dealerCountQuery.getFacilitatorId(), "服务商id不能为空");
        return dealerChargeConfigBL.selectDealerChargeConfigById(dealerCountQuery);
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreQuery;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ProductChargeConfigConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.DealerChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductStorePOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ISubOrgService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * @author: lidengfeng
 * @date 2018/9/27 17:36
 */
@Service
public class ProductChargeConfigBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductChargeConfigBL.class);

    @Autowired
    private ProductChargeConfigPOMapper productChargeConfigPOMapper;

    @Autowired
    private ProductChargeConfigConvert configConvert;

    @Autowired
    private DealerChargeConfigPOMapper dealerChargeConfigPOMapper;

    @Autowired
    private ProductStorePOMapper productStorePOMapper;

    @Reference
    private ISubOrgService iSubOrgService;

    /**
     * 根据skuid集合，经销商或者服务商id查寻所有的产品收费配置
     * 
     * @return
     */
    public List<ProductChargeConfigDTO> selectProductChargeConfigList(ProductCountQuery query) {
        List<ProductChargeConfigDTO> productChargeConfigDTOS =
            productChargeConfigPOMapper.selectProductChargeConfigList(query);
        return productChargeConfigDTOS;
    }

    /**
     * 批量新增产品费率配置
     *
     * @param
     */
    public void saveProductChargeConfig(List<ProductChargeConfigDTO> productChargeConfigDTOList) {
        List<ProductChargeConfigPO> poList = configConvert.reverseConvert(productChargeConfigDTOList);
        if (poList != null && poList.size() > 0) {
            poList.forEach(p -> p.setId(UUIDGenerator.getUUID(ProductChargeConfigPO.class.getName())));
            productChargeConfigPOMapper.saveProductChargeConfig(poList);
        }
    }

    /**
     * 新增或修改产品标准费率
     *
     * @param dto
     * @return: void
     */
    public void saveOrUpdateChargeConfig(ProductChargeConfigDTO dto) {
        LOG.info("新增或修改仓库标准费率：" + JSON.toJSONString(dto));
        ProductChargeConfigPO po = configConvert.reverseConvert(dto);
        ProductCountQuery productCountQuery = new ProductCountQuery();
        productCountQuery.setDealerId(dto.getDealerId());
        productCountQuery.setFacilitatorId((dto.getFacilitatorId()));
        productCountQuery.setProductSpecificationId(dto.getProductSpecificationId());

        int count = productChargeConfigPOMapper.selectCountByProductId(productCountQuery);
        LOG.info("查询参数：{}", JSON.toJSONString(count));
        if (count > 0) {
            productChargeConfigPOMapper.updateProductChargeConfig(po);
        } else {
            po.setId(UUIDGenerator.getUUID(ProductChargeConfigPO.class.getName()));
            productChargeConfigPOMapper.insertProductChargeConfig(po);
        }
    }

    /**
     * 产品标准费率明细查询
     *
     * @param productCountQuery
     * @return
     */
    public ProductChargeConfigDTO selectProductChargeConfigById(ProductCountQuery productCountQuery) {
        ProductChargeConfigDTO productChargeConfigDTO =
            productChargeConfigPOMapper.selectProductChargeConfigById(productCountQuery);
        return productChargeConfigDTO;
    }

    /**
     * 根据产品规格参数id集合,经销商id，服务商id集合查询标准费率
     *
     * @param
     * @return
     */
    public Map<Long, ProductChargeConfigDTO> selectProductChargeList(ProductChargeQuery productChargeQuery) {
        int facilitatorId = Math.toIntExact(productChargeQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(productChargeQuery.getFacilitatorId());
        productChargeQuery.setFacilitatorIdList(facilitatorIdList);
        Map<Long, ProductChargeConfigDTO> map = productChargeConfigPOMapper.selectProductChargeList(productChargeQuery);
        return map;
    }

    /**
     * 得到产品费率是否配置
     *
     * @param
     * @return
     */
    public Boolean selectCountByProductId(ProductCountQuery productCountQuery) {
        Boolean flag = true;
        int count = productChargeConfigPOMapper.selectCountByProductId(productCountQuery);
        if (count > 0) {
            flag = false;
        }
        return flag;
    }

    /**
     * 获取经销商 的托管产品
     *
     * @param dealerProductQuery
     * @return
     */
    public PageList<DealerProductDTO> selectDealerProductList(DealerProductQuery dealerProductQuery) {
        int facilitatorId = Math.toIntExact(dealerProductQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(dealerProductQuery.getFacilitatorId());
        dealerProductQuery.setFacilitatorIdList(facilitatorIdList);
        LOG.info("获取托管商品入参: {}", JSON.toJSONString(dealerProductQuery));
        PageList<DealerProductDTO> pageList = new PageList<>();
        PageResult<DealerProductDTO> dealerProductDTOS =
            productChargeConfigPOMapper.selectDealerProductList(dealerProductQuery);
        // 获取规格参数id集合
        List<Long> idList =
            dealerProductDTOS.stream().map(p -> p.getProductSpecificationId()).distinct().collect(Collectors.toList());
        // 得到产品收费的map
        ProductChargeQuery productChargeQuery = new ProductChargeQuery();
        productChargeQuery.setDealerId(dealerProductQuery.getDealerId());
        productChargeQuery.setProductSpecificationIdList(idList);
        productChargeQuery.setFacilitatorId(dealerProductQuery.getFacilitatorId());
        Map<Long, ProductChargeConfigDTO> productChargeConfigDTOMap = this.selectProductChargeList(productChargeQuery);
        Map<Long, ProductChargeConfigDTO> productChargeConfigMap = this.selectProductCharge(productChargeQuery);
        productChargeConfigMap.putAll(productChargeConfigDTOMap);
        // 赋费用
        productChargeConfigMap.forEach((k, v) -> {
            dealerProductDTOS.forEach(e -> {
                if (e.getProductSpecificationId().equals(k)) {
                    e.setLandingCharge(v.getLandingCharge());
                    e.setCustodianCharge(v.getCustodianCharge());
                    e.setLoadingCharge(v.getLoadingCharge());
                    e.setSortingCharge(v.getSortingCharge());
                    e.setTransportCharge(v.getTransportCharge());
                    e.setUnloadingCharge(v.getUnloadingCharge());
                }
            });

        });
        pageList.setDataList(dealerProductDTOS);
        pageList.setPager(dealerProductDTOS.getPager());
        return pageList;
    }

    /**
     * 查询申请入库商品和经销商的费用配置 经销商仓配申请入库产品
     *
     * @param productInStockQuery
     * @return
     */
    public PageList<ProductStoreStockDTO> selectProductInStockList(ProductInStockQuery productInStockQuery) {
        int facilitatorId = Math.toIntExact(productInStockQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(productInStockQuery.getFacilitatorId());
        productInStockQuery.setFacilitatorIdList(facilitatorIdList);
        PageList<ProductStoreStockDTO> pageList = new PageList<>();
        PageResult<ProductStoreStockDTO> productInStockList =
            productChargeConfigPOMapper.selectProductInStockList(productInStockQuery);
        pageList.setDataList(productInStockList);
        pageList.setPager(productInStockList.getPager());
        return pageList;
    }

    /**
     * 查询申请入库商品和经销商的费用配置明细 经销商仓配申请入库产品
     *
     * @param dealerProductDetailQuery
     * @return
     */
    public ProductStoreStockDTO selectDealerProductDetail(DealerProductDetailQuery dealerProductDetailQuery) {
        return productChargeConfigPOMapper.selectDealerProductDetail(dealerProductDetailQuery);

    }

    /**
     * 根据经销商id，仓库id查询仓库商品的库存
     *
     * @param productStockStoreQuery
     * @return
     */
    public PageList<ProductStockStoreDTO> findProductStoreList(ProductStockStoreQuery productStockStoreQuery) {
        PageList<ProductStockStoreDTO> pageList = new PageList<>();
        PageResult<ProductStockStoreDTO> productStoreList =
            productStorePOMapper.findProductStoreList(productStockStoreQuery);
        pageList.setDataList(productStoreList);
        pageList.setPager(productStoreList.getPager());
        return pageList;

    }

    /**
     * 启用停用经销商费用配置
     *
     * @param dto
     * @return
     */
    public void updateProductConfigStatus(ProductChargeConfigDTO dto) {
        LOG.info("经销商停用设置： {}", JSON.toJSONString(dto));
        ProductChargeConfigPO po = configConvert.reverseConvert(dto);
        LOG.info("经销商停用转换设置： {}", JSON.toJSONString(po));
        productChargeConfigPOMapper.updateProductConfigStatus(po);

    }

    /**
     * 根据服务商和产品集合获取费用
     *
     * @param productChargeQuery
     * @return
     */
    public Map<Long, ProductChargeConfigDTO> selectProductCharge(ProductChargeQuery productChargeQuery) {
        int facilitatorId = Math.toIntExact(productChargeQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(productChargeQuery.getFacilitatorId());
        productChargeQuery.setFacilitatorIdList(facilitatorIdList);
        return productChargeConfigPOMapper.selectProductCharge(productChargeQuery);

    }

    /**
     * 查询产品
     *
     * @param productProDetailQuery
     * @return
     */
    public PageList<ProductProDetailDTO> findProductProList(ProductProDetailQuery productProDetailQuery) {
        PageList<ProductProDetailDTO> pageList = new PageList<>();
        PageResult<ProductProDetailDTO> productProList =
            productChargeConfigPOMapper.findProductProList(productProDetailQuery);
        pageList.setDataList(productProList);
        pageList.setPager(productProList.getPager());
        return pageList;

    }

}

package com.yijiupi.himalaya.supplychain.batchinventory.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class RedisUtil<T> {
    private static final StringRedisSerializer STRING_SERIALIZER = new StringRedisSerializer();
    private static final JdkSerializationRedisSerializer VALUE_SERIALIZER = new JdkSerializationRedisSerializer();
    @Autowired
    private RedisTemplate<String, T> redisTemplate;

    @PostConstruct
    public void init() {
        redisTemplate.setKeySerializer(STRING_SERIALIZER);
        redisTemplate.setHashKeySerializer(STRING_SERIALIZER);
        redisTemplate.setHashValueSerializer(VALUE_SERIALIZER);
        redisTemplate.setValueSerializer(VALUE_SERIALIZER);
    }

    public Boolean hasKey(String key, String field) {
        return redisTemplate.opsForHash().hasKey(key, field);
    }

    public void setHash(String key, String field, T value, long timeout, TimeUnit unit) {
        redisTemplate.opsForHash().put(key, field, value);
        redisTemplate.opsForHash().getOperations().expire(key, timeout, unit);
    }

    public T getHash(String key, String field) {
        if (hasKey(key, field)) {
            return (T)redisTemplate.opsForHash().get(key, field);
        }
        return null;
    }

    public void delHash(String key, String[] field) {
        redisTemplate.opsForHash().delete(key, field);
    }

    public RedisTemplate getRedisTemplate() {
        return redisTemplate;
    }

    public void multiSetHash(String key, Map<String, T> fieldAndValue, long timeout, TimeUnit unit) {
        redisTemplate.opsForHash().putAll(key, fieldAndValue);
        redisTemplate.opsForHash().getOperations().expire(key, timeout, unit);
    }

    public void setList(String key, List<T> values, long timeout, TimeUnit timeUnit) {
        redisTemplate.opsForList().rightPushAll(key, values);
        redisTemplate.opsForList().getOperations().expire(key, timeout, timeUnit);
    }

    public List<T> getList(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    public void delList(String key, long count, T value) {
        redisTemplate.opsForList().remove(key, count, value);
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

/**
 * 批属性模板管理关联表
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeTemplateRelationPO {
    /**
     * id
     */
    private Long id;
    /**
     * 属性编码(字典表id)
     */
    private Long attributeId;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性类型
     */
    private Byte attributeType;
    /**
     * 有效位数
     */
    private Integer effectiveDigit;
    /**
     * 是否必须
     */
    private Boolean required;
    /**
     * 关联表id templateId
     */
    private Long templateId;

    /**
     * 是否参与批属性计算,不参与(0),参与计算(1)
     */
    private Byte isCalculation;

    /**
     * 是否参与盘点,不参与(0),参与(1)
     */
    private Byte isStoreCheck;

    /**
     * 获取 id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 属性名称
     */
    public String getAttributeName() {
        return this.attributeName;
    }

    /**
     * 设置 属性名称
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    /**
     * 获取 属性类型
     */
    public Byte getAttributeType() {
        return this.attributeType;
    }

    /**
     * 设置 属性类型
     */
    public void setAttributeType(Byte attributeType) {
        this.attributeType = attributeType;
    }

    /**
     * 获取 有效位数
     */
    public Integer getEffectiveDigit() {
        return this.effectiveDigit;
    }

    /**
     * 设置 有效位数
     */
    public void setEffectiveDigit(Integer effectiveDigit) {
        this.effectiveDigit = effectiveDigit;
    }

    /**
     * 获取 是否必须
     */
    public Boolean getRequired() {
        return this.required;
    }

    /**
     * 设置 是否必须
     */
    public void setRequired(Boolean required) {
        this.required = required;
    }

    /**
     * 获取 关联表id templateId
     */
    public Long getTemplateId() {
        return this.templateId;
    }

    /**
     * 设置 关联表id templateId
     */
    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    /**
     * 获取 属性编码
     */
    public Long getAttributeId() {
        return this.attributeId;
    }

    /**
     * 设置 属性编码
     */
    public void setAttributeId(Long attributeId) {
        this.attributeId = attributeId;
    }

    public Byte getIsCalculation() {
        return isCalculation;
    }

    public void setIsCalculation(Byte isCalculation) {
        this.isCalculation = isCalculation;
    }

    public Byte getIsStoreCheck() {
        return isStoreCheck;
    }

    public void setIsStoreCheck(Byte isStoreCheck) {
        this.isStoreCheck = isStoreCheck;
    }
}

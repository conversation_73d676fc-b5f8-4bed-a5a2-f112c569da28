package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchInventoryQueryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.LocationBatchInventoryQueryInfoDTO;

/**
 * 批次库存
 *
 * <AUTHOR> 2018/1/25
 */
@Mapper
public interface ProductStoreBatchQueryMapper {

    PageResult<BatchInventoryPO> findBatchInventoryList(@Param("so") BatchInventoryQueryBO queryBO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    PageResult<BatchInventoryPO> findLocationBatchInventory(@Param("dto") LocationBatchInventoryQueryInfoDTO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

}

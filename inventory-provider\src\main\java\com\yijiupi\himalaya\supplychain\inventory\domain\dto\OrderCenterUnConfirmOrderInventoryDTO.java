package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: OrderCenterUnConfirmOrderInventoryDTO
 * @description: 通过仓库Id查询仓库库存未确认数量
 * http://ocop.test.yijiupidev.com/#/apiDoc/PUBLIC:%2Faggregatequery%2FOrderInventoryQueryService%2FfindUnConfirmOrderInventory
 * @date 2023-03-09 16:09
 */
public class OrderCenterUnConfirmOrderInventoryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;


    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}

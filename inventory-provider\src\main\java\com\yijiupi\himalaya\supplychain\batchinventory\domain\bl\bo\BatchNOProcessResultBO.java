package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeInfoPO;

import java.io.Serializable;
import java.util.List;

public class BatchNOProcessResultBO implements Serializable {
    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 关联信息ID
     */
    private Long refInfoId;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 批次编号
     */
    private String batchAttributeInfoNo;

    /**
     * 批次信息
     */
    private List<BatchAttributeInfoPO> batchInfoList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getRefInfoId() {
        return refInfoId;
    }

    public void setRefInfoId(Long refInfoId) {
        this.refInfoId = refInfoId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public List<BatchAttributeInfoPO> getBatchInfoList() {
        return batchInfoList;
    }

    public void setBatchInfoList(List<BatchAttributeInfoPO> batchInfoList) {
        this.batchInfoList = batchInfoList;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.Date;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;

/**
 * @author: lidengfeng
 * @date 2018/11/29 15:57
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class IProductChargeConfigServiceTest {

    @Reference
    private IProductChargeConfigService iProductChargeConfigService;

    /**
     * 新增修改产品费率
     */
    @Test
    public void saveOrUpdateChargeConfig() {
        ProductChargeConfigDTO productChargeConfigDTO = new ProductChargeConfigDTO();
        productChargeConfigDTO.setDealerId(169818873699270077L);
        productChargeConfigDTO.setProductSpecificationId(666325L);
        productChargeConfigDTO.setCreateUser(169818873699270372L);
        productChargeConfigDTO.setLastUpdateUser(169818874699270072L);
        productChargeConfigDTO.setProductName("新增的2产品");
        productChargeConfigDTO.setProductBrand("产品2");
        productChargeConfigDTO.setBusinessCity("武汉2");
        productChargeConfigDTO.setWarehouseName("小仓库2");
        productChargeConfigDTO.setSpecificationName("10瓶/件");
        productChargeConfigDTO.setFirstInStockTime(new Date());
        productChargeConfigDTO.setStatus((byte)0);
        productChargeConfigDTO.setCityId(111);
        productChargeConfigDTO.setWarehouseId(1031);
        iProductChargeConfigService.saveOrUpdateChargeConfig(productChargeConfigDTO);
    }

    @Test
    public void selectProductChargeConfigById() {
        // ProductChargeConfigDTO productChargeConfigDTO =
        // iProductChargeConfigService.selectProductChargeConfigById(66133);
        // System.out.println(JSON.toJSONString(productChargeConfigDTO));
    }

    @Test
    public void selectProductInStockList() {
        ProductInStockQuery productInStockQuery = new ProductInStockQuery();
        productInStockQuery.setFacilitatorId(Long.valueOf(6));
        productInStockQuery.setStatus((byte)0);
        productInStockQuery.setWarehouseId(5);
        productInStockQuery.setCityId(6);
        productInStockQuery.setPageNum(1);
        productInStockQuery.setPageSize(10);
        PageList<ProductStoreStockDTO> productInStockDTOPageList =
            iProductChargeConfigService.selectProductInStockList(productInStockQuery);
        System.out.println(JSON.toJSONString(productInStockDTOPageList));

    }

    @Test
    public void selectDealerProductList() {
        DealerProductQuery dealerProductQuery = new DealerProductQuery();
        dealerProductQuery.setDealerId(169818873699270071L);
        PageList<DealerProductDTO> pageList = iProductChargeConfigService.selectDealerProductList(dealerProductQuery);
        System.out.println(JSON.toJSONString(pageList));

    }

    @Test
    public void selectDealerProductDetail() {
        DealerProductDetailQuery dealerProductDetailQuery = new DealerProductDetailQuery();
        dealerProductDetailQuery.setDealerId(169818873699270071L);
        dealerProductDetailQuery.setProductSpecificationId(1L);
        ProductStoreStockDTO stockDTO = iProductChargeConfigService.selectDealerProductDetail(dealerProductDetailQuery);
        System.out.println(JSON.toJSONString(stockDTO));
    }

    @Test
    public void selectCountByProductId() {
        ProductCountQuery productCountQuery = new ProductCountQuery();
        productCountQuery.setProductSpecificationId(111L);
        productCountQuery.setDealerId(111L);
        Boolean countByProductId = iProductChargeConfigService.selectCountByProductId(productCountQuery);
        System.out.println(JSON.toJSONString(countByProductId));

    }

}
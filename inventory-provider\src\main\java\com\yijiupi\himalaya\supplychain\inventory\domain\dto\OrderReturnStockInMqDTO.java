package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;

/**
 * 退货单详情
 * 
 * @author: yanpin
 * @date: 2019年4月25日 下午4:29:23
 */
public class OrderReturnStockInMqDTO implements Serializable {
    private static final long serialVersionUID = -8430146644914370378L;
    /**
     * 订单id
     */
    private Long businessId;
    /**
     * 退货单id
     */
    private Long returnOrderId;
    /**
     * 操作人id
     */
    private Integer operaterId;
    /**
     * 是否装车(一般都是true)
     */
    private Boolean hasAssignCar;
    /**
     * 车辆id
     */
    private Long carId;
    /**
     * 状态:13:延迟推货11:已完成
     */
    private Byte state;
    /**
     * 配送员id
     */
    private Integer deliveryUserId;
    /**
     * 装卸工id
     */
    private Integer stevedoreUserId;
    /**
     * 退货单状态:-1:未标记,8:全部退货,9:拒绝退货,13:延迟退货
     */
    private Byte receiptState;

    public Byte getReceiptState() {
        return receiptState;
    }

    public void setReceiptState(Byte receiptState) {
        this.receiptState = receiptState;
    }

    @Override
    public String toString() {
        return "OrderReturnStockInMqDTO [businessId=" + businessId + ", returnOrderId=" + returnOrderId
            + ", operaterId=" + operaterId + ", hasAssignCar=" + hasAssignCar + ", carId=" + carId + ", state=" + state
            + ", deliveryUserId=" + deliveryUserId + ", stevedoreUserId=" + stevedoreUserId + ", receiptState="
            + receiptState + "]";
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getReturnOrderId() {
        return returnOrderId;
    }

    public void setReturnOrderId(Long returnOrderId) {
        this.returnOrderId = returnOrderId;
    }

    public Integer getOperaterId() {
        return operaterId;
    }

    public void setOperaterId(Integer operaterId) {
        this.operaterId = operaterId;
    }

    public Boolean getHasAssignCar() {
        return hasAssignCar;
    }

    public void setHasAssignCar(Boolean hasAssignCar) {
        this.hasAssignCar = hasAssignCar;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getDeliveryUserId() {
        return deliveryUserId;
    }

    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    public Integer getStevedoreUserId() {
        return stevedoreUserId;
    }

    public void setStevedoreUserId(Integer stevedoreUserId) {
        this.stevedoreUserId = stevedoreUserId;
    }
}

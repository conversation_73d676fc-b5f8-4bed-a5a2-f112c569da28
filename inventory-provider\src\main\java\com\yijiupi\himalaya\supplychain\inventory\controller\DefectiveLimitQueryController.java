package com.yijiupi.himalaya.supplychain.inventory.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.ROResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.erp.ErpDefectiveLimitBL;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/8/19
 */
@RestController
public class DefectiveLimitQueryController {

    @Autowired
    private ErpDefectiveLimitBL erpDefectiveLimitBL;

    /**
     * 库存报表
     */
    @RequestMapping(value = "/defective/queryDefectiveLimit", method = RequestMethod.POST)
    public ROResult<String> queryDefectiveLimit(@RequestBody BatchInventoryQueryDTO queryDTO) {
        String result = erpDefectiveLimitBL.queryDefectiveLimit(queryDTO.getWarehouseId());

        return ROResult.getResult(result);
    }

}

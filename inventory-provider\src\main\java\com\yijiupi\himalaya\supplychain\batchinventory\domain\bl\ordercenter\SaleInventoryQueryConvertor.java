package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.ordercenter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
public class SaleInventoryQueryConvertor {

    public static List<OrderCenterSaleInventoryQueryDTO>
        convertSaleInventoryQueryRootDTO(Map<Integer, List<ProductionDateDTO>> queryMap) {

        List<OrderCenterSaleInventoryQueryDTO> queryDTOS = new ArrayList<>();
        queryMap.forEach((warehouseId, productBasicList) -> {
            // 获取销售库存
            List<OrderCenterSaleInventoryQueryDTO> tmpQueryDTOList = productBasicList.stream().map(m -> {
                OrderCenterSaleInventoryQueryDTO saleInventoryQueryDTO = new OrderCenterSaleInventoryQueryDTO();
                saleInventoryQueryDTO.setCityId(m.getOrgId());
                saleInventoryQueryDTO.setWarehouseId(m.getWarehouseId());
                saleInventoryQueryDTO.setProductSpecId(m.getProductSpecificationId());
                saleInventoryQueryDTO.setOwnerId(m.getOwnerId());
                saleInventoryQueryDTO.setSecOwnerId(m.getSecOwnerId());

                return saleInventoryQueryDTO;
            }).collect(Collectors.toList());

            queryDTOS.addAll(tmpQueryDTOList);
        });

        // List<OmsInventoryQueryDTO> omsInventoryQueryList = new ArrayList<>();
        // queryMap.forEach((warehouseId, productBasicList) -> {
        // // 获取销售库存
        // List<String> internalKeys =
        // productBasicList.stream().map(ProductionDateDTO::getSkuSign).distinct().collect(Collectors.toList());
        // OmsInventoryQueryDTO omsInventoryQueryDTO = new OmsInventoryQueryDTO();
        // omsInventoryQueryDTO.setWarehouseId(warehouseId);
        // omsInventoryQueryDTO.setInternalKeys(internalKeys);
        // omsInventoryQueryList.add(omsInventoryQueryDTO);
        // });

        return queryDTOS;
    }

}

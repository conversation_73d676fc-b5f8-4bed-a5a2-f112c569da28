package com.yijiupi.himalaya.supplychain.inventory.controller.model;

import java.util.List;

/**
 * 库存报表查询
 * 
 * <AUTHOR>
 * @since 2017年3月30日 下午2:13:09
 */
public class FindStorePageDTO {
    private Integer userId;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库
     */
    private List<Integer> warehouseIds;
    /**
     * 货位
     */
    private Integer goodsPositionId;
    /**
     * 供应商
     */
    private Long supplierId;
    /**
     * 库存分类
     */
    private Integer storeOwnerType;
    /**
     * 页码
     */
    private Integer currentPage;
    /**
     * 大小
     */
    private Integer pageSize;
    private Integer warehouseId;
    private Integer cityId;

    /**
     * 产品skuid集合
     */
    private List<Long> productSkuIds;

    /**
     * 是否限制产品范围（1：表示只查本仓库的产品）
     */
    private Byte limitSku;

    /**
     * 是否有库存： null: 查所有 1：库存大于零 0：库存等于小于零 -1：库存小于零
     */
    private Byte hasRealStoreType;

    /**
     * 最小库龄
     */
    private Long startStockAge;

    /**
     * 最大库龄
     */
    private Long endStockAge;

    private String inventoryPinProperty;

    /**
     * 分仓属性
     */
    private Integer warehouseAllocationType;

    public String getInventoryPinProperty() {
        return inventoryPinProperty;
    }

    public void setInventoryPinProperty(String inventoryPinProperty) {
        this.inventoryPinProperty = inventoryPinProperty;
    }

    public Byte getHasRealStoreType() {
        return hasRealStoreType;
    }

    public void setHasRealStoreType(Byte hasRealStoreType) {
        this.hasRealStoreType = hasRealStoreType;
    }

    public Byte getLimitSku() {
        return limitSku;
    }

    public void setLimitSku(Byte limitSku) {
        this.limitSku = limitSku;
    }

    public List<Long> getProductSkuIds() {
        return productSkuIds;
    }

    public void setProductSkuIds(List<Long> productSkuIds) {
        this.productSkuIds = productSkuIds;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductSkuName() {
        return productSkuName;
    }

    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    public List<Integer> getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(List<Integer> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public Integer getGoodsPositionId() {
        return goodsPositionId;
    }

    public void setGoodsPositionId(Integer goodsPositionId) {
        this.goodsPositionId = goodsPositionId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getStoreOwnerType() {
        return storeOwnerType;
    }

    public void setStoreOwnerType(Integer storeOwnerType) {
        this.storeOwnerType = storeOwnerType;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getStartStockAge() {
        return startStockAge;
    }

    public void setStartStockAge(Long startStockAge) {
        this.startStockAge = startStockAge;
    }

    public Long getEndStockAge() {
        return endStockAge;
    }

    public void setEndStockAge(Long endStockAge) {
        this.endStockAge = endStockAge;
    }

    public Integer getWarehouseAllocationType() {
        return warehouseAllocationType;
    }

    public void setWarehouseAllocationType(Integer warehouseAllocationType) {
        this.warehouseAllocationType = warehouseAllocationType;
    }
}

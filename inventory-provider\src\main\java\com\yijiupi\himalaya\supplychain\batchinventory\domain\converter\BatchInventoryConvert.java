package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.dto.ReportBatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductSaleMode;
import com.yijiupi.himalaya.supplychain.enums.WarehouseAllocationTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ShelfLifeUnitEnum;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 批次库存转换类
 *
 * <AUTHOR> 2018/1/29
 */
public class BatchInventoryConvert {

    public static BatchInventoryDTO batchInventoryPO2BatchInventoryDTO(BatchInventoryPO batchInventoryPO) {
        if (batchInventoryPO == null) {
            return null;
        }
        BatchInventoryDTO batchInventoryDTO = new BatchInventoryDTO();
        BeanUtils.copyProperties(batchInventoryPO, batchInventoryDTO);

        // 获取销售模式名称
        if (null != batchInventoryPO.getSaleModel()) {
            batchInventoryDTO
                .setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(batchInventoryPO.getSaleModel())));
        }

        if (batchInventoryPO.getStorageAttribute() != null) {
            // 分仓属性
            Integer warehouseAllocationType = batchInventoryPO.getStorageAttribute().intValue();
            WarehouseAllocationTypeEnum warehouseAllocationTypeEnum = WarehouseAllocationTypeEnum.getEnum(warehouseAllocationType);
            batchInventoryDTO.setWarehouseAllocationType(warehouseAllocationType);
            batchInventoryDTO.setWarehouseAllocationTypeName(warehouseAllocationTypeEnum.getDesc());
        }

        return batchInventoryDTO;
    }

    public static List<BatchInventoryDTO>
        batchInventoryPOS2BatchInventoryDTOS(List<BatchInventoryPO> batchInventoryPOS) {
        List<BatchInventoryDTO> batchInventoryDTOS = new ArrayList<>();
        for (BatchInventoryPO batchInventoryPO : batchInventoryPOS) {
            batchInventoryDTOS.add(batchInventoryPO2BatchInventoryDTO(batchInventoryPO));
        }
        return batchInventoryDTOS;
    }

    public static ProductStoreBatchPO
        BatchInventoryTransferDTO2ProductStoreBatchPO(BatchInventoryTransferDTO batchInventoryTransferDTO) {
        if (batchInventoryTransferDTO == null) {
            return null;
        }
        ProductStoreBatchPO productStoreBatchPO = new ProductStoreBatchPO();
        productStoreBatchPO.setProductStoreId(batchInventoryTransferDTO.getProductStoreId());
        productStoreBatchPO.setLocationId(batchInventoryTransferDTO.getLocationId());
        productStoreBatchPO.setLocationName(batchInventoryTransferDTO.getLocationName());
        productStoreBatchPO.setLocationCategory(batchInventoryTransferDTO.getLocationCategory());
        productStoreBatchPO.setSubcategory(batchInventoryTransferDTO.getSubcategory());
        productStoreBatchPO.setBatchProperty(batchInventoryTransferDTO.getBatchProperty());
        productStoreBatchPO.setBusinessType(batchInventoryTransferDTO.getBusinessType());

        return productStoreBatchPO;
    }

    public static List<BatchInventoryPO>
        erpExhibitInventoryDTOS2BatchInventoryPOS(List<ERPDisposedInventoryDTO> erpExhibitInventories) {
        List<BatchInventoryPO> batchInventoryPOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(erpExhibitInventories)) {
            return batchInventoryPOS;
        }

        erpExhibitInventories.forEach(erp -> {
            BatchInventoryPO batchInventoryPO = new BatchInventoryPO();
            batchInventoryPO.setProductSkuId(Long.valueOf(erp.getProductSkuId()));
            batchInventoryPO.setStoreTotalCount(erp.getStockCount());

            batchInventoryPOS.add(batchInventoryPO);
        });
        return batchInventoryPOS;
    }

    public static List<ProductStoreBatchDTO>
        productStoreBatchPOToProductStoreBatchDTO(List<ProductStoreBatchPO> productStoreBatchPOS) {
        List<ProductStoreBatchDTO> productStoreBatchDTOS = new ArrayList<>();

        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            return productStoreBatchDTOS;
        }

        productStoreBatchPOS.forEach(poElem -> {
            ProductStoreBatchDTO dto = new ProductStoreBatchDTO();
            BeanUtils.copyProperties(poElem, dto);
            productStoreBatchDTOS.add(dto);
        });
        return productStoreBatchDTOS;
    }

    /**
     * 保质期单位 1：年(365) 2：月(30) 3：日(1)
     */
    public static Integer YEAR_COUNT = 365;
    public static Integer MONTH_COUNT = 30;
    public static Integer DAY_COUNT = 1;

    /**
     * 计算过期时间天数
     * 
     * @param monthOfShelfLife 产品保质期
     * @param shelfLifeUnit 保质期单位 1：年 2：月 3：日
     * @return
     */
    public static Integer countExpireDays(Integer monthOfShelfLife, Integer shelfLifeUnit) {
        Integer expireDays = 0;
        if (null != monthOfShelfLife && 0 != monthOfShelfLife && null != shelfLifeUnit && 0 != shelfLifeUnit) {
            if (shelfLifeUnit.equals(ShelfLifeUnitEnum.年.getType())) {
                expireDays = monthOfShelfLife * YEAR_COUNT;
            } else if (shelfLifeUnit.equals(ShelfLifeUnitEnum.月.getType())) {
                expireDays = monthOfShelfLife * MONTH_COUNT;
            } else if (shelfLifeUnit.equals(ShelfLifeUnitEnum.日.getType())) {
                expireDays = monthOfShelfLife * DAY_COUNT;
            }
        }
        return expireDays;
    }

    /**
     * 根据生产日期,保质期计算过期时间
     */
    public static Date countExpireTime(Date productionDate, Integer monthOfShelfLife, Integer shelfLifeUnit) {
        Integer dayOfShelfLife = countExpireDays(monthOfShelfLife, shelfLifeUnit);
        // 处理过期时间
        if (productionDate != null && dayOfShelfLife != null && dayOfShelfLife > 0) {
            // 有生产日期且保质期大于0
            Calendar exCal = Calendar.getInstance();
            exCal.setTime(productionDate);
            exCal.add(Calendar.DAY_OF_YEAR, dayOfShelfLife);
            // 赋值过期时间
            return exCal.getTime();
        }
        return null;
    }

    public static List<BatchLocationInfoDTO> reportToBatchLocationInfoDTO(List<ReportBatchLocationInfoDTO> reportList) {
        if (CollectionUtils.isEmpty(reportList)) {
            return new ArrayList<>();
        }
        List<BatchLocationInfoDTO> toList = new ArrayList<>();
        reportList.forEach(report -> {
            BatchLocationInfoDTO to = new BatchLocationInfoDTO();
            BeanUtils.copyProperties(report, to);
            toList.add(to);
        });
        return toList;
    }
}

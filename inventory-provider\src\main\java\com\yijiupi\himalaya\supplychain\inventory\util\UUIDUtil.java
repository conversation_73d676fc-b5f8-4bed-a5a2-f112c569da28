/*********************************************
 * ClassName: UUIDUtil Description: id生成工具类
 * 
 * <AUTHOR>
 * @date
 *********************************************/

/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.util;

import java.security.MessageDigest;
import java.util.UUID;

import com.yijiupi.himalaya.uuid.UUIDGenerator;

public class UUIDUtil {

    public static final String IN_STOCK_ORDER_ITEM_DETAIL = "instockorderitemdetail";
    public static final String OUT_STOCK_ORDER_ITEM_DETAIL = "outstockorderitemdetail";

    private UUIDUtil() {
        super();
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String MD5(String s) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(s.getBytes("utf-8"));
            return toHex(bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String toHex(byte[] bytes) {
        final char[] HEX_DIGITS = "0123456789ABCDEF".toCharArray();
        StringBuilder ret = new StringBuilder(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            ret.append(HEX_DIGITS[(bytes[i] >> 4) & 0x0f]);
            ret.append(HEX_DIGITS[bytes[i] & 0x0f]);
        }
        return ret.toString();
    }

    public static long getUUIDLong(String table) {
        return UUIDGenerator.getUUID(table);
    }
}

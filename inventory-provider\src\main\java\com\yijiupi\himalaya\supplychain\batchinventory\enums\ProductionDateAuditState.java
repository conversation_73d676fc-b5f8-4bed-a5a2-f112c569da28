package com.yijiupi.himalaya.supplychain.batchinventory.enums;

import com.yijiupi.himalaya.assignment.enums.ValueBasedEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-14 14:29
 **/
public enum ProductionDateAuditState implements ValueBasedEnum {
    /**
     * 审核中
     */
    AUDITING(0),

    /**
     * 审核通过
     */
    AUDIT_PASSED(1),

    /**
     * 审核拒绝
     */
    AUDIT_REJECTED(2),
    ;

    private final Integer value;

    /**
     * 待审核和审核通过
     */
    public static final List<Integer> AUDITING_AND_PASSED = Arrays.asList(AUDITING.value, AUDIT_PASSED.value);

    /**
     * 审核过一次
     */
    public static final List<Integer> HAS_AUDITED = Arrays.asList(AUDIT_PASSED.value, AUDIT_REJECTED.value);

    ProductionDateAuditState(Integer value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}

local info = ''
for _, v in pairs(ARGV) do
    local key1, value1 = string.match(v, '([_?%d+]+)%s*=%s*([\-]?%d+)')
    local temp = redis.call('hget', KEYS[1], key1)
    local kc = (temp and tonumber(temp)) or tonumber('0')
    if ((kc + tonumber(value1)) < 0) then
        info = info .. '{' .. key1 .. ':' .. kc .. ':' .. value1 .. '}'
    end
end
if (string.len(info) > 0) then
    return info
end
return 'OK' 
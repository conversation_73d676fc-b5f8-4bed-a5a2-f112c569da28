package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/8/19
 */
public class WarehouseDefectiveProductQueryDTO implements Serializable {
    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 获取 仓库id
     *
     * @return warehouseId 仓库id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库id
     *
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }
}

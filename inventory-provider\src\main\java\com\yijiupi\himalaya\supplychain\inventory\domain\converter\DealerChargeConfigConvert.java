package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import org.springframework.stereotype.Component;

import com.github.pagehelper.StringUtil;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigDTO;

/**
 * 经销商费用配置转换
 * 
 * @author: lidengfeng
 * @date 2018/9/15 10:36
 */
@Component
public class DealerChargeConfigConvert extends ConvertUtils<DealerChargeConfigPO, DealerChargeConfigDTO> {

    /**
     * 经销商费用配置PO转DTO
     * 
     * @param po
     * @return
     */
    @Override
    public DealerChargeConfigDTO convert(DealerChargeConfigPO po) {
        DealerChargeConfigDTO dto = new DealerChargeConfigDTO();
        dto.setId(String.valueOf(po.getId()));
        dto.setDealerId(String.valueOf(po.getDealerId()));
        dto.setDealerName(po.getDealerName());
        dto.setIsGetWarehouseCharge(po.getIsGetWarehouseCharge());
        dto.setBusinessType(po.getBusinessType());
        dto.setFacilitatorId(po.getFacilitatorId());
        dto.setFacilitatorName(po.getFacilitatorName());
        dto.setStatus(po.getStatus());
        dto.setCreateUser(po.getCreateUser());
        dto.setCreateTime(po.getCreateTime());
        dto.setLastUpdateUser(po.getLastUpdateUser());
        dto.setLastUpdateTime(po.getLastUpdateTime());
        dto.setMobileNo(String.valueOf(po.getMobileNo()));
        dto.setFirstServiceTime(po.getFirstServiceTime());
        return dto;
    }

    /**
     * 经销商费用配置DTO转PO
     * 
     * @param dto
     * @return
     */
    @Override
    public DealerChargeConfigPO reverseConvert(DealerChargeConfigDTO dto) {
        DealerChargeConfigPO po = new DealerChargeConfigPO();
        if (StringUtil.isNotEmpty(dto.getId())) {
            po.setId(Long.valueOf(dto.getId()));
        }
        po.setDealerId(Long.valueOf(dto.getDealerId()));
        po.setDealerName(dto.getDealerName());
        po.setIsGetWarehouseCharge(dto.getIsGetWarehouseCharge());
        po.setBusinessType(dto.getBusinessType());
        po.setFacilitatorId(dto.getFacilitatorId());
        po.setFacilitatorName(dto.getFacilitatorName());
        po.setStatus(dto.getStatus());
        po.setCreateUser(dto.getCreateUser());
        po.setCreateTime(dto.getCreateTime());
        po.setLastUpdateUser(dto.getLastUpdateUser());
        po.setLastUpdateTime(dto.getLastUpdateTime());
        if (StringUtil.isNotEmpty(dto.getMobileNo())) {
            po.setMobileNo(Long.valueOf(dto.getMobileNo()));
        }
        po.setFirstServiceTime(dto.getFirstServiceTime());
        return po;
    }
}

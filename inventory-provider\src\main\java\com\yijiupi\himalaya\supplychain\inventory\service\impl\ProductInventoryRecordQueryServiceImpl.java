package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.text.ParseException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.InventoryProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductStoreChangeRecordBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangeInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangePageDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.StoreChangeInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductInventoryRecordQueryService;
import com.yijiupi.himalaya.supplychain.search.ProductStoreChangeRecordByOrderSO;
import com.yijiupi.himalaya.supplychain.search.ProductStoreRecordSO;
import com.yijiupi.himalaya.supplychain.util.DateUtil;

@Service
public class ProductInventoryRecordQueryServiceImpl implements IProductInventoryRecordQueryService {

    @Autowired
    private ProductStoreChangeRecordBL productStoreChangeRecordBL;

    private static final String MIN_DATE = "2021-01-01";
    private static final String MIN_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 查询仓库库存变更记录明细列表
     */
    @Override
    public PageList<ProductStoreChangeRecordDTO> findProductStoreRecordList(ProductStoreRecordSO productStoreRecordSO,
        PagerCondition pager, Long productSkuId) {
        if (productStoreRecordSO != null) {
            try {
                if ((StringUtils.isNotEmpty(productStoreRecordSO.getTimeS())
                    && DateUtil.parse(productStoreRecordSO.getTimeS(), MIN_DATE_FORMAT)
                        .compareTo(DateUtil.parse(MIN_DATE, MIN_DATE_FORMAT)) < 0)) {
                    productStoreRecordSO.setTimeS(MIN_DATE);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            try {
                if ((StringUtils.isNotEmpty(productStoreRecordSO.getTimeE())
                    && DateUtil.parse(productStoreRecordSO.getTimeE(), MIN_DATE_FORMAT)
                        .compareTo(DateUtil.parse(MIN_DATE, MIN_DATE_FORMAT)) < 0)) {
                    productStoreRecordSO.setTimeE(MIN_DATE);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return productStoreChangeRecordBL.findProductInventoryRecordList(productStoreRecordSO, pager, productSkuId);
    }

    @Override
    public PageList<ProductStoreChangeRecordDTO>
        findProductInventoryChangeRecordPOListBySkuId(ProductStoreRecordSO productStoreRecordSO, PagerCondition pager) {
        AssertUtils.notNull(productStoreRecordSO, "参数不能为空！");
        AssertUtils.notNull(productStoreRecordSO.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notNull(productStoreRecordSO.getProductSkuId(), "SkuId不能为空！");
        try {
            if ((StringUtils.isNotEmpty(productStoreRecordSO.getTimeS())
                && DateUtil.parse(productStoreRecordSO.getTimeS(), MIN_DATE_FORMAT)
                    .compareTo(DateUtil.parse(MIN_DATE, MIN_DATE_FORMAT)) < 0)) {
                productStoreRecordSO.setTimeS(MIN_DATE);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        try {
            if ((StringUtils.isNotEmpty(productStoreRecordSO.getTimeE())
                && DateUtil.parse(productStoreRecordSO.getTimeE(), MIN_DATE_FORMAT)
                    .compareTo(DateUtil.parse(MIN_DATE, MIN_DATE_FORMAT)) < 0)) {
                productStoreRecordSO.setTimeE(MIN_DATE);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return productStoreChangeRecordBL.findProductInventoryChangeRecordPOListBySkuId(productStoreRecordSO, pager);
    }

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    @Override
    public PageList<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordByOrder(ProductStoreChangeRecordByOrderSO orderSO) {
        return productStoreChangeRecordBL.listProductStoreChangeRecordByOrder(orderSO);
    }

    /**
     * 查询批次库存变更记录
     *
     * @param orderSO
     * @return
     */
    @Override
    public List<InventoryProductStoreBatchChangeRecordDTO>
        findProductStoreBatchChangeRecordList(ProductStoreChangeRecordByOrderSO orderSO) {
        return productStoreChangeRecordBL.findProductStoreBatchChangeRecordList(orderSO);
    }

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    @Override
    public List<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordGroupByOrder(ProductStoreChangeRecordByOrderSO orderSO) {
        return productStoreChangeRecordBL.listProductStoreChangeRecordGroupByOrder(orderSO);
    }
    //
    // /**
    // * 查询ERP库存变更记录明细列表
    // */
    // @Override
    // public PageList<ProductStoreChangeRecordDTO> findErpProductStoreRecordList(
    // ProductStoreRecordSO productStoreRecordSO, PagerCondition pager, Integer prodcutInfoSpecId) {
    // return productStoreChangeRecordBL.findERPProductInventoryRecordList(productStoreRecordSO, pager,
    // prodcutInfoSpecId);
    // }
    //
    // /**
    // * 查询sku在对应城市下所有仓库库存变更记录集合
    // */
    // @Override
    // public PageList<CityInventoryRecordDTO> findCityProductInventoryRecordList(CityInventoryRecordQueryDTO
    // cityInventoryRecordQueryDTO, PagerCondition pager) {
    // AssertUtils.notNull(cityInventoryRecordQueryDTO.getProductSkuId(), "skuId不能为空");
    // AssertUtils.notNull(cityInventoryRecordQueryDTO.getCityId(), "城市ID不能为空");
    // return productStoreChangeRecordBL.findCityProductInventoryRecord(cityInventoryRecordQueryDTO, pager);
    // }

    @Override
    public PageList<StoreChangeInfoDTO> findInventoryChangeBySpec(ProductStoreRecordSO querySO, PagerCondition pager) {
        return productStoreChangeRecordBL.findInventoryChangeBySpec(querySO, pager);
    }

    /**
     * 查询仓库库存变更记录明细列表
     */
    @Override
    public PageList<FindStoreChangeInfoDTO> findStoreChangePage(FindStoreChangePageDTO findStoreChangePageDTO) {
        return productStoreChangeRecordBL.findStoreChangePage(findStoreChangePageDTO);
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 批次库存变更记录
 *
 * <AUTHOR> 2018/1/26
 */
public class ProductStoreBatchChangeRecordPO {
    /**
     * productstorebatchchangerecord主键
     */
    private String id;
    /**
     * 关联批次库存表Id
     */
    private String batchId;
    /**
     * 关联库存变更表记录Id
     */
    private String changeRecordId;
    /**
     * 批次库存变化的数量
     */
    private BigDecimal changeCount;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 获取 productstorebatchchangerecord主键
     *
     * @return id productstorebatchchangerecord主键
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 productstorebatchchangerecord主键
     *
     * @param id productstorebatchchangerecord主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 关联批次库存表Id
     *
     * @return batchId 关联批次库存表Id
     */
    public String getBatchId() {
        return this.batchId;
    }

    /**
     * 设置 关联批次库存表Id
     *
     * @param batchId 关联批次库存表Id
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    /**
     * 获取 关联库存变更表记录Id
     *
     * @return changeRecordId 关联库存变更表记录Id
     */
    public String getChangeRecordId() {
        return this.changeRecordId;
    }

    /**
     * 设置 关联库存变更表记录Id
     *
     * @param changeRecordId 关联库存变更表记录Id
     */
    public void setChangeRecordId(String changeRecordId) {
        this.changeRecordId = changeRecordId;
    }

    /**
     * 获取 批次库存变化的数量
     *
     * @return changeCount 批次库存变化的数量
     */
    public BigDecimal getChangeCount() {
        return this.changeCount;
    }

    /**
     * 设置 批次库存变化的数量
     *
     * @param changeCount 批次库存变化的数量
     */
    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}

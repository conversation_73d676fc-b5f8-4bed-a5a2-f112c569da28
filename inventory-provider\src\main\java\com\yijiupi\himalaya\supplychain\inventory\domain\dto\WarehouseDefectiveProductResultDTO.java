package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/8/19
 */
public class WarehouseDefectiveProductResultDTO implements Serializable {
    /**
     * 事业部名称 （酒类 粮调副食 日化百货 乳啤 休食 饮料）
     */
    private String businessDivision;
    /**
     * 事业部枚举（ 酒类 = 0, 休食 = 5, 日化百货 = 7, 粮调副食 = 8, 饮料 = 9, 乳啤 = 10）
     */
    private Integer divisionType;
    /**
     * 总库存金额
     */
    private BigDecimal totalInventoryAmount;
    /**
     * 额度比例
     */
    private BigDecimal quotaRatio;
    /**
     * 残次品额度 （TotalInventoryAmount * QuotaRatio）
     */
    private BigDecimal defectiveQuotaAmount;
    /**
     * 已用额度
     */
    private BigDecimal usedQuota;
    /**
     * 剩余额度 （DefectiveQuotaAmount - UsedQuota）
     */
    private BigDecimal residueQuota;

    /**
     * 获取 事业部名称 （酒类 粮调副食 日化百货 乳啤 休食 饮料）
     *
     * @return businessDivision 事业部名称 （酒类 粮调副食 日化百货 乳啤 休食 饮料）
     */
    public String getBusinessDivision() {
        return this.businessDivision;
    }

    /**
     * 设置 事业部名称 （酒类 粮调副食 日化百货 乳啤 休食 饮料）
     *
     * @param businessDivision 事业部名称 （酒类 粮调副食 日化百货 乳啤 休食 饮料）
     */
    public void setBusinessDivision(String businessDivision) {
        this.businessDivision = businessDivision;
    }

    /**
     * 获取 事业部枚举（ 酒类 = 0 休食 = 5 日化百货 = 7 粮调副食 = 8 饮料 = 9 乳啤 = 10）
     *
     * @return divisionType 事业部枚举（ 酒类 = 0 休食 = 5 日化百货 = 7 粮调副食 = 8 饮料 = 9 乳啤 = 10）
     */
    public Integer getDivisionType() {
        return this.divisionType;
    }

    /**
     * 设置 事业部枚举（ 酒类 = 0 休食 = 5 日化百货 = 7 粮调副食 = 8 饮料 = 9 乳啤 = 10）
     *
     * @param divisionType 事业部枚举（ 酒类 = 0 休食 = 5 日化百货 = 7 粮调副食 = 8 饮料 = 9 乳啤 = 10）
     */
    public void setDivisionType(Integer divisionType) {
        this.divisionType = divisionType;
    }

    /**
     * 获取 总库存金额
     *
     * @return totalInventoryAmount 总库存金额
     */
    public BigDecimal getTotalInventoryAmount() {
        return this.totalInventoryAmount;
    }

    /**
     * 设置 总库存金额
     *
     * @param totalInventoryAmount 总库存金额
     */
    public void setTotalInventoryAmount(BigDecimal totalInventoryAmount) {
        this.totalInventoryAmount = totalInventoryAmount;
    }

    /**
     * 获取 额度比例
     *
     * @return quotaRatio 额度比例
     */
    public BigDecimal getQuotaRatio() {
        return this.quotaRatio;
    }

    /**
     * 设置 额度比例
     *
     * @param quotaRatio 额度比例
     */
    public void setQuotaRatio(BigDecimal quotaRatio) {
        this.quotaRatio = quotaRatio;
    }

    /**
     * 获取 残次品额度 （TotalInventoryAmount QuotaRatio）
     *
     * @return defectiveQuotaAmount 残次品额度 （TotalInventoryAmount QuotaRatio）
     */
    public BigDecimal getDefectiveQuotaAmount() {
        return this.defectiveQuotaAmount;
    }

    /**
     * 设置 残次品额度 （TotalInventoryAmount QuotaRatio）
     *
     * @param defectiveQuotaAmount 残次品额度 （TotalInventoryAmount QuotaRatio）
     */
    public void setDefectiveQuotaAmount(BigDecimal defectiveQuotaAmount) {
        this.defectiveQuotaAmount = defectiveQuotaAmount;
    }

    /**
     * 获取 已用额度
     *
     * @return usedQuota 已用额度
     */
    public BigDecimal getUsedQuota() {
        return this.usedQuota;
    }

    /**
     * 设置 已用额度
     *
     * @param usedQuota 已用额度
     */
    public void setUsedQuota(BigDecimal usedQuota) {
        this.usedQuota = usedQuota;
    }

    /**
     * 获取 剩余额度 （DefectiveQuotaAmount - UsedQuota）
     *
     * @return residueQuota 剩余额度 （DefectiveQuotaAmount - UsedQuota）
     */
    public BigDecimal getResidueQuota() {
        return this.residueQuota;
    }

    /**
     * 设置 剩余额度 （DefectiveQuotaAmount - UsedQuota）
     *
     * @param residueQuota 剩余额度 （DefectiveQuotaAmount - UsedQuota）
     */
    public void setResidueQuota(BigDecimal residueQuota) {
        this.residueQuota = residueQuota;
    }
}

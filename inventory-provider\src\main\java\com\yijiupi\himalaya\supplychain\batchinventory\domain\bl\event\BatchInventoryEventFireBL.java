package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.batchinventory.config.BatchInventoryMQProperties;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IReplenishmentManageService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 库存消息发送
 */
@Service
public class BatchInventoryEventFireBL {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Reference
    private IReplenishmentManageService replenishmentManageService;

    public void sendAddReplenishmentEvent(List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS) {
        if (CollectionUtils.isNotEmpty(replenishmentTaskItemDTOS)) {
            // 校验是否开启补货配置
            if (replenishmentManageService
                .checkIsOpenReplenishment(replenishmentTaskItemDTOS.get(0).getWarehouseId())) {
                rabbitTemplate.convertAndSend(BatchInventoryMQProperties.ADD_REPLENISHMENT_EXCHANGE, null, replenishmentTaskItemDTOS);
            }
        }
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductSkuPOMapper">

    <select id="findProductInfoStoreList"
            resultType="com.yijiupi.himalaya.supplychain.dto.easysell.ProductInfoStoreDTO">
        select
        ps.Warehouse_Id as warehouseId,
        wh.Name as warehouseName,
        ps.TotalCount_MinUnit as totalCountMinUnit,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.Name as name,
        psku.specificationName as specificationName,
        psku.packageName as packageName,
        psku.unitName as unitName,
        psku.packageQuantity as packageQuantity
        from productsku psku
        inner join productstore ps on ps.ProductSpecification_Id = psku.ProductSpecification_Id
        and psku.City_id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        inner join warehouse wh on ps.Warehouse_Id=wh.Id
        and psku.ProductSpecification_Id =#{productSpecificationId}
        and ps.Owner_Id = #{shopId}
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>

    </select>


    <select id="findProductDetailsList" resultType="com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO">
        select
        ps.ProductSpecification_Id as productSpecificationId,
        psku.Name as name,
        psku.specificationName as specificationName,
        psku.packageName as packageName,
        psku.unitName as unitName,
        psku.packageQuantity as packageQuantity,
        ps.TotalCount_MinUnit as totalCountMinUnit
        from productsku psku
        inner join productstore ps on ps.ProductSpecification_Id = psku.ProductSpecification_Id
        and psku.City_id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where
        psku.ProductSpecification_Id in
        (
        <foreach collection="productSpecificationIdList" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="shopId != null">
            and ps.Owner_Id = #{shopId}
        </if>


    </select>
</mapper>
package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChargeConfigDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseChargeConfigService;

/**
 * @author: lidengfeng
 * @date 2018/9/19 19:25
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WarehouseChargeConfigServiceImplTest {

    @Reference
    private IWarehouseChargeConfigService iWarehouseChargeConfigService;

    @Test
    public void selectWarehouseChargeConfigList() {}

    @Test
    public void saveOrUpdateChargeConfig() {}

    @Test
    public void selectWarehouseChargeConfigById() {
        WarehouseChargeConfigDTO warehouseChargeConfigDTO =
            iWarehouseChargeConfigService.selectWarehouseChargeConfigById(1041);
        System.out.println(JSON.toJSONString(warehouseChargeConfigDTO));
    }

    @Test
    public void updateChargeConfigStatus() {
        WarehouseChargeConfigDTO dto = new WarehouseChargeConfigDTO();
        dto.setWarehouseId(1041);
        dto.setStatus((byte)1);
        dto.setLastupdateuser(1112L);
        iWarehouseChargeConfigService.updateChargeConfigStatus(dto);
    }

    @Test
    public void selectWarehouseChargeList() {
        List<Integer> list = new ArrayList<>();
        list.add(1041);
        Map<Integer, WarehouseChargeConfigDTO> map = iWarehouseChargeConfigService.selectWarehouseChargeList(list);
        System.out.println(JSON.toJSONString(map));
    }
}
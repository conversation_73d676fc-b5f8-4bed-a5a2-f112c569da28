package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import com.yijiupi.himalaya.supplychain.enums.StoreOrderType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;

/**
 * 订单类型转换
 *
 * <AUTHOR>
 * @date 2018/6/7 9:24
 */
public class OrderTypeConvert {

    /**
     * 酒批订单类型转换为库存订单类型
     *
     * @param jiupiOrderType
     * @return
     */
    public static Integer convertOrderType(Integer jiupiOrderType) {
        switch (jiupiOrderType) {
            // 普通订单
            case JiupiOrderTypeEnum.ORDER_TYPE_NORMAL:
                return StoreOrderType.订单;
            // 合并订单
            case JiupiOrderTypeEnum.ORDER_TYPE_COMBINE:
                return StoreOrderType.订单;
            // 大货批发订单
            case JiupiOrderTypeEnum.ORDER_TYPE_WHOLESALE:
                return StoreOrderType.订单;
            // 招商订单 和 分销商订单
            case JiupiOrderTypeEnum.ORDER_TYPE_BUS:
                return StoreOrderType.合作商订单;
            // 临期产品订单
            case JiupiOrderTypeEnum.ORDER_TYPE_SOON_TO_EXPIRE:
                return StoreOrderType.临期产品订单;
            // 大商转配送
            case JiupiOrderTypeEnum.ORDER_TYPE_PARTNERTOJIUPI:
                return StoreOrderType.大商转配送;
            // 轻加盟订单
            case JiupiOrderTypeEnum.ORDER_TYPE_LIGHT:
                return StoreOrderType.轻加盟订单;
            // 酒批经销商订单
            case JiupiOrderTypeEnum.ORDER_TYPE_JIUPISHOP:
                return StoreOrderType.经销商订单;
            // 共享仓配易经销订单
            case JiupiOrderTypeEnum.ORDER_TYPE_SHOP_DELIVERY:
                return StoreOrderType.易经销配送单;
            // 团购直营
            case JiupiOrderTypeEnum.ORDER_GROUP_PURCHASE:
                return StoreOrderType.团购直营;
            // 兑奖订单
            case JiupiOrderTypeEnum.ORDER_TYPE_AWARD:
                return StoreOrderType.兑奖单;
            // 兑奖配送单
            case JiupiOrderTypeEnum.ORDER_TYPE_AWARD_DELIVERY:
                return StoreOrderType.兑奖单;
            // 退货订单
            case JiupiOrderTypeEnum.ORDER_TYPE_RETURN:
                return StoreOrderType.退货单;
            // 拼团订单
            case JiupiOrderTypeEnum.ORDER_TYPE_GROUP:
                return StoreOrderType.拼团订单;
            case JiupiOrderTypeEnum.ORDER_TYPE_ZHZG:
                return StoreOrderType.知花知果订单;
            case JiupiOrderTypeEnum.ORDER_TYPE_ALLOT:
                return StoreOrderType.调拨出库;
            case JiupiOrderTypeEnum.ORDER_TYPE_OEFLINE:
                return StoreOrderType.易款便利线下单;
            case JiupiOrderTypeEnum.ORDER_TYPE_RTURNOFFLINE:
                return StoreOrderType.易款便利线下退货单;
            case JiupiOrderTypeEnum.ORDER_TYPE_AWARD_OUTSTOCK:
                return StoreOrderType.实物兑奖出库单;
            default:
                return jiupiOrderType;
        }
    }

}

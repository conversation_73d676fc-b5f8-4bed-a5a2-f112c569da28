package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.List;

/**
 * 转换工具类
 *
 * @param <M> 待转对象类型
 * @param <N> 预转对象类型
 * @author: yanpin
 * @date: 2017年8月15日 上午11:09:27
 */
public abstract class ConvertUtils<M, N> {
    /**
     * Description: 单个对象转换
     *
     * @param m 待转对象
     * @return 转换后的对象
     */
    public abstract N convert(M m);

    /**
     * Description: 单个对象反向转换
     *
     * @param n 待转对象
     * @return 转换后的对象
     */
    public abstract M reverseConvert(N n);

    /**
     * Description: 集合转换
     *
     * @param mList 待转对象集合
     * @return 转换后的对象集合
     */
    public List<N> convert(List<M> mList) {
        List<N> nList = null;
        if (mList != null) {
            nList = new ArrayList<N>(mList.size());
            for (M m : mList) {
                nList.add(convert(m));
            }
        }
        return nList;
    }

    /**
     * Description: 集合转换
     *
     * @return 转换后的对象集合
     */
    public List<M> reverseConvert(List<N> nList) {
        List<M> mList = null;
        if (nList != null) {
            mList = new ArrayList<M>(nList.size());
            for (N n : nList) {
                mList.add(reverseConvert(n));
            }
        }
        return mList;
    }

}

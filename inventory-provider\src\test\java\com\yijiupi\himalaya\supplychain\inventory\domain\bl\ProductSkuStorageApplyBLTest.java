package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.product.ProductSkuStorageApplyBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ApplyProductStorageAgeDTO;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class ProductSkuStorageApplyBLTest {

    @Autowired
    private ProductSkuStorageApplyBL productSkuStorageApplyBL;

    @Test
    public void applyProductStorageAgeTest() {
        ApplyProductStorageAgeDTO dto = new ApplyProductStorageAgeDTO();
        dto.setSkuId(4863512194069297672L);
        dto.setOptUserId(125);
        dto.setReason("我就是想申请");
        dto.setWarehouseId(9981);
        dto.setOrgId(998);
        dto.setStoreBatchId("424e270b7bd34255be63bb0bfbcc6fea");
        dto.setCurrentStorageAge(25);
        dto.setApplyStorageAge(30);

        productSkuStorageApplyBL.applyProductStorageAge(dto);
    }

}

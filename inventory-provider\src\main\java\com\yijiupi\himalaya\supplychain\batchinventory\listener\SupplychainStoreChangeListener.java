package com.yijiupi.himalaya.supplychain.batchinventory.listener;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.framework.rabbit.delay.DelayMessageTemplate;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.aspect.BatchInventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeInfoBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryManageBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.ProductStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductDateSyncPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.CcpPriceMessageDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.StoreCheckUpdateBatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.StoreCheckUpdateBatchInventoryItemDTO;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.enums.ProductStoreBatchPropertyEnum;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreBaseDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.CheckResultItemDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.StoreCheckProductQueryDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.StoreCheckProductResultDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.so.CheckResultItemQuerySO;
import com.yijiupi.himalaya.supplychain.storecheck.service.StoreCheckOrderService;
import com.yijiupi.himalaya.supplychain.storecheck.service.StoreCheckResultService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 供应链仓库库存变更事件.
 *
 * <AUTHOR> 2018/1/26
 */
@Component
public class SupplychainStoreChangeListener {
    private static final Logger LOG = LoggerFactory.getLogger(SupplychainStoreChangeListener.class);

    @Autowired
    private ProductStoreMapper productStoreMapper;

    @Autowired
    private ProductStoreBatchBL productStoreBatchBL;
    @Autowired
    private BatchAttributeInfoBL batchAttributeInfoBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IWarehouseInventoryQueryService iWarehouseInventoryQueryService;

    @Reference
    private ILocationService iLocationService;

    @Reference
    private StoreCheckOrderService storeCheckOrderService;

    @Reference
    private StoreCheckResultService storeCheckResultService;

    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;

    @Reference
    private IBatchTaskQueryService iBatchTaskQueryService;

    @Autowired
    private BatchInventorySendFaildMQ batchInventorySendFaildMQ;

    @Autowired
    private DelayMessageTemplate delayMessageTemplate;

    @Value("${ex.supplychain.batchinventory.orderproductiondate}")
    private String orderOutboundExchange;

    private final Integer DELAY_SECONDS = 10;

    @Reference
    private LocationAreaService locationAreaService;

    @RabbitListener(queues = "${mq.supplychain.inventory.productstorechange}")
    public void syncApply(List<ProductInventoryChangeRecordPO> msgs) {
        processStoreChangeMsg(msgs);
    }

    public void processStoreChangeMsg(List<ProductInventoryChangeRecordPO> msgs) {
        String syncData = JSON.toJSONString(msgs);
        Integer warehouseId = msgs.get(0).getWarehouseId();
        boolean isOpenLocationStock =
                warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        /** begin luo kang 新增查询仓库是否开通货位组 2021-06-07 */
        boolean isOpenLocationStockGroup =
                warehouseId == null ? false : warehouseConfigService.isOpenLocationGroup(warehouseId);
        LOG.info(String.format("[V%s]批次库存变更事件同步消息：%s", isOpenLocationStock ? 3 : 2, syncData));
        Iterator<ProductInventoryChangeRecordPO> msgIterator = msgs.iterator();
        while (msgIterator.hasNext()) {
            // 元数据
            ProductInventoryChangeRecordPO next = msgIterator.next();
            // 拷贝数据
            ProductInventoryChangeRecordPO msg = new ProductInventoryChangeRecordPO();
            BeanUtils.copyProperties(next, msg);
            // 查询库存
            waitingStoreProcess(msg.getProductStoreId());
            // 插入批属性. 返回batchNo
            if (msg.getBatchTime() == null) {
                msg.setBatchTime(Calendar.getInstance().getTime());
            }
            if (StringUtils.isBlank(msg.getBatchAttributeInfoNo())) {
                String batchNo = batchAttributeInfoBL.addBatchAttributeInfo(msg);
                msg.setBatchAttributeInfoNo(batchNo);
            }
            if (StringUtils.isNotEmpty(msg.getOrderNo())) {
                Boolean isAdvent = msg.getIsAdvent();
                if (!isOpenLocationStock) {
                    // 是否残次品调拨出库
                    boolean isDefectiveAllotOut =
                            msg.getOrderType() != null && msg.getOrderType().equals(ERPType.残次品调拨库.getType())
                                    && msg.getTotalCount().compareTo(BigDecimal.ZERO) < 0;
                    boolean isCanCiChenLie =
                            msg.getOrderType() != null && (msg.getOrderType().equals(ERPType.处理品转入.getType())
                                    || msg.getOrderType().equals(ERPType.处理品转出.getType())
                                    || msg.getOrderType().equals(ERPType.陈列品转入.getType())
                                    || msg.getOrderType().equals(ERPType.陈列品转出.getType()) || isDefectiveAllotOut);
                    if (isCanCiChenLie) {
                        // 处理品转入和转出，两种都包含（从一个货位移入+从一个货位移出），总体货位库存不变，不需要单独处理
                        // 没开启货位库存：残次品限定其货位[残次品区]
                        boolean isCanCiPin =
                                msg.getOrderType() != null && (msg.getOrderType().equals(ERPType.处理品转入.getType())
                                        || msg.getOrderType().equals(ERPType.处理品转出.getType()));
                        if (isCanCiPin) {
                            msg.setLocationId(null);
                            msg.setTotalCount(msg.getTotalCount().multiply(new BigDecimal(-1)));
                            productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.残次品区.getType(), null,
                                    false, isAdvent);
                        } else if (isDefectiveAllotOut) {
                            LOG.info(String.format("[V%s]残次品调拨出库单[%s-%s]默认从残次品区出库,msg:%s", isOpenLocationStock ? 3 : 2,
                                    msg.getOrderId(), msg.getOrderNo(), JSON.toJSONString(msg)));
                            productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.残次品区.getType(), null,
                                    true, isAdvent);
                        }
                    } else {
                        // 是否有残次品位
                        boolean hasDefectiveGoodsLocation = batchInventoryManageBL.isDefectiveGoodsLocation(msg);
                        // 2.5没开启货位扣的，有推荐货位信息传过来，需要处理掉
                        // 非处理品限定其货位类型：不能是【LocationEnum：残次品位】
                        msg.setLocationIds(null);
                        msg.setLocationId(null);
                        msg.setLocationName(null);
                        // 不是 处理品转入、处理品转出：
                        // 1、有残次品货位或者残次品区则让其从该货位处理库存 2、没有残次品位或者残次品区则限定其货位不能从残次品位处理
                        if (hasDefectiveGoodsLocation) {
                            productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.残次品区.getType(), null,
                                    true, isAdvent);
                        } else {
                            productStoreBatchBL.processLocationInventory(msg, null,
                                    Lists.newArrayList(LocationEnum.残次品位.getType(), LocationAreaEnum.残次品区.getType()), true, isAdvent);
                        }
                    }
                    // 未开启货位库存处理盘点单生产日期
                    processProductionDate(msg);
                } else {
                    // 处理erp
                    if (msg.getErpEventType() != null) {
                        isOpenLocationStockGroupBuild(msg, isOpenLocationStockGroup);
                        BigDecimal oldTotalCount = msg.getTotalCount();
                        if (msg.getLocationId() == null) {
                            productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.待检区.getType(), null,
                                    false, isAdvent);
                        } else {
                            productStoreBatchBL.processLocationInventory(msg, null, null, false, isAdvent);
                        }
                        // 处理品转入（出库单）
                        // 正品转处理品 从指定区域移除库存，并将库存转到暂存区
                        // 处理品转出（入库单）
                        // 处理品转正品 将库存转到指定区域，并从暂存区移除库存

                        // 处理品转入，还需要加上将出库产品挪到暂存区
                        // 处理品转出，原消息只处理了将处理品数量加到指定货位，还需要将暂存区库存移除掉
                        boolean isCanCiPin =
                                msg.getOrderType() != null && (msg.getOrderType().equals(ERPType.处理品转入.getType())
                                        || msg.getOrderType().equals(ERPType.处理品转出.getType()));
                        if (isCanCiPin) {
                            msg.setLocationId(null);
                            msg.setTotalCount(oldTotalCount.multiply(new BigDecimal(-1)));
                            productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.残次品区.getType(), null,
                                    false, isAdvent);
                        }
                        boolean isChenLie =
                                msg.getOrderType() != null && (msg.getOrderType().equals(ERPType.陈列品转入.getType())
                                        || msg.getOrderType().equals(ERPType.陈列品转出.getType()));
                        if (isChenLie) {
                            msg.setLocationId(null);
                            msg.setTotalCount(oldTotalCount.multiply(new BigDecimal(-1)));
                            productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.陈列品区.getType(), null,
                                    false, isAdvent);
                        }
                    }
                    // 处理酒批订单
                    else {
                        if (msg.getLocationId() != null) {
                            isOpenLocationStockGroupBuild(msg, isOpenLocationStockGroup);
                            // 如果有指定货位，优先处理指定货位
                            productStoreBatchBL.processLocationInventory(msg, null, null, true, isAdvent);
                        } else {
                            if (msg.getTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                                // 延迟配送 部分配送 配送失败
                                // 入库到退货暂存区
                                boolean isReturnOrder = msg.getJiupiEventType() != null
                                        && (msg.getJiupiEventType().equals(JiupiEventType.订单延迟配送.getType())
                                        || msg.getJiupiEventType().equals(JiupiEventType.配送失败返仓库库存.getType())
                                        || msg.getJiupiEventType().equals(JiupiEventType.退货单返仓库库存.getType())
                                        || msg.getJiupiEventType().equals(JiupiEventType.部分配送返仓库库存.getType()));
                                if (isReturnOrder) {
                                    productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.退货区.getType(),
                                            null, true, isAdvent);
                                } else {
                                    // 入库到待检区
                                    productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.待检区.getType(),
                                            null, true, isAdvent);
                                }
                            } else if (msg.getTotalCount().compareTo(BigDecimal.ZERO) < 0) {
                                if (msg.getJiupiEventType() != null
                                        && msg.getJiupiEventType().equals(JiupiEventType.订单召回.getType())) {
                                    productStoreBatchBL.processLocationInventory(msg, LocationAreaEnum.退货区.getType(),
                                            null, true, isAdvent);
                                } else {
                                    // 从周转区出库
                                    // 先根据订单id->batchNo->去batchTask表中找到tolocationID 如果找到了就不走周转区.
                                    List<Long> toLocationIdByIdS = null;
                                    // 实际处理订单
                                    String orderId = msg.getOrderId();
                                    String orderNo = msg.getOrderNo();
                                    if (StringUtils.isNotEmpty(msg.getOldOrderId())) {
                                        // 优先获取原单
                                        orderId = msg.getOldOrderId();
                                        orderNo = msg.getOldOrderNo();
                                    }
                                    if (!StringUtils.isEmpty(orderId)) {
                                        if (StringUtils.isNotEmpty(msg.getProductStoreId())) {
                                            ProductStoreBaseDTO productStoreBaseDTO = iWarehouseInventoryQueryService
                                                    .getProductStoreById(msg.getProductStoreId());
                                            // LOG.info("查询仓库库存：{}", JSON.toJSONString(productStoreBaseDTO));
                                            toLocationIdByIdS = iBatchTaskQueryService
                                                    .findOrderItemToLocationIdBySpecId(Long.parseLong(orderId),
                                                            productStoreBaseDTO.getProductSpecificationId(),
                                                            productStoreBaseDTO.getOwnerId());
                                        } else {
                                            // 取拣货任务的出库位,取不到则取订单项的
                                            toLocationIdByIdS = iBatchTaskQueryService
                                                    .findToLocationIdByOrderId(Long.parseLong(orderId));
                                        }
                                        // if (CollectionUtils.isEmpty(toLocationIdByIdS)) {
                                        // // 取订单项的出库位
                                        // toLocationIdByIdS =
                                        // iOutStockQueryService.findOutStockOrderItemLocationIdByOrderId(Long.parseLong(orderId));
                                        // }
                                    }
                                    if (CollectionUtils.isNotEmpty(toLocationIdByIdS)) {
                                        if (isOpenLocationStockGroup) {
                                            LOG.info("开启货位库存并且开启货位组逻辑处理原值locationIds ：{}",
                                                    JSON.toJSONString(toLocationIdByIdS));
                                            List<String> idList = toLocationIdByIdS.stream().map(x -> x + "")
                                                    .collect(Collectors.toList());
                                            List<LocationReturnDTO> locationReturnDTOS =
                                                    locationAreaService.findLocationListById(idList);
                                            List<Long> locationIds = locationReturnDTOS.stream()
                                                    .map(x -> x.getArea_Id()).collect(Collectors.toList());
                                            // 货位Id值替换成货区Id
                                            msg.setLocationIds(locationIds);
                                            LOG.info("开启货位库存并且开启货位组逻辑处理替换值locationIds ：{}",
                                                    JSON.toJSONString(msg.getLocationIds()));
                                        } else {
                                            msg.setLocationIds(toLocationIdByIdS);
                                        }
                                        LOG.info(String.format("订单[%s-%s]/[%s-%s]拣货存放区域：%s,msg:%s", orderId, orderNo,
                                                msg.getOrderId(), msg.getOrderNo(), toLocationIdByIdS,
                                                JSON.toJSONString(msg)));
                                        productStoreBatchBL.processLocationInventory(msg, null, null, true, isAdvent);
                                    } else {
                                        if (Objects.equals(msg.getOrderType(),
                                                Integer.valueOf(OutStockOrderTypeEnum.奖券出库.getType()))
                                                || Objects.equals(msg.getOrderType(),
                                                Integer.valueOf(OutStockOrderTypeEnum.兑奖差异出库.getType()))) {
                                            LOG.info(String.format("奖券出库单[%s-%s]/[%s-%s]没找到存放区域,msg:%s", orderId,
                                                    orderNo, msg.getOrderId(), msg.getOrderNo(), JSON.toJSONString(msg)));
                                            productStoreBatchBL.processLocationInventory(msg, null, null, true, isAdvent);
                                        } else if (Objects.equals(msg.getOrderType(),
                                                Integer.valueOf(OutStockOrderTypeEnum.残次品调拨单.getType()))) {
                                            LOG.info(String.format("残次品调拨出库单[%s-%s]/[%s-%s]默认从残次品区出库,msg:%s", orderId,
                                                    orderNo, msg.getOrderId(), msg.getOrderNo(), JSON.toJSONString(msg)));
                                            productStoreBatchBL.processLocationInventory(msg,
                                                    LocationAreaEnum.残次品区.getType(), null, true, isAdvent);
                                        } else {
                                            LOG.info(String.format("订单[%s-%s]/[%s-%s]没找到存放区域：从周转区扣库存,msg:%s", orderId,
                                                    orderNo, msg.getOrderId(), msg.getOrderNo(), JSON.toJSONString(msg)));
                                            productStoreBatchBL.processLocationInventory(msg,
                                                    LocationAreaEnum.周转区.getType(), null, true, isAdvent);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //扣库存，默认为出库单
                if (msg.getTotalCount().compareTo(BigDecimal.ZERO) < 0 && !Objects.equals(msg.getOrderType(), ERPType.库存盘点单.getType())) {
                    ProductDateSyncPO syncPO = new ProductDateSyncPO();
                    Long orderId = StringUtils.isNotEmpty(msg.getOrderId()) && StringUtils.isNumeric(msg.getOrderId()) ? Long.valueOf(msg.getOrderId()) : null;
                    syncPO.setOrderId(orderId);
                    syncPO.setOrderNo(msg.getOrderNo());
                    syncPO.setWarehouseId(msg.getWarehouseId());
                    delayMessageTemplate.convertAndSend(orderOutboundExchange, null, syncPO, Duration.ofSeconds(DELAY_SECONDS));
                }
            } else {// 处理手动修改
                if (msg.getJiupiEventType() != null
                        && JiupiEventType.手动修改.getType().intValue() == msg.getJiupiEventType().intValue()) {
                    // productStoreBatchBL.processManualBatchInventory(msg);
                    productStoreBatchBL.processLocationInventory(msg, null, null, true, null);
                }
            }
            // 处理成功则移除
            msgIterator.remove();
        }
    }

    /**
     * luo kang
     *
     * @param msg
     * @param isOpenLocationStockGroup 是否开启货位组
     * @date 2020-06-07
     */
    private void isOpenLocationStockGroupBuild(ProductInventoryChangeRecordPO msg, boolean isOpenLocationStockGroup) {
        if (!isOpenLocationStockGroup) {
            return;
        }

        LOG.info("开启货位库存并且开启货位组逻辑处理原值locationId ：{}", msg.getLocationId());
        if (Objects.isNull(msg.getLocationId())) {
            return;
        }
        List<LocationReturnDTO> locationReturnDTOS = locationAreaService
                .findLocationAreaListExcludeDefective(Arrays.asList(String.valueOf(msg.getLocationId())));
        if (CollectionUtils.isEmpty(locationReturnDTOS)) {
            return;
        }

        // 货位Id值替换成货区Id
        msg.setLocationId(locationReturnDTOS.get(0).getArea_Id());
        LOG.info("开启货位库存并且开启货位组逻辑处理替换值locationId ：{}", msg.getLocationId());
    }

    private void waitingStoreProcess(String productStoreId) {
        if (StringUtils.isBlank(productStoreId)) {
            return;
        }
        // 去查库存 ownertype 如果是经销商,保存批次时间.
        ProductInventoryPO inventoryPO = productStoreMapper.findInventoryPO(productStoreId);
        if (inventoryPO == null) {
            // todo 临时方案，没有库存数据先睡眠。后续改成缓存模式
            try {
                TimeUnit.SECONDS.sleep(4);
            } catch (InterruptedException e) {
                LOG.error("SupplychainStoreChangeListener - Interrupted!", e);
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 处理未开启货位库存盘点单产品生产日期
     */
    @Async
    public void processProductionDate(ProductInventoryChangeRecordPO changeRecordPO) {
        // 只在盘点单审核时处理库存信息
        if (changeRecordPO == null || !ERPType.库存盘点单.getType().equals(changeRecordPO.getOrderType())
                || !Objects.equals(ERPEventType.单据审核.getType(), changeRecordPO.getErpEventType())
                || StringUtils.isBlank(changeRecordPO.getOrderNo())) {
            return;
        }
        String reportItemNo = changeRecordPO.getOrderNo();
        Integer cityId = changeRecordPO.getCityId();
        Integer warehouseId = changeRecordPO.getWarehouseId();
        CheckResultItemQuerySO querySO = new CheckResultItemQuerySO();
        querySO.setOrgId(cityId);
        querySO.setWarehouseId(warehouseId);
        querySO.setReportItemNo(reportItemNo);
        CheckResultItemDTO checkResultItemDTO = storeCheckResultService.selectByReportItemNo(querySO);
        if (checkResultItemDTO == null) {
            LOG.info("批次库存变更处理盘点生产日期没有找到对应盘盈盘亏单[{}]信息！", reportItemNo);
            return;
        }
        String noteId = checkResultItemDTO.getNoteId();
        StoreCheckProductQueryDTO queryDTO = new StoreCheckProductQueryDTO();
        queryDTO.setOrgId(cityId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setNoteId(noteId);
        queryDTO.setProductSkuIdList(Collections.singletonList(checkResultItemDTO.getProductskuId()));
        List<StoreCheckProductResultDTO> productInfoList =
                storeCheckOrderService.findStoreCheckProductInfoList(queryDTO);
        if (org.springframework.util.CollectionUtils.isEmpty(productInfoList)) {
            return;
        }
        StoreCheckProductResultDTO productResultDTO = productInfoList.get(0);
        cityId = cityId == null ? productResultDTO.getOrgId() : cityId;
        warehouseId = warehouseId == null ? productResultDTO.getWarehouseId() : warehouseId;
        String noteNo = productResultDTO.getNoteNo();
        List<StoreCheckUpdateBatchInventoryItemDTO> updateBatchInventoryItemDTOS = productInfoList.stream()
                .filter(pi -> pi != null
                        && ObjectUtils.defaultIfNull(pi.getUnitTotalCount(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0)
                .map(pi -> {
                    StoreCheckUpdateBatchInventoryItemDTO batchInventoryItemDTO =
                            new StoreCheckUpdateBatchInventoryItemDTO();
                    batchInventoryItemDTO.setProductSkuId(pi.getProductSkuId());
                    batchInventoryItemDTO.setLocationId(pi.getLocationId());
                    batchInventoryItemDTO.setLocationName(pi.getLocationName());
                    batchInventoryItemDTO.setProductionDate(
                            pi.getBatchProductionDate() != null ? pi.getBatchProductionDate() : pi.getProductionDate());
                    batchInventoryItemDTO
                            .setBatchTime(pi.getBatchInStockTime() != null ? pi.getBatchInStockTime() : pi.getBatchTime());
                    batchInventoryItemDTO.setUnitTotalCount(pi.getUnitTotalCount());
                    return batchInventoryItemDTO;
                }).collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(updateBatchInventoryItemDTOS)) {
            return;
        }
        StoreCheckUpdateBatchInventoryDTO batchInventoryDTO = new StoreCheckUpdateBatchInventoryDTO();
        batchInventoryDTO.setOrgId(cityId);
        batchInventoryDTO.setWarehouseId(warehouseId);
        batchInventoryDTO.setNoteId(noteId);
        batchInventoryDTO.setNoteNo(noteNo);
        batchInventoryDTO.setChangeItemList(updateBatchInventoryItemDTOS);
        batchInventoryManageBL.nonOpenStockChangeBatchInventoryByStoreCheck(batchInventoryDTO);
    }
}

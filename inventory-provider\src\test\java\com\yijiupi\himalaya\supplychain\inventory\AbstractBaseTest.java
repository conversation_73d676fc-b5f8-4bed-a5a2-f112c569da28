package com.yijiupi.himalaya.supplychain.inventory;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 抽象的测试基类
 * 
 * <AUTHOR>
 * @date 2021/07/29
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public abstract class AbstractBaseTest {
	protected final Logger logger = LoggerFactory.getLogger(this.getClass());

	private static final ObjectMapper objectMapper = new ObjectMapper();

	public static <T> T readValue(String content, Class<T> valueType) {
		try {
			return objectMapper.readValue(content, valueType);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static <T> String writeValueAsString(T t) {
		try {
			return objectMapper.writeValueAsString(t);
		} catch (Exception e) {
			
		}
		return "";
	}
}

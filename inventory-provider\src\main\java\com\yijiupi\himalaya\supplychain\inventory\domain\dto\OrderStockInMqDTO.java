package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class OrderStockInMqDTO implements Serializable {
    private static final long serialVersionUID = -8430146644914370378L;
    /**
     * 订单项列表
     */
    private List<OrderItemStockInMqDTO> items;
    private Long id;
    private Long businessId;
    private Integer cityId;
    /**
     * 来源城市id
     */
    private Integer fromCityId;
    /**
     * 来源城市id(知花知果id)
     */
    private Long fromOrgId;
    /**
     * 配送状态:-1:默认,0:全部配送,1:部分发货,2:部分配送,3:延迟配送,4:配送失败
     */
    private Byte deliveryState;
    /**
     * 司机id
     */
    private Integer deliveryUserId;
    private Integer operaterId;
    /**
     * 订单id:知花知果id
     */
    private Long orderId;
    /**
     * 订单编号:知花知果编号
     */
    private String orderNo;
    /**
     * 订单状态:14:延迟配送,10:完成
     */
    private Byte state;
    /**
     * 拣货员id
     */
    private Integer stevedoreUserId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    @Override
    public String toString() {
        return "OrderStockInMqDTO [items=" + items + ", id=" + id + ", businessId=" + businessId + ", cityId=" + cityId
            + ", fromCityId=" + fromCityId + ", fromOrgId=" + fromOrgId + ", deliveryState=" + deliveryState
            + ", deliveryUserId=" + deliveryUserId + ", operaterId=" + operaterId + ", orderId=" + orderId
            + ", orderNo=" + orderNo + ", state=" + state + ", stevedoreUserId=" + stevedoreUserId + ", warehouseId="
            + warehouseId + ", userId=" + userId + ", payableAmount=" + payableAmount + "]";
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public List<OrderItemStockInMqDTO> getItems() {
        return items;
    }

    public void setItems(List<OrderItemStockInMqDTO> items) {
        this.items = items;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getFromCityId() {
        return fromCityId;
    }

    public void setFromCityId(Integer fromCityId) {
        this.fromCityId = fromCityId;
    }

    public Byte getDeliveryState() {
        return deliveryState;
    }

    public void setDeliveryState(Byte deliveryState) {
        this.deliveryState = deliveryState;
    }

    public Integer getDeliveryUserId() {
        return deliveryUserId;
    }

    public void setDeliveryUserId(Integer deliveryUserId) {
        this.deliveryUserId = deliveryUserId;
    }

    public Integer getOperaterId() {
        return operaterId;
    }

    public void setOperaterId(Integer operaterId) {
        this.operaterId = operaterId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getStevedoreUserId() {
        return stevedoreUserId;
    }

    public void setStevedoreUserId(Integer stevedoreUserId) {
        this.stevedoreUserId = stevedoreUserId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }

    /**
     * 获取fromOrgId
     * 
     * @return fromOrgId fromOrgId
     */
    public Long getFromOrgId() {
        return fromOrgId;
    }

    /**
     * 设置fromOrgId
     * 
     * @param fromOrgId fromOrgId
     */
    public void setFromOrgId(Long fromOrgId) {
        this.fromOrgId = fromOrgId;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.inventory.dto.StockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.dto.VirtualStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.dto.VirtualStockOrderItemInventoryDTO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/23
 */
public class StockOrderInventoryDTOConvert {

    public static VirtualStockOrderInventoryDTO convertToVirtualStockOrderInventoryDTO(
            StockOrderInventoryDTO stockOrderInventoryDTO) {
        if (stockOrderInventoryDTO == null) {
            return new VirtualStockOrderInventoryDTO();
        }

        VirtualStockOrderInventoryDTO dto = new VirtualStockOrderInventoryDTO();
        BeanUtils.copyProperties(stockOrderInventoryDTO, dto);
        if(CollectionUtils.isEmpty(stockOrderInventoryDTO.getStockOrderItemDTOS())){
            return dto;
        }
        List<VirtualStockOrderItemInventoryDTO> itemDTOS =  stockOrderInventoryDTO.getStockOrderItemDTOS().stream().map(it -> {
            VirtualStockOrderItemInventoryDTO item = new VirtualStockOrderItemInventoryDTO();
            BeanUtils.copyProperties(it, item);
            return item;
        }).collect(Collectors.toList());
        dto.setStockOrderItemDTOS(itemDTOS);
        return dto;
    }
}

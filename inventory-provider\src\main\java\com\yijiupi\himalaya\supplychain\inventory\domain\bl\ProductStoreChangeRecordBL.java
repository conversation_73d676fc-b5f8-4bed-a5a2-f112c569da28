/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.*;
import com.yijiupi.himalaya.supplychain.dubbop.adapter.IDictionaryTypeService;
import com.yijiupi.himalaya.supplychain.dubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.dubbop.dto.DictionaryType;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.FindStoreChangeInfoConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.StoreChangeInfoConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryChangeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangeInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreChangePageDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.StoreChangeInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ChildOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommQueryService;
import com.yijiupi.himalaya.supplychain.search.ProductStoreChangeRecordByOrderSO;
import com.yijiupi.himalaya.supplychain.search.ProductStoreRecordSO;
import com.yijiupi.himalaya.supplychain.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库库存变更记录
 */
@Service
public class ProductStoreChangeRecordBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductStoreChangeRecordBL.class);

    @Autowired
    private ProductInventoryChangeRecordPOMapper productInventoryChangeRecordPOMapper;

    @Autowired
    private WarehouseInventoryQueryBL queryBL;
    @Autowired
    private ProductSkuQueryBL skuQueryBL;
    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;

    @Reference
    private IOutStockCommQueryService iOutStockCommQueryService;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IDictionaryTypeService iDictionaryTypeService;

    @Autowired
    private StoreChangeInfoConvert storeChangeInfoConvert;

    /**
     * yyyy-MM-dd的日期格式
     */
    private final String DATE_FORMAT = "\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])";

    private static final String MIN_DATE = "2021-01-01";
    private static final String MIN_DATE_FORMAT = "yyyy-MM-dd";
    // 库存服务-仓库库存
    public static final Integer INVENTROY_STORE_TYPE_WAREHOUSE = 1;

    @Autowired
    private FindStoreChangeInfoConvert findStoreChangeInfoConvert;

    /**
     * 批量新增仓库库存变更记录
     *
     * @param productInventoryChangeRecordDTOList
     */
    public void saveProductInventoryChangeRecordBatch(
        List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOList) {
        if (CollectionUtils.isEmpty(productInventoryChangeRecordDTOList)) {
            return;
        }
        List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOList =
            productInventoryChangeRecordDTOList.stream().map(dto -> {
                ProductInventoryChangeRecordPO po = new ProductInventoryChangeRecordPO();
                BeanUtils.copyProperties(dto, po);
                return po;
            }).collect(Collectors.toList());
        insertProductInventoryChangeRecordBatch(productInventoryChangeRecordPOList, false, true);
    }

    /**
     * 批量新增仓库库存变更记录,处理批次库存
     *
     * @param productInventoryChangeRecordPOList
     */
    private void insertProductInventoryChangeRecordBatch(
        List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOList, boolean isUpdateProductBatchStore,
        boolean isUpdateProductStore) {
        if (CollectionUtils.isEmpty(productInventoryChangeRecordPOList)) {
            return;
        }
        productInventoryChangeRecordPOList.forEach(p -> p.setSystemSource(0));
        // 如果只做移库，不更新库存，不需要增加库存变更记录
        if (isUpdateProductStore) {
            Lists.partition(productInventoryChangeRecordPOList, 200).forEach(p -> {
                productInventoryChangeRecordPOMapper.insertProductInventoryChangeRecordBatch(p);
            });
        }
        if (isUpdateProductBatchStore) {
            if (!isUpdateProductStore) {
                // 增加一条空的总库存变更记录, 因为库存查询是货位库存关联了总库存变更记录
                buildEmptyInventoryChangeRecord(productInventoryChangeRecordPOList.get(0));
            }
            // 发送仓库库存变更事件，处理批次库存
            inventoryChangeEventFireBL.storeInventoryChangeEvent(productInventoryChangeRecordPOList);
        }
    }

    /**
     * 组合单库存变更明细替换为子单单号
     * 
     * @param warehouseChangeList
     */
    private void setChildOrderStoreChangeRecord(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return;
        }
        // 获取组合订单的订单项ID集合
        List<Long> orderItemIds = warehouseChangeList.stream()
            .filter(p -> p.getOrderNo() != null && p.getOrderNo().startsWith("H") && p.getOrderItemId() != null)
            .map(p -> p.getOrderItemId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return;
        }
        // 根据组合订单项ID查询的子订单号
        List<ChildOrderDTO> childOrderDTOS = iOutStockCommQueryService.listChildOrderByItemId(orderItemIds);
        if (CollectionUtils.isEmpty(childOrderDTOS)) {
            return;
        }
        // LOG.info("根据组合订单项ID: {}, 查询的子订单号：{}", JSON.toJSONString(orderItemIds), JSON.toJSONString(childOrderDTOS));
        final String strCombinOrder = "H";
        warehouseChangeList.forEach(bo -> {
            if (!bo.getOrderNo().startsWith(strCombinOrder) || bo.getOrderItemId() == null) {
                return;
            }
            List<ChildOrderDTO> filterList = childOrderDTOS.stream()
                .filter(p -> Objects.equals(p.getItemId(), bo.getOrderItemId())
                    && !Objects.equals(p.getChildOrderNo(), bo.getOrderNo()) && !p.getChildOrderNo().startsWith("H"))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                return;
            }
            ChildOrderDTO childOrderDTO = filterList.get(0);
            // 存在子订单，则替换
            if (childOrderDTO.getChildOrderId() != null && !StringUtils.isEmpty(childOrderDTO.getChildOrderNo())) {
                // 保留原单
                bo.setOldOrderId(bo.getOrderId());
                bo.setOldOrderNo(bo.getOrderNo());
                // 赋值子单
                bo.setOrderId(childOrderDTO.getChildOrderId().toString());
                bo.setOrderNo(childOrderDTO.getChildOrderNo());
            }
        });
        // LOG.info("替换后的warehouseChangeList：{}", JSON.toJSONString(warehouseChangeList));
    }

    /**
     * 批量库存扣减记录
     *
     * @param warehouseChangeList
     * @param productInventoryPOS
     */
    public void processProductStoreChangeRecord(List<WarehouseInventoryChangeBO> warehouseChangeList,
        ArrayList<ProductInventoryPO> productInventoryPOS, boolean isUpdateProductBatchStore,
        boolean isUpdateProductStore) {
        // 组合单库存变更明细替换为子单单号
        setChildOrderStoreChangeRecord(warehouseChangeList);

        // 创建库存扣减记录（以订单项为维度）
        final List<ProductInventoryChangeRecordPO> lstProductInventoryChangeRecordPO = new ArrayList<>();
        // 区分同一批次相同SKU的创建时间
        HashMap<Long, Integer> skuMap = new HashMap<>(16);

        warehouseChangeList.forEach(bo -> {
            if (bo.getCount().compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            Optional<ProductInventoryPO> inventoryPO = productInventoryPOS.stream()
                .filter(q -> Objects.equals(q.getWarehouseId(), bo.getWarehouseId())
                    && Objects.equals(q.getChannel(), bo.getChannel())
                    && Objects.equals(q.getProductSpecificationId(), bo.getProductSpecificationId())// (q.getProductSpecificationId()
                                                                                                    // == null ||
            // && (q.getProductSkuId() == null || Objects.equals(q.getProductSkuId(), bo.getProductSkuId()))
                    && Objects.equals(q.getOwnerId(), bo.getOwnId())
                    && Objects.equals(q.getSecOwnerId(), bo.getSecOwnerId()))
                .findFirst();
            if (!inventoryPO.isPresent()) {
                LOG.warn(String.format("没有找到匹配的库存，不处理库存变更记录，BO：%s，inventory:%s", JSON.toJSONString(bo),
                    JSON.toJSONString(productInventoryPOS)));
                return;
            }
            ProductInventoryPO productInventoryPO = inventoryPO.get();
            ProductInventoryChangeRecordPO productInventoryChangeRecordPO = bo.createProductInventoryChangeRecordPO(
                productInventoryPO.getId(), productInventoryPO.getTotalCountMinUnit());
            productInventoryChangeRecordPO
                .setCreateUser(bo.getCreateUserName() == null ? bo.getCreateUserId() : bo.getCreateUserName());
            // 批属性
            productInventoryChangeRecordPO.setAttributeList(bo.getAttributeList());
            // 配送方式
            productInventoryChangeRecordPO.setDeliveryMode(bo.getDeliveryMode());
            productInventoryChangeRecordPO.setLocationId(bo.getLocationId());
            productInventoryChangeRecordPO.setLocationName(bo.getLocationName());
            productInventoryChangeRecordPO.setProductionDate(bo.getProductionDate());
            productInventoryChangeRecordPO.setExpireTime(bo.getExpireTime());
            productInventoryChangeRecordPO.setBatchTime(bo.getBatchTime());
            productInventoryChangeRecordPO.setWarehouseId(bo.getWarehouseId());
            // 批次库存ID
            productInventoryChangeRecordPO.setProductStoreBatchId(bo.getProductStoreBatchId());
            // SCM2-9937 批次编号
            productInventoryChangeRecordPO.setBatchAttributeInfoNo(bo.getBatchNo());

            Integer countExits = skuMap.get(bo.getProductSkuId());
            countExits = (countExits == null) ? 0 : countExits;
            if (countExits > 0) {
                // 按扣减顺序，把同一批次有多个相同SKU的创建时间加1毫秒
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(productInventoryChangeRecordPO.getCreateTime());
                calendar.add(Calendar.MILLISECOND, countExits);
                productInventoryChangeRecordPO.setCreateTime(calendar.getTime());
            }

            productInventoryChangeRecordPO.setCityId(productInventoryPO.getCityId());

            // 添加OwnerType和OwnerId
            productInventoryChangeRecordPO.setOwnerId(productInventoryPO.getOwnerId());
            productInventoryChangeRecordPO.setOwnerType(productInventoryPO.getOwnerType());
            productInventoryChangeRecordPO.setSecOwnerId(productInventoryPO.getSecOwnerId());
            productInventoryChangeRecordPO.setPackageQuantity(productInventoryPO.getPackageQuantity());

            productInventoryChangeRecordPO.setProductSpecificationId(productInventoryPO.getProductSpecificationId());

            productInventoryChangeRecordPO.setDescription(bo.getDescription());

            // 原单
            productInventoryChangeRecordPO.setOldOrderId(bo.getOldOrderId());
            productInventoryChangeRecordPO.setOldOrderNo(bo.getOldOrderNo());

            // 增加SKUID传递
            productInventoryChangeRecordPO.setProductSkuId(bo.getProductSkuId());
            // 是否临期
            productInventoryChangeRecordPO.setIsAdvent(bo.getIsAdvent());
            lstProductInventoryChangeRecordPO.add(productInventoryChangeRecordPO);
            countExits++;
            skuMap.put(bo.getProductSkuId(), countExits);
            // 将原来的SourceTotalCount变化，不然同一批次有多个相同SKU的话，SourceTotalCount是一样的
            productInventoryPO.setTotalCountMinUnit(productInventoryPO.getTotalCountMinUnit().add(bo.getCount()));
        });
        // LOG.info("库存新增记录:{}", JSON.toJSONString(lstProductInventoryChangeRecordPO));
        // 批量新增仓库库存变更记录,处理批次库存
        insertProductInventoryChangeRecordBatch(lstProductInventoryChangeRecordPO, isUpdateProductBatchStore,
            isUpdateProductStore);
        skuMap.clear();
    }

    /**
     * 分页查询仓库库存变更记录
     */
    public PageList<ProductStoreChangeRecordDTO> findProductInventoryRecordList(
        ProductStoreRecordSO productStoreRecordSO, PagerCondition pager, Long productSkuId) {
        PageList<ProductStoreChangeRecordDTO> resultList = new PageList<>();
        PageResult<ProductInventoryChangeRecordPO> poList = productInventoryChangeRecordPOMapper
            .findProductInventoryChangeRecordPOList(productStoreRecordSO, pager.getCurrentPage(), pager.getPageSize());
        List<ProductStoreChangeRecordDTO> recordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(poList)) {
            recordList = poList.stream().map(p -> convertToProductStoreChangeRecordDTO(p, productSkuId))
                .collect(Collectors.toList());
        }
        resultList.setDataList(recordList);
        resultList.setPager(poList.getPager());
        return resultList;
    }

    /**
     * 分页查询仓库库存变更记录
     */
    public PageList<ProductStoreChangeRecordDTO>
        findProductInventoryChangeRecordPOListBySkuId(ProductStoreRecordSO productStoreRecordSO, PagerCondition pager) {
        PageList<ProductStoreChangeRecordDTO> resultList = new PageList<>();
        PageResult<ProductInventoryChangeRecordPO> poList =
            productInventoryChangeRecordPOMapper.findProductInventoryChangeRecordPOListBySkuId(productStoreRecordSO,
                pager.getCurrentPage(), pager.getPageSize());
        List<ProductStoreChangeRecordDTO> recordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(poList)) {
            recordList =
                poList.stream().map(p -> convertToProductStoreChangeRecordDTO(p, null)).collect(Collectors.toList());
        }
        resultList.setDataList(recordList);
        resultList.setPager(poList.getPager());
        return resultList;
    }

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    public PageList<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordByOrder(ProductStoreChangeRecordByOrderSO orderSO) {
        AssertUtils.notNull(orderSO, "查询订单库存变更记录参数不能为空！");
        if (StringUtils.isEmpty(orderSO.getOrderId()) && StringUtils.isEmpty(orderSO.getOrderNo())
            && CollectionUtils.isEmpty(orderSO.getOrderNoList())) {
            throw new DataValidateException("订单ID或订单号不能为空");
        }
        PageResult<ProductStoreChangeRecordByOrderDTO> pageResult =
            productInventoryChangeRecordPOMapper.listProductStoreChangeRecordByOrder(orderSO);
        PageList<ProductStoreChangeRecordByOrderDTO> pageList = pageResult.toPageList();
        if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
            pageList.getDataList().forEach(p -> {
                p.setAddStoreCountDTO(new ProductStoreCountDTO(p.getTotalCount(), p.getSpecQuantity()));
                p.setSourceStoreCountDTO(new ProductStoreCountDTO(p.getSourceTotalCount(), p.getSpecQuantity()));
                p.setNewStoreCountDTO(
                    new ProductStoreCountDTO(p.getSourceTotalCount().add(p.getTotalCount()), p.getSpecQuantity()));
            });
        }
        return pageList;
    }

    public List<InventoryProductStoreBatchChangeRecordDTO>
        findProductStoreBatchChangeRecordList(ProductStoreChangeRecordByOrderSO orderSO) {
        AssertUtils.notNull(orderSO, "查询订单库存变更记录参数不能为空！");
        AssertUtils.notNull(orderSO.getWarehouseId(), "仓库信息不能为空！");
        AssertUtils.notEmpty(orderSO.getOrderNoList(), "订单信息不能为空！");

        return productInventoryChangeRecordPOMapper.findProductStoreBatchChangeRecordList(orderSO);
    }

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    public List<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordGroupByOrder(ProductStoreChangeRecordByOrderSO orderSO) {
        AssertUtils.notNull(orderSO, "查询订单库存变更记录参数不能为空！");
        AssertUtils.notNull(orderSO.getWarehouseId(), "仓库Id不能为空！");
        if (CollectionUtils.isEmpty(orderSO.getOrderNoList())) {
            throw new DataValidateException("订单号不能为空！");
        }
        List<ProductStoreChangeRecordByOrderDTO> pageList =
            productInventoryChangeRecordPOMapper.listProductStoreChangeRecordGroupByOrder(orderSO);
        return pageList;
    }

    /**
     * 构建仓库库存变更记录DTO对象
     */
    private ProductStoreChangeRecordDTO convertToProductStoreChangeRecordDTO(
        ProductInventoryChangeRecordPO productInventoryChangeRecordPO, Long productSkuId) {
        // 通过skuid、source获取 转换系数.
        ProductSkuPO productSkuPO = null;
        if (productSkuId != null) {
            productSkuPO = skuQueryBL.getProductSkuBySkuId(productSkuId);
        }
        // 通过productStoreId查询转换系数
        ProductInventoryPO productInventoryPO =
            queryBL.selectInventoryByPrimaryKey(productInventoryChangeRecordPO.getProductStoreId());
        ProductStoreChangeRecordDTO changeRecord = new ProductStoreChangeRecordDTO();
        changeRecord.setCityId(productInventoryChangeRecordPO.getCityId());
        changeRecord.setCreateTime(productInventoryChangeRecordPO.getCreateTime());
        changeRecord.setDes(productInventoryChangeRecordPO.getDescription());
        changeRecord.setId(productInventoryChangeRecordPO.getId());
        if (productInventoryChangeRecordPO.getOrderNo() != null) {
            changeRecord.setOrderNo(productInventoryChangeRecordPO.getOrderNo());
        }
        if (productInventoryChangeRecordPO.getErpEventType() != null) {
            changeRecord.setErpEventType(productInventoryChangeRecordPO.getErpEventType());
        }
        if (productInventoryChangeRecordPO.getJiupiEventType() != null) {
            changeRecord.setJiupiEventType(productInventoryChangeRecordPO.getJiupiEventType());
        }
        changeRecord.setRecordId(productInventoryChangeRecordPO.getProductStoreId());
        BigDecimal packageQuantity =
            productSkuPO == null ? productInventoryPO.getPackageQuantity() : productSkuPO.getPackageQuantity();
        if (packageQuantity == null || packageQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            packageQuantity = BigDecimal.ONE;
        }
        changeRecord.setAddStoreCountDTO(
            new ProductStoreCountDTO(productInventoryChangeRecordPO.getTotalCount(), packageQuantity));
        changeRecord.setSourceStoreCountDTO(
            new ProductStoreCountDTO(productInventoryChangeRecordPO.getSourceTotalCount(), packageQuantity));
        changeRecord.setNewStoreCountDTO(new ProductStoreCountDTO(
            productInventoryChangeRecordPO.getSourceTotalCount().add(productInventoryChangeRecordPO.getTotalCount()),
            packageQuantity));
        changeRecord
            .setMaxUnit(productSkuPO == null ? productInventoryPO.getPackageName() : productSkuPO.getPackageName());
        changeRecord.setMinUnit(productSkuPO == null ? productInventoryPO.getUnitName() : productSkuPO.getUnitName());
        changeRecord.setCreateUser(productInventoryChangeRecordPO.getCreateUser());
        changeRecord.setOrderType(productInventoryChangeRecordPO.getOrderType());
        changeRecord.setStoreType(productInventoryChangeRecordPO.getStoreType());
        return changeRecord;
    }

    @Transactional(rollbackFor = Exception.class)
    public ProductInventoryChangeRecordPO
        buildEmptyInventoryChangeRecord(ProductInventoryChangeRecordPO changeRecordPO) {
        if (changeRecordPO == null) {
            return null;
        }
        ProductInventoryChangeRecordPO inventoryChangeRecordPO = new ProductInventoryChangeRecordPO();
        BeanUtils.copyProperties(changeRecordPO, inventoryChangeRecordPO);
        inventoryChangeRecordPO.setId(StringUtils.isBlank(inventoryChangeRecordPO.getId()) ? UUIDUtil.getUUID()
            : inventoryChangeRecordPO.getId());
        inventoryChangeRecordPO.setProductStoreId(""); // 库存ID要置空,否则会查询出来
        inventoryChangeRecordPO.setCountMaxUnit(BigDecimal.ZERO);
        inventoryChangeRecordPO.setCountMinUnit(BigDecimal.ZERO);
        inventoryChangeRecordPO.setTotalCount(BigDecimal.ZERO);
        inventoryChangeRecordPO.setSourceTotalCount(BigDecimal.ZERO);
        inventoryChangeRecordPO.setSystemSource(0);
        // 新增库存变更记录
        productInventoryChangeRecordPOMapper
            .insertProductInventoryChangeRecordBatch(Lists.newArrayList(inventoryChangeRecordPO));
        return inventoryChangeRecordPO;
    }
    //
    // /**
    // * 分页查询ERP仓库库存变更记录
    // */
    // public PageList<ProductStoreChangeRecordDTO> findERPProductInventoryRecordList(ProductStoreRecordSO
    // productStoreRecordSO, PagerCondition pager, Integer prodcutInfoSpecId) {
    // PageList<ProductStoreChangeRecordDTO> resultList = new PageList<>();
    // PageResult<ProductInventoryChangeRecordPO> poList =
    // productInventoryChangeRecordPOMapper.findProductInventoryChangeRecordPOList(productStoreRecordSO,
    // pager.getCurrentPage(), pager.getPageSize());
    // List<ProductStoreChangeRecordDTO> recordList = new ArrayList<>();
    // if (CollectionUtils.isNotEmpty(poList)) {
    // recordList = poList.stream().map(p -> convertToERPProductStoreChangeRecordDTO(p,
    // prodcutInfoSpecId)).collect(Collectors.toList());
    // }
    // resultList.setDataList(recordList);
    // resultList.setPager(poList.getPager());
    // return resultList;
    // }
    //
    // /**
    // * 构建ERP仓库库存变更记录DTO对象
    // */
    // private ProductStoreChangeRecordDTO convertToERPProductStoreChangeRecordDTO(ProductInventoryChangeRecordPO
    // productInventoryChangeRecordPO, Integer prodcutInfoSpecId) {
    // ProductStoreChangeRecordDTO changeRecord = new ProductStoreChangeRecordDTO();
    // changeRecord.setCityId(productInventoryChangeRecordPO.getCityId());
    // changeRecord.setCreateTime(productInventoryChangeRecordPO.getCreateTime());
    // changeRecord.setDes(productInventoryChangeRecordPO.getDescription());
    // changeRecord.setId(productInventoryChangeRecordPO.getId());
    // if (productInventoryChangeRecordPO.getOrderNo() != null) {
    // changeRecord.setOrderNo(productInventoryChangeRecordPO.getOrderNo());
    // }
    // if (productInventoryChangeRecordPO.getErpEventType() != null) {
    // changeRecord.setErpEventType(productInventoryChangeRecordPO.getErpEventType());
    // }
    // if (productInventoryChangeRecordPO.getJiupiEventType() != null) {
    // changeRecord.setJiupiEventType(productInventoryChangeRecordPO.getJiupiEventType());
    // }
    // changeRecord.setRecordId(productInventoryChangeRecordPO.getProductStoreId());
    // SpecificationInfoPO specificationInfo = productSkuQueryBL.getSpecificationInfo(prodcutInfoSpecId);
    // if (specificationInfo != null) {
    // BigDecimal quantity = specificationInfo.getPackageQuantity();
    // String unitName = specificationInfo.getUnitName();
    // String packageName = specificationInfo.getPackageName();
    // changeRecord.setAddStoreCountDTO(new ProductStoreCountDTO(productInventoryChangeRecordPO.getTotalCount(),
    // quantity));
    // changeRecord.setSourceStoreCountDTO(new
    // ProductStoreCountDTO(productInventoryChangeRecordPO.getSourceTotalCount(),
    // quantity));
    // changeRecord.setNewStoreCountDTO(new ProductStoreCountDTO(
    // productInventoryChangeRecordPO.getSourceTotalCount().add(productInventoryChangeRecordPO.getTotalCount()),
    // quantity));
    // changeRecord.setMaxUnit(packageName);
    // changeRecord.setMinUnit(unitName);
    // }
    //
    // changeRecord.setCreateUser(productInventoryChangeRecordPO.getCreateUser());
    // changeRecord.setOrderType(productInventoryChangeRecordPO.getOrderType());
    // changeRecord.setStoreType(productInventoryChangeRecordPO.getStoreType());
    // return changeRecord;
    // }
    //
    // /**
    // * 查询sku对应城市下仓库库存变更记录
    // */
    // public PageList<CityInventoryRecordDTO> findCityProductInventoryRecord(CityInventoryRecordQueryDTO
    // cityInventoryRecordQueryDTO, PagerCondition pager) {
    // PageResult<CityInventoryRecordDTO> cityInventoryRecordList = productInventoryChangeRecordPOMapper.
    // findCityInventoryRecordList(cityInventoryRecordQueryDTO, pager.getCurrentPage(), pager.getPageSize());
    // return cityInventoryRecordList.toPageList();
    // }
    //
    // /**
    // * 分页查询sku对应城市下仓库库存变更记录
    // */
    // public PageResult<CityInventoryRecordDTO> findCityInventoryRecordList(CityInventoryRecordQueryDTO dto, int
    // pageNum, int pageSize) {
    // return productInventoryChangeRecordPOMapper.findCityInventoryRecordList(dto, pageNum, pageSize);
    // }
    //
    // /**
    // * 分页查询仓库库存变更记录
    // */
    // public PageResult<ProductInventoryChangeRecordPO> findProductInventoryChangeRecordPOList(ProductStoreRecordSO
    // dto, int pageNum, int pageSize) {
    // return productInventoryChangeRecordPOMapper.findProductInventoryChangeRecordPOList(dto, pageNum, pageSize);
    // }

    /**
     * 更新仓库库存变更记录
     *
     * @param dto
     */
    public void updateProductInventoryChangeRecord(ProductInventoryChangeRecordDTO dto) {
        AssertUtils.notNull(dto.getId(), "仓库库存变更记录id不能为空");
        ProductInventoryChangeRecordPO po = new ProductInventoryChangeRecordPO();
        BeanUtils.copyProperties(dto, po);
        productInventoryChangeRecordPOMapper.updateProductInventoryChangeRecord(po);
    }

    /**
     * 根据规格id、货主id、二级货主id查询库存变更记录
     */
    public PageList<StoreChangeInfoDTO> findInventoryChangeBySpec(ProductStoreRecordSO querySO, PagerCondition pager) {
        AssertUtils.notNull(querySO, "请求不能为空");
        AssertUtils.notNull(querySO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(querySO.getProductSpecId(), "产品规格不能为空");
        this.checkProductStoreRecordSOTime(querySO);

        PageResult<ProductInventoryChangeRecordPO> poList = productInventoryChangeRecordPOMapper
            .findInventoryChangeBySpec(querySO, pager.getCurrentPage(), pager.getPageSize());

        PageList<StoreChangeInfoDTO> resultList = new PageList<>();

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ProductStoreChangeRecordDTO> dtoList =
                poList.stream().map(p -> convertToProductStoreChangeRecordDTO(p, null)).collect(Collectors.toList());
            resultList.setDataList(storeChangeInfoConvert.convert(dtoList));
        }
        resultList.setPager(poList.getPager());
        return resultList;
    }

    /**
     * 时间格式的校验
     */
    private void checkProductStoreRecordSOTime(ProductStoreRecordSO productStoreRecordSO) {
        if (StringUtils.isNotEmpty(productStoreRecordSO.getTimeS())
            && !productStoreRecordSO.getTimeS().matches(DATE_FORMAT)) {
            throw new BusinessValidateException("起始时间格式不正确");
        }
        if (StringUtils.isNotEmpty(productStoreRecordSO.getTimeE())
            && !productStoreRecordSO.getTimeE().matches(DATE_FORMAT)) {
            throw new BusinessValidateException("结束时间格式不正确");
        }
    }

    /**
     * 分页查询仓库库存变更记录
     */
    public PageList<FindStoreChangeInfoDTO> findStoreChangePage(FindStoreChangePageDTO findStoreChangePageDTO) {
        PageList<FindStoreChangeInfoDTO> resultList = new PageList<>();
        ProductStoreRecordSO productStoreRecordSO = new ProductStoreRecordSO();
        productStoreRecordSO.setStoreId(findStoreChangePageDTO.getProductStoreId());
        productStoreRecordSO.setTimeS(findStoreChangePageDTO.getStartTime());
        productStoreRecordSO.setTimeE(findStoreChangePageDTO.getEndTime());
        if (findStoreChangePageDTO.getOrderType() != null && findStoreChangePageDTO.getOrderType() >= 0) {
            productStoreRecordSO.setRecordType(findStoreChangePageDTO.getOrderType());
        }
        productStoreRecordSO.setStoreType(INVENTROY_STORE_TYPE_WAREHOUSE);
        if (productStoreRecordSO != null) {
            try {
                if ((com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(productStoreRecordSO.getTimeS())
                    && DateUtil.parse(productStoreRecordSO.getTimeS(), MIN_DATE_FORMAT)
                        .compareTo(DateUtil.parse(MIN_DATE, MIN_DATE_FORMAT)) < 0)) {
                    productStoreRecordSO.setTimeS(MIN_DATE);
                }
            } catch (Exception e) {
                LOG.error("时间转换异常", e);
            }
            try {
                if ((com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(productStoreRecordSO.getTimeE())
                    && DateUtil.parse(productStoreRecordSO.getTimeE(), MIN_DATE_FORMAT)
                        .compareTo(DateUtil.parse(MIN_DATE, MIN_DATE_FORMAT)) < 0)) {
                    productStoreRecordSO.setTimeE(MIN_DATE);
                }
            } catch (Exception e) {
                LOG.error("时间转换错误", e);
            }
        }

        PageList<ProductStoreChangeRecordDTO> pageList = findProductInventoryRecordList(productStoreRecordSO,
            new PagerCondition(findStoreChangePageDTO.getPageNum(), findStoreChangePageDTO.getPageSize()),
            findStoreChangePageDTO.getProductSkuId());
        if (pageList != null && pageList.getDataList() != null) {
            Map<String, String> mapOrderType = getDictionaryTypeDetailByCodeMap("StoreOrderType");
            Map<String, String> mapJiupiEventType = getDictionaryTypeDetailByCodeMap("JiupiEventType");
            // Map<String, String> mapOrderType = StoreOrderType.getStoreOrderType();
            /** Map<String, String> mapJiupiEventType = JiupiEventType.getJiupiEventType(); */
            resultList.setDataList(
                findStoreChangeInfoConvert.convert(pageList.getDataList(), mapOrderType, new HashMap<>(16)));
            resultList.setPager(pageList.getPager());
            return resultList;
        }
        resultList.setDataList(new ArrayList<FindStoreChangeInfoDTO>());
        resultList.setPager(new Pager());
        return resultList;
    }

    /**
     * 数据字典查询
     *
     * @param typeCode
     * @return
     */
    public Map<String, String> getDictionaryTypeDetailByCodeMap(String typeCode) {
        if (StringUtils.isEmpty(typeCode)) {
            throw new BusinessValidateException("词典分类为空！");
        }
        Map<String, String> map = new HashMap<>(16);
        DictionaryType dictionaryTypeResult = this.iDictionaryTypeService.getDictionaryTypeDetailByCode(typeCode);
        List<com.yijiupi.himalaya.supplychain.dubbop.dto.Dictionary> dictionaryList = dictionaryTypeResult.getItems();
        if (CollectionUtils.isNotEmpty(dictionaryList)) {
            for (com.yijiupi.himalaya.supplychain.dubbop.dto.Dictionary dictionaryItem : dictionaryList) {
                map.put(dictionaryItem.getCode(), dictionaryItem.getName());
            }
        }
        return map;
    }
}

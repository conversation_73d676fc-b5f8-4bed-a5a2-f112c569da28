<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.DeliveryStoreRecordMapper">
    <!--发货中的库存记录,发货时changeCount为负数,这里负负得正,发货中数量为正,其他情况相反-->
    <insert id="insertOrUpdateDeliveryRecordBatch">
        INSERT INTO deliverystorerecord (
        Id,ProductSku_Id,City_Id,
        Warehouse_Id,
        ProductSpecification_Id,OwnerType,
        Owner_Id,
        SecOwner_Id,
        Channel,DeliveryedCount_MinUnit,CreateTime,LastUpdateTime)
        VALUES
        <foreach collection="poList" item="item" index="index" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.productSkuId,jdbcType=BIGINT},#{item.cityId,jdbcType=INTEGER},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.productSpecificationId,jdbcType=BIGINT},#{item.ownerType,jdbcType=INTEGER},
            #{item.ownerId,jdbcType=BIGINT},
            #{item.secOwnerId,jdbcType=BIGINT},
            #{item.channel,jdbcType=INTEGER},#{item.changeCount,jdbcType=DECIMAL},NOW(),NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE DeliveryedCount_MinUnit = DeliveryedCount_MinUnit + VALUES (DeliveryedCount_MinUnit),
        LastUpdateTime =VALUES(LastUpdateTime)
    </insert>

    <!--批量更新已发货总数量-->
    <update id="updateDeliveryStoreRecordBatch">
        update deliverystorerecord
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="DeliveryedCount_MinUnit = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when Id = #{item.id}
                    then #{item.deliveryedCount}
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>
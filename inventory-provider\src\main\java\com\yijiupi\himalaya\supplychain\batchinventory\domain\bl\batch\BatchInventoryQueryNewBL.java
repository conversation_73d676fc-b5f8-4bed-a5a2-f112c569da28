package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchLocationInfoQueryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL.getProductStoreBatchOrderByProductionDateAndBatchDate;

/**
 * 批次库存相关
 *
 * <AUTHOR> 2018/3/28
 */
@Service
public class BatchInventoryQueryNewBL {

    @Resource
    private ProductSkuQueryHelper productSkuQueryHelper;

    // 查询批次库存,货位信息
    public Map<Long, List<BatchLocationInfoDTO>> getLocationInfoBySkuIdS(List<OrderItemDTO> orderItemDTOList, Integer subcategory) {
        Map<Long, List<BatchLocationInfoDTO>> map = new HashMap<>(16);
        Map<String, BatchLocationInfoQueryPO> queryMap = orderItemDTOList.stream().map(itemDTO -> {
            BatchLocationInfoQueryPO batchLocationInfoQueryPO = new BatchLocationInfoQueryPO();
            batchLocationInfoQueryPO.setProductSkuId(itemDTO.getProductSkuId());
            batchLocationInfoQueryPO.setWarehouseId(itemDTO.getWarehouseId());
            batchLocationInfoQueryPO.setChannel(itemDTO.getChannel());
            batchLocationInfoQueryPO.setSource(itemDTO.getSource());
            return batchLocationInfoQueryPO;
        }).collect(Collectors.toMap(BatchLocationInfoQueryPO::getGroupKey, Function.identity(), (old, newV) -> old));
        if (queryMap.isEmpty()) {
            // LOGGER.info(String.format("getLocationInfoBySkuIdS参数为空！%s", JSON.toJSONString(orderItemDTOList)));
            return map;
        }
        // //传入发货城市id,为了解决招商订单与长株潭合并skuId不一致问题.
        // //前面做了断言,orderItemDTOList一定不为null
        // Integer deliverCityId = warehouseCityBL.getCityIdByWarehouseId(orderItemDTOList.get(0).getWarehouseId());
        Integer warehouseId = orderItemDTOList.get(0).getWarehouseId();
        // 查询批次库存,货位信息
        List<BatchLocationInfoDTO> batchDTOBySkuList =
                productSkuQueryHelper.listLocationInfoBySkuList(queryMap.values(), warehouseId, subcategory);
        // LOGGER.info("获取货位结果：" + JSON.toJSONString(batchDTOBySkuList));
        batchDTOBySkuList.forEach(p -> {
            if (p.getAreaId() == null && p.getLocationId() != null && p.getLocationCategory() != null
                    && p.getLocationCategory().intValue() == 1) {
                p.setAreaId(p.getLocationId());
                p.setAreaName(p.getLocationName());
            }
        });
        batchDTOBySkuList = getProductStoreBatchOrderByProductionDateAndBatchDate(batchDTOBySkuList);
        // k(productSkuId)->v
        map = batchDTOBySkuList.stream().collect(Collectors.groupingBy(BatchLocationInfoDTO::getProductSkuId));
        return map;
    }

}

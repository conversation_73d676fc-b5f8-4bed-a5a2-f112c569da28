package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

import java.io.Serializable;

/**
 * 自定义批属性管理(字典)
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeDicPO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性类型
     */
    private Byte attributeType;
    /**
     * 属性可选值
     */
    private String attributeValue;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private Boolean enable;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 属性名称
     */
    public String getAttributeName() {
        return this.attributeName;
    }

    /**
     * 设置 属性名称
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    /**
     * 获取 属性类型
     */
    public Byte getAttributeType() {
        return this.attributeType;
    }

    /**
     * 设置 属性类型
     */
    public void setAttributeType(Byte attributeType) {
        this.attributeType = attributeType;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 状态
     */
    public Boolean getEnable() {
        return this.enable;
    }

    /**
     * 设置 状态
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 属性可选值
     */
    public String getAttributeValue() {
        return this.attributeValue;
    }

    /**
     * 设置 属性可选值
     */
    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }
}

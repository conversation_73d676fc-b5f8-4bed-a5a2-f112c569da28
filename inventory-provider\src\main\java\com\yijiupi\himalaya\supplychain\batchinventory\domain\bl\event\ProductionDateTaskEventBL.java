package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.framework.rabbit.delay.DelayMessageTemplate;
import com.yijiupi.himalaya.supplychain.batchinventory.config.BatchInventoryMQProperties;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductDateTaskMessage;

/**
 * 生产日期治理任务消息
 */
@Service
public class ProductionDateTaskEventBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(ProductionDateTaskEventBL.class);

    @Autowired
    private DelayMessageTemplate delayMessageTemplate;

    /**
     * 批量通知完成生产日期治理任务
     */
    public void notifyProductionDateTaskCompleteBatch(List<ProductDateTaskMessage> taskMessageList) {
        if (CollectionUtils.isEmpty(taskMessageList)) {
            return;
        }

        taskMessageList.stream().forEach(task -> {
            notifyProductionDateTaskComplete(task);
        });
    }

    /**
     * 通知完成生产日期治理任务
     */
    public void notifyProductionDateTaskComplete(ProductDateTaskMessage taskMessage) {
        if (Objects.isNull(taskMessage)) {
            return;
        }

        try {
            String payload = JSON.toJSONString(taskMessage);
            LOGGER.info("延迟发送完成生产日期治理任务消息：{}", payload);
            delayMessageTemplate.convertAndSend(BatchInventoryMQProperties.COMPLETE_PRODUCTION_DATE_TASK_EXCHANGE, null, taskMessage,
                Duration.ofSeconds(10));
        } catch (Exception ex) {
            LOGGER.error("延迟发送完成生产日期治理任务消息失败！", ex);
        }
    }
}

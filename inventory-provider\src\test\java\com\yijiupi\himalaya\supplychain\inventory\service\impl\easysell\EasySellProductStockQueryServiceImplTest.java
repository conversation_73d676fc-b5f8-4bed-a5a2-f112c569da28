package com.yijiupi.himalaya.supplychain.inventory.service.impl.easysell;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreReturnDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.easysell.IEasySellProductStockQueryService;

/**
 * @author: lidengfeng
 * @date 2018/9/19 19:22
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EasySellProductStockQueryServiceImplTest {

    @Reference
    private IEasySellProductStockQueryService iEasySellProductStockQueryService;

    @Test
    public void findProductStoreList() {

        ProductStoreQueryDTO productStoreQueryDTO = new ProductStoreQueryDTO();
        productStoreQueryDTO.setWarehouseId(7501);
        // productStoreQueryDTO.setOwnerId(146);
        PageList<ProductStoreReturnDTO> storeList =
            iEasySellProductStockQueryService.findProductStoreList(productStoreQueryDTO);
        System.out.println(JSON.toJSONString(storeList));
    }
}
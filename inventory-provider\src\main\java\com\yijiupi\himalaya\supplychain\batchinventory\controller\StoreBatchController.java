
package com.yijiupi.himalaya.supplychain.batchinventory.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryManageBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.PromotionStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryMoveDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.CCPAmountCheckDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.ROResult;

/**
 * 批次库存
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@RestController
public class StoreBatchController {
    private static final Logger LOG = LoggerFactory.getLogger(StoreBatchController.class);

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;

    /**
     * 批次库存自动转残次品
     */
    @RequestMapping(value = "/storeBatch/transferToDefective", method = RequestMethod.POST)
    public BaseResult storeBatchTransferToDefective(@RequestBody BatchInventoryMoveDTO moveDTO) {
        LOG.info("批次库存自动转残次品入参：{}", JSON.toJSONString(moveDTO));
        promotionStoreBatchBL.storeBatchTransferToDefective(moveDTO);
        return BaseResult.getSuccessResult();
    }

    /**
     * 计算残次品额度
     */
    @RequestMapping(value = "/storeBatch/calCcpPrice", method = RequestMethod.POST)
    public ROResult<List<DefectiveInventoryResultDTO>> calCcpPrice(@RequestBody CCPAmountCheckDTO moveDTO) {
        return ROResult.getResult(batchInventoryManageBL.calCcpPriceNew(moveDTO));
    }

    /**
     * 查询是否混合批次库存标识
     */
    @RequestMapping(value = "/storeBatch/listProductMixedBatchFlag", method = RequestMethod.POST)
    public BaseResult listProductMixedBatchFlag(@RequestBody PromotionStoreBatchQueryDTO queryDTO) {
        LOG.info("查询是否混合批次库存标识入参：{}", JSON.toJSONString(queryDTO));
        List<PromotionStoreBatchResultDTO> lstResult = promotionStoreBatchBL.listProductMixedBatchFlag(queryDTO);
        return new ROResult<>(lstResult);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseChargeConfigBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWareHouseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWarehouseQuery;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseChargeConfigService;

/**
 * 仓库标准费率
 * 
 * @author: lidengfeng
 * @date 2018/9/15 11:46
 */
@Service
public class WarehouseChargeConfigServiceImpl implements IWarehouseChargeConfigService {

    @Autowired
    private WarehouseChargeConfigBL warehouseChargeConfigBL;

    /**
     * 新增或修改仓库标准费率
     * 
     * @param dto
     * @return: void
     */
    @Override
    public void saveOrUpdateChargeConfig(WarehouseChargeConfigDTO dto) {
        warehouseChargeConfigBL.saveOrUpdateChargeConfig(dto);

    }

    /**
     * 仓库标准费率明细查询
     * 
     * @param warehouseId
     * @return
     */
    @Override
    public WarehouseChargeConfigDTO selectWarehouseChargeConfigById(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库标准费率仓库id不能为空");
        return warehouseChargeConfigBL.selectWarehouseChargeConfigById(warehouseId);
    }

    /**
     * 启用停用仓库标准费率
     * 
     * @param dto
     * @return
     */
    @Override
    public void updateChargeConfigStatus(WarehouseChargeConfigDTO dto) {
        AssertUtils.notNull(dto.getId(), "启用停用仓库标准费率id不能为空");
        AssertUtils.notNull(dto.getStatus(), "仓库标准费率状态不能为空");
        AssertUtils.notNull(dto.getLastupdateuser(), "最后修改人不能为空");
        warehouseChargeConfigBL.updateChargeConfigStatus(dto);
    }

    /**
     * 根据仓库id集合查询标准费率
     * 
     * @param list
     * @return
     */
    @Override
    public Map<Integer, WarehouseChargeConfigDTO> selectWarehouseChargeList(List<Integer> list) {
        Map<Integer, WarehouseChargeConfigDTO> map = warehouseChargeConfigBL.selectWarehouseChargeList(list);
        return map;
    }

    /**
     * 根据经销商id，服务商id，城市查询仓库信息及费用
     * 
     * @param agencyStockWarehouseQuery
     * @return
     */
    @Override
    public PageList<AgencyStockWareHouseDTO>
        findWarehouseChargeList(AgencyStockWarehouseQuery agencyStockWarehouseQuery) {
        AssertUtils.notNull(agencyStockWarehouseQuery.getCityId(), "城市id不能为空");
        return warehouseChargeConfigBL.findWarehouseChargeList(agencyStockWarehouseQuery);
    }

    /**
     * 根据仓库id,经销商Id,商品信息查询入库单费用
     * 
     * @param shopChargeDTO
     * @return
     */
    @Override
    public InStockCharge findInStockTotalCharge(ShopChargeDTO shopChargeDTO) {
        AssertUtils.notNull(shopChargeDTO.getShopId(), "经销商id不能为空");
        return warehouseChargeConfigBL.findInStockTotalCharge(shopChargeDTO);
    }

    /**
     * 根据仓库id,经销商Id,商品信息查询出库单或委托配送费用
     * 
     * @param shopChargeDTO
     * @return
     */
    @Override
    public OutStockCharge findOutStockTotalCharge(ShopChargeDTO shopChargeDTO) {
        AssertUtils.notNull(shopChargeDTO.getShopId(), "经销商id不能为空");
        return warehouseChargeConfigBL.findOutStockTotalCharge(shopChargeDTO);
    }

    @Override
    public PageList<WarehouseChooseReturnDTO> findWarehouseChooseList(WarehouseChooseDTO warehouseChooseDTO) {
        AssertUtils.notNull(warehouseChooseDTO.getShopId(), "经销商id不能为空");
        AssertUtils.notNull(warehouseChooseDTO.getWarehouseChooseType(), "仓库选择类型不能为空");
        return warehouseChargeConfigBL.findWarehouseChooseList(warehouseChooseDTO);
    }

    /**
     * 城市下服务商的仓库查询
     * 
     * @param cityWarehouseQuery
     * @return
     */
    @Override
    public PageList<WarehouseChargeConfigDTO> findWarehouseChargeDetailList(CityWarehouseQuery cityWarehouseQuery) {
        AssertUtils.notNull(cityWarehouseQuery.getFacilitatorId(), "服务商id不能为空");
        return warehouseChargeConfigBL.findWarehouseChargeDetailList(cityWarehouseQuery);
    }

    /**
     * 校验校验仓库是否可停用
     * 
     * @param productWarehouseDTO
     * @return
     */
    @Override
    public Boolean warehouseStock(ProductWarehouseDTO productWarehouseDTO) {
        AssertUtils.notNull(productWarehouseDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(productWarehouseDTO.getWarehouseId(), "经销商id不能为空");
        return warehouseChargeConfigBL.warehouseStock(productWarehouseDTO);
    }

    /**
     * 查询仓库信息及经销商信息 仓库管理仓库信息
     * 
     * @param cityWarehouseQuery
     * @return
     */
    @Override
    public WarehouseDealerDetailDTO getWarehouseDealerDetail(CityWarehouseQuery cityWarehouseQuery) {
        AssertUtils.notNull(cityWarehouseQuery.getWarehouseId(), "仓库id不能为空");
        return warehouseChargeConfigBL.getWarehouseDealerDetail(cityWarehouseQuery);
    }

    /**
     * 仓库管理 仓库库存
     * 
     * @param warehouseServiceStoreQuery
     * @return
     */
    @Override
    public PageList<WarehouseServiceStoreDTO>
        findWarehouseServiceStoreList(WarehouseServiceStoreQuery warehouseServiceStoreQuery) {

        AssertUtils.notNull(warehouseServiceStoreQuery.getFacilitatorId(), "服务商id不能为空");
        return warehouseChargeConfigBL.findWarehouseServiceStoreList(warehouseServiceStoreQuery);
    }

    /**
     * 查询仓库信息
     * 
     * @param cityWarehouseQuery
     * @return
     */
    @Override
    public WarehouseReturnDTO findByWarehouseId(CityWarehouseQuery cityWarehouseQuery) {
        AssertUtils.notNull(cityWarehouseQuery.getWarehouseId(), "服务商id不能为空");
        return warehouseChargeConfigBL.findByWarehouseId(cityWarehouseQuery);
    }

    /**
     * 根据仓库id集合查询仓库信息
     * 
     * @param warehouseIdList
     * @return
     */
    @Override
    public PageList<WarehouseReturnDTO> findByWarehouseList(List<Integer> warehouseIdList) {
        return warehouseChargeConfigBL.findByWarehouseList(warehouseIdList);
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import org.springframework.stereotype.Component;

import com.github.pagehelper.StringUtil;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChargeConfigDTO;

/**
 * 仓库标准费率配置转换
 * 
 * @author: lidengfeng
 * @date 2018/9/15 10:36
 */
@Component
public class WarehouseChargeConfigConvert extends ConvertUtils<WarehouseChargeConfigPO, WarehouseChargeConfigDTO> {

    /**
     * 仓库标准费率配置PO转DTO
     * 
     * @param po
     * @return
     */
    @Override
    public WarehouseChargeConfigDTO convert(WarehouseChargeConfigPO po) {
        WarehouseChargeConfigDTO dto = new WarehouseChargeConfigDTO();
        dto.setId(String.valueOf(po.getId()));
        dto.setWarehouseId(po.getWarehouseId());
        dto.setUnloadingcharge(po.getUnloadingcharge());
        dto.setSortingcharge(po.getSortingcharge());
        dto.setCustodiancharge(po.getCustodiancharge());
        dto.setLoadingcharge(po.getLoadingcharge());
        dto.setTransportcharge(po.getTransportcharge());
        dto.setLandingcharge(po.getLandingcharge());
        dto.setStatus(po.getStatus());
        dto.setCreateuser(po.getCreateuser());
        dto.setCreatetime(po.getCreatetime());
        dto.setLastupdateuser(po.getLastupdateuser());
        dto.setLastupdatetime(po.getLastupdatetime());
        return dto;
    }

    /**
     * 仓库标准费率配置DTO转PO
     * 
     * @param dto
     * @return
     */
    @Override
    public WarehouseChargeConfigPO reverseConvert(WarehouseChargeConfigDTO dto) {
        WarehouseChargeConfigPO po = new WarehouseChargeConfigPO();
        if (StringUtil.isNotEmpty(dto.getId())) {
            po.setId(Long.valueOf(dto.getId()));
        }
        po.setWarehouseId(dto.getWarehouseId());
        po.setUnloadingcharge(dto.getUnloadingcharge());
        po.setSortingcharge(dto.getSortingcharge());
        po.setCustodiancharge(dto.getCustodiancharge());
        po.setLoadingcharge(dto.getLoadingcharge());
        po.setTransportcharge(dto.getTransportcharge());
        po.setLandingcharge(dto.getLandingcharge());
        po.setStatus(dto.getStatus());
        po.setCreateuser(dto.getCreateuser());
        po.setCreatetime(dto.getCreatetime());
        po.setLastupdateuser(dto.getLastupdateuser());
        po.setLastupdatetime(dto.getLastupdatetime());

        return po;
    }
}

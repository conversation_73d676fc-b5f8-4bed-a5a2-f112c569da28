package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryProductDateTaskBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductDateTaskMessage;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateTaskDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryProductDateTaskService;

/**
 * 批次库存过期产品任务
 *
 * <AUTHOR>
 * @date 2025/2/8
 */
@Service(timeout = 60000)
public class BatchInventoryProductDateTaskImpl implements IBatchInventoryProductDateTaskService {

    @Autowired
    private BatchInventoryProductDateTaskBL batchInventoryProductDateTaskBL;

    @Override
    public Map<String, Boolean> checkCompleteProductDateTask(List<ProductionDateTaskDTO> taskDTOS) {
        return batchInventoryProductDateTaskBL.checkCompleteProductDateTask(taskDTOS);
    }

    @Override
    public void completeProductDateTaskNotify(List<ProductDateTaskMessage> taskMessageList) {
        batchInventoryProductDateTaskBL.completeProductDateTaskNotify(taskMessageList);
    }
}

/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.math.BigDecimal;
import java.util.Date;

public class ErpStorePO {

    private String id;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 库存所属类型(酒批(0), 合作商(1))
     */
    private Integer ownerType;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * ERP显示库存数量
     */
    private BigDecimal erpDisplayCount;
    /**
     * 未审核销售数量
     */
    private BigDecimal saleCount;
    /**
     * 未审核退货数量
     */
    private BigDecimal saleReturnCount;
    /**
     * 未审核采购数量
     */
    private BigDecimal buyCount;
    /**
     * 未审核采购退货数量
     */
    private BigDecimal buyReturnCount;
    /**
     * ERP实际库存数量
     */
    private BigDecimal erpRealCount;
    /**
     * 创建时间
     */
    private Date createTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public BigDecimal getErpDisplayCount() {
        return erpDisplayCount;
    }

    public void setErpDisplayCount(BigDecimal erpDisplayCount) {
        this.erpDisplayCount = erpDisplayCount;
    }

    public BigDecimal getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(BigDecimal saleCount) {
        this.saleCount = saleCount;
    }

    public BigDecimal getSaleReturnCount() {
        return saleReturnCount;
    }

    public void setSaleReturnCount(BigDecimal saleReturnCount) {
        this.saleReturnCount = saleReturnCount;
    }

    public BigDecimal getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(BigDecimal buyCount) {
        this.buyCount = buyCount;
    }

    public BigDecimal getBuyReturnCount() {
        return buyReturnCount;
    }

    public void setBuyReturnCount(BigDecimal buyReturnCount) {
        this.buyReturnCount = buyReturnCount;
    }

    public BigDecimal getErpRealCount() {
        return erpRealCount;
    }

    public void setErpRealCount(BigDecimal erpRealCount) {
        this.erpRealCount = erpRealCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
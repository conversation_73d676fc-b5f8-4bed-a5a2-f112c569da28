package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.InventorySyncRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordSO;

/**
 * 库存对账结果记录
 */
public interface InventorySyncRecordMapper {

    /**
     * 批量新增库存对账记录
     *
     * @return
     */
    int insertList(@Param("list") List<InventorySyncRecordPO> record);

    /**
     * 获取库存矫正记录列表
     *
     * @return
     */
    PageResult<InventorySyncRecordPO> listInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO);

    /**
     * 更新库存矫正记录
     */
    void updateInventorySyncRecord(InventorySyncRecordPO inventorySyncRecordPO);

    /**
     * 批量更新库存矫正记录的校正状态
     */
    void batchUpdateRecordState(@Param("list") List<Long> recordIds, @Param("state") Byte state);

    /**
     * 删除当天已经同步过的记录
     */
    int deleteTodaySyncRecordByCityId(@Param("orgId") Integer orgId);
}
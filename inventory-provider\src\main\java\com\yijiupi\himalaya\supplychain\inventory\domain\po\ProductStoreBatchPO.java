package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存批次库存
 * 
 * <AUTHOR> 2018/1/25
 */
public class ProductStoreBatchPO {
    /**
     * 主键id
     */
    private String id;
    /**
     * store主键
     */
    private String productStoreId;
    /**
     * 库存小数量
     */
    private BigDecimal totalCount;
    /**
     * 批次入库时间
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期日期
     */
    private Date expireTime;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * '货区或货位类型：0:货位，1:货区',
     */
    private Integer locationCategory;
    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    private Integer subcategory;
    /**
     * 是否更新批次库存
     */
    private boolean needUpdateBatchInventory = true;
    /**
     * 创建人
     */
    private Integer createUserId;

    /**
     * 库存属性（0：默认，1：自动转入）
     */
    private Byte batchProperty;

    /**
     * 业务类型（0：默认，1：生产日期治理任务）
     */
    private Byte businessType;

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取 主键id
     *
     * @return id 主键id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     *
     * @param id 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 store主键
     *
     * @return productStoreId store主键
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 store主键
     *
     * @param productStoreId store主键
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 库存小数量
     *
     * @return totalCount 库存小数量
     */
    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 库存小数量
     *
     * @param totalCount 库存小数量
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 批次入库时间
     *
     * @return batchTime 批次入库时间
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次入库时间
     *
     * @param batchTime 批次入库时间
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取
     *
     * @return needUpdateBatchInventory
     */
    public boolean isNeedUpdateBatchInventory() {
        return this.needUpdateBatchInventory;
    }

    /**
     * 设置
     *
     * @param needUpdateBatchInventory
     */
    public void setNeedUpdateBatchInventory(boolean needUpdateBatchInventory) {
        this.needUpdateBatchInventory = needUpdateBatchInventory;
    }

    /**
     * 获取 生产日期
     *
     * @return productionDate 生产日期
     */
    public Date getProductionDate() {
        return this.productionDate;
    }

    /**
     * 设置 生产日期
     *
     * @param productionDate 生产日期
     */
    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    /**
     * 获取 过期日期
     *
     * @return expireTime 过期日期
     */
    public Date getExpireTime() {
        return this.expireTime;
    }

    /**
     * 设置 过期日期
     *
     * @param expireTime 过期日期
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 获取 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 '货区或货位类型：0:货位，1:货区',
     */
    public Integer getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 '货区或货位类型：0:货位，1:货区',
     */
    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public Integer getSubcategory() {
        return this.subcategory;
    }

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 创建人
     */
    public Integer getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStoreRecordPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStorePO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="TotalCount_MinUnit" property="totalCountMinUnit" jdbcType="INTEGER"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="OwnerType" property="ownerType" jdbcType="TINYINT"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="Channel" property="channel" jdbcType="TINYINT"/>
    </resultMap>

    <select id="findProductStoreList" resultType="com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreReturnDTO">
        select
        distinct
        ps.ProductSpecification_Id as productSpecificationId,
        ps.TotalCount_MinUnit as totalCountMinUnit
        from
        productstore ps
        where Channel=0
        <if test="warehouseId != null">
            and ps.Warehouse_Id =#{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="shopId != null">
            and ps.Owner_Id =#{shopId,jdbcType=BIGINT}
        </if>


    </select>

    <select id="findShopIdList" parameterType="java.util.List" resultType="java.lang.Long">
        select
        ps.Owner_Id
        from
        productstore ps
        where ps.Owner_Id is not null
        <if test="list != null">
            and ps.Warehouse_Id in
            (
            <foreach collection="list" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>

        group by ps.Owner_Id

    </select>


</mapper>
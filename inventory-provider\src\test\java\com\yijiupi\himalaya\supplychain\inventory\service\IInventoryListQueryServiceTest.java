package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.dto.product.StoreDTOBySupplierOp;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.InventoryListQueryServiceImpl;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.WarehouseInventoryQueryServiceImpl;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseStoreBySupplierOpSO;

/**
 * <AUTHOR> 2017/12/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class IInventoryListQueryServiceTest {
    @Autowired
    private InventoryListQueryServiceImpl inventoryListQueryServicel;
    @Autowired
    private WarehouseInventoryQueryServiceImpl warehouseInventoryQueryService;

    @Test
    public void getProductInventoryPOBySpecId() {
        // ProductInventoryPO sellInventoryPOBySpecId = inventoryKeyBL
        // .getSellInventoryPOBySpecId(9991, 12, 999,0);
    }

    /**
     * 查询仓库库存列表(轻加盟调用) 1/22通过
     */
    @Test
    public void listWarehouseInventory() {
        WarehouseInventoryQueryDTO dto = new WarehouseInventoryQueryDTO();
        // dto.setProductSkuId(10000000034392L);
        dto.setWarehouseId(99916);
        dto.setCityId(999);
        // dto.setProductName("宣酒十年窖40度460ml");
        PagerCondition pager = new PagerCondition();
        pager.setCurrentPage(2);
        pager.setPageSize(30);
        // dto.setChannel(0);
        PageList<WarehouseInventoryDTO> list = inventoryListQueryServicel.listWarehouseInventory(dto, pager);
    }

    /**
     * 查询城市SKU库存. 1/22通过
     */
    @Test
    public void listProductSkuInventoyByCity() {
        // List<ProductSkuInventoryCountDTO> productSkuInventoryCountDTOS = inventoryListQueryServicel.
        // listProductSkuInventoyByCity(100, 0,0,1);
    }

    /**
     * 用户范围库存报表 1/22通过
     */
    @Test
    public void findStoreReportPageByAuth() {
        StockReportSO so = new StockReportSO();
        // so.setProductSkuName("茅台白金酱酒红酱A1/53度500ml");
        ArrayList<Integer> list = new ArrayList<>();
        list.add(9991);
        so.setWarehouseIds(list);
        // so.setChannel(0);
        // so.setSecOwnerId(1);
        // so.setSource(0);
        PagerCondition pager = new PagerCondition();
        pager.setCurrentPage(1);
        pager.setPageSize(100);
        PageList<InventoryReportDTO> storeReportPageByAuth =
            inventoryListQueryServicel.findStoreReportPageByAuth(so, pager);
    }

    /**
     * 调拨查询 1/22通过
     */
    @Test
    public void findProductWarehouseStoreListForAllocation() {
        WarehouseStoreBySupplierOpSO so = new WarehouseStoreBySupplierOpSO();
        so.setProductInfoSpecId(3L);
        so.setCityId(100);
        // so.setSecOwnerId(1);
        so.setSource(1);
        so.setChannel(0);
        so.setSaleModes(Collections.singletonList(0));
        PagerCondition pager = new PagerCondition();
        pager.setCurrentPage(1);
        pager.setPageSize(20);
        PageList<StoreDTOBySupplierOp> list =
            inventoryListQueryServicel.findProductWarehouseStoreListForAllocation(so, pager);
    }

    /**
     * 产品信息汇总 库存信息汇总(供应商门户) 1/22通过
     */
    @Test
    public void getProductStoreForSupplierOp() {
        WarehouseStoreBySupplierOpSO so = new WarehouseStoreBySupplierOpSO();
        so.setProductInfoSpecId(3L);
        // so.setCityId(100);
        // so.setSecOwnerId(1);
        so.setSource(0);
        so.setChannel(0);

        // StoreDTOBySupplierOp op = inventoryListQueryServicel.getProductStoreForSupplierOp(so);
    }

    /**
     * 查询配送中数量 1/30 通过
     */
    @Test
    public void getWarehouseInventoryList() {
        // Map<Long, Integer> map = warehouseInventoryQueryService.
        // getdeliveryCountList(Collections.singletonList(99900050761713L), 999135, 0);
    }

    @Test
    public void findStoreReportPageByAuthTest() {
        StockReportSO so = new StockReportSO();
        so.setWarehouseIds(Arrays.asList(9981));
        // so.setProductSkuName("麻辣鱼01测试");
        // so.setStartStockAge(100L);
        // so.setEndStockAge(300L);
        PagerCondition pager = new PagerCondition();
        pager.setCurrentPage(1);
        pager.setPageSize(20);
        PageList<InventoryReportDTO> list = inventoryListQueryServicel.findStoreReportPageInfoByAuth(so, pager);
        System.out.println("list : " + JSON.toJSONString(list));
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * <AUTHOR> 2017/12/4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryBatchInventoryProductSkuMapperTest {

    @Autowired
    private InventoryProductSkuMapper inventoryProductSkuMapper;

    // 根据skuId查询productSku表 1//18通过
    // @Test
    // public void getProductSkuBySkuId() {
    // ProductSkuPO productSkuBySkuId = productSkuMapper.getProductSkuBySkuId(10000000003376L);
    // }
    //
    // //招商订单，根据SKUId，查找发货城市SKUID 1/11通过
    // @Test
    // public void getAnotherCityProductSkuId() {
    // Long skuId = productSkuMapper.getAnotherCityProductSkuId(10000000003376L, 100);
    // }

    // 根据skuId查询productSku表(批量) 1/18通过
    // @Test
    // public void getProductSkuListByIds(){
    // ArrayList<Long> list = new ArrayList<>();
    // list.add(10000000003376L);
    // list.add(10000000004564L);
    // List<ProductSkuPO> productSkuListByIds = productSkuMapper.getProductSkuListByIds(list);
    // }

    // 批量查询 需要加上城市过滤,不同 城市下的ProductSpecification_Id有可能一样 1/11通过
    // @Test
    // public void getInventoryProductSkuMap() {
    // HashSet<Long> set = new HashSet<>();
    // set.add(99900050780124L);
    // set.add(99900050779542L);
    // List<Map<Long, Integer>> inventoryProductSkuMap = productSkuMapper.getInventoryProductSkuMap(set,0);
    // System.out.println(inventoryProductSkuMap);
    // HashMap<Long, Integer> map = new HashMap<>(16);
    // inventoryProductSkuMap.forEach(n->{
    // map.put(Long.parseLong(n.get("productSkuId") + ""), n.get("sumInventory"));
    // });
    // }
}

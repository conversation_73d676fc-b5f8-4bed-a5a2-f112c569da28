package com.yijiupi.himalaya.supplychain.inventory.util;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.base.utils.AssertUtils;

/**
 * Created by 余明 on 2018-03-24.
 */
@Component
public class UuidGenerator {

    private static NoteNoGenerator batchNoGenerator;

    @Autowired
    private NoteNoGenerator batchNoGenerator_Private;
    private static int sequence = 100000;
    private static final int MAX_NUM = 999999;

    public synchronized static Long getUUidInt() {
        UuidGenerator.sequence++;
        // 产生一个时间戳
        long now = System.currentTimeMillis();
        String uuid = now + "" + sequence;
        if (sequence >= MAX_NUM) {
            sequence = 100000;
        }
        return Long.parseLong(uuid);
    }

    @PostConstruct
    public void beforeInit() {
        batchNoGenerator = batchNoGenerator_Private;
    }

    public static String generator(Integer warehouseId, String type) {
        AssertUtils.notNull(warehouseId, "仓库Id不能为空！");
        return batchNoGenerator.generator(warehouseId, type);
    }

    public static String generatorId() {
        return batchNoGenerator.generatorId();
    }

    public static Long generatorLongId() {
        return Long.valueOf(batchNoGenerator.generatorId());
    }

}

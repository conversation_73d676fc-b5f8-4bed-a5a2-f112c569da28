package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.distributedlock.annotation.DistributeLock;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductStoreBatchChangeRecordMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductDateSyncPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderQueryConditionDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.ProductionDateSyncDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommManageService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 单据生产日期计算及落库BL
 *
 * <AUTHOR>
 */
@Service
public class ProductDateSyncBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(ProductDateSyncBL.class);

    @Autowired
    private ProductStoreBatchChangeRecordMapper productStoreBatchChangeRecordMapper;

    @Reference
    private IOutStockCommManageService outStockCommManageService;

    @Reference
    private IOutStockCommQueryService outStockCommQueryService;

    /**
     * 根据订单，同步出库单生产日期
     */
    @DistributeLock(conditions = "#orderKey", sleepMills = 3000, key = "syncProductDateByOrder",
            lockType = DistributeLock.LockType.WAITLOCK, expireMills = 30000)
    @Transactional(rollbackFor = Exception.class)
    @RabbitListener(queues = "${mq.supplychain.batchinventory.orderproductiondate}")
    public void syncProductDateByOrder(Message message) {
        String json;
        try {
            json = new String(message.getBody(), "UTF-8");
            String messageId = message.getMessageProperties().getMessageId();
            LOGGER.info("同步订单生产日期>>>【mq.supplychain.batchinventory.orderproductiondate】[{}]{}", messageId, json);
            ProductDateSyncPO order = JSON.parseObject(json, ProductDateSyncPO.class);
            List<ProductionDateSyncDTO> lstProductionDate = calcProductionDateByOrder(order.getOrderId(), order.getOrderNo(), order.getWarehouseId());
            LOGGER.info("计算生产日期结果：{}", JSON.toJSONString(lstProductionDate));
            // 2.2 汇总生产日期数据并落库
            if (CollectionUtils.isNotEmpty(lstProductionDate)) {
                outStockCommManageService.updateBatchForProductionDate(lstProductionDate);
            }
        } catch (Exception e) {
            LOGGER.info("同步出库单生产日期失败，错误信息：{}", e.getMessage(), e);
        }
    }

    /**
     * 按OrderId或者按单号+仓库计算订单出库时的生产日期
     *
     * @param orderId
     * @param orderNo
     * @param warehouseId
     * @return
     */
    private List<ProductionDateSyncDTO> calcProductionDateByOrder(Long orderId, String orderNo, Integer warehouseId) {
        List<ProductionDateSyncDTO> lstRecord = new ArrayList<>();
        OutStockOrderDTO orderDTO = null;
        //1、查询单据及对应的批次库存记录
        if (orderId != null) {
            orderDTO = outStockCommQueryService.getOutStockOrderById(orderId);
        } else if (StringUtils.isNotEmpty(orderNo) && warehouseId != null) {
            OutStockOrderQueryConditionDTO conditionDTO = new OutStockOrderQueryConditionDTO();
            conditionDTO.setWarehouseId(warehouseId);
            conditionDTO.setRefOrderNoList(Collections.singletonList(orderNo));
            List<OutStockOrderDTO> lstOrder = outStockCommQueryService.findOutStockOrderAllInfoByCondition(conditionDTO);
            if (CollectionUtils.isNotEmpty(lstOrder)) {
                orderDTO = lstOrder.get(0);
            }
        }
        if (orderDTO == null || CollectionUtils.isEmpty(orderDTO.getOutStockOrderItemDTOS())) {
            return lstRecord;
        }
        List<OutStockOrderItemDTO> lstItem = orderDTO.getOutStockOrderItemDTOS();

        //过滤订单项中生产日期不未空的数据，不为空说明已经处理过
        lstItem.removeIf(p -> p.getProductionDate() != null);
        // 1.1 没有符合条件需要计算的订单项，不处理
        if (CollectionUtils.isEmpty(lstItem)) {
            return lstRecord;
        }

        ProductStoreBatchChangeRecordQueryDTO changeRecordQueryDTO = new ProductStoreBatchChangeRecordQueryDTO();
        changeRecordQueryDTO.setOrderNo(orderDTO.getRefOrderNo());
        changeRecordQueryDTO.setWarehouseId(orderDTO.getWarehouseId());
        List<ProductStoreBatchChangeRecordDTO> lstProductStoreChangeRecord = productStoreBatchChangeRecordMapper.selectProductStoreBatchChangeRecords(changeRecordQueryDTO);

        LOGGER.info("订单项：{}，批次库存：{}", JSON.toJSONString(lstItem), JSON.toJSONString(lstProductStoreChangeRecord));

        // 1.2 没有的说明批次库存处理失败，不处理
        if (CollectionUtils.isEmpty(lstProductStoreChangeRecord)) {
            return lstRecord;
        }
        // 1.3 过滤 只处理扣库存的 排除入库单
        List<ProductStoreBatchChangeRecordDTO> lstChangeRecord = lstProductStoreChangeRecord.stream()
                .filter(p -> p.getChangeCount().compareTo(BigDecimal.ZERO) < 0
                        && p.getProductionDate() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstChangeRecord)) {
            return lstRecord;
        }

        LOGGER.info("符合条件待生产日期的批次库存：{}", JSON.toJSONString(lstChangeRecord));

        //2、根据批次库存记录及订单明细，计算订单项生产日期
        // 2.1 根据批次库存记录分配到Item
        lstItem.forEach(item -> {
            //取不为空且有效的最小生产日期
            Optional<Date> minDate = lstChangeRecord.stream()
                    .filter(p -> Objects.equals(String.format("%s-%s", item.getProductSpecificationId(), item.getOwnerId())
                            , String.format("%s-%s", p.getProductSpecificationId(), p.getOwnerId())))
                    .map(ProductStoreBatchChangeRecordDTO::getProductionDate)
                    .min(Date::compareTo);

            if (minDate.isPresent()) {
                ProductionDateSyncDTO dateRecordPO = new ProductionDateSyncDTO();
                dateRecordPO.setProductionDate(minDate.get());
                dateRecordPO.setOrderItemId(item.getId());
                lstRecord.add(dateRecordPO);
            }
        });
        return lstRecord;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskDTO;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.productSku.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateTodoTaskDTO;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.batchinventory.enums.ProductionDateAuditState.*;

/**
 * <AUTHOR>
 * @since 2025-03-14 15:54
 **/
@Service
public class ProductionDateAuditConverter {

    @Reference
    private IAdminUserService adminUserService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Reference
    private IProductCategoryService productCategoryService;

    @Resource
    private BatchInventoryProductSkuMapper batchInventoryProductSkuMapper;

    public ProductionDateAuditPO toPO(ProductionDateAuditDTO dto) {
        Integer userId = dto.getUserId();
        AdminUser user = adminUserService.getAdminUserWithoutAuthById(userId);
        AssertUtils.notNull(user, "用户不存在");
        ProductionDateAuditPO po = new ProductionDateAuditPO();
        po.setId(UUIDGenerator.getUUID(ProductionDateAuditPO.class.getName()));
        po.setWarehouseId(dto.getWarehouseId());
        po.setSkuId(dto.getSkuId());
        po.setProductName(dto.getProductName());
        po.setState(AUDITING.getValue());
        po.setProductionDate(DateUtils.getDateByDateString(dto.getProductionDate()));
        po.setUnitTotalCount(dto.getUnitTotalCount());
        po.setRefTodoTaskNo(dto.getRefTodoTaskNo());
        po.setRefTodoTaskId(dto.getRefTodoTaskId());
        po.setApplyUser(user.getTrueName());
        po.setMobileNo(user.getMobileNo());
        po.setAuditRemark(dto.getAuditRemark());
        po.setCreateTime(new Date());
        po.setCreateUser(userId);
        po.setLastUpdateTime(new Date());
        po.setLastUpdateUser(userId);
        return po;
    }

    public ProductionDateAuditPO toUpdatePO(ProductionDateAuditDTO dto) {
        ProductionDateAuditPO po = new ProductionDateAuditPO();
        po.setId(dto.getId());
        po.setState((Boolean.TRUE.equals(dto.getApplyPassed()) ? AUDIT_PASSED : AUDIT_REJECTED).getValue());
        po.setAuditRemark(dto.getAuditRemark());
        po.setLastUpdateUser(dto.getUserId());
        if (dto.getBusinessType() != null) {
            po.setBusinessType(dto.getBusinessType());
        }
        return po;
    }

    public List<ProductionDateAuditDTO> toDTO(List<ProductionDateAuditPO> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        Map<Long, ProductSkuPO> skuMap = querySkuMap(pos);
        Map<Integer, Warehouse> warehouseMap = queryWarehouseMap(pos);
        List<ProductCategoryDTO> periodBySkuIds = productCategoryService.getCategoryPeriodBySkuIds(new ArrayList<>(skuMap.keySet()));
        return pos.stream().map(it -> toDTO(it, skuMap, warehouseMap.get(it.getWarehouseId()), periodBySkuIds))
                .collect(Collectors.toList());
    }

    public ProductionDateAuditDTO toDTO(ProductionDateAuditPO po, Map<Long, ProductSkuPO> skuMap, Warehouse warehouse, List<ProductCategoryDTO> periodBySkuIds) {
        ProductSkuPO sku = skuMap.get(po.getSkuId());
        ProductionDateAuditDTO dto = new ProductionDateAuditDTO();
        dto.setId(po.getId());
        dto.setWarehouseId(po.getWarehouseId());
        dto.setWarehouseName(warehouse.getName());
        dto.setProductName(po.getProductName());
        dto.setSkuId(po.getSkuId());
        dto.setPackageSpecName(sku.getSpecificationName());
        dto.setUnitTotalCount(po.getUnitTotalCount());
        dto.setShelfLife(sku.getMonthOfShelfLife());
        dto.setShelfLifeUnit(sku.getShelfLifeUnit());
        dto.setProductionDate(DateUtils.getDateFormat(po.getProductionDate()));
        BigDecimal[] divide = po.getUnitTotalCount().divideAndRemainder(sku.getPackageQuantity());
        String packageCount = divide[0].stripTrailingZeros().toPlainString();
        String unitCount = divide[1].stripTrailingZeros().toPlainString();
        dto.setBatchInventoryCount(String.format("%s大件%s小件", packageCount, unitCount));
        dto.setRefTodoTaskNo(po.getRefTodoTaskNo());
        dto.setRefTodoTaskId(po.getRefTodoTaskId());
        dto.setApplyUser(po.getApplyUser());
        dto.setApplyUserMobile(po.getMobileNo());
        dto.setState(po.getState());
        dto.setAuditRemark(po.getAuditRemark());
        dto.setUserId(null);
        dto.setBusinessType(po.getBusinessType());

        // 增加产品类目配置的几个日期
        ProductCategoryDTO productCategoryDTO = periodBySkuIds
                .stream().filter(p -> Objects.equals(p.getProductSkuId(), po.getSkuId()))
                .findAny().orElse(null);
        if (productCategoryDTO != null) {
            dto.setAttentionPeriod(productCategoryDTO.getAttentionPeriod());
            dto.setNearExpiryPeriod(productCategoryDTO.getNearExpiryPeriod());
            dto.setForbidSalesPeriod(productCategoryDTO.getForbidSalesPeriod());
        }

        return dto;
    }

    public ProductionDateTodoTaskDTO toDTO(@Nonnull TodoTaskDTO todoTask) {
        ProductionDateTodoTaskDTO dto = new ProductionDateTodoTaskDTO();
        dto.setId(todoTask.getId());
        dto.setTaskNo(todoTask.getTaskNo());
        dto.setTaskTypeId(todoTask.getTaskTypeId());
        dto.setTaskTypeName(todoTask.getTaskTypeName());
        dto.setTaskStatus(todoTask.getTaskStatus());
        dto.setTaskRank(todoTask.getTaskRank());
        dto.setIsDeleted(todoTask.getIsDeleted());
        dto.setBusinessNo(todoTask.getBusinessNo());
        dto.setTaskDetail(todoTask.getTaskDetail());
        dto.setBeginTime(todoTask.getBeginTime());
        dto.setOverdueTime(todoTask.getOverdueTime());
        dto.setFinishUser(todoTask.getFinishUser());
        dto.setFinishUserName(todoTask.getFinishUserName());
        dto.setFinishTime(todoTask.getFinishTime());
        dto.setCreateTime(todoTask.getCreateTime());
        dto.setLastUpdateTime(todoTask.getLastUpdateTime());
        dto.setIsOverdue(todoTask.getIsOverdue());
        dto.setTaskProperty(todoTask.getTaskProperty());
        dto.setDirector(todoTask.getDirector());
        return dto;
    }

    private Map<Long, ProductSkuPO> querySkuMap(List<ProductionDateAuditPO> pos) {
        Set<Long> skuIds = pos.stream().map(ProductionDateAuditPO::getSkuId).collect(Collectors.toSet());
        return batchInventoryProductSkuMapper.getProductSkuListByIds(skuIds).stream()
                .collect(Collectors.toMap(ProductSkuPO::getProductSkuId, Function.identity()));
    }

    private Map<Integer, Warehouse> queryWarehouseMap(List<ProductionDateAuditPO> pos) {
        List<Integer> warehouseIds = pos.stream().map(ProductionDateAuditPO::getWarehouseId).distinct()
                .collect(Collectors.toList());
        List<Warehouse> warehouses = warehouseQueryService.listWarehouseByIds(warehouseIds);
        if (CollectionUtils.isEmpty(warehouses)) {
            return Collections.emptyMap();
        }
        return warehouses.stream().collect(Collectors.toMap(Warehouse::getId, Function.identity()));
    }

}

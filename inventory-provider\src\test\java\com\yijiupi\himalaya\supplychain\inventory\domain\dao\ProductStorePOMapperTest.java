package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * <AUTHOR> 2018/1/11
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class ProductStorePOMapperTest {

    @Autowired
    private ProductStorePOMapper productStorePOMapper;

    // 查询库存 1/18通过
    // @Test
    // public void findProductWarehouseStoreList() {
    // ProductWarehouseStoreSO so = new ProductWarehouseStoreSO();
    // so.setProductName("测试斯嘉迪香槟2");
    // so.setSecOwnerId(1);
    // PageResult<ProductStorePO> list = productStorePOMapper.findProductWarehouseStoreList(so, null, 1, 20);
    // }

    // 调拨系统库存信息汇总 1/18通过
    // @Test
    // public void getProductStoreForSupplierOp() {
    // WarehouseStoreBySupplierOpSO so = new WarehouseStoreBySupplierOpSO();
    // so.setProductInfoSpecId(1);
    //// so.setSecOwnerId(1);
    // StoreDTOBySupplierOp productStoreForSupplierOp = productStorePOMapper.getProductStoreForSupplierOp(so);
    // }

    // 通过产品信息规格id和cityid,查询库存. 1/18通过
    // @Test
    // public void findProductSpecCityListStore() {
    // ProductSpecCityListStoreSO so = new ProductSpecCityListStoreSO();
    // so.setProductSpecId(1);
    // so.setCityIds(Collections.singletonList(999));
    //// so.setSecOwnerId(1);
    // List<ProductSpecCityStoreDTO> productSpecCityListStore = productStorePOMapper.findProductSpecCityListStore(so);
    // }

    // 获取产品SKU信息对应仓库库存(条件productSkuId,warehouseId,channel)//1/18通过
    // @Test
    // public void findProductWarehouseListStore(){
    // List<ProductWarehouseStoreDTO> productWarehouseListStore = productStorePOMapper.
    // findProductWarehouseListStore(Collections.singletonList(9991), Collections.singletonList(99900050779542L),
    // Collections.singletonList(0));
    // }

    // 调拨系统产品查询库存用的 1/18通过
    // @Test
    // public void findProductWarehouseStoreListForAllocation() {
    // WarehouseStoreBySupplierOpSO so = new WarehouseStoreBySupplierOpSO();
    // so.setProductInfoSpecId(1);
    // so.setSource(0);
    //// so.setSecOwnerId(1);
    // PageResult<StoreDTOBySupplierOp> list = productStorePOMapper.findProductWarehouseStoreListForAllocation(so, 1,
    // 20);
    // }
}

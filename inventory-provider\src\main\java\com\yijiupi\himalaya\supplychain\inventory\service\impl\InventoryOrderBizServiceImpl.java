package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ErpInventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryErpBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.async.InStockTransferOrderAsyncBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.BatchOutStockConfirmDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ZiTiConfirmDeliverDTO;
import com.yijiupi.himalaya.supplychain.inventory.listener.ErpSyncListener;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryOrderBizService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStoreOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommQueryService;
import com.yijiupi.himalaya.supplychain.pushorder.enums.pushCapabilityTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 库存订单业务服务. Created by Lifeng on 2017/7/13.
 */
@Service(timeout = 300000)
public class InventoryOrderBizServiceImpl implements IInventoryOrderBizService {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryOrderBizServiceImpl.class);

    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;
    @Autowired
    private InventoryErpBL inventoryErpBL;
    @Autowired
    private ErpSyncListener erpSyncListener;

    @Reference
    private IProductSkuService iProductSkuService;

    @Autowired
    private ErpInventoryOrderBizBL erpInventoryOrderBizBL;

    @Autowired
    private InStockTransferOrderAsyncBL inStockTransferOrderAsyncBL;

    @Reference
    private IOutStockCommQueryService outStockCommQueryService;

    /**
     * 订单缺货标记.（返销售库存）
     */
    @Override
    public void outOfStockRemark(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        AssertUtils.notEmpty(deliveryOrders, "配送单ID列表为空");
        String syncData = JSON.toJSONString(deliveryOrders);
        LOG.info("收到订单缺货标记请求：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");
            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        inventoryOrderBizBL.outOfStockRemark(deliveryOrders);
    }

    /**
     * 扣交易系统销售库存
     */
    @Override
    public void returnSaleInventoryByOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        AssertUtils.notEmpty(deliveryOrders, "配送单ID列表为空");
        String syncData = JSON.toJSONString(deliveryOrders);
        LOG.info("收到扣交易系统销售库存请求：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            // AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");
            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        inventoryOrderBizBL.returnSaleInventoryByOrder(deliveryOrders);
    }

    /**
     * 配送单批量发货
     *
     * @return
     */
    @Override
    public List<InventoryDeliveryJiupiOrder> batchDeliver(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        AssertUtils.notEmpty(deliveryOrders, "配送单ID列表为空");
        String syncData = JSON.toJSONString(deliveryOrders);
        LOG.info("收到配送单批量发货请求：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");

            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        return inventoryOrderBizBL.orderDeliveryNew(deliveryOrders, true, true);
    }

    /**
     * 配送单批量发货
     */
    @Override
    public List<InventoryDeliveryJiupiOrder>
    batchDeliverNoValidateStore(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        AssertUtils.notEmpty(deliveryOrders, "配送单ID列表为空");
        String syncData = JSON.toJSONString(deliveryOrders);
        LOG.info("收到配送单批量发货-不校验扣除请求：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");

            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        return inventoryOrderBizBL.orderDeliveryNew(deliveryOrders, false, true);
    }

    /**
     * 内配单批量发货
     *
     * @return
     */
    @Override
    public List<InventoryDeliveryJiupiOrder>
    internalDistributionOrderDelivery(InternalDistributionOrderDeliveryDTO deliveryOrders) {
        AssertUtils.notNull(deliveryOrders, "内配单信息不能为空");
        AssertUtils.notNull(deliveryOrders.getDeliveryCity(), "发货城市不能为空");
        AssertUtils.notNull(deliveryOrders.getDeliveryWarehouseId(), "发货仓库不能为空");
        AssertUtils.notNull(deliveryOrders.getReceiptCity(), "收货城市不能为空");
        AssertUtils.notNull(deliveryOrders.getReceiptWarehouseId(), "收货仓库不能为空");
        AssertUtils.notEmpty(deliveryOrders.getDeliveryJiupiOrders(), "内配单列表不能为空");
        String syncData = JSON.toJSONString(deliveryOrders);
        LOG.info("收到内配单批量发货请求：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders.getDeliveryJiupiOrders()) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");
            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        return inventoryOrderBizBL.internalDistributionOrderDelivery(deliveryOrders);
    }

    /**
     * 自提出库
     */
    @Override
    public void ziTiDeliver(List<Long> businessIdList, List<Long> productSourceCodeIdList, Integer userId) {
        AssertUtils.notEmpty(businessIdList, "配送单ID列表为空");
        inventoryOrderBizBL.ziTiDeliver(businessIdList, productSourceCodeIdList, userId);
    }

    /**
     * 召回->发货批次
     */
    @Override
    public void recallDeliver(RecallDeliverDTO recallDeliverDTO) {
        LOG.info("召回->发货批次参数: {}", JSON.toJSONString(recallDeliverDTO));
        AssertUtils.notNull(recallDeliverDTO, "发货批次召回参数为空");
        AssertUtils.notEmpty(recallDeliverDTO.getRefOrderNoList(), "配送单编号列表为空");
        AssertUtils.notNull(recallDeliverDTO.getWarehouseId(), "仓库ID为空");
        inventoryOrderBizBL.recallDeliver(recallDeliverDTO);
    }

    /**
     * 财务收款确认入库完成
     */
    @Override
    public void inStockComplete(InStockCompleteDTO completeDTO) {
        return;
    }

    /**
     * 订单确认入库操作
     */
    @Override
    public void affirmInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        LOG.info("非重构订单确认入库操作 : {}", JSON.toJSONString(inventoryInStockOrderBatchDTO));
        AssertUtils.notNull(inventoryInStockOrderBatchDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(inventoryInStockOrderBatchDTO.getWarehouseId(), "仓库id不能为空");
        List<InventoryOrderDTO> orderList = inventoryInStockOrderBatchDTO.getOrderList();
        for (InventoryOrderDTO inventoryOrderDTO : orderList) {
            AssertUtils.notNull(inventoryOrderDTO.getFromCityId(), "下单城市id不能为空");
            AssertUtils.notNull(inventoryOrderDTO.getOrderCreateTime(), "下单时间不能为空");
            for (InventoryOrderItemDTO inventoryOrderItemDTO : inventoryOrderDTO.getItems()) {
                AssertUtils.notNull(inventoryOrderItemDTO.getSpecQuantity(), "包装规格系数不能为空");
                AssertUtils.notNull(inventoryOrderItemDTO.getSkuId(), "skuId不能为空");
                AssertUtils.notNull(inventoryOrderItemDTO.getProductSpecification_Id(), "规格Id不能为空");
            }
        }
        inventoryOrderBizBL.affirmInStock(inventoryInStockOrderBatchDTO);
    }

    /**
     * 出入库客户端直接加库存
     */
    @Override
    public void processInventoryByErp(List<StockOrderStoreDTO> stockOrderStoreDTOS) {
        erpInventoryOrderBizBL.processInventoryByErp(stockOrderStoreDTOS);
    }

    /**
     * 出入库客户端直接加库存（异常重试）
     *
     * @param json
     */
    @Override
    public void processInventoryByErpJson(String json) {
        List<StockOrderStoreDTO> stockOrderStoreDTOS = JSON.parseArray(json, StockOrderStoreDTO.class);
        LOG.info("出入库客户端直接加库存异常重试参数：{}", JSON.toJSONString(stockOrderStoreDTOS));
        genProductSpecificationIdBySkuId(stockOrderStoreDTOS);
        processInventoryByErp(stockOrderStoreDTOS);
    }

    /**
     * ERP同步消息监听（异常重试）
     *
     * @param json
     */
    @Override
    public void processErpMsgJson(String json) {
        ErpStoreOrderDTO storeOrder = JSON.parseObject(json, ErpStoreOrderDTO.class);
        LOG.info("ERP同步消息监听异常重试参数：{}", JSON.toJSONString(storeOrder));
        erpSyncListener.processErpMsg(storeOrder);
    }

    @Override
    public void handleStockCheckNoDiff(String orderId) {
        inventoryErpBL.processStockOrder(orderId, null, false, false, null, null);
    }

    /**
     * 内配单自动入库
     */
    @Override
    public void internalDeliveryInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        LOG.info("内配单入库操作 : {}", JSON.toJSONString(inventoryInStockOrderBatchDTO));
        AssertUtils.notEmpty(inventoryInStockOrderBatchDTO.getOrderList(), "订单数据不能为空");
        inventoryInStockOrderBatchDTO.getOrderList().forEach(order -> {
            AssertUtils.notEmpty(order.getItems(), "订单项数据不能为空");
        });
        inventoryOrderBizBL.internalDeliveryInStock(inventoryInStockOrderBatchDTO);
    }

    /**
     * 内配单自动入库(整车转运)
     */
    @Override
    public void vehicleTransshipmentInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        LOG.info("整车转运入库操作 : {}", JSON.toJSONString(inventoryInStockOrderBatchDTO));
        AssertUtils.notEmpty(inventoryInStockOrderBatchDTO.getOrderList(), "订单数据不能为空");
        inventoryInStockOrderBatchDTO.getOrderList().forEach(order -> {
            AssertUtils.notEmpty(order.getItems(), "订单项数据不能为空");
        });
        inventoryOrderBizBL.vehicleTransshipmentInStock(inventoryInStockOrderBatchDTO);
    }

    /**
     * 恢复出库单
     */
    @Override
    public void resetOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        inventoryOrderBizBL.resetOrder(deliveryOrders);
    }

    /**
     * 按单号生成入库单
     */
    @Override
    public void createInStockOrderByOrderNo(InternalDistributionOrderDeliveryDTO deliveryOrders) {
        LOG.info("按单号生成入库单:{}", JSON.toJSONString(deliveryOrders));
        AssertUtils.notNull(deliveryOrders, "单据信息不能为空");
        AssertUtils.notNull(deliveryOrders.getDeliveryCity(), "发货城市不能为空");
        AssertUtils.notNull(deliveryOrders.getReceiptCity(), "收货城市不能为空");
        AssertUtils.notNull(deliveryOrders.getReceiptWarehouseId(), "收货仓库不能为空");
        AssertUtils.notEmpty(deliveryOrders.getDeliveryJiupiOrders(), "订单信息不能为空");
        List<String> orderNOs = deliveryOrders.getDeliveryJiupiOrders().stream()
                .map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
        List<InStockOrderDTO> inStockOrderDTOList =
                inventoryOrderBizBL.createInStockOrderByOrderNo(deliveryOrders, orderNOs, false);
        inventoryOrderBizBL.saveInStockOrderList(inStockOrderDTOList);
    }

    /**
     * 校验发货库存数量
     */
    @Override
    @Deprecated
    public List<DeliveryProductInventoryDTO>
    validateOrderDeliveryProductInventory(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        LOG.info("发货校验库存数量参数:{}", JSON.toJSONString(deliveryOrders));
        return inventoryOrderBizBL.validateOrderDeliveryProductInventory(deliveryOrders,
                OrderDeliveryOpType.ORDER_DELIVER_MESSAGE_TYPE);
    }

    /**
     * 校验发货库存数量
     */
    @Override
    public List<DeliveryProductInventoryDTO> validateOrderDeliveryProductInventory(
            List<InventoryDeliveryJiupiOrder> deliveryOrders, String orderDeliveryOpType) {
        LOG.info("发货校验库存数量参数:{},orderDeliveryOpType:{}", JSON.toJSONString(deliveryOrders), orderDeliveryOpType);
        return inventoryOrderBizBL.validateOrderDeliveryProductInventory(deliveryOrders, orderDeliveryOpType);
    }

    /**
     * 内配单-发货入库信息异常重试信息
     */
    @Override
    public void retriesBatchAddInStockTransferOrder(String message) {
        LOG.info("内配单-发货入库信息异常重试信息:{}", message);
        if (StringUtils.isBlank(message)) {
            return;
        }
        InternalDistributionOrderDeliveryDTO deliveryOrders =
                JSON.parseObject(message, InternalDistributionOrderDeliveryDTO.class);
        inStockTransferOrderAsyncBL.batchAddInStockTransferOrder(deliveryOrders, null);
    }

    /**
     * 订单发货
     */
    @Override
    public void orderDelivery(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        LOG.info("orderDelivery - 收到配送单批量发货请求：{}", JSON.toJSONString(deliveryOrders));
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getWarehouseId(), "发货仓库Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromWarehouseId(), "下单仓库Id不能为空");
            AssertUtils.notNull(deliveryOrder.getOutStockMode(), "出库方式不能为空");

            AssertUtils.notEmpty(deliveryOrder.getItems(), "订单明细不能为空");
            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notEmpty(item.getSecOwnerDetail(), "订单项详情数据不能为空");
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
                AssertUtils.notNull(item.getProductSkuId(), "产品skuId不能为空");
            }
        }
        inventoryOrderBizBL.orderDelivery(deliveryOrders);
    }

    /**
     * SCM2-15145 虚仓实配确认出库 （处理库存及单据状态）
     *
     * @param confirmOutStockDTO
     */
    @Override
    public void confirmOutStockByOrder(ConfirmOutStockDTO confirmOutStockDTO) {
        throw new BusinessException("NOT SUPPORT!");
        // OutStockOrderQueryConditionDTO queryConditionDTO = new OutStockOrderQueryConditionDTO();
        // queryConditionDTO.setRefOrderNoList(confirmOutStockDTO.getOrderNos());
        // queryConditionDTO.setWarehouseId(confirmOutStockDTO.getWarehouseId());
        // LOG.info("虚仓实配确认出库-findOutStockOrderAllInfoByCondition请求参数：{}", JSON.toJSONString(queryConditionDTO));
        // List<OutStockOrderDTO> outStockOrderDTOS =
        // outStockCommQueryService.findOutStockOrderAllInfoByCondition(queryConditionDTO);
        // if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
        // throw new BusinessException(
        // "虚仓实配确认出库找不到对应的出库单，请联系技术支持！Id：" + JSON.toJSONString(confirmOutStockDTO.getOrderNos()));
        // }
        // outStockOrderDTOS = outStockOrderDTOS.stream()
        // .filter(outStockOrder -> !Objects.equals(outStockOrder.getState(), OutStockOrderStateEnum.已出库.getType()))
        // .collect(Collectors.toList());
        // List<OutStockOrderDTO> unPickingOrders = outStockOrderDTOS.stream()
        // .filter(order -> !Objects.equals(order.getState(), OutStockOrderStateEnum.已拣货.getType())
        // && !Objects.equals(order.getState(), OutStockOrderStateEnum.已出库.getType()))
        // .collect(Collectors.toList());
        //
        // if (!CollectionUtils.isEmpty(unPickingOrders)) {
        // String orderNos =
        // unPickingOrders.stream().map(OutStockOrderDTO::getRefOrderNo).collect(Collectors.joining(","));
        // throw new BusinessValidateException("以下订单未完成拣货任务:" + orderNos);
        // }
        //
        // List<InventoryDeliveryJiupiOrder> deliveryOrders =
        // OrderDTOConvert.outStockOrderDTOS2InventoryDeliveryJiupiOrders(outStockOrderDTOS);
        // if (CollectionUtils.isEmpty(deliveryOrders)) {
        // LOG.info("WMS中找不到对应的出库单，可能已出库！Id：" + JSON.toJSONString(confirmOutStockDTO.getOrderNos()));
        // } else {
        // LOG.info("虚仓实配确认出库-orderDeliveryNew请求参数：{}", JSON.toJSONString(deliveryOrders));
        // inventoryOrderBizBL.orderDeliveryNew(deliveryOrders, true, false);
        // }

        // RealWarehouseInOutStockDTO realWarehouseInOutStockDTO = new RealWarehouseInOutStockDTO();
        // BeanUtils.copyProperties(confirmOutStockDTO, realWarehouseInOutStockDTO);
        // if (realWarehouseInOutStockDTO != null) {
        // LOG.info("虚仓实配确认出库调用OMS-realWarehouseOutStock请求参数：{}", JSON.toJSONString(realWarehouseInOutStockDTO));
        // inoutWarehouseService.realWarehouseOutStock(realWarehouseInOutStockDTO);
        // }
    }

    @Override
    public void financeCashInventoryChange(InStockCompleteDTO completeDTO) {
        LOG.info("InventoryOrderBizServiceImpl.financeCashInventoryChange 确认收款直接变更库存信息 completeDTO={}",
                JSON.toJSONString(completeDTO));
        AssertUtils.notNull(completeDTO, "参数不能为空");
        AssertUtils.notNull(completeDTO.getOrgId(), "城市ID不能为空");
        AssertUtils.notNull(completeDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(completeDTO.getRefOrderNoList(), "订单编号不能为空");
        inventoryOrderBizBL.financeCashInventoryChange(completeDTO);
    }

    /**
     * 根据SkuId获取ProductSpecificationId
     *
     * @param stockOrderStoreList
     */
    public void genProductSpecificationIdBySkuId(List<StockOrderStoreDTO> stockOrderStoreList) {
        if (CollectionUtils.isEmpty(stockOrderStoreList)) {
            return;
        }
        List<StockOrderStoreItemDTO> itemList =
                stockOrderStoreList.stream().filter(s -> s != null && !CollectionUtils.isEmpty(s.getProductSkuList()))
                        .flatMap(s -> s.getProductSkuList().stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<Long> skuIds = itemList.stream().filter(d -> d.getProductSkuId() != null)
                .map(d -> Long.valueOf(d.getProductSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        Map<Long, ProductSkuInfoReturnDTO> productSkuInfoMap = iProductSkuService.getProductInfoBySkuId(skuIds);
        if (CollectionUtils.isEmpty(productSkuInfoMap)) {
            return;
        }
        itemList.stream().filter(d -> d.getProductSkuId() != null).forEach(d -> {
            ProductSkuInfoReturnDTO skuInfoReturnDTO = productSkuInfoMap.get(Long.valueOf(d.getProductSkuId()));
            if (skuInfoReturnDTO != null) {
                if (d.getProductSpecificationId() == null) {
                    d.setProductSpecificationId(skuInfoReturnDTO.getProductSpecificationId());
                }
                if (d.getOwnerId() == null) {
                    d.setOwnerId(skuInfoReturnDTO.getCompanyId());
                }
                if (d.getSecOwnerId() == null) {
                    d.setSecOwnerId(skuInfoReturnDTO.getSecOwnerId());
                }
            }
        });
    }

    /**
     * 直接出、入库处理
     */
    @Override
    public void directProcessingOrderSync(List<OrderDTO> orders) {
        LOG.info("直接出入库订单库存处理，入参 : {}", JSON.toJSONString(orders));
        AssertUtils.notEmpty(orders, "订单数据不能为空");

        List<OrderDTO> outStockOrders = orders.stream()
                .filter(order -> pushCapabilityTypeEnum.直接出库.getType() == order.getCapabilityType()
                        || pushCapabilityTypeEnum.奖券差异出库.getType() == order.getCapabilityType()
                        || pushCapabilityTypeEnum.奖券出库.getType() == order.getCapabilityType()
                        || (order.getRegistrationPromotion() != null && order.getRegistrationPromotion()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(outStockOrders)) {
            inventoryOrderBizBL.directCompleteOutStockOrder(outStockOrders);
        }

        List<OrderDTO> returnOrders = orders.stream()
                .filter(order -> pushCapabilityTypeEnum.直接入库.getType() == order.getCapabilityType()
                        || pushCapabilityTypeEnum.调拨强制完成.getType() == order.getCapabilityType()
                        || pushCapabilityTypeEnum.调拨强制取消.getType() == order.getCapabilityType()
                        || pushCapabilityTypeEnum.奖券入库单.getType() == order.getCapabilityType())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(returnOrders)) {
            inventoryOrderBizBL.directCompleteInStockOrder(returnOrders);
        }
    }

    @Override
    public void batchConfirmOutStockByOrder(BatchOutStockConfirmDTO confirmOutStockDTO) {
        AssertUtils.notNull(confirmOutStockDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(confirmOutStockDTO.getFetchTaskList(), "取货任务列表不能为空");

        throw new BusinessException("NOT SUPPORT!");
        // List<String> refOrderNoList = confirmOutStockDTO.getFetchTaskList().stream()
        // .filter(elem -> !CollectionUtils.isEmpty(elem.getRefOrderNoList()))
        // .flatMap(elem -> elem.getRefOrderNoList().stream()).filter(Objects::nonNull).distinct()
        // .collect(Collectors.toList());
        // AssertUtils.notEmpty(refOrderNoList, "出库单不能为空");
        //
        // // 1.查询出库单
        // OutStockOrderQueryConditionDTO queryConditionDTO = new OutStockOrderQueryConditionDTO();
        // queryConditionDTO.setRefOrderNoList(refOrderNoList);
        // queryConditionDTO.setWarehouseId(confirmOutStockDTO.getWarehouseId());
        // LOG.info("[虚仓实配批量出库]查找出库单请求：{}", JSON.toJSONString(queryConditionDTO));
        // List<OutStockOrderDTO> outStockOrderDTOS =
        // outStockCommQueryService.findOutStockOrderAllInfoByCondition(queryConditionDTO);
        // if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
        // throw new BusinessException("虚仓实配确认出库找不到对应的出库单，请联系技术支持！出库单号：" + refOrderNoList);
        // }
        // outStockOrderDTOS = outStockOrderDTOS.stream()
        // .filter(outStockOrder -> !Objects.equals(outStockOrder.getState(), OutStockOrderStateEnum.已出库.getType()))
        // .collect(Collectors.toList());
        //
        // // 2.未拣货校验
        // if (outStockOrderDTOS.stream()
        // .anyMatch(order -> !Objects.equals(order.getState(), OutStockOrderStateEnum.已拣货.getType())
        // && !Objects.equals(order.getState(), OutStockOrderStateEnum.已出库.getType()))) {
        // String unPickingOrderNos = outStockOrderDTOS.stream()
        // .filter(order -> !Objects.equals(order.getState(), OutStockOrderStateEnum.已拣货.getType())
        // && !Objects.equals(order.getState(), OutStockOrderStateEnum.已出库.getType()))
        // .map(OutStockOrderDTO::getRefOrderNo).collect(Collectors.joining(","));
        // throw new BusinessValidateException("以下订单未完成拣货任务:" + unPickingOrderNos);
        // }
        //
        // // 3.出库
        // List<InventoryDeliveryJiupiOrder> deliveryOrders =
        // OrderDTOConvert.outStockOrderDTOS2InventoryDeliveryJiupiOrders(outStockOrderDTOS);
        // if (CollectionUtils.isEmpty(deliveryOrders)) {
        // LOG.info("[虚仓实配批量出库]WMS中找不到对应的出库单，可能已出库！出库单号：{}", refOrderNoList);
        // } else {
        // LOG.info("[虚仓实配批量出库]确认出库请求：{}", JSON.toJSONString(deliveryOrders));
        // inventoryOrderBizBL.orderDeliveryNew(deliveryOrders, true, false);
        // }

        // 4.调oms批量出库接口
        // BatchConfirmOutStockDTO omsOutStockDTO = convertOmsOutStockDTO(confirmOutStockDTO);
        // LOG.info("虚仓实配确认出库调用OMS请求：{}", JSON.toJSONString(omsOutStockDTO));
        // inoutWarehouseService.realWarehouseBatchOutStock(omsOutStockDTO);
    }

    // private BatchConfirmOutStockDTO convertOmsOutStockDTO(BatchOutStockConfirmDTO confirmOutStockDTO) {
    // BatchConfirmOutStockDTO omsOutStockDTO = new BatchConfirmOutStockDTO();
    // omsOutStockDTO.setCityId(confirmOutStockDTO.getCityId());
    // omsOutStockDTO.setWarehouseId(confirmOutStockDTO.getWarehouseId());
    // if (!CollectionUtils.isEmpty(confirmOutStockDTO.getFetchTaskList())) {
    // List<FetchTaskInfoDTO> fetchTaskInfos = new ArrayList<>();
    // confirmOutStockDTO.getFetchTaskList().forEach(task -> {
    // FetchTaskInfoDTO taskInfoDTO = new FetchTaskInfoDTO();
    // taskInfoDTO.setFetchTaskId(task.getFetchTaskId());
    // taskInfoDTO.setFetchTaskType(task.getFetchTaskType());
    // taskInfoDTO.setRefOrderNoList(task.getRefOrderNoList());
    // fetchTaskInfos.add(taskInfoDTO);
    // });
    // omsOutStockDTO.setFetchTaskList(fetchTaskInfos);
    // }
    // omsOutStockDTO.setUpdateUserId(confirmOutStockDTO.getUpdateUserId());
    // omsOutStockDTO.setUpdateUserName(confirmOutStockDTO.getUpdateUserName());
    // return omsOutStockDTO;
    // }

    /**
     * 快递直发配送单批量发货
     *
     * @return
     */
    @Override
    public List<InventoryDeliveryJiupiOrder>
    batchDeliverByExpressOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        AssertUtils.notEmpty(deliveryOrders, "配送单ID列表为空");
        String syncData = JSON.toJSONString(deliveryOrders);
        LOG.info("收到配送单批量发货请求：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");

            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        return inventoryOrderBizBL.orderDeliveryNew(deliveryOrders, true, false);
    }

    /**
     * 经销商订单出入库直接处理库存
     */
    @Override
    public void directCompleteInStockOrderByDealer(List<OrderDTO> orders) {
        LOG.info("经销商订单出入库直接处理库存，入参 : {}", JSON.toJSONString(orders));
        AssertUtils.notEmpty(orders, "订单数据不能为空");

        List<OrderDTO> outStockOrders = orders.stream()
                .filter(order -> Objects.equals(pushCapabilityTypeEnum.经销商发货.getType(), order.getCapabilityType())
                        || (order.getRegistrationPromotion() != null && order.getRegistrationPromotion()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(outStockOrders)) {
            inventoryOrderBizBL.directCompleteOutStockOrder(outStockOrders);
        }

        List<OrderDTO> deliveryOrders = orders.stream()
                .filter(order -> Objects.equals(pushCapabilityTypeEnum.经销商退货.getType(), order.getCapabilityType())
                        || Objects.equals(pushCapabilityTypeEnum.经销商配送失败.getType(), order.getCapabilityType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deliveryOrders)) {
            String messageType = OrderDeliveryOpType.ORDER_DIRECTLY_TYPE;
            if (Objects.equals(pushCapabilityTypeEnum.经销商退货.getType(), orders.get(0).getCapabilityType())) {
                messageType = OrderDeliveryOpType.DELIVERY_RETURN_TYPE;
            } else if (Objects.equals(pushCapabilityTypeEnum.经销商配送失败.getType(), orders.get(0).getCapabilityType())) {
                messageType = OrderDeliveryOpType.DELIVERY_FAILED_TYPE;
            }
            inventoryOrderBizBL.directCompleteInStockOrderByDealer(deliveryOrders, messageType);
        }
    }

    /**
     * 自提出库
     *
     * @param dto
     */
    @Override
    public void ziTiConfirmDeliver(ZiTiConfirmDeliverDTO dto) {
        AssertUtils.notEmpty(dto.getBusinessIdList(), "配送单ID列表为空");
        inventoryOrderBizBL.ziTiConfirmDeliver(dto);
    }
}

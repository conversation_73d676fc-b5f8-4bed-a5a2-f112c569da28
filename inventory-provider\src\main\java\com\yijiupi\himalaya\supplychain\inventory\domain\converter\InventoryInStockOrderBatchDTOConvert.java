package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemExtContentDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.StockOrderRelatedItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryInStockOrderBatchDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryOrderItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryOrderRelatedItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.InStockOrderBatchDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemRelatedDTO;

/**
 * <AUTHOR> 2018/5/3
 */
public class InventoryInStockOrderBatchDTOConvert {

    public static InStockOrderBatchDTO inventoryInStockOrderBatchDTO2InStockOrderBatchDTO(
        InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        if (inventoryInStockOrderBatchDTO == null) {
            return null;
        }
        InStockOrderBatchDTO inStockOrderBatchDTO = new InStockOrderBatchDTO();
        inStockOrderBatchDTO.setTaskNumber(inventoryInStockOrderBatchDTO.getTaskNumber());
        inStockOrderBatchDTO.setStockInTime(inventoryInStockOrderBatchDTO.getStockInTime());
        inStockOrderBatchDTO.setTurnoutTime(inventoryInStockOrderBatchDTO.getTurnoutTime());
        inStockOrderBatchDTO.setCarId(inventoryInStockOrderBatchDTO.getCarId());
        inStockOrderBatchDTO.setCarName(inventoryInStockOrderBatchDTO.getCarName());
        inStockOrderBatchDTO.setDeliveryUserId(inventoryInStockOrderBatchDTO.getDeliveryUserId());
        inStockOrderBatchDTO.setDeliveryUserName(inventoryInStockOrderBatchDTO.getDeliveryUserName());
        inStockOrderBatchDTO.setStevedoreUserId(inventoryInStockOrderBatchDTO.getStevedoreUserId());
        inStockOrderBatchDTO.setStevedoreUserName(inventoryInStockOrderBatchDTO.getStevedoreUserName());
        inStockOrderBatchDTO.setReturnPackageCount(inventoryInStockOrderBatchDTO.getReturnPackageCount());
        inStockOrderBatchDTO.setReturnUnitCount(inventoryInStockOrderBatchDTO.getReturnUnitCount());
        inStockOrderBatchDTO.setOrgId(inventoryInStockOrderBatchDTO.getOrgId());
        inStockOrderBatchDTO.setWarehouseId(inventoryInStockOrderBatchDTO.getWarehouseId());
        // SCM-8476车次ID
        inStockOrderBatchDTO.setTrainNumberId(inventoryInStockOrderBatchDTO.getTrainNumberId());
        inStockOrderBatchDTO
            .setOrderList(inventoryOrderDTOList2OrderDTOList(inventoryInStockOrderBatchDTO.getOrderList()));
        return inStockOrderBatchDTO;
    }

    private static List<OrderDTO> inventoryOrderDTOList2OrderDTOList(List<InventoryOrderDTO> inventoryOrderDTOList) {
        ArrayList<OrderDTO> orderDTOS = new ArrayList<>();
        for (InventoryOrderDTO inventoryOrderDTO : inventoryOrderDTOList) {
            orderDTOS.add(inventoryOrderDTO2OrderDTO(inventoryOrderDTO));
        }
        return orderDTOS;
    }

    public static OrderDTO inventoryOrderDTO2OrderDTO(InventoryOrderDTO inventoryOrderDTO) {
        if (inventoryOrderDTO == null) {
            return null;
        }
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setItems(inventoryOrderItemDTOS2OrderItemDTOS(inventoryOrderDTO.getItems()));
        orderDTO.setId(
            inventoryOrderDTO.getOmsOrderId() != null ? inventoryOrderDTO.getOmsOrderId() : inventoryOrderDTO.getId());
        orderDTO.setRefOrderNo(inventoryOrderDTO.getRefOrderNo());
        orderDTO.setOrgId(inventoryOrderDTO.getOrgId());
        orderDTO.setOrderType(inventoryOrderDTO.getOrderType());
        orderDTO.setSkuCount(inventoryOrderDTO.getSkuCount());
        orderDTO.setWarehouseId(inventoryOrderDTO.getWarehouseId());
        orderDTO.setUserName(inventoryOrderDTO.getUserName());
        orderDTO.setShopName(inventoryOrderDTO.getShopName());
        orderDTO.setMobileNo(inventoryOrderDTO.getMobileNo());
        orderDTO.setDetailAddress(inventoryOrderDTO.getDetailAddress());
        orderDTO.setAddressId(inventoryOrderDTO.getAddressId());
        orderDTO.setProvince(inventoryOrderDTO.getProvince());
        orderDTO.setCity(inventoryOrderDTO.getCity());
        orderDTO.setCounty(inventoryOrderDTO.getCounty());
        orderDTO.setStreet(inventoryOrderDTO.getStreet());
        orderDTO.setParterId(inventoryOrderDTO.getParterId());
        orderDTO.setParterName(inventoryOrderDTO.getParterName());
        orderDTO.setOrderCreateTime(inventoryOrderDTO.getOrderCreateTime());
        orderDTO.setDeliveryOrderType(inventoryOrderDTO.getDeliveryOrderType());
        orderDTO.setPayableAmount(inventoryOrderDTO.getPayableAmount());
        orderDTO.setOrderAmount(inventoryOrderDTO.getOrderAmount());
        orderDTO.setReturnAmount(inventoryOrderDTO.getReturnAmount());
        orderDTO.setBusinessType(inventoryOrderDTO.getBusinessType());
        orderDTO.setAreaId(inventoryOrderDTO.getAreaId());
        orderDTO.setAreaName(inventoryOrderDTO.getAreaName());
        orderDTO.setRouteId(inventoryOrderDTO.getRouteId());
        orderDTO.setRouteName(inventoryOrderDTO.getRouteName());
        orderDTO.setRouteSequence(inventoryOrderDTO.getRouteSequence());
        orderDTO.setFromCityId(inventoryOrderDTO.getFromCityId());
        orderDTO.setOrderState(inventoryOrderDTO.getOrderState());
        orderDTO.setDeliveryMode(inventoryOrderDTO.getDeliveryMode());
        orderDTO.setReceiptState(inventoryOrderDTO.getReceiptState());
        orderDTO.setDeliveryMarkState(inventoryOrderDTO.getDeliveryMarkState());
        return orderDTO;
    }

    private static List<OrderItemDTO>
        inventoryOrderItemDTOS2OrderItemDTOS(List<InventoryOrderItemDTO> inventoryOrderItemDTOS) {
        ArrayList<OrderItemDTO> orderItemDTOS = new ArrayList<>();
        for (InventoryOrderItemDTO inventoryOrderItemDTO : inventoryOrderItemDTOS) {
            orderItemDTOS.add(inventoryOrderItemDTO2OrderItemDTO(inventoryOrderItemDTO));
        }
        return orderItemDTOS;
    }

    public static OrderItemDTO inventoryOrderItemDTO2OrderItemDTO(InventoryOrderItemDTO inventoryOrderItemDTO) {
        if (inventoryOrderItemDTO == null) {
            return null;
        }
        OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setId(inventoryOrderItemDTO.getOmsOrderItemId() != null ? inventoryOrderItemDTO.getOmsOrderItemId()
            : inventoryOrderItemDTO.getId());
        orderItemDTO.setOrgId(inventoryOrderItemDTO.getOrgId());
        orderItemDTO.setOrderId(inventoryOrderItemDTO.getOrderId());
        orderItemDTO.setProductName(inventoryOrderItemDTO.getProductName());
        orderItemDTO.setSkuId(inventoryOrderItemDTO.getSkuId());
        orderItemDTO.setProductBrand(inventoryOrderItemDTO.getProductBrand());
        orderItemDTO.setCategoryName(inventoryOrderItemDTO.getCategoryName());
        orderItemDTO.setSpecName(inventoryOrderItemDTO.getSpecName());
        orderItemDTO.setSpecQuantity(inventoryOrderItemDTO.getSpecQuantity());
        orderItemDTO.setSaleSpec(inventoryOrderItemDTO.getSaleSpec());
        orderItemDTO.setSaleSpecQuantity(inventoryOrderItemDTO.getSaleSpecQuantity());
        orderItemDTO.setPackageName(inventoryOrderItemDTO.getPackageName());
        orderItemDTO.setUnitName(inventoryOrderItemDTO.getUnitName());
        orderItemDTO.setUnitTotalCount(inventoryOrderItemDTO.getReturnCount());
        orderItemDTO.setSaleMode(inventoryOrderItemDTO.getSaleMode());
        orderItemDTO.setReturnCount(inventoryOrderItemDTO.getReturnCount());
        orderItemDTO.setSaleCount(inventoryOrderItemDTO.getSaleCount());
        orderItemDTO.setSellUnit(inventoryOrderItemDTO.getSellUnit());
        orderItemDTO.setTotalAmount(inventoryOrderItemDTO.getTotalAmount());
        orderItemDTO.setPayAmount(inventoryOrderItemDTO.getPayAmount());
        orderItemDTO.setSource(inventoryOrderItemDTO.getSource());
        orderItemDTO.setChannel(inventoryOrderItemDTO.getChannel());
        // 增加货位信息
        orderItemDTO.setLocationId(inventoryOrderItemDTO.getLocationId());
        orderItemDTO.setLocationName(inventoryOrderItemDTO.getLocationName());

        orderItemDTO.setProductSpecificationId(inventoryOrderItemDTO.getProductSpecification_Id());
        orderItemDTO.setOwnerId(inventoryOrderItemDTO.getOwnerId());
        orderItemDTO.setSecOwnerId(inventoryOrderItemDTO.getSecOwnerId());
        // 入库项关联数据
        List<InventoryOrderRelatedItemDTO> inventoryOrderRelatedItemDTOS =
            inventoryOrderItemDTO.getInventoryOrderRelatedItemDTOS();
        if (CollectionUtils.isNotEmpty(inventoryOrderRelatedItemDTOS)) {
            List<OrderItemRelatedDTO> relatedDTOS =
                inventoryOrderRelatedItemDTOS.stream().filter(e -> e != null).map(e -> {
                    OrderItemRelatedDTO relatedDTO = new OrderItemRelatedDTO();
                    BeanUtils.copyProperties(e, relatedDTO);
                    return relatedDTO;
                }).collect(Collectors.toList());
            orderItemDTO.setItemRelatedDTOList(relatedDTOS);
        }
        return orderItemDTO;
    }

    public static List<InStockOrderDTO> inventoryOrderDTOS2InStockOrderDTOS(List<InStockOrderDTO> inStockOrderDTOS) {
        if (CollectionUtils.isEmpty(inStockOrderDTOS)) {
            return null;
        }
        List<InStockOrderDTO> inStockOrders = new ArrayList<>();
        inStockOrderDTOS.forEach(order -> {
            order.getInStockOrderItemDTOList().forEach(item -> {
                String extContent = item.getExtContent();
                if (StringUtils.isNotEmpty(extContent)) {
                    InStockOrderDTO inStockOrderDTO = new InStockOrderDTO();
                    BeanUtils.copyProperties(order, inStockOrderDTO);

                    List<InStockOrderItemDTO> inStockOrderItemDTOS = new ArrayList<>();
                    InStockOrderItemDTO inStockOrderItemDTO = new InStockOrderItemDTO();
                    BeanUtils.copyProperties(item, inStockOrderItemDTO);
                    InStockOrderItemExtContentDTO extContentDTO =
                        JSON.parseObject(extContent, InStockOrderItemExtContentDTO.class);
                    if (extContentDTO != null && CollectionUtils.isNotEmpty(extContentDTO.getItemRelatedDTOList())) {
                        List<StockOrderRelatedItemDTO> stockOrderRelatedItemDTOS = new ArrayList<>();
                        extContentDTO.getItemRelatedDTOList().forEach(relatedItem -> {
                            StockOrderRelatedItemDTO stockOrderRelatedItemDTO = new StockOrderRelatedItemDTO();
                            BeanUtils.copyProperties(item, stockOrderRelatedItemDTO);
                            if (item.getChannel() != null) {
                                stockOrderRelatedItemDTO.setFromChannel(item.getChannel().toString());
                                stockOrderRelatedItemDTO.setToChannel(item.getChannel().toString());
                            }
                            stockOrderRelatedItemDTO.setFromLocationId(item.getLocationId());
                            stockOrderRelatedItemDTO.setFromLocationName(item.getLocationName());
                            stockOrderRelatedItemDTO.setSpecQuantity(item.getSpecQuantity());
                            stockOrderRelatedItemDTO.setSpecName(item.getSpecName());
                            stockOrderRelatedItemDTO.setOrgId(order.getOrgId());
                            stockOrderRelatedItemDTO.setWarehouseId(order.getWarehouseId());
                            stockOrderRelatedItemDTO.setToLocationId(relatedItem.getToLocationId());
                            stockOrderRelatedItemDTO.setToLocationName(relatedItem.getToLocationName());
                            stockOrderRelatedItemDTO.setProductStockType(relatedItem.getProductStockType());
                            stockOrderRelatedItemDTO.setPackageCount(relatedItem.getPackageCount());
                            stockOrderRelatedItemDTO.setUnitCount(relatedItem.getUnitCount());
                            stockOrderRelatedItemDTO.setUnitTotalCount(relatedItem.getUnitTotalCount());

                            stockOrderRelatedItemDTOS.add(stockOrderRelatedItemDTO);
                        });
                        inStockOrderItemDTO.setStockOrderRelatedItemDTOS(stockOrderRelatedItemDTOS);
                        inStockOrderItemDTOS.add(inStockOrderItemDTO);
                    }
                    inStockOrderDTO.setInStockOrderItemDTOList(inStockOrderItemDTOS);

                    inStockOrders.add(inStockOrderDTO);
                }
            });
        });
        return inStockOrders;
    }
}

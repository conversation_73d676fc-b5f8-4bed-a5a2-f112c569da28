package com.yijiupi.himalaya.supplychain.inventory.domain.bl.notify;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Sets;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.notify.PdaLocationNotifyParam;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IPdaMessageNotifyService;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-07-19 10:50
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class PdaMessageNotifyBL {

    @Reference
    private IPdaMessageNotifyService pdaMessageNotifyService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    private static final Set<Byte> JIU_PI_WAREHOUSE_TYPES = Sets.newHashSet(
            WarehouseTypeEnum.城市仓库.getType(), WarehouseTypeEnum.集货点.getType(),
            WarehouseTypeEnum.店仓合一.getType(), WarehouseTypeEnum.前置仓.getType()
    );

    private static final Logger logger = LoggerFactory.getLogger(PdaMessageNotifyBL.class);

    /**
     * SCM-15219 2.5+实施优化，产品入库后如果未关联货位，给仓管pda推送通知
     *
     * @param changeList 库存变动信息
     */
    @Async("inventoryTaskExecutor")
    public void notifyWhenInStock(List<WarehouseInventoryChangeBO> changeList) {
        // 负数的 count 是出库, 这里只需要入库的, 入库是正数
        List<WarehouseInventoryChangeBO> inStockInventoryChange = changeList.stream()
                .filter(it -> BigDecimal.ZERO.compareTo(it.getCount()) < 0).collect(Collectors.toList());
        if (inStockInventoryChange.isEmpty()) {
            logger.info("没有入库库存变动信息, 不做处理");
            return;
        }
        logger.info("入库库存变动: {}", JSON.toJSONString(changeList, SerializerFeature.WriteMapNullValue));
        Integer warehouseId = inStockInventoryChange.get(0).getWarehouseId();
        byte warehouseType = warehouseQueryService.findWarehouseById(warehouseId).getWarehouseType().byteValue();
        if (!JIU_PI_WAREHOUSE_TYPES.contains(warehouseType)) {
            logger.info("非久批仓库, 不做后续处理");
            return;
        }
        Set<Long> inStockSkuIds = inStockInventoryChange.stream().map(WarehouseInventoryChangeBO::getProductSkuId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        pdaMessageNotifyService.sendPDALocationNotify(PdaLocationNotifyParam.of(warehouseId, inStockSkuIds));
    }

}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.AttributeRuleType;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchAttributeRuleConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchAttributeRuleRelationConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeRuleMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeRuleRelationMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRulePO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationReturnPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleReturnPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.*;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * <AUTHOR> 2018/4/9
 */
@Service
public class BatchAttributeRuleBL {

    @Autowired
    private BatchAttributeRuleMapper batchAttributeRuleMapper;
    @Autowired
    private BatchAttributeRuleRelationMapper batchAttributeRuleRelationMapper;

    /**
     * 新增
     *
     * @param batchAttributeRuleDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatchAttributeRule(BatchAttributeRuleDTO batchAttributeRuleDTO) {
        BatchAttributeRulePO batchAttributeRulePO =
            BatchAttributeRuleConvert.batchAttributeRuleDTO2PO(batchAttributeRuleDTO);
        batchAttributeRulePO.setId(UUIDGenerator.getUUID(batchAttributeRulePO.getClass().getName()));
        batchAttributeRuleMapper.insert(batchAttributeRulePO);

        // 管理relation
        List<BatchAttributeRuleRelationPO> relationPOS = createRelationPO(batchAttributeRuleDTO);
        relationPOS.forEach(n -> n.setRuleId(batchAttributeRulePO.getId()));
        relationPOS.forEach(n -> n.setId(UUIDGenerator.getUUID(n.getClass().getName())));
        batchAttributeRuleRelationMapper.insertList(relationPOS);
    }

    /**
     * 编辑
     *
     * @param batchAttributeRuleDTO
     */
    public void updateBatchAttributeRule(BatchAttributeRuleDTO batchAttributeRuleDTO) {
        Long ruleId = batchAttributeRuleDTO.getId();
        BatchAttributeRulePO batchAttributeRulePO =
            BatchAttributeRuleConvert.batchAttributeRuleDTO2PO(batchAttributeRuleDTO);
        batchAttributeRuleMapper.update(batchAttributeRulePO);
        batchAttributeRuleRelationMapper.deleteByRuleId(ruleId);
        List<BatchAttributeRuleRelationPO> relationPOS = createRelationPO(batchAttributeRuleDTO);
        relationPOS.forEach(n -> {
            n.setRuleId(ruleId);
        });
        relationPOS.forEach(n -> n.setId(UUIDGenerator.getUUID(n.getClass().getName())));
        batchAttributeRuleRelationMapper.insertList(relationPOS);
    }

    /**
     * 构建relationPO
     */
    private List<BatchAttributeRuleRelationPO> createRelationPO(BatchAttributeRuleDTO batchAttributeRuleDTO) {
        ArrayList<BatchAttributeRuleRelationDTO> batchAttributeRuleRelationDTOS = new ArrayList<>();
        List<BatchAttributeRuleRelationDTO> warehouseRelationList = batchAttributeRuleDTO.getWarehouseRelationList();
        List<BatchAttributeRuleRelationDTO> ownerRelationList = batchAttributeRuleDTO.getOwnerRelationList();
        List<BatchAttributeRuleRelationDTO> categoryRelationList = batchAttributeRuleDTO.getCategoryRelationList();
        List<BatchAttributeRuleRelationDTO> brandRelationList = batchAttributeRuleDTO.getBrandRelationList();
        List<BatchAttributeRuleRelationDTO> serviceProviderList = batchAttributeRuleDTO.getServiceProviderList();
        if (CollectionUtils.isNotEmpty(warehouseRelationList)) {
            batchAttributeRuleRelationDTOS.addAll(warehouseRelationList);
        }
        if (CollectionUtils.isNotEmpty(ownerRelationList)) {
            batchAttributeRuleRelationDTOS.addAll(ownerRelationList);
        }
        if (CollectionUtils.isNotEmpty(categoryRelationList)) {
            batchAttributeRuleRelationDTOS.addAll(categoryRelationList);
        }
        if (CollectionUtils.isNotEmpty(brandRelationList)) {
            batchAttributeRuleRelationDTOS.addAll(brandRelationList);
        }
        if (CollectionUtils.isNotEmpty(serviceProviderList)) {
            validateServiceProviderRuleRelation(serviceProviderList);
            batchAttributeRuleRelationDTOS.addAll(serviceProviderList);
        }

        return BatchAttributeRuleRelationConvert.BatchAttributeRuleRelationDTOS2POS(batchAttributeRuleRelationDTOS);
    }

    /**
     * 删除
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        batchAttributeRuleMapper.deleteById(id);
        batchAttributeRuleRelationMapper.deleteByRuleId(id);
    }

    /**
     * 列表
     *
     * @param batchAttributeRuleQueryDTO
     * @return
     */
    public PageList<BatchAttributeRuleReturnDTO>
        findBatchAttributeRuleList(BatchAttributeRuleQueryDTO batchAttributeRuleQueryDTO) {
        PageResult<BatchAttributeRuleReturnPO> batchAttributeRuleList =
            batchAttributeRuleMapper.findBatchAttributeRuleList(batchAttributeRuleQueryDTO,
                batchAttributeRuleQueryDTO.getPageNum(), batchAttributeRuleQueryDTO.getPageSize());
        PageList<BatchAttributeRuleReturnPO> batchAttributeRuleReturnPOPageList = batchAttributeRuleList.toPageList();
        List<BatchAttributeRuleReturnPO> dataList = batchAttributeRuleReturnPOPageList.getDataList();
        List<BatchAttributeRuleReturnDTO> batchAttributeRuleReturnDTOS = BatchAttributeRuleReturnPO2DTO(dataList);

        PageList<BatchAttributeRuleReturnDTO> pageList = new PageList<>();
        pageList.setDataList(batchAttributeRuleReturnDTOS);
        pageList.setPager(batchAttributeRuleReturnPOPageList.getPager());
        return pageList;
    }

    private List<BatchAttributeRuleReturnDTO>
        BatchAttributeRuleReturnPO2DTO(List<BatchAttributeRuleReturnPO> dataList) {
        List<BatchAttributeRuleReturnDTO> batchAttributeRuleReturnDTOS = new ArrayList<>();
        for (BatchAttributeRuleReturnPO batchAttributeRuleReturnPO : dataList) {
            BatchAttributeRuleReturnDTO batchAttributeRuleReturnDTO = new BatchAttributeRuleReturnDTO();

            batchAttributeRuleReturnDTO.setId(batchAttributeRuleReturnPO.getId());
            batchAttributeRuleReturnDTO.setTemplateId(batchAttributeRuleReturnPO.getTemplateId());
            batchAttributeRuleReturnDTO.setTemplateName(batchAttributeRuleReturnPO.getTemplateName());
            batchAttributeRuleReturnDTO.setRemark(batchAttributeRuleReturnPO.getRemark());
            batchAttributeRuleReturnDTO.setCreateUser(batchAttributeRuleReturnPO.getCreateUser());
            batchAttributeRuleReturnDTO.setCreateTime(batchAttributeRuleReturnPO.getCreateTime());
            // 通过AttributeRuleType进行分组.
            List<BatchAttributeRuleRelationReturnPO> relationList = batchAttributeRuleReturnPO.getRelationList();
            Map<Byte, List<BatchAttributeRuleRelationReturnPO>> map =
                relationList.stream().collect(Collectors.groupingBy(BatchAttributeRuleRelationReturnPO::getRuleType));

            batchAttributeRuleReturnDTO.setWarehouseRelationList(BatchAttributeRuleConvert
                .batchAttributeRuleRelationReturnPOS2DTOS(map.get(AttributeRuleType.WAREHOUSE)));
            batchAttributeRuleReturnDTO.setOwnerRelationList(
                BatchAttributeRuleConvert.batchAttributeRuleRelationReturnPOS2DTOS(map.get(AttributeRuleType.OWNER)));
            batchAttributeRuleReturnDTO.setCategoryRelationList(BatchAttributeRuleConvert
                .batchAttributeRuleRelationReturnPOS2DTOS(map.get(AttributeRuleType.CATEGORY)));
            batchAttributeRuleReturnDTO.setBrandRelationList(
                BatchAttributeRuleConvert.batchAttributeRuleRelationReturnPOS2DTOS(map.get(AttributeRuleType.BRAND)));
            batchAttributeRuleReturnDTO.setServiceProviderList(BatchAttributeRuleConvert
                .batchAttributeRuleRelationReturnPOS2DTOS(map.get(AttributeRuleType.SERVICEPROVIDER)));
            batchAttributeRuleReturnDTOS.add(batchAttributeRuleReturnDTO);
        }
        return batchAttributeRuleReturnDTOS;
    }

    private void validateServiceProviderRuleRelation(List<BatchAttributeRuleRelationDTO> ruleRelationPOS) {
        if (CollectionUtils.isEmpty(ruleRelationPOS)) {
            return;
        }
        List<BatchAttributeRuleRelationDTO> serviceProviderList = ruleRelationPOS.stream()
            .filter(e -> e != null && Objects.equals(AttributeRuleType.SERVICEPROVIDER, e.getRuleType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(serviceProviderList)) {
            return;
        }
        // 重复数据
        Collection<List<BatchAttributeRuleRelationDTO>> repeatServiceProviderList =
            serviceProviderList.stream().filter(e -> e != null).collect(Collectors.collectingAndThen(
                Collectors.groupingBy(e -> String.format("%s%s", e.getRuleType(), e.getAttributeValueId())), map -> {
                    // 删除不重复数据
                    map.values().removeIf(values -> values.size() == 1);
                    return map.values();
                }));
        if (CollectionUtils.isNotEmpty(repeatServiceProviderList)) {
            // 有重复数据则报错提示
            List<String> repeatMsg = repeatServiceProviderList.stream().filter(Objects::nonNull)
                .flatMap(e -> e.stream()).filter(Objects::nonNull)
                .map(e -> String.format("%s[%s]", e.getAttributeValueName(), e.getAttributeValueId())).distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(repeatMsg)) {
                throw new BusinessValidateException(
                    repeatMsg.stream().filter(Objects::nonNull).collect(Collectors.joining(",")) + "存在重复批属性配置关系！");
            }
        }
        // 数据库校验
        List<Byte> ruleTypeList =
            serviceProviderList.stream().map(e -> e.getRuleType()).distinct().collect(Collectors.toList());
        List<String> attributeValueIdList =
            serviceProviderList.stream().map(e -> e.getAttributeValueId()).distinct().collect(Collectors.toList());
        RuleRelationQueryDTO queryDTO = new RuleRelationQueryDTO();
        queryDTO.setRuleTypeList(ruleTypeList);
        queryDTO.setAttributeValueIdList(attributeValueIdList);
        List<BatchAttributeRuleRelationPO> existRelationList =
            batchAttributeRuleRelationMapper.selectRuleRelationByRuleTypeAndAttribute(queryDTO);
        if (CollectionUtils.isNotEmpty(existRelationList)) {
            // 有重复数据则报错提示
            List<String> existRelationMsg = existRelationList.stream().filter(Objects::nonNull)
                .map(e -> String.format("%s[%s]", e.getAttributeValueName(), e.getAttributeValueId())).distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existRelationMsg)) {
                throw new BusinessValidateException(
                    existRelationMsg.stream().filter(Objects::nonNull).collect(Collectors.joining(","))
                        + "存在重复批属性配置关系！");
            }
        }
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductionDateAuditPOMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        <!--@Table productiondateaudit-->
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="WarehouseId" jdbcType="INTEGER" property="warehouseId"/>
        <result column="SkuId" jdbcType="BIGINT" property="skuId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
        <result column="ProductionDate" jdbcType="DATE" property="productionDate"/>
        <result column="UnitTotalCount" jdbcType="DECIMAL" property="unitTotalCount"/>
        <result column="RefTodoTaskNo" jdbcType="VARCHAR" property="refTodoTaskNo"/>
        <result column="RefTodoTaskId" jdbcType="BIGINT" property="refTodoTaskId"/>
        <result column="ApplyUser" jdbcType="VARCHAR" property="applyUser"/>
        <result column="MobileNo" jdbcType="VARCHAR" property="mobileNo"/>
        <result column="AuditRemark" jdbcType="VARCHAR" property="auditRemark"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="INTEGER" property="createUser"/>
        <result column="LastUpdateTime" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="LastUpdateUser" jdbcType="INTEGER" property="lastUpdateUser"/>
        <result column="businessType" jdbcType="INTEGER" property="businessType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        Id, WarehouseId, SkuId, ProductName, `State`, ProductionDate, UnitTotalCount, RefTodoTaskNo,
        RefTodoTaskId, ApplyUser, MobileNo, AuditRemark, CreateTime, `CreateUser`, LastUpdateTime,
        LastUpdateUser,businessType
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from productiondateaudit
        where Id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from productiondateaudit
        where Id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        insert into productiondateaudit (Id, WarehouseId, SkuId,
        ProductName, `State`, ProductionDate,
        UnitTotalCount, RefTodoTaskNo, RefTodoTaskId,
        ApplyUser, MobileNo, AuditRemark,
        CreateTime, `CreateUser`, LastUpdateTime,
        LastUpdateUser)
        values (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{skuId,jdbcType=BIGINT},
        #{productName,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, #{productionDate,jdbcType=DATE},
        #{unitTotalCount,jdbcType=DECIMAL}, #{refTodoTaskNo,jdbcType=VARCHAR}, #{refTodoTaskId,jdbcType=BIGINT},
        #{applyUser,jdbcType=VARCHAR}, #{mobileNo,jdbcType=VARCHAR}, #{auditRemark,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{lastUpdateUser,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        insert into productiondateaudit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
            <if test="skuId != null">
                SkuId,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="state != null">
                `State`,
            </if>
            <if test="productionDate != null">
                ProductionDate,
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount,
            </if>
            <if test="refTodoTaskNo != null">
                RefTodoTaskNo,
            </if>
            <if test="refTodoTaskId != null">
                RefTodoTaskId,
            </if>
            <if test="applyUser != null">
                ApplyUser,
            </if>
            <if test="mobileNo != null">
                MobileNo,
            </if>
            <if test="auditRemark != null">
                AuditRemark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                `CreateUser`,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=DATE},
            </if>
            <if test="unitTotalCount != null">
                #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="refTodoTaskNo != null">
                #{refTodoTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="refTodoTaskId != null">
                #{refTodoTaskId,jdbcType=BIGINT},
            </if>
            <if test="applyUser != null">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="auditRemark != null">
                #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        update productiondateaudit
        <set>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                SkuId = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `State` = #{state,jdbcType=TINYINT},
            </if>
            <if test="productionDate != null">
                ProductionDate = #{productionDate,jdbcType=DATE},
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="refTodoTaskNo != null">
                RefTodoTaskNo = #{refTodoTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="refTodoTaskId != null">
                RefTodoTaskId = #{refTodoTaskId,jdbcType=BIGINT},
            </if>
            <if test="applyUser != null">
                ApplyUser = #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                MobileNo = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="auditRemark != null">
                AuditRemark = #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                `CreateUser` = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                businessType = #{businessType,jdbcType=TINYINT},
            </if>
        </set>
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        update productiondateaudit
        set WarehouseId = #{warehouseId,jdbcType=INTEGER},
        SkuId = #{skuId,jdbcType=BIGINT},
        ProductName = #{productName,jdbcType=VARCHAR},
        `State` = #{state,jdbcType=TINYINT},
        ProductionDate = #{productionDate,jdbcType=DATE},
        UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
        RefTodoTaskNo = #{refTodoTaskNo,jdbcType=VARCHAR},
        RefTodoTaskId = #{refTodoTaskId,jdbcType=BIGINT},
        ApplyUser = #{applyUser,jdbcType=VARCHAR},
        MobileNo = #{mobileNo,jdbcType=VARCHAR},
        AuditRemark = #{auditRemark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        `CreateUser` = #{createUser,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER},
        businessType = #{businessType,jdbcType=INTEGER}
        where Id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update productiondateaudit
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="SkuId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="ProductName = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`State` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="ProductionDate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.productionDate,jdbcType=DATE}
                </foreach>
            </trim>
            <trim prefix="UnitTotalCount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.unitTotalCount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="RefTodoTaskNo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.refTodoTaskNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="RefTodoTaskId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.refTodoTaskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="ApplyUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.applyUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="MobileNo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.mobileNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="AuditRemark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.auditRemark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="`CreateUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="businessType = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when Id = #{item.id,jdbcType=BIGINT} then #{item.businessType,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update productiondateaudit
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="WarehouseId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.warehouseId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.warehouseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SkuId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.skuId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductName = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productName != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`State` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.state != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductionDate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productionDate != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.productionDate,jdbcType=DATE}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UnitTotalCount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.unitTotalCount != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.unitTotalCount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RefTodoTaskNo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.refTodoTaskNo != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.refTodoTaskNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RefTodoTaskId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.refTodoTaskId != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.refTodoTaskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ApplyUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.applyUser,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MobileNo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mobileNo != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.mobileNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="AuditRemark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.auditRemark != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.auditRemark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`CreateUser` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.createUser,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateTime != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastUpdateUser != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.lastUpdateUser,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="businessType = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.businessType != null">
                        when Id = #{item.id,jdbcType=BIGINT} then #{item.businessType,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where Id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into productiondateaudit
        (Id, WarehouseId, SkuId, ProductName, `State`, ProductionDate, UnitTotalCount, RefTodoTaskNo,
        RefTodoTaskId, ApplyUser, MobileNo, AuditRemark, CreateTime, `CreateUser`, LastUpdateTime,
        LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.skuId,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR}, #{item.state,jdbcType=TINYINT}, #{item.productionDate,jdbcType=DATE},
            #{item.unitTotalCount,jdbcType=DECIMAL}, #{item.refTodoTaskNo,jdbcType=VARCHAR},
            #{item.refTodoTaskId,jdbcType=BIGINT}, #{item.applyUser,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.auditRemark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP}, #{item.lastUpdateUser,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <insert id="batchInsertOrUpdate" parameterType="map">
        <!--@mbg.generated-->
        insert into productiondateaudit
        (Id, WarehouseId, SkuId, ProductName, `State`, ProductionDate, UnitTotalCount, RefTodoTaskNo,
        RefTodoTaskId, ApplyUser, MobileNo, AuditRemark, CreateTime, `CreateUser`, LastUpdateTime,
        LastUpdateUser)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.warehouseId,jdbcType=INTEGER}, #{item.skuId,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR}, #{item.state,jdbcType=TINYINT}, #{item.productionDate,jdbcType=DATE},
            #{item.unitTotalCount,jdbcType=DECIMAL}, #{item.refTodoTaskNo,jdbcType=VARCHAR},
            #{item.refTodoTaskId,jdbcType=BIGINT}, #{item.applyUser,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.auditRemark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=INTEGER},
            #{item.lastUpdateTime,jdbcType=TIMESTAMP}, #{item.lastUpdateUser,jdbcType=INTEGER}
            )
        </foreach>
        on duplicate key update
        Id=values(Id),
        WarehouseId=values(WarehouseId),
        SkuId=values(SkuId),
        ProductName=values(ProductName),
        State=values(State),
        ProductionDate=values(ProductionDate),
        UnitTotalCount=values(UnitTotalCount),
        RefTodoTaskNo=values(RefTodoTaskNo),
        RefTodoTaskId=values(RefTodoTaskId),
        ApplyUser=values(ApplyUser),
        MobileNo=values(MobileNo),
        AuditRemark=values(AuditRemark),
        CreateTime=values(CreateTime),
        CreateUser=values(CreateUser),
        LastUpdateTime=values(LastUpdateTime),
        LastUpdateUser=values(LastUpdateUser)
    </insert>
    <delete id="deleteByPrimaryKeyIn">
        <!--@mbg.generated-->
        delete from productiondateaudit where Id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
    <insert id="insertOrUpdate"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        insert into productiondateaudit
        (Id, WarehouseId, SkuId, ProductName, `State`, ProductionDate, UnitTotalCount, RefTodoTaskNo,
        RefTodoTaskId, ApplyUser, MobileNo, AuditRemark, CreateTime, `CreateUser`, LastUpdateTime,
        LastUpdateUser)
        values
        (#{id,jdbcType=BIGINT}, #{warehouseId,jdbcType=INTEGER}, #{skuId,jdbcType=BIGINT},
        #{productName,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, #{productionDate,jdbcType=DATE},
        #{unitTotalCount,jdbcType=DECIMAL}, #{refTodoTaskNo,jdbcType=VARCHAR}, #{refTodoTaskId,jdbcType=BIGINT},
        #{applyUser,jdbcType=VARCHAR}, #{mobileNo,jdbcType=VARCHAR}, #{auditRemark,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=INTEGER}, #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{lastUpdateUser,jdbcType=INTEGER})
        on duplicate key update
        Id = #{id,jdbcType=BIGINT},
        WarehouseId = #{warehouseId,jdbcType=INTEGER},
        SkuId = #{skuId,jdbcType=BIGINT},
        ProductName = #{productName,jdbcType=VARCHAR},
        `State` = #{state,jdbcType=TINYINT},
        ProductionDate = #{productionDate,jdbcType=DATE},
        UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
        RefTodoTaskNo = #{refTodoTaskNo,jdbcType=VARCHAR},
        RefTodoTaskId = #{refTodoTaskId,jdbcType=BIGINT},
        ApplyUser = #{applyUser,jdbcType=VARCHAR},
        MobileNo = #{mobileNo,jdbcType=VARCHAR},
        AuditRemark = #{auditRemark,jdbcType=VARCHAR},
        CreateTime = #{createTime,jdbcType=TIMESTAMP},
        `CreateUser` = #{createUser,jdbcType=INTEGER},
        LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
        LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER}
    </insert>
    <insert id="insertOrUpdateSelective"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO">
        <!--@mbg.generated-->
        insert into productiondateaudit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                Id,
            </if>
            <if test="warehouseId != null">
                WarehouseId,
            </if>
            <if test="skuId != null">
                SkuId,
            </if>
            <if test="productName != null">
                ProductName,
            </if>
            <if test="state != null">
                `State`,
            </if>
            <if test="productionDate != null">
                ProductionDate,
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount,
            </if>
            <if test="refTodoTaskNo != null">
                RefTodoTaskNo,
            </if>
            <if test="refTodoTaskId != null">
                RefTodoTaskId,
            </if>
            <if test="applyUser != null">
                ApplyUser,
            </if>
            <if test="mobileNo != null">
                MobileNo,
            </if>
            <if test="auditRemark != null">
                AuditRemark,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="createUser != null">
                `CreateUser`,
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime,
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=DATE},
            </if>
            <if test="unitTotalCount != null">
                #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="refTodoTaskNo != null">
                #{refTodoTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="refTodoTaskId != null">
                #{refTodoTaskId,jdbcType=BIGINT},
            </if>
            <if test="applyUser != null">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="auditRemark != null">
                #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                Id = #{id,jdbcType=BIGINT},
            </if>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                SkuId = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                ProductName = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `State` = #{state,jdbcType=TINYINT},
            </if>
            <if test="productionDate != null">
                ProductionDate = #{productionDate,jdbcType=DATE},
            </if>
            <if test="unitTotalCount != null">
                UnitTotalCount = #{unitTotalCount,jdbcType=DECIMAL},
            </if>
            <if test="refTodoTaskNo != null">
                RefTodoTaskNo = #{refTodoTaskNo,jdbcType=VARCHAR},
            </if>
            <if test="refTodoTaskId != null">
                RefTodoTaskId = #{refTodoTaskId,jdbcType=BIGINT},
            </if>
            <if test="applyUser != null">
                ApplyUser = #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                MobileNo = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="auditRemark != null">
                AuditRemark = #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                `CreateUser` = #{createUser,jdbcType=INTEGER},
            </if>
            <if test="lastUpdateTime != null">
                LastUpdateTime = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateUser != null">
                LastUpdateUser = #{lastUpdateUser,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="pageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productiondateaudit
        <where>
            <if test="warehouseId != null">
                WarehouseId = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="productName != null and productName != ''">
                <bind name="likeProductName" value="'%'+productName+'%'"/>
                and ProductName like #{likeProductName}
            </if>
            <if test="skuId != null">
                and SkuId = #{skuId,jdbcType=BIGINT}
            </if>
            <if test="productionDate != null and productionDate != ''">
                and ProductionDate = #{productionDate,jdbcType=VARCHAR}
            </if>
            <if test="state != null and state.size() != 0">
                and State in
                <foreach collection="state" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by WarehouseId,ProductName,ProductionDate
    </select>

    <select id="selectListByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productiondateaudit
        where Id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

</mapper>
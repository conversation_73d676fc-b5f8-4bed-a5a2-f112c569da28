package com.yijiupi.himalaya.supplychain.inventory.domain.bl.erp;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventorySyncAsyncBL;
import com.yijiupi.himalaya.supplychain.batchinventory.util.HttpUtil;
import com.yijiupi.himalaya.supplychain.instockorder.utils.ErpRoResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.WarehouseDefectiveProductQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.WarehouseDefectiveProductResultDTO;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/8/19
 */
@Service
public class ErpDefectiveLimitBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchInventorySyncAsyncBL.class);

    public String queryDefectiveLimit(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库信息不能为空！");

        List<WarehouseDefectiveProductResultDTO> list = getErpDefectiveList(warehouseId);
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        // 残次品总额度
        BigDecimal defectiveTotalAmount = list.stream().map(WarehouseDefectiveProductResultDTO::getDefectiveQuotaAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<WarehouseDefectiveProductResultDTO> outOfLimitList =
            list.stream().filter(this::isOutOfLimit).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfLimitList)) {
            return buildOutOfLimitString(outOfLimitList, defectiveTotalAmount);
        }

        return buildNormalString(list, defectiveTotalAmount);
    }

    public List<WarehouseDefectiveProductResultDTO> getErpDefectiveList(Integer warehouseId) {
        try {
            WarehouseDefectiveProductQueryDTO queryDTO = new WarehouseDefectiveProductQueryDTO();
            queryDTO.setWarehouseId(warehouseId);

            LOGGER.info("查询残次品额度请求: {}", warehouseId);
            ErpRoResult<List<WarehouseDefectiveProductResultDTO>> baseResponse = HttpUtil.httpGet(getUrl(warehouseId),
                new TypeToken<ErpRoResult<List<WarehouseDefectiveProductResultDTO>>>() {}.getType());
            LOGGER.info("查询残次品额度响应: {}", JSON.toJSONString(baseResponse));
            if (baseResponse != null && !baseResponse.getSuccess()) {
                LOGGER.warn("查询残次品出错:{}", baseResponse.getMessage());
                return Collections.emptyList();
            } else if (baseResponse == null) {
                LOGGER.warn("查询残次品出错, 没有得到返回值");
                return Collections.emptyList();
            }

            List<WarehouseDefectiveProductResultDTO> list = baseResponse.getData();

            return list;
        } catch (Exception e) {
            LOGGER.error("查询erp残次品报错", e);
        }

        return Collections.emptyList();
    }

    private String getUrl(Integer warehouseId) {
        StringBuffer buffer = new StringBuffer("");
        buffer.append(
            "http://in-erp5-innerapi.yjp.com/erpinventorynotes/api/DefectiveProduct/QueryDefectiveQuoteForStoreHouseId");
        buffer.append("?");
        buffer.append("storeHouseId=");
        buffer.append(warehouseId);

        return buffer.toString();
    }

    // 残次品总额度XXX元，其中XX类目超出XX元…
    private String buildOutOfLimitString(List<WarehouseDefectiveProductResultDTO> outOfLimitList,
        BigDecimal defectiveTotalAmount) {
        StringBuffer buffer = new StringBuffer("");
        buffer.append("残次品总额度");
        buffer.append(defectiveTotalAmount.stripTrailingZeros().toPlainString());
        buffer.append("元，其中");

        // 只展示前三个
        List<WarehouseDefectiveProductResultDTO> topThreeList =
            outOfLimitList.stream().limit(3).collect(Collectors.toList());
        for (WarehouseDefectiveProductResultDTO resultDTO : topThreeList) {
            buffer.append(resultDTO.getBusinessDivision());
            buffer.append("超出");
            buffer.append(resultDTO.getResidueQuota().abs());
            buffer.append("元");
        }
        return buffer.toString();
    }

    // 残次品总额度XXX元，当前残次品总额XXX元
    private String buildNormalString(List<WarehouseDefectiveProductResultDTO> totalList,
        BigDecimal defectiveTotalAmount) {
        StringBuffer buffer = new StringBuffer("");
        buffer.append("残次品总额度");
        buffer.append(defectiveTotalAmount.stripTrailingZeros().toPlainString());
        BigDecimal alreadyUseDefectiveAmount = totalList.stream().map(WarehouseDefectiveProductResultDTO::getUsedQuota)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        buffer.append("，当前残次品总额");
        buffer.append(alreadyUseDefectiveAmount.stripTrailingZeros().toPlainString());
        buffer.append("元");
        return buffer.toString();
    }

    private boolean isOutOfLimit(WarehouseDefectiveProductResultDTO resultDTO) {
        if (resultDTO.getQuotaRatio().compareTo(BigDecimal.ZERO) <= 0) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}

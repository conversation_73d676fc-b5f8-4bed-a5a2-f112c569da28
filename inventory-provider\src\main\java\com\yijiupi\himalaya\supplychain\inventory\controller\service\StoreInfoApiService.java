package com.yijiupi.himalaya.supplychain.inventory.controller.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.gson.Gson;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.baseutil.ArrayUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.manager.OmsInventoryManager;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.controller.convertor.ProductOwnerDTOConvertor;
import com.yijiupi.himalaya.supplychain.inventory.controller.convertor.StoreReportConvert;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.FindStoreInfoVO;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.FindStorePageDTO;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.ROResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.CityMergeDTO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/19 10:01
 * @Version 1.0
 */
@Component
public class StoreInfoApiService {
	private static final Logger LOG = LoggerFactory.getLogger(StoreInfoApiService.class);
	private static final Gson GSON = new Gson();
	@Reference
	private IStoreWareHouseService iStoreWareHouseService;
	@Autowired
	private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
	@Autowired
	private StoreReportConvert storeReportConvert;
	@Resource
	private OmsInventoryManager omsInventoryManager;
	@Reference
	private IProductSkuService iProductSkuService;

	/**
	 * 库存报表
	 */
	public ROResult<List<FindStoreInfoVO>> findStorePage(FindStorePageDTO findStorePageDTO) {
		if (findStorePageDTO.getWarehouseIds() == null || findStorePageDTO.getWarehouseIds().size() <= 0) {
			WareHouseDTO wareHouseDTO = iStoreWareHouseService.getWareHouseByCityId(findStorePageDTO.getCityId());
			// List<Warehouse> warehouseList =
			// warhouseService.getWarehouseByCityId(findStorePageDTO.getCityId());
			// List<Integer> warehouseIds = warehouseList.stream().map(t ->
			// t.getId()).collect(Collectors.toList());
			findStorePageDTO.setWarehouseIds(Collections.singletonList(wareHouseDTO.getId()));
		}
		StopWatch interfaceWatch = new StopWatch("interfaceWatch");
		interfaceWatch.start("interfaceWatch");
		StopWatch stopWatch = new StopWatch("查询库存信息接口");
		ROResult<List<FindStoreInfoVO>> result = new ROResult<>();
		StockReportSO so = new StockReportSO();
		so.setInventoryPinProperty(findStorePageDTO.getInventoryPinProperty());
		so.setProductSkuName(findStorePageDTO.getProductSkuName());
		so.setWarehouseIds(findStorePageDTO.getWarehouseIds());
		if (findStorePageDTO.getSupplierId() != null && findStorePageDTO.getSupplierId() > 0) {
			so.setSupplierId(findStorePageDTO.getSupplierId());
		}
		if (findStorePageDTO.getStoreOwnerType() != null && findStorePageDTO.getStoreOwnerType() >= 0) {
			so.setStoreOwnerType(findStorePageDTO.getStoreOwnerType());
		}
		so.setProductSkuIds(findStorePageDTO.getProductSkuIds());
		// 是否限制产品范围
		so.setLimitSku(findStorePageDTO.getLimitSku());
		so.setHasRealStoreType(findStorePageDTO.getHasRealStoreType());
		so.setWarehouseAllocationType(findStorePageDTO.getWarehouseAllocationType());

		PagerCondition pager = new PagerCondition(findStorePageDTO.getCurrentPage(), findStorePageDTO.getPageSize());
		stopWatch.start("查询库存信息");
		PageList<InventoryReportDTO> pageList = warehouseInventoryQueryBL.findStoreReportPageByAuth(so, pager);
		stopWatch.stop();

		LOG.info("查询库存完成，耗时：{}", stopWatch.prettyPrint());
		List<InventoryReportDTO> dataList = pageList.getDataList();
		if (!CollectionUtils.isEmpty(dataList)) {
			List<Integer> warehouseIds =
					dataList.stream().map(InventoryReportDTO::getWarhouseId).distinct().collect(Collectors.toList());
			List<CityMergeDTO> lstParam = new ArrayList<>();
			dataList.forEach(p -> {
				CityMergeDTO dto = new CityMergeDTO();
				dto.setProductOwnerId(p.getOwnerId());
				dto.setProductSpecificationId(p.getProductSpecificationId());
				lstParam.add(dto);
			});
			// List<CityMergeDTO> lstWaiting =
			// iOrderQueryService.getWaitingDeliveryStateSecCountByProductSkuIds(lstParam, warehouseIds.get(0));
			WareHouseDTO wareHouseDTO = iStoreWareHouseService.findWareHouseById(warehouseIds.get(0));
			// 合作商库存不查
			List<InventoryReportDTO> dataListWithOutPartner = ArrayUtils.deepCopy(dataList);
			dataListWithOutPartner.removeIf(t -> Objects.equals(OwnerTypeConst.合作商, t.getStoreOwnerType()));
			// 获取销售库存
			List<ProductOwnerInfoDTO> productOwnerDTOList =
					ProductOwnerDTOConvertor.convertByInventoryReport(dataListWithOutPartner);
			Map<String, BigDecimal> saleInventoryMap =
					getSaleInventoryMap(productOwnerDTOList, warehouseIds.get(0), findStorePageDTO.getCityId());
			Map<String, BigDecimal> mapWaiting = new HashMap<>();
			//// System.err.println("saleInventoryMap:" + GSON.toJson(saleInventoryMap));
			// if (!CollectionUtils.isEmpty(lstWaiting)) {
			// lstWaiting.forEach(p -> {
			// mapWaiting.put(String.format("%s-%s-%s", p.getProductSpecificationId(), p.getProductOwnerId(),
			//// p.getProductSecOwnerId()), p.getCount());
			// });
			// }
			// 在途库存
			Map<String, BigDecimal> allotDeliveryingCountMap = new HashMap<>();// getAllotDeliveryingCountMap(warehouseIds.get(0),
			// lstParam);

			// System.err.println("mapWaiting:" + GSON.toJson(mapWaiting));
			List<FindStoreInfoVO> convert = storeReportConvert.convert(dataList, wareHouseDTO, mapWaiting,
					saleInventoryMap, allotDeliveryingCountMap);

			stopWatch.start("查询瓶码等信息");
			setBoxCodeInfo(convert, findStorePageDTO.getCityId());
			stopWatch.stop();
			LOG.info("查询库存信息完成，耗时：{}", stopWatch.prettyPrint());
			result.setData(convert);
			result.setTotalCount(pageList.getPager().getRecordCount());
		}
		interfaceWatch.stop();
		LOG.info("interfaceWatch查询库存信息完成，耗时：{}", interfaceWatch.prettyPrint());
		return result;
	}

	/**
	 * 查询销售库存
	 */
	private Map<String, BigDecimal> getSaleInventoryMap(List<ProductOwnerInfoDTO> productOwners, Integer warehouseId,
														Integer cityId) {
		Map<String, BigDecimal> saleMap = new HashMap<>();
		if (CollectionUtils.isEmpty(productOwners)) {
			return Collections.emptyMap();
		}
		List<SaleInventoryInfoDTO> saleInventoryDTOS = omsInventoryManager.findInventoryByProductOwners(cityId, warehouseId, productOwners);
		if (!CollectionUtils.isEmpty(saleInventoryDTOS)) {
			saleInventoryDTOS.forEach(p -> {
				String productSkuIdCityIDWarehouseId = String.format("%s-%s-%s-%s", p.getWarehouseId(),
						p.getProductSpecId(), p.getOwnerId(), p.getSecOwnerId());
				saleMap.put(productSkuIdCityIDWarehouseId, p.getSaleInventoryCount());
			});
		}
		return saleMap;
	}

	private void setBoxCodeInfo(List<FindStoreInfoVO> lstItem, Integer cityId) {
		List<Long> lstSkuIds = lstItem.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
		if (CollectionUtils.isEmpty(lstSkuIds)) {
			return;
		}
		Set<Long> skuIdSet = new HashSet<>(lstSkuIds);
		StopWatch stopWatch = new StopWatch("查询瓶码箱码信息接口");
		stopWatch.start("查询瓶码箱码信息");
		// 拿到瓶码箱码信息
		Map<Long, ProductCodeDTO> codeMap = iProductSkuService.getPackageAndUnitCode(skuIdSet, cityId);
		stopWatch.stop();
		LOG.info("获取瓶码箱码信息：{}", GSON.toJson(codeMap));
		stopWatch.start("查询sku信息");
		Map<Long, ProductSkuInfoReturnDTO> productInfoBySkuId = iProductSkuService.getProductInfoBySkuId(lstSkuIds);
		stopWatch.stop();
		lstItem.forEach(p -> {
			ProductSkuInfoReturnDTO dto = productInfoBySkuId.get(p.getProductSkuId());
			if (dto != null) {
				p.setUnpackage(dto.getUnpackage());
			}
			ProductCodeDTO productCodeDTO = codeMap.get(p.getProductSkuId());
			if (null != productCodeDTO) {
				// 箱码
				p.setPackageCode(productCodeDTO.getPackageCode());
				// 瓶码
				p.setUnitCode(productCodeDTO.getUnitCode());
			}
		});
		LOG.info("查询瓶码箱码信息接口耗时：{}", stopWatch.prettyPrint());
	}

	/**
	 * 库存报表
	 */
	public ROResult<List<FindStoreInfoVO>> findStorePageInfo(FindStorePageDTO findStorePageDTO) {
		if (findStorePageDTO.getWarehouseIds() == null || findStorePageDTO.getWarehouseIds().size() <= 0) {
			WareHouseDTO wareHouseDTO = iStoreWareHouseService.getWareHouseByCityId(findStorePageDTO.getCityId());
			// List<Warehouse> warehouseList =
			// warhouseService.getWarehouseByCityId(findStorePageDTO.getCityId());
			// List<Integer> warehouseIds = warehouseList.stream().map(t ->
			// t.getId()).collect(Collectors.toList());
			findStorePageDTO.setWarehouseIds(Collections.singletonList(wareHouseDTO.getId()));
		}
		ROResult<List<FindStoreInfoVO>> result = new ROResult<>();
		StockReportSO so = new StockReportSO();
		so.setInventoryPinProperty(findStorePageDTO.getInventoryPinProperty());
		so.setProductSkuName(findStorePageDTO.getProductSkuName());
		so.setWarehouseIds(findStorePageDTO.getWarehouseIds());
		if (findStorePageDTO.getSupplierId() != null && findStorePageDTO.getSupplierId() > 0) {
			so.setSupplierId(findStorePageDTO.getSupplierId());
		}
		if (findStorePageDTO.getStoreOwnerType() != null && findStorePageDTO.getStoreOwnerType() >= 0) {
			so.setStoreOwnerType(findStorePageDTO.getStoreOwnerType());
		}
		so.setProductSkuIds(findStorePageDTO.getProductSkuIds());
		// 是否限制产品范围
		so.setLimitSku(findStorePageDTO.getLimitSku());
		so.setHasRealStoreType(findStorePageDTO.getHasRealStoreType());
		// 库龄范围
		so.setStartStockAge(findStorePageDTO.getStartStockAge());
		so.setEndStockAge(findStorePageDTO.getEndStockAge());
		so.setCityId(findStorePageDTO.getCityId());
		so.setWarehouseAllocationType(findStorePageDTO.getWarehouseAllocationType());

		PageList<InventoryReportDTO> pageList = warehouseInventoryQueryBL.findStoreReportPageInfoByAuthNew(so,
				new PagerCondition(findStorePageDTO.getCurrentPage(), findStorePageDTO.getPageSize()));
		List<InventoryReportDTO> dataList = pageList.getDataList();
		if (!CollectionUtils.isEmpty(dataList)) {
			List<Integer> warehouseIds =
					dataList.stream().map(InventoryReportDTO::getWarhouseId).distinct().collect(Collectors.toList());
			List<CityMergeDTO> lstParam = new ArrayList<>();
			dataList.forEach(p -> {
				CityMergeDTO dto = new CityMergeDTO();
				dto.setProductOwnerId(p.getOwnerId());
				dto.setProductSpecificationId(p.getProductSpecificationId());
				lstParam.add(dto);
			});
			// List<CityMergeDTO> lstWaiting =
			// iOrderQueryService.getWaitingDeliveryStateSecCountByProductSkuIds(lstParam, warehouseIds.get(0));
			WareHouseDTO wareHouseDTO = iStoreWareHouseService.findWareHouseById(warehouseIds.get(0));
			// 合作商库存不查
			List<InventoryReportDTO> dataListWithOutPartner = ArrayUtils.deepCopy(dataList);
			dataListWithOutPartner.removeIf(t -> Objects.equals(OwnerTypeConst.合作商, t.getStoreOwnerType()));
			// 获取销售库存
			List<ProductOwnerInfoDTO> productOwnerDTOList =
					ProductOwnerDTOConvertor.convertByInventoryReport(dataListWithOutPartner);
			Map<String, BigDecimal> saleInventoryMap = getSaleInventoryMap(productOwnerDTOList, warehouseIds.get(0), findStorePageDTO.getCityId());
			Map<String, BigDecimal> mapWaiting = new HashMap<>();
			//// System.err.println("saleInventoryMap:" + GSON.toJson(saleInventoryMap));
			// if (!CollectionUtils.isEmpty(lstWaiting)) {
			// lstWaiting.forEach(p -> {
			// mapWaiting.put(String.format("%s-%s-%s", p.getProductSpecificationId(), p.getProductOwnerId(),
			//// p.getProductSecOwnerId()), p.getCount());
			// });
			// }
			// 在途库存
			Map<String, BigDecimal> allotDeliveryingCountMap = new HashMap<>();// getAllotDeliveryingCountMap(warehouseIds.get(0),
			// lstParam);

			// System.err.println("mapWaiting:" + GSON.toJson(mapWaiting));
			List<FindStoreInfoVO> convert = storeReportConvert.convert(dataList, wareHouseDTO, mapWaiting,
					saleInventoryMap, allotDeliveryingCountMap);
			setBoxCodeInfo(convert, findStorePageDTO.getCityId());
			result.setData(convert);
			result.setTotalCount(pageList.getPager().getRecordCount());
		}
		return result;
	}
}

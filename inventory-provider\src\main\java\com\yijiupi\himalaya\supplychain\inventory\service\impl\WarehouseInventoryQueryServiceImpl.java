package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.math.BigDecimal;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.*;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductSpecCityStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductSkuQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductSkuZhaoShangBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ActualSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryQueryService;
import com.yijiupi.himalaya.supplychain.search.WarehouseInventoryReportSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductSpecCityListStoreSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductWarehouseListStoreItemSO;

/**
 * 仓库库存查询服务. Created by Lifeng on 2017/7/20.
 */
@Service(timeout = 500000)
public class WarehouseInventoryQueryServiceImpl implements IWarehouseInventoryQueryService {

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private ProductSkuZhaoShangBL productSkuZhaoShangBL;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseInventoryQueryServiceImpl.class);

    /**
     * 查询单个商品仓库库存数.
     */
    @Override
    public BigDecimal getWarehouseInventory(Long productSkuId, Integer warehouseId, Integer channel, Long secOwnerId) {
        AssertUtils.notNull(productSkuId, "产品SKUID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notNull(channel, "渠道ID不能为空");
        return warehouseInventoryQueryBL
            .getWarehouseInventory(Collections.singletonList(productSkuId), warehouseId, channel, secOwnerId)
            .get(productSkuId);
    }

    /**
     * 查询多个商品仓库库存数.
     */
    @Override
    public Map<Long, BigDecimal> getWarehouseInventoryList(List<Long> productSkuIds, Integer warehouseIdList,
        Integer channel, Long secOwnerId) {
        AssertUtils.notEmpty(productSkuIds, "产品SKUID集合不能为空");
        AssertUtils.notNull(warehouseIdList, "仓库ID不能为空");
        AssertUtils.notNull(channel, "渠道ID不能为空");
        return warehouseInventoryQueryBL.getWarehouseInventory(productSkuIds, warehouseIdList, channel, secOwnerId);
    }

    // /**
    // * 查询一个产品在多个仓库下的库存数量 k(仓库id)-v(库存数量)
    // */
    // @Override
    // public Map<Integer, BigDecimal> getWarehouseInventoryMap(Long productSkuId, List<Integer> warehouseId, Integer
    // channel, Long secOwnerId) {
    // AssertUtils.notNull(productSkuId, "产品SKUID不能为空");
    // AssertUtils.notEmpty(warehouseId, "仓库ID集合不能为空");
    // AssertUtils.notNull(channel, "渠道ID不能为空");
    // return warehouseInventoryQueryBL.getWarehouseInventory(productSkuId, warehouseId, channel, secOwnerId);
    // }

    // /**
    // * 查询单个商品在对应城市下所有仓库库存数总和
    // */
    // @Override
    // public BigDecimal getSumWarehouseInventory(Long productSkuId, Integer cityId, Integer channel, Long secOwnerId) {
    // AssertUtils.notNull(productSkuId, "产品SKUID不能为空");
    // AssertUtils.notNull(cityId, "城市ID不能为空");
    // AssertUtils.notNull(channel, "渠道ID不能为空");
    // return warehouseInventoryQueryBL.getSumWarehouseInventory(productSkuId, cityId, channel, secOwnerId);
    // }

    // /**
    // * 查询多个商品在对应城市下的仓库库存数总和(sku就是在某一个城市下)
    // */
    // @Override
    // public Map<Long, BigDecimal> getCityWarehouseInventory(Set<Long> productSkuIds, Integer channel, Long secOwnerId)
    // {
    // AssertUtils.notEmpty(productSkuIds, "SKUID集合不能为空");
    // AssertUtils.notNull(channel, "渠道ID不能为空");
    // return warehouseInventoryQueryBL.getCityWarehouseInventory(productSkuIds, channel, secOwnerId);
    // }

    /**
     * 获取产品信息对应城市库存
     */
    @Override
    public List<ProductSpecCityStoreDTO> findProductSpecCityListStore(ProductSpecCityListStoreSO so) {
        if (so == null || so.getProductSpecId() == null || so.getCityIds().size() == 0) {
            throw new BusinessException("传入参数为空！");
        }
        AssertUtils.notNull(so.getChannel(), "渠道ID不能为空");
        return warehouseInventoryQueryBL.findProductSpecCityListStore(so);
    }

    /**
     * 获取产品SKU信息对应仓库库存(productSkuId,warehouseId)
     */
    @Override
    public List<ProductWarehouseStoreDTO> findProductWarehouseListStore(List<ProductWarehouseListStoreItemSO> so) {
        if (so == null || so.size() == 0) {
            throw new BusinessException("传入参数为空！");
        }
        for (ProductWarehouseListStoreItemSO productWarehouseListStoreItemSO : so) {
            AssertUtils.notNull(productWarehouseListStoreItemSO.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(productWarehouseListStoreItemSO.getChannel(), "渠道ID不能为空");
            AssertUtils.notNull(productWarehouseListStoreItemSO.getProductSkuId(), "skuID不能为空");
            AssertUtils.notNull(productWarehouseListStoreItemSO.getSource(), "产品来源不能为空");
        }
        return warehouseInventoryQueryBL.findProductWarehouseListStore(so);
    }
    //
    // @Deprecated
    // @Override
    // public Map<Long, BigDecimal> getdeliveryCountList(List<Long> productSkuIds, Integer warehouseId, Integer channel)
    // {
    // AssertUtils.notEmpty(productSkuIds, "产品SKUID集合不能为空");
    // AssertUtils.notNull(warehouseId, "仓库ID不能为空");
    // AssertUtils.notNull(channel, "渠道ID不能为空");
    // return warehouseInventoryQueryBL.getdeliveryCountList(productSkuIds, warehouseId, channel);
    // }
    //
    // /**
    // * 根据skuId和仓库Id获取库存信息
    // */
    // @Override
    // public WarehouseStoreDTO getProductInventoryDetail(Long productSkuId, Integer warehouseId, Integer channel, Long
    // secOwnerId) {
    // AssertUtils.notNull(productSkuId, "skuId不能为空");
    // AssertUtils.notNull(warehouseId, "仓库ID不能为空");
    // AssertUtils.notNull(channel, "渠道ID不能为空");
    // return warehouseInventoryQueryBL.getProductInventoryDetail(productSkuId, warehouseId, channel, secOwnerId);
    // }

    /**
     * 根据skuId和仓库cityId获取库存信息
     */
    @Override
    public List<InventoryBySkuIdCityIdDTO> getProductInventoryBySkuIdCityId(List<ProductSkuForInventoryDTO> dtos) {
        if (dtos == null || dtos.size() == 0) {
            throw new DataValidateException("传入参数为空！");
        }
        return warehouseInventoryQueryBL.getProductInventoryBySkuIdCityId(dtos);
    }

    /**
     * 将普通skuId转换成招商skuId
     */
    @Override
    public Map<Long, Long> process2ZhaoShangSku(List<Long> productSkuIds, Integer CityId) {
        return warehouseInventoryQueryBL.process2ZhaoShangSku(productSkuIds, CityId);
    }

    // @Override
    // public Map<Long, ProductShopStoreReturnDTO> getProductInventoryStoreId(ProductSkuForInventoryShopDTO
    // productSkuForInventoryShopDTO) {
    // AssertUtils.notNull(productSkuForInventoryShopDTO.getWarehouseId(), "仓库ID不能为空");
    // AssertUtils.notNull(productSkuForInventoryShopDTO.getOwnerId(), "货主id不能为空");
    // productSkuForInventoryShopDTO.setOwnerType(OwnerTypeConst.合作商);
    // return warehouseInventoryQueryBL.getProductInventoryStoreId(productSkuForInventoryShopDTO);
    // }

    // @Override
    // public Map<String, Object> getProductInventoryStoreIdNew(ProductSkuForInventoryShopDTO
    // productSkuForInventoryShopDTO) {
    // AssertUtils.notNull(productSkuForInventoryShopDTO.getWarehouseId(), "仓库ID不能为空");
    // AssertUtils.notNull(productSkuForInventoryShopDTO.getOwnerId(), "货主id不能为空");
    // productSkuForInventoryShopDTO.setOwnerType(OwnerTypeConst.合作商);
    // return warehouseInventoryQueryBL.getProductInventoryStoreIdNew(productSkuForInventoryShopDTO);
    // }

    @Override
    public Map<Long, Long> getActualDeliverySkuIdMap(List<Long> productSkuIds, Integer warehouseId,
        Integer deliveryCityId) {
        AssertUtils.notEmpty(productSkuIds, "SKUID集合不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notNull(deliveryCityId, "发货城市ID不能为空");
        return productSkuZhaoShangBL.getActualDeliverySkuIdMap(productSkuIds, warehouseId, deliveryCityId);
    }

    private static final int MAX_PARAM_SIZE = 2000;

    /**
     * 根据规格Id+OwnerId查询仓库库存-关联SKU
     *
     * @return
     */
    @Override
    public List<WarehouseStoreDTO> getProductInventorys(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        AssertUtils.notNull(wareHoseInventoryQueryDTO.getWarehouseId(), "仓库Id查询参数不能为空");
        AssertUtils.notEmpty(wareHoseInventoryQueryDTO.getSpecAndOwnerIds(), "规格Id和OwnerId查询参数不能为空");
        if (CollectionUtils.isNotEmpty(wareHoseInventoryQueryDTO.getSpecAndOwnerIds())) {
            if (wareHoseInventoryQueryDTO.getSpecAndOwnerIds().size() > MAX_PARAM_SIZE) {
                throw new BusinessException("分页条数不能大于2000！");
            }
        }
        return warehouseInventoryQueryBL.getProductInventorys(wareHoseInventoryQueryDTO);
    }

    /**
     * 分页查询仓库库存-关联SKU
     *
     * @return
     */
    @Override
    public PageList<WarehouseStoreDTO>
        getProductInventorysByPager(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        AssertUtils.notNull(wareHoseInventoryQueryDTO.getWarehouseId(), "仓库Id查询参数不能为空");
        if (CollectionUtils.isNotEmpty(wareHoseInventoryQueryDTO.getSpecAndOwnerIds())) {
            if (wareHoseInventoryQueryDTO.getSpecAndOwnerIds().size() > MAX_PARAM_SIZE) {
                throw new BusinessException("分页条数不能大于2000！");
            }
        }
        LOG.info("查询入参为:{}", JSON.toJSONString(wareHoseInventoryQueryDTO));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getProductInventoryByPager");
        PageList<WarehouseStoreDTO> result =
            warehouseInventoryQueryBL.getProductInventoryByHandPager(wareHoseInventoryQueryDTO);
        stopWatch.stop();
        if (stopWatch.getTotalTimeSeconds() >= 30) {
            LOG.error("查询时间过长:{}", stopWatch.prettyPrint());
        }
        return result;
        // AssertUtils.notNull(wareHoseInventoryQueryDTO.getWarehouseId(), "仓库Id查询参数不能为空");
        // if (CollectionUtils.isNotEmpty(wareHoseInventoryQueryDTO.getSpecAndOwnerIds())) {
        // if (wareHoseInventoryQueryDTO.getSpecAndOwnerIds().size() > MAX_PARAM_SIZE) {
        // throw new BusinessException("分页条数不能大于2000！");
        // }
        // }
        // wareHoseInventoryQueryDTO.setPageSize(1000);
        // PageList<WarehouseStoreDTO> returnList = new PageList<>();
        // // 遍历剩余的页数
        // Integer totalPage = 1;
        // int pageSize = 1000;
        // List<WarehouseStoreDTO> warehouseStoreDTOList = new ArrayList<>();
        // for (int i = 1; i <= totalPage; i++) {
        // if (i == 1) {
        // PageHelper.startPage(i, pageSize);
        // } else {
        // PageHelper.startPage(i, pageSize, false);
        // }
        // wareHoseInventoryQueryDTO.setCurrentPage(i);
        // PageList<WarehouseStoreDTO> pagerResult =
        // warehouseInventoryQueryBL.getProductInventorysByPager(wareHoseInventoryQueryDTO);
        // if (pagerResult != null && pagerResult.getDataList() != null && pagerResult.getDataList().size() > 0) {
        // if (i == 1) {
        // totalPage = pagerResult.getPager().getTotalPage();
        // returnList.setPager(pagerResult.getPager());
        // }
        // warehouseStoreDTOList.addAll(pagerResult.getDataList());
        // }
        // }
        // returnList.setDataList(warehouseStoreDTOList);
        // return returnList;

    }

    /**
     * 根据规格Id+OwnerId查询仓库库存-不关联SKU
     *
     * @return
     */
    @Override
    public PageList<WarehouseStoreDTO>
        getProductInventoryBySpecIds(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        AssertUtils.notNull(wareHoseInventoryQueryDTO.getWarehouseId(), "仓库Id查询参数不能为空");
        AssertUtils.notEmpty(wareHoseInventoryQueryDTO.getSpecAndOwnerIds(), "规格Id和OwnerId查询参数不能为空");
        if (CollectionUtils.isNotEmpty(wareHoseInventoryQueryDTO.getSpecAndOwnerIds())) {
            if (wareHoseInventoryQueryDTO.getSpecAndOwnerIds().size() > MAX_PARAM_SIZE) {
                throw new BusinessException("分页条数不能大于2000！");
            }
        }
        return warehouseInventoryQueryBL
            .getProductInventorysFromProductInfoSpecificationByPager(wareHoseInventoryQueryDTO);
    }

    /**
     * 产品查询
     */
    @Override
    public PageList<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO) {
        return productSkuQueryBL.listProductSkuInfo(productSkuInfoSO);
    }

    /**
     * 查询产品SKU基本信息
     */
    @Override
    public PageList<ProductSkuInfoDTO> findProductBaseInfo(ProductSkuInfoSO productSkuInfoSO) {
        return productSkuQueryBL.findProductBaseInfo(productSkuInfoSO);
    }

    /**
     * 查询产品SKU基本信息
     */
    @Override
    public PageList<ProductSkuInfoDTO> findProductBaseInfoByStoreCheck(ProductSkuInfoSO productSkuInfoSO) {
        return productSkuQueryBL.findProductBaseInfoByStoreCheck(productSkuInfoSO);
    }

    /**
     * 批量查询库存（酒批和大宗）
     *
     * @param productSkuIdList
     * @param warehouseId
     * @return
     */
    @Override
    public Map<Long, ProductStoreDTO> listProductInventoryBySkuIds(List<Long> productSkuIdList, Integer warehouseId) {
        return warehouseInventoryQueryBL.listProductInventoryBySkuIds(productSkuIdList, warehouseId);
    }

    /**
     * 批量查询库存（包含供应商） key:-产品skuid value - 自己与供应商库存实体
     */
    @Override
    public Map<Long, List<ProductInventoryDTO>> findProductInventoryIncludeSupplierBySkuIds(List<Long> productSkuIdList,
        Integer warehouseId) {
        return warehouseInventoryQueryBL.findProductInventoryIncludeSupplierBySkuIds(productSkuIdList, warehouseId);
    }

    /**
     * 查找仓库库存（易经销）
     *
     * @param warehouseInventoryYJXQueryDTO
     * @return
     */
    @Override
    public PageList<WarehouseInventoryYJXDTO>
        listWarehouseInventoryYJX(WarehouseInventoryYJXQueryDTO warehouseInventoryYJXQueryDTO) {
        return warehouseInventoryQueryBL.listWarehouseInventoryYJX(warehouseInventoryYJXQueryDTO);
    }

    /**
     * 获取已下单未发货的数量（待出库数量） key: %s-%s（规格id + ownerId） value: 待出库数量
     */
    @Override
    public Map<String, BigDecimal> getWaitDeliveryCountMap(Integer cityId, Integer warehouseId,
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        return warehouseInventoryQueryBL.getWaitDeliveryCountMap(cityId, warehouseId, specAndOwnerIds);
    }

    /**
     * 获取待出库产品列表
     */
    @Override
    public PageList<ProductWaitDeliveryDTO> listProductWaitDelivery(ProductWaitDeliverySO productWaitDeliverySO) {
        AssertUtils.notNull(productWaitDeliverySO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productWaitDeliverySO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notEmpty(productWaitDeliverySO.getSkuIds(), "产品skuId不能为空");
        return warehouseInventoryQueryBL.listProductWaitDelivery(productWaitDeliverySO);
    }

    /**
     * 仓库库存报表
     *
     * @return
     */
    @Override
    public PageList<WarehouseInventoryReportDTO>
        listWarehouseInventoryReport(WarehouseInventoryReportSO warehouseInventoryReportSO) {
        return warehouseInventoryQueryBL.listWarehouseInventoryReport(warehouseInventoryReportSO);
    }

    /**
     * 动销产品查询
     *
     * @param queryDTO
     * @return
     */
    @Override
    public PageList<DynamicProductDTO> findDynamicProductList(DynamicProductQueryDTO queryDTO) {
        return warehouseInventoryQueryBL.findDynamicProductList(queryDTO);
    }

    @Override
    public Map<Long, Long> getActualDeliverySkuIdS(List<Long> productSkuIds, Integer cityId) {
        return productSkuQueryBL.getActualDeliverySkuIdS(productSkuIds, null, cityId);
    }

    @Override
    public Map<Long, Long> getNormalProductStateSkuIdS(List<Long> productSkuIds) {
        productSkuIds.removeIf(Objects::isNull);
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.EMPTY_MAP;
        }
        List<ActualSkuPO> actualSkuList = productSkuQueryBL.getNormalProductStateSkuIdS(productSkuIds);
        if (CollectionUtils.isEmpty(actualSkuList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, Long> result = new HashMap<>(16);
        actualSkuList.forEach(p -> {
            result.put(p.getOrderSkuId(), p.getActualDeliverySkuId());
        });
        return result;
    }

    /**
     * 获取产品关联的合并产品的库存总和（不包括当前查询产品本身的库存）
     *
     * @return
     */
    @Override
    public Map<Long, BigDecimal> getRefProductInventoryMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        return warehouseInventoryQueryBL.getRefProductInventoryMap(cityId, warehouseId, productSkuIds);
    }

    /**
     * 获取产品的可用库存（仓库库存-已分波次数量）
     *
     * @return
     */
    @Override
    public Map<Long, BigDecimal> getEnableStoreCountMap(Integer cityId, Integer warehouseId, List<Long> skuIds) {
        return warehouseInventoryQueryBL.getEnableStoreCountMap(cityId, warehouseId, skuIds);
    }

    /**
     * 根据库存ID获取库存信息
     *
     * @return
     */
    @Override
    public ProductStoreBaseDTO getProductStoreById(String id) {
        return warehouseInventoryQueryBL.getProductStoreById(id);
    }

    /**
     * 查看产品及关联产品的销售库存
     *
     * @return
     */
    @Override
    public List<ProductRelateSaleStoreDTO> getProductRelateSaleStoreMap(ProductRelateSaleStoreSO productRelateStoreSO) {
        return warehouseInventoryQueryBL.getProductRelateSaleStoreMap(productRelateStoreSO);
    }

    /**
     * 根据sku获取产品和其关联产品总库存数量(包含产品和关联产品库存数)
     *
     * @param productSkuIdList 产品skuId
     * @param warehouseId 仓库ID
     * @return 产品对应的库存（库存 = 产品自身库存 + 关联产品库存）
     */
    @Override
    public Map<Long, BigDecimal> genProductAndRefProductTotalStore(List<Long> productSkuIdList, Integer warehouseId) {
        return warehouseInventoryQueryBL.genProductAndRefProductTotalStore(productSkuIdList, warehouseId);
    }

    /**
     * 根据规格+货主查询仓库库存
     */
    @Override
    public List<ProductStoreBaseDTO> findProductStoreBySpec(ProductStoreQueryDTO productStoreQueryDTO) {
        AssertUtils.notEmpty(productStoreQueryDTO.getSpecAndOwnerIds(), "规格货主信息不能为空");
        return warehouseInventoryQueryBL.findProductStoreBySpec(productStoreQueryDTO);
    }

    /**
     * 查询经销商在指定仓库是否有库存
     *
     * @return
     */
    @Override
    public Boolean isExistProductStoreByOwner(Integer warehouseId, Long ownerId) {
        return warehouseInventoryQueryBL.isExistProductStoreByOwner(warehouseId, ownerId);
    }

    /**
     * 分页查出动盘产品
     *
     * @return
     */
    @Override
    public PageList<DynamicProductDTO> findDynamicProductPageList(DynamicProductQueryDTO queryDTO) {
        return warehouseInventoryQueryBL.findDynamicProductPageList(queryDTO);
    }

    /**
     * 查询未开启货位库存动盘产品
     */
    @Override
    public List<NotOpenStockDailyPickingProductDTO>
        findNotOpenStockDynamicProductList(NotOpenStockDailyPickingProductQueryDTO queryDTO) {
        return warehouseInventoryQueryBL.findNotOpenStockDynamicProductList(queryDTO);
    }

    @Override
    public List<WarehouseStoreDTO> getDisposedProductInventories(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        return warehouseInventoryQueryBL.getDisposedProductInventories(wareHoseInventoryQueryDTO);
    }

    /**
     * 根据规格+货主查找对应的二级货主
     *
     * @return
     */
    @Override
    public Map<String, List<Long>> getSecOwnerIdMap(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO) {
        AssertUtils.notNull(productSecOwnerIdQueryDTO, "参数不能为空");
        AssertUtils.notNull(productSecOwnerIdQueryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(productSecOwnerIdQueryDTO.getSpecAndOwnerIds(), "规格货主不能为空");
        return warehouseInventoryQueryBL.getSecOwnerIdMap(productSecOwnerIdQueryDTO);
    }

    /**
     * 查询预占库存
     */
    @Override
    public List<WarehouseStoreDTO> getPreemptProductInventories(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        AssertUtils.notNull(wareHoseInventoryQueryDTO.getCityId(), "城市D不能为空");
        AssertUtils.notNull(wareHoseInventoryQueryDTO.getWarehouseId(), "仓库ID不能为空");
        return warehouseInventoryQueryBL.getPreemptProductInventories(wareHoseInventoryQueryDTO);
    }

    @Override
    public List<ProductStoreBaseDTO> findProductStoreByIds(List<String> ids) {
        AssertUtils.notEmpty(ids, "参数不能为空");
        return warehouseInventoryQueryBL.findProductStoreByIds(ids);
    }

    /**
     * 根据中台Sku+仓库查询仓库库存
     *
     * @param productStoreQueryDTO
     * @return
     */
    @Override
    public List<ProductStoreBaseDTO> findProductStoreByUnifySkuId(ProductStoreQueryDTO productStoreQueryDTO) {
        return warehouseInventoryQueryBL.findProductStoreByUnifySkuId(productStoreQueryDTO);
    }

    @Override
    public PageList<WarehouseInventoryReportDTO> findProductInWarehouseInventory(WarehouseInventoryReportSO querySO) {
        AssertUtils.notNull(querySO, "参数不能为空");
        return warehouseInventoryQueryBL.findProductInWarehouseInventory(querySO);
    }

    @Override
    public List<ProductStoreBaseDTO> findStoreBySpecOwner(ProductStoreQueryDTO productStoreQueryDTO) {
        AssertUtils.notEmpty(productStoreQueryDTO.getSpecAndOwnerIds(), "规格货主信息不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getCityId() == null ? productStoreQueryDTO.getWarehouseId() : null,
            "城市或仓库不能为空");
        return warehouseInventoryQueryBL.findStoreBySpecOwner(productStoreQueryDTO);
    }

    @Override
    public Boolean isHaveWarehouseInventory(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        return warehouseInventoryQueryBL.isHaveWarehouseInventory(warehouseId);
    }

    @Override
    public PageList<WarehouseStoreDTO> listProductStoreBySkuInfo(WarehouseInventoryReportSO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        return warehouseInventoryQueryBL.listProductStoreBySkuInfo(queryDTO);
    }
}

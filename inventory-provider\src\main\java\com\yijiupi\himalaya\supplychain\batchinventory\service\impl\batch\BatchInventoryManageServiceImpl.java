package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.aspect.BatchInventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryManageBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory.BatchTaskItemCompleteTransferBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.BatchInventoryEventFireBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.model.CCPConfigByAllocationDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryPriceDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.DefectiveInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.listener.SupplychainStoreChangeListener;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.dto.WarehouseConfigDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ReplenishmentTaskItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.erp.ErpDefectiveLimitBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.WarehouseDefectiveProductResultDTO;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductInfoQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2018/3/31
 */
@Service(timeout = 60000)
public class BatchInventoryManageServiceImpl implements IBatchInventoryManageService {

    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventoryManageServiceImpl.class);

    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;

    @Autowired
    private ErpDefectiveLimitBL erpDefectiveLimitBL;
    @Autowired
    private SupplychainStoreChangeListener supplychainStoreChangeListener;
    @Autowired
    private BatchTaskItemCompleteTransferBL batchTaskItemCompleteTransferBL;

    @Autowired
    private BatchInventoryEventFireBL batchInventoryEventFireBL;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Autowired
    private BatchInventorySendFaildMQ batchInventorySendFaildMQ;

    @Reference
    private IProductInfoQueryService productInfoQueryService;

    /**
     * 拣货移库校验-新
     *
     * @param pickUpDTOList
     * @return
     */
    @Override
    public List<PickUpDTO> checkInventoryTransferForPickUp(List<PickUpDTO> pickUpDTOList) {
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        List<PickUpDTO> realPickUpDTOList = new ArrayList<>();
        batchTaskItemCompleteTransferBL.checkInventoryTransferForPickUp(pickUpDTOList, true, false, realPickUpDTOList);
        return realPickUpDTOList;
    }

    /**
     * 拣货校验移库
     */
    @Override
    public List<PickUpDTO> checkInventoryTransferByPickUp(List<PickUpDTO> pickUpDTOList) {
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        List<PickUpDTO> realPickUpDTOList = new ArrayList<>();
        batchInventoryManageBL.checkInventoryTransfer(pickUpDTOList, true, false, realPickUpDTOList);
        return realPickUpDTOList;
    }

    /**
     * 拣货完成(按产品拣货) 从存储区移到周转区
     *
     * @param pickUpDTOList
     */
    @Override
    public List<PickUpDTO> pickupCompleteBySku(List<PickUpDTO> pickUpDTOList,
                                               PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("拣货完成:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = false;
        Integer orgId = null;
        if (warehouseId != null) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
            isOpenStock = warehouseConfigDTO.getIsOpenLocationStock();
            orgId = warehouseConfigDTO.getOrg_Id();
        }
        if (!isOpenStock) {
            return Collections.EMPTY_LIST;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        // 返回实际移库数量
        List<PickUpDTO> realPickUpDTOList =
                batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO, true, false);

        // 触发补货机制
        List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS = new ArrayList<>();
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            ReplenishmentTaskItemDTO replenishmentTaskItemDTO = new ReplenishmentTaskItemDTO();
            replenishmentTaskItemDTO.setOrgId(orgId);
            replenishmentTaskItemDTO.setWarehouseId(pickUpDTO.getWarehouseId());
            replenishmentTaskItemDTO.setProductSkuId(pickUpDTO.getProductSkuId());
            replenishmentTaskItemDTO.setToLocationId(pickUpDTO.getFromLocationId());

            replenishmentTaskItemDTOS.add(replenishmentTaskItemDTO);
        }
        batchInventoryEventFireBL.sendAddReplenishmentEvent(replenishmentTaskItemDTOS);

        return realPickUpDTOList;
    }

    /**
     * 完成拣货任务明细
     *
     * @param pickUpDTOList
     * @param pickUpChangeRecordDTO
     * @return
     */
    @Override
    public List<PickUpDTO> batchTaskItemComplete(List<PickUpDTO> pickUpDTOList,
                                                 PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("拣货完成新:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = false;
        Integer orgId = null;
        if (warehouseId != null) {
            WarehouseConfigDTO warehouseConfigDTO = warehouseConfigService.getConfigByWareHouseId(warehouseId);
            isOpenStock = warehouseConfigDTO.getIsOpenLocationStock();
            orgId = warehouseConfigDTO.getOrg_Id();
        }
        if (!isOpenStock) {
            return Collections.emptyList();
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "拣货数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        // 返回实际移库数量
        List<PickUpDTO> realPickUpDTOList =
                batchTaskItemCompleteTransferBL.batchTaskItemComplete(pickUpDTOList, pickUpChangeRecordDTO, true, false);

        // 触发补货机制
        List<ReplenishmentTaskItemDTO> replenishmentTaskItemDTOS = new ArrayList<>();
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            ReplenishmentTaskItemDTO replenishmentTaskItemDTO = new ReplenishmentTaskItemDTO();
            replenishmentTaskItemDTO.setOrgId(orgId);
            replenishmentTaskItemDTO.setWarehouseId(pickUpDTO.getWarehouseId());
            replenishmentTaskItemDTO.setProductSkuId(pickUpDTO.getProductSkuId());
            replenishmentTaskItemDTO.setToLocationId(pickUpDTO.getFromLocationId());

            replenishmentTaskItemDTOS.add(replenishmentTaskItemDTO);
        }
        batchInventoryEventFireBL.sendAddReplenishmentEvent(replenishmentTaskItemDTOS);

        return realPickUpDTOList;
    }

    /**
     * 上架移库
     *
     * @param pickUpDTOList
     */
    @Override
    public void batchInventoryTransferBySku(List<PickUpDTO> pickUpDTOList,
                                            PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("上架移库:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "上架拣货产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO, false, false);
    }

    /**
     * 上架批量【多个上架任务】移库
     */
    @Override
    public void putAwayTaskBatchPickUp(List<PickUpRecordDTO> pickUpChangeRecordList, Integer warehouseId) {
        LOGGER.info("上架批量【多个上架任务】移库参数 : {}", JSON.toJSONString(pickUpChangeRecordList));
        AssertUtils.notEmpty(pickUpChangeRecordList, "上架拣货产品不能为空");
        List<PickUpDTO> pickUpDTOS =
                pickUpChangeRecordList.stream().filter(e -> e != null && CollectionUtils.isNotEmpty(e.getPickUpDTOList()))
                        .flatMap(e -> e.getPickUpDTOList().stream()).collect(Collectors.toList());
        AssertUtils.notEmpty(pickUpDTOS, "上架批量【多个上架任务】移库信息不能为空！");
        for (PickUpDTO pickUpDTO : pickUpDTOS) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        batchInventoryManageBL.putAwayTaskBatchPickUp(pickUpChangeRecordList, warehouseId);
    }

    /**
     * 修改出库位
     */
    @Override
    public void updateLocationByCk(List<PickUpDTO> pickUpDTOList, PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        LOGGER.info("修改出库位:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "出库位产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getCount(), "移库数量不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "原出库位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "出库位不能为空");
        }
        batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO, true, true);
    }

    /**
     * 修改货位库存（只修改数量）
     */
    @Override
    public void updateProductStoreBatch(List<ProductStoreBatchDTO> productStoreBatchDTOS, String operaterUser) {
        AssertUtils.notNull(productStoreBatchDTOS, "参数不能为空");
        productStoreBatchDTOS.forEach(p -> {
            AssertUtils.notNull(p.getId(), "货位库存id不能为空");
            AssertUtils.notNull(p.getTotalCount(), "货位库存数量不能为空");
            AssertUtils.notNull(p.getBatchAttributeInfoNo(), "批次号不能为空");
        });
        batchInventoryManageBL.updateProductStoreBatch(productStoreBatchDTOS, operaterUser);
    }

    /**
     * 批次库存异常重试
     *
     * @param json
     */
    @Override
    public void processBatchInventoryByJson(String json) {
        LOGGER.info("批次库存异常重试参数：{}", JSON.toJSONString(json));
        List<ProductInventoryChangeRecordPO> recordPOList = JSON.parseArray(json, ProductInventoryChangeRecordPO.class);
        try {
            supplychainStoreChangeListener.processStoreChangeMsg(recordPOList);
        } catch (Exception e) {
            LOGGER.error("批次库存异常重试异常，错误信息：", e);
            batchInventorySendFaildMQ.mqSendFaild(JSON.toJSONString(recordPOList), "mq.supplychain.inventory.productstorechange", e);
        }
    }

    /**
     * 移库
     *
     * @param pickUpDTOList
     */
    @Override
    public List<PickUpDTO> batchInventoryTransfer(List<PickUpDTO> pickUpDTOList,
                                                  PickUpChangeRecordDTO pickUpChangeRecordDTO, BatchInventoryTransferCheckDTO batchInventoryTransferCheckDTO) {
        LOGGER.info("移库:{}", JSON.toJSONString(pickUpDTOList));
        AssertUtils.notEmpty(pickUpDTOList, "产品不能为空");
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId == null ? false : warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }
        for (PickUpDTO pickUpDTO : pickUpDTOList) {
            AssertUtils.notNull(pickUpDTO.getProductSkuId(), "skuId不能为空");
            AssertUtils.notNull(pickUpDTO.getFromChannel(), "来源渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getToChannel(), "目标渠道不能为空");
            AssertUtils.notNull(pickUpDTO.getFromSource(), "from产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getToSource(), "to产品来源不能为空");
            AssertUtils.notNull(pickUpDTO.getFromLocationId(), "来源货位不能为空");
            AssertUtils.notNull(pickUpDTO.getLocationId(), "目标货位不能为空");
        }
        return batchInventoryManageBL.batchInventoryTransferBySku(pickUpDTOList, pickUpChangeRecordDTO,
                batchInventoryTransferCheckDTO.getIgnoreProductionDate(),
                batchInventoryTransferCheckDTO.getIgnoreHasNotEnoughStore());
    }

    @Override
    public List<BatchInventoryInfoUpdateDTO>
    updateBatchInventoryInfo(List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS) {
        return batchInventoryManageBL.updateBatchInventoryInfo(batchInventoryInfoUpdateDTOS, false);
    }

    @Override
    public void processLocationInventory(ProductInventoryChangeDTO productInventoryChangeDTO) {
        AssertUtils.notNull(productInventoryChangeDTO, "参数不能为空");
        AssertUtils.notNull(productInventoryChangeDTO.getProductStoreId(), "库存id不能为空");
        batchInventoryManageBL.processLocationInventory(productInventoryChangeDTO);
    }

    /**
     * 未开启货位库存变更产品批次属性
     */
    @Override
    public void nonOpenStockChangeBatchInventoryByStoreCheck(StoreCheckUpdateBatchInventoryDTO changeDTO) {
        batchInventoryManageBL.nonOpenStockChangeBatchInventoryByStoreCheck(changeDTO);
    }

    @Override
    public void clearBeforeProductionDate(List<Integer> warehouseIdList, Date endProductionDate) {
        AssertUtils.notEmpty(warehouseIdList, "仓库ID不能为空");
        AssertUtils.notNull(endProductionDate, "截止生产日期不能为空");
        batchInventoryManageBL.clearBeforeProductionDate(warehouseIdList, endProductionDate);
    }

    @Override
    public DefectiveInventoryPriceDTO calCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
                                                            Map<Long, BigDecimal> skuCount) {
        DefectiveInventoryPriceDTO resultDTO = new DefectiveInventoryPriceDTO();
        CCPAmountCheckDTO ccpAmountCheckDTO = new CCPAmountCheckDTO();
        ccpAmountCheckDTO.setSkuCount(skuCount);
        ccpAmountCheckDTO.setOrgId(orgId);
        ccpAmountCheckDTO.setWarehouseId(warehouseId);
        List<DefectiveInventoryResultDTO> defectiveInventoryResultDTO = batchInventoryManageBL.calCcpPriceNew(ccpAmountCheckDTO);
        if (CollectionUtils.isNotEmpty(defectiveInventoryResultDTO)) {
            BigDecimal maxAmount = defectiveInventoryResultDTO.stream()
                    .map(DefectiveInventoryResultDTO::getTotalAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalAmount = defectiveInventoryResultDTO.stream()
                    .map(DefectiveInventoryResultDTO::getCalculateDefectiveAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            resultDTO.setMaxAmount(maxAmount);
            resultDTO.setTotalAmount(totalAmount);
            LOGGER.info("残次品金额，计算结果：{}", JSON.toJSONString(resultDTO));
            String strBusinessName = defectiveInventoryResultDTO.stream()
                    .filter(p -> p.getTotalAmount().compareTo(BigDecimal.ZERO) == 0)
                    .map(DefectiveInventoryResultDTO::getBusinessDivision)
                    .collect(Collectors.joining(","));
            if (StringUtils.isNotEmpty(strBusinessName)) {
                throw new BusinessValidateException(strBusinessName + "事业部残次品额度为0，请联系技术支持！");
            }

            // 使用ERP返回的事业部额度进行校验
            if (resultDTO.getTotalAmount().compareTo(resultDTO.getMaxAmount()) > 0) {
                // 按仓库配置限额进行二次校验
                if (!checkCcpPriceByWarehouseConfig(warehouseId, resultDTO, defectiveInventoryResultDTO)) {
                    strBusinessName = defectiveInventoryResultDTO.stream()
                            .filter(p -> p.getCalculateDefectiveAmount().compareTo(p.getTotalAmount()) > 0)
                            .map(DefectiveInventoryResultDTO::getBusinessDivision)
                            .collect(Collectors.joining(","));
                    throw new BusinessValidateException(strBusinessName + "事业部残次品金额超限，请先清理完残次品后再试！");
                }
            }
        }
        return resultDTO;
    }

    /**
     * 按仓库配置进行二次校验
     * @param warehouseId
     * @param resultDTO
     * @param defectiveInventoryResultDTO
     */
    private boolean checkCcpPriceByWarehouseConfig(Integer warehouseId, DefectiveInventoryPriceDTO resultDTO
            , List<DefectiveInventoryResultDTO> defectiveInventoryResultDTO) {
        boolean result = false;

        // 查询ERP当前所有事业部已用额度
        List<WarehouseDefectiveProductResultDTO> erpList = erpDefectiveLimitBL.getErpDefectiveList(warehouseId);

        // 当前产品额度
        BigDecimal currentAmount = resultDTO.getTotalAmount();

        // 判断是否开启分仓
        boolean isOpenAllocation = batchInventoryManageBL.isOpenAllocation(warehouseId);

        BigDecimal usedAmount;
        BigDecimal totalLimit;

        if (!isOpenAllocation) {
            // 未开启分仓：已用额度 = ERP所有事业部已使用额度累加
            usedAmount = erpList.stream()
                    .map(WarehouseDefectiveProductResultDTO::getUsedQuota)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 总额度 = 仓库总额度配置
            totalLimit = batchInventoryManageBL.getMaxAmountConfig(warehouseId);

            LOGGER.info("[残次品金额]未开启分仓校验，仓库ID：{}，ERP已用额度：{}，当前产品额度：{}，仓库总额度：{}",
                    warehouseId, usedAmount, currentAmount, totalLimit);
        } else {
            // 开启分仓：提取当前转入的事业部
            Set<String> currentDivisions = defectiveInventoryResultDTO.stream()
                    .map(DefectiveInventoryResultDTO::getBusinessDivision)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());

            // 已用额度 = ERP中相关事业部的已用额度累加
            usedAmount = erpList.stream()
                    .filter(erp -> currentDivisions.contains(erp.getBusinessDivision()))
                    .map(WarehouseDefectiveProductResultDTO::getUsedQuota)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 总额度 = 包含当前事业部的分仓配置额度累加
            List<CCPConfigByAllocationDTO> allocationConfigs = batchInventoryManageBL.getAllocationMaxAmountConfig(warehouseId);
            totalLimit = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(allocationConfigs)) {
                for (CCPConfigByAllocationDTO config : allocationConfigs) {
                    if (CollectionUtils.isNotEmpty(config.getDept()) && config.getDept().stream().anyMatch(currentDivisions::contains)) {
                        totalLimit = totalLimit.add(config.getValue());
                    }
                }
            }

            LOGGER.info("[残次品金额]开启分仓校验，仓库ID：{}，涉及事业部：{}，ERP已用额度：{}，当前产品额度：{}，分仓总额度：{}，分仓配置：{}",
                    warehouseId, currentDivisions, usedAmount, currentAmount, totalLimit, JSON.toJSONString(allocationConfigs));
        }

        // 统一校验逻辑：已用额度 + 当前产品额度 ≤ 总额度
        if (totalLimit != null && totalLimit.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalAfterTransfer = usedAmount.add(currentAmount);
            if (totalAfterTransfer.compareTo(totalLimit) > 0) {
                LOGGER.info("[残次品金额]二次校验失败，仓库ID：{}，已用额度：{}，当前产品额度：{}，总额度：{}",
                        warehouseId, usedAmount, currentAmount, totalLimit);
            } else {
                result = true;
                LOGGER.info("[残次品金额]二次校验通过，仓库ID：{}，已用额度：{}，当前产品额度：{}，总额度：{}",
                        warehouseId, usedAmount, currentAmount, totalLimit);
            }
        }
        return result;
    }

    @Override
    public DefectiveInventoryPriceDTO saveCcpPriceByInventory(Integer orgId, Integer warehouseId) {
        return new DefectiveInventoryPriceDTO();
    }

    @Override
    public DefectiveInventoryPriceDTO saveCcpPriceBySkuCount(Integer orgId, Integer warehouseId,
                                                             Map<Long, BigDecimal> skuCount) {
        return new DefectiveInventoryPriceDTO();
    }

    @Override
    public DefectiveInventoryPriceDTO getCcpPrice(Integer orgId, Integer warehouseId) {
        return new DefectiveInventoryPriceDTO();
    }

    @Override
    public void updateProductionDateBySpec(BatchInventoryDTO updateDTO) {
        batchInventoryManageBL.updateProductionDateBySpec(updateDTO);
    }

    @Override
    public void clearBeforeProductionDateUtil(BatchInventoryDTO updateDTO) {
        batchInventoryManageBL.clearBeforeProductionDate(Arrays.asList(updateDTO.getWarehouseId()),
                updateDTO.getProductionDate());
    }
}

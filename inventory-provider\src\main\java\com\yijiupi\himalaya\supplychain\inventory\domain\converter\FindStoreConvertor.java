package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.inventory.dto.FindStoreInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLocationDetailDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;

/**
 * 转换类加抽出的方法
 * 
 * @author: chengkai
 * @date: 2022年9月6日
 */
@Component
public class FindStoreConvertor extends ConvertUtils<InventoryReportDTO, FindStoreInfoDTO> {

    @Reference
    private IProductLocationService iProductLocationService;

    @Override
    public FindStoreInfoDTO convert(InventoryReportDTO m) {
        FindStoreInfoDTO findStoreInfoVO = new FindStoreInfoDTO();
        if (m != null) {
            findStoreInfoVO.setSequence(m.getSequence());
            findStoreInfoVO.setProductStoreId(m.getProductStoreId());
            findStoreInfoVO.setProductSkuName(m.getProductSkuName());
            findStoreInfoVO.setSpecificationName(m.getSpecificationName());
            findStoreInfoVO.setSupplierName(m.getSupplierName());
            findStoreInfoVO.setStoreTotalCount(m.getStoreTotalCount());
            findStoreInfoVO.setStoreCountMax(m.getStoreCountMax());
            findStoreInfoVO.setProductSkuId(m.getProductSkuId());
            findStoreInfoVO.setStoreCountMin(m.getStoreCountMin());
            findStoreInfoVO.setPackageName(m.getPackageName());
            findStoreInfoVO.setUnitName(m.getUnitName());
            findStoreInfoVO.setStoreWaringCount(m.getStoreWaringCount());
            // 只有零售渠道才加可销售库存
            if (m.getChannel() == 0) {
                findStoreInfoVO.setStoreSaleTotalCount(m.getStoreSaleCount());
            }
            findStoreInfoVO.setStorePreTotalCount(m.getStorePreCount());
            findStoreInfoVO.setWarehouseId(m.getWarhouseId());
            findStoreInfoVO.setChannel(m.getChannel());// 渠道
            findStoreInfoVO.setSpecQuantity(m.getPackageQuantity());
            if (m.getPackageQuantity() != null && m.getPackageQuantity().intValue() > 0) {
                if (m.getStorePreCount() != null) {
                    BigDecimal[] storePre = m.getStorePreCount().divideAndRemainder(m.getPackageQuantity());
                    findStoreInfoVO.setStorePreCountMin(storePre[1]);
                    findStoreInfoVO.setStorePreCountMax(storePre[0]);
                }
                if (m.getStoreSaleCount() != null) {
                    BigDecimal[] storeSale = m.getStoreSaleCount().divideAndRemainder(m.getPackageQuantity());
                    findStoreInfoVO.setStoreSaleCountMax(storeSale[0]);
                    findStoreInfoVO.setStoreSaleCountMin(storeSale[1]);
                }
                if (m.getStoreTotalCount() != null) {
                    BigDecimal[] countRemainder = m.getStoreTotalCount().divideAndRemainder(m.getPackageQuantity());
                    findStoreInfoVO.setStoreCountMax(countRemainder[0]);
                    findStoreInfoVO.setStoreCountMin(countRemainder[1]);
                }
            }
            findStoreInfoVO.setOwnerName(m.getOwnerName());
            findStoreInfoVO.setProductSpecificationId(m.getProductSpecificationId());
            findStoreInfoVO.setProductType(m.getProductType());
        }
        return findStoreInfoVO;
    }

    public List<FindStoreInfoDTO> convert(List<InventoryReportDTO> mList, WareHouseDTO wareHouseDTO,
        Map<String, BigDecimal> mapWaiting, Map<String, BigDecimal> saleInventoryMap) {
        List<FindStoreInfoDTO> nList = null;
        if (mList != null) {
            nList = new ArrayList<>(mList.size());
            for (InventoryReportDTO m : mList) {
                FindStoreInfoDTO vo = convert(m);
                if (wareHouseDTO != null) {
                    vo.setWarehouseName(wareHouseDTO.getName());
                }
                if (m.getStoreOwnerType() != null) {
                    String ownerTypeName = OwnerTypeConst.getEnmuName(m.getStoreOwnerType());
                    if (ownerTypeName == null) {
                        ownerTypeName = OwnerTypeConst.getEnmuName(OwnerTypeConst.易酒批);
                    }
                    vo.setStoreOwnerTypeName(ownerTypeName);
                }
                if (mapWaiting != null) {
                    vo.setUnDeliveryCount(
                        mapWaiting.get(String.format("%s%s", m.getProductSpecificationId(), m.getOwnerId())));
                }
                if (m.getDeliveryedCount() != null) {
                    vo.setDeliveryingCount(m.getDeliveryedCount());
                }
                if (vo.getUnDeliveryCount() != null && m.getPackageQuantity() != null) {
                    BigDecimal[] countRemainder = vo.getUnDeliveryCount().divideAndRemainder(m.getPackageQuantity());
                    vo.setUnDeliveryCountMax(countRemainder[0]);
                    vo.setUnDeliveryCountMin(countRemainder[1]);
                }
                if (vo.getDeliveryingCount() != null && m.getPackageQuantity() != null) {
                    BigDecimal[] countRemainder = vo.getDeliveryingCount().divideAndRemainder(m.getPackageQuantity());
                    vo.setDeliveryingCountMax(countRemainder[0]);
                    vo.setDeliveryingCountMin(countRemainder[1]);
                }
                // 销售库存
                BigDecimal saleTotalCount = saleInventoryMap
                    .get(String.format("%s-%s-%s", m.getProductSpecificationId(), m.getOwnerId(), m.getWarhouseId()));
                if (saleTotalCount != null && m.getPackageQuantity() != null) {
                    vo.setStoreSaleCountMax(saleTotalCount.divideAndRemainder(m.getPackageQuantity())[0]);
                    vo.setStoreSaleCountMin(saleTotalCount.divideAndRemainder(m.getPackageQuantity())[1]);
                }
                vo.setOwnerName(m.getOwnerName());
                vo.setOwnerId(m.getOwnerId());
                vo.setSecOwnerName(m.getSecOwnerName());
                vo.setSecOwnerId(m.getSecOwnerId());
                nList.add(vo);
            }
            // 初始化货位信息
            Map<Long, String> params = initProductLocationData(nList);
            // 设置货位信息
            for (FindStoreInfoDTO vo : nList) {
                vo.setProductLocation(params.get(vo.getProductSkuId()));
            }
        }
        return nList;
    }

    private Map<Long, String> initProductLocationData(List<FindStoreInfoDTO> nList) {
        if (nList == null || nList.isEmpty()) {
            return new HashMap<>(16);
        }
        Map<Integer, LocationQueryDTO> params = new HashMap<>(16);
        for (FindStoreInfoDTO vo : nList) {
            LocationQueryDTO queryDTO = params.get(vo.getWarehouseId());
            if (queryDTO == null) {
                queryDTO = new LocationQueryDTO();
                params.put(vo.getWarehouseId(), queryDTO);
            }
            queryDTO.setWarehouseId(vo.getWarehouseId());
            if (queryDTO.getSkuIdList() == null) {
                queryDTO.setSkuIdList(new ArrayList<>());
            }
            queryDTO.getSkuIdList().add(vo.getProductSkuId());
        }
        List<ProductLocationDetailDTO> totalLocationDTOList = new ArrayList<>(nList.size());
        for (LocationQueryDTO queryDTO : params.values()) {
            List<ProductLocationDetailDTO> locationDTOList =
                iProductLocationService.findProductLocationBySkuId(queryDTO);
            if (locationDTOList != null && !locationDTOList.isEmpty()) {
                totalLocationDTOList.addAll(locationDTOList);
            }
        }
        Map<Long, String> locationParams = new HashMap<>(16);
        for (ProductLocationDetailDTO model : totalLocationDTOList) {
            if (model != null) {
                locationParams.put(model.getProductSkuId(), model.getName());
            }
        }
        return locationParams;
    }

    @Override
    public InventoryReportDTO reverseConvert(FindStoreInfoDTO findStoreInfoDTO) {
        return null;
    }

}

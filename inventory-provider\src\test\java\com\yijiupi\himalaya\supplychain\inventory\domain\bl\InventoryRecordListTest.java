package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.CityInventoryRecordQueryDTO;

/**
 * Created by wang<PERSON> on 2017-09-30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryRecordListTest {
    @Autowired
    private ProductStoreChangeRecordBL productStoreChangeRecordBL;

    @Test
    public void listCityInventoryRecord() {
        CityInventoryRecordQueryDTO cityInventoryRecordQueryDTO = new CityInventoryRecordQueryDTO();
        cityInventoryRecordQueryDTO.setCityId(999);
        cityInventoryRecordQueryDTO.setProductSkuId(99900000176907L);
        List<String> idlist = new ArrayList<>();
        idlist.add("ef8c88468b254b44abd45b7b264c5d97");
        idlist.add("950407bd64c44d10a319ac3f673ead31");
        cityInventoryRecordQueryDTO.setProductInventoryIdList(idlist);
        PagerCondition pagerCondition = new PagerCondition();
        pagerCondition.setCurrentPage(1);
        // productStoreChangeRecordBL.findCityProductInventoryRecord(cityInventoryRecordQueryDTO, pagerCondition);
    }
}

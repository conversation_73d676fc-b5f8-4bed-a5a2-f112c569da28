package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * <AUTHOR> 2017/12/12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryKeyBLTest {
    // @Autowired
    // private InventoryKeyBL inventoryKeyBL;
    //
    // @Test
    // public void getStoreInventoryPO() {
    // ProductInventoryPO storeInventoryPO = inventoryKeyBL.getStoreInventoryPO(135151515125L, 9991, null);
    // }
}

package com.yijiupi.himalaya.supplychain.inventory.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.ShopWarehouseInventoryRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryModDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryTransfersDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.WarehouseInventoryManagerServiceImpl;

/**
 * 设置仓库库存
 *
 * <AUTHOR> 2017/12/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class IWarehouseInventoryManageServiceTest {

    @Autowired
    private WarehouseInventoryManagerServiceImpl modShopWarehouseInventory;

    @Test
    public void modShopWarehouseInventory() {
        WarehouseInventoryModDTO dto = new WarehouseInventoryModDTO();
        dto.setProductSkuId(99900050777475L);
        dto.setUnitCount(new BigDecimal(0));
        dto.setPackageCount(new BigDecimal(100));
        dto.setWarehouseId(999126);
        dto.setChannel(0);
        modShopWarehouseInventory.modShopWarehouseInventory(dto, 123);
    }

    @Test
    public void modWarehouseInventory() {
        WarehouseInventoryModDTO dto = new WarehouseInventoryModDTO();
        dto.setProductSkuId(99900050741294L);
        dto.setUnitCount(new BigDecimal(60));
        dto.setWarehouseId(999127);
        dto.setChannel(1);
        modShopWarehouseInventory.modWarehouseInventory(dto, 123);
    }

    @Test
    public void modTransfersWarehouseInventory() {
        // WarehouseInventoryTransfersDTO dto = new WarehouseInventoryTransfersDTO();
        // dto.setPackageCount(0);
        // dto.setUnitCount(1);
        // dto.setProductSkuId(9990050723541L);
        // dto.setWarehouseId(999129);
        // dto.setBeChannel(0);
        // dto.setChannel(1);
        String json =
            "{\"beChannel\":0,\"channel\":1,\"fromLocationId\":169675656907565098,\"packageCount\":0,\"productSkuId\":99900040884324,\"toLocationId\":169675656907562931,\"unitCount\":4000,\"warehouseId\":9991}";
        WarehouseInventoryTransfersDTO warehouseInventoryTransfersDTO =
            JSON.parseObject(json, WarehouseInventoryTransfersDTO.class);
        modShopWarehouseInventory.modTransfersWarehouseInventory(warehouseInventoryTransfersDTO, 56);
    }

    @Test
    public void modShopWarehouseInventory1() {
        List<ShopWarehouseInventoryRecordDTO> list = new ArrayList<>();
        ShopWarehouseInventoryRecordDTO dto1 = new ShopWarehouseInventoryRecordDTO();
        ShopWarehouseInventoryRecordDTO dto2 = new ShopWarehouseInventoryRecordDTO();
        ShopWarehouseInventoryRecordDTO dto3 = new ShopWarehouseInventoryRecordDTO();
        dto1.setWarehouseId(1001);
        dto1.setChannel(0);
        dto1.setProductSkuId(10000000003376L);
        dto1.setChangCount(new BigDecimal(100));

        dto2.setWarehouseId(1011);
        dto2.setChannel(0);
        dto2.setProductSkuId(10100000010475L);
        dto2.setChangCount(new BigDecimal(100));

        dto3.setWarehouseId(8971);
        dto3.setChannel(0);
        dto3.setProductSkuId(89700050771804L);
        dto3.setChangCount(new BigDecimal(100));
        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        // modShopWarehouseInventory.sendShopWarehouseInventoryRecord(list);
    }

    @Test
    public void getCityIdByWarehouseId() {

        Integer cityIdByWarehouseId = modShopWarehouseInventory.getCityIdByWarehouseId(9991);
        System.out.println(cityIdByWarehouseId);

    }

}

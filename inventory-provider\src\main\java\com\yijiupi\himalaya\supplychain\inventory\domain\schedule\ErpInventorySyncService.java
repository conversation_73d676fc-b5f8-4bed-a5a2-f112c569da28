/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ERP库存定时同步
 */
@Service
public class ErpInventorySyncService {

    @Reference
    private IWarehouseInventoryCheckService iWarehouseInventoryCheckService;
    @Reference
    private IStoreWareHouseService iStoreWareHouseService;
    @Reference
    private IContentConfigurationService contentConfigurationService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;
    @Reference
    private IOrgService iOrgService;

    private static final Logger LOGGER = LoggerFactory.getLogger(ErpInventorySyncService.class);

    private static final String AUTOSYNCERPSTORECITYID = "AutoSyncErpStoreCityId";

    // 指定每晚0点
    // @Scheduled(cron = "0 0 0 * * ?")
    @XxlJob("wms_syncInventory")
    public void syncInventory() {
        WareHouseDTO wareHouseDTO = new WareHouseDTO();
        PagerCondition pager = new PagerCondition();
        pager.setPageSize(Integer.MAX_VALUE);
        pager.setCurrentPage(1);
        LOGGER.info("开始同步仓库库存");
        PageList<WareHouseDTO> listWareHouse = iStoreWareHouseService.getListWareHouse(wareHouseDTO, pager);
        if (Objects.isNull(listWareHouse) || CollectionUtils.isEmpty(listWareHouse.getDataList())) {
            return;
        }

        List<WareHouseDTO> dataList = listWareHouse.getDataList();
        List<Integer> lstCityId = dataList.stream().map(WareHouseDTO::getCityId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        for (Integer cityId : lstCityId) {
            try {
                if (cityId < 100) {
                    continue;
                }
                if (cityId >= 900 && cityId <= 10000) {
                    continue;
                }
                // if ((txtConfigCity + "、").contains(cityId + "、")) {
                LOGGER.info("开始同步仓库库存：{}", cityId);
                updateStoreInventoryByCityId(cityId);
                LOGGER.info("结束同步仓库库存：{}", cityId);
                // }
            } catch (Exception e) {
                LOGGER.info("{}{}", String.format("CityId:%s 同步仓库库存出错！", cityId), e.getMessage());
            }
        }

        LOGGER.info("结束同步仓库库存");
    }

    /**
     * 根据城市id,根据ERP库存更新仓库库存
     *
     * @param cityId
     */
    public void updateStoreInventoryByCityId(Integer cityId) {
        OrgDTO org = iOrgService.getOrg(cityId);
        // 加盟城市不同步
        if (org != null && org.getOrgMode() != null
            && Objects.equals(org.getOrgMode(), OrgConstant.ORG_MODE_PARTENER)) {
            LOGGER.info("加盟城市不同步仓库库存：{}", JSON.toJSONString(org));
            return;
        }
        LOGGER.info("开始同步城市：{}", JSON.toJSONString(org));
        iWarehouseInventoryCheckService.checkStoreInventoryByCityId(cityId, 1, false);
    }

}

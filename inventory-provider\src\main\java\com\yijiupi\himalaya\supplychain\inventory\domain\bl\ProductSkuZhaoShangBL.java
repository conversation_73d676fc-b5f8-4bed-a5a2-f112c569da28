/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.constant.OtherConstTypes;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ActualSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSyncByZskxService;

/**
 * 招商产品转换BL
 */
@Service
public class ProductSkuZhaoShangBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryOrderBizBL.class);

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;
    @Autowired
    private WarehouseCityBL warehouseCityBL;

    @Reference
    private IProductSyncByZskxService iProductSyncByZskxService;

    public Map<Long, Long> getActualDeliverySkuIdMap(List<Long> productSkuIds, Integer warehouseId,
        Integer deliveryCityId) {
        if (deliveryCityId == null) {
            if (warehouseId == null) {
                throw new DataValidateException("仓库Id不能为空！");
            }
            deliveryCityId = warehouseCityBL.getCityIdByWarehouseId(warehouseId);
        }
        List<ActualSkuPO> actualDeliverySkuIdS =
            productSkuQueryBL.getActualDeliverySkuIdS(productSkuIds, deliveryCityId);
        actualDeliverySkuIdS.removeIf(p -> p.getActualDeliverySkuId() == null && p.getOrderSkuId() == null);
        // 如果查出来的数量不一致
        if (actualDeliverySkuIdS.size() != productSkuIds.size()) {
            List<Long> orderSkuIdList =
                actualDeliverySkuIdS.stream().map(ActualSkuPO::getOrderSkuId).collect(Collectors.toList());
            // 不存在的下单sku
            List<Long> noExistSkuList =
                productSkuIds.stream().filter(n -> !orderSkuIdList.contains(n)).collect(Collectors.toList());
            if (noExistSkuList.size() > 0) {
                String errorMsg = "SKU不存在！ SkuIDList:" + noExistSkuList + ",warehouseId:" + warehouseId;
                throw new BusinessException(errorMsg);
            }
        }
        List<ActualSkuPO> noExistDeliverySkuList =
            actualDeliverySkuIdS.stream().filter(n -> n.getActualDeliverySkuId() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noExistDeliverySkuList)) {
            if (noExistDeliverySkuList.size() > 0) {
                List<Long> orderIds =
                    noExistDeliverySkuList.stream().map(n -> n.getOrderSkuId()).collect(Collectors.toList());
                // 掌上快销城市，找不到发货城市sku，会创建一个sku
                if (iProductSyncByZskxService.isZskcCity(deliveryCityId)) {
                    processZskxSku(warehouseId, deliveryCityId, orderIds, actualDeliverySkuIdS);
                } else {
                    String errorMsg = "该sku找不到发货城市sku: " + JSON.toJSONString(orderIds) + ",warehouseId:" + warehouseId;
                    throw new BusinessValidateException(errorMsg);
                }
            }
        }
        // k(下单skuId)->v(实际发货skuId)
        Map<Long, Long> actualDeliverySkuIdMap = new HashMap<>(16);

        actualDeliverySkuIdS.stream().forEach(p -> {
            if (p.getOrderSkuId() != null && p.getActualDeliverySkuId() != null
                && !actualDeliverySkuIdMap.containsKey(p.getOrderSkuId())) {
                actualDeliverySkuIdMap.put(p.getOrderSkuId(), p.getActualDeliverySkuId());
            }
        });

        return actualDeliverySkuIdMap;
    }

    /**
     * 处理掌上快销城市sku
     */
    private void processZskxSku(Integer warehouseId, Integer deliveryCityId, List<Long> orderIds,
        List<ActualSkuPO> actualDeliverySkuIdS) {
        Map<Long, Long> zskxMap = iProductSyncByZskxService.createProductSkuByCityId(deliveryCityId, orderIds);
        LOG.info("下单城市：{}, 掌上快销发货城市skuMap：{}", JSON.toJSONString(orderIds), JSON.toJSONString(zskxMap));
        actualDeliverySkuIdS.forEach(p -> {
            if (p.getActualDeliverySkuId() == null) {
                p.setActualDeliverySkuId(zskxMap.get(p.getOrderSkuId()));
            }
        });
        List<Long> noExistDeliverySkuIdList = actualDeliverySkuIdS.stream()
            .filter(n -> n.getActualDeliverySkuId() == null).map(n -> n.getOrderSkuId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noExistDeliverySkuIdList)) {
            String errorMsg =
                "该sku找不到掌上快销发货城市sku: " + JSON.toJSONString(noExistDeliverySkuIdList) + ",warehouseId:" + warehouseId;
            throw new BusinessException(errorMsg);
        }
    }

    /**
     * 校验是否是招商订单 如果为招商订单，则需要先把招商城市SKU转换为发货城市的SKU，否则发货时找不到对应的库存，发货失败 （招商订单是招商城市下单，在另一个城市发货，SKUID是招商城市的，仓库Id是发货城市）
     *
     * @param jiupiOrder
     * @return
     */
    public InventoryDeliveryJiupiOrder processOrderItemProductSkuId(InventoryDeliveryJiupiOrder jiupiOrder) {

        Integer deliveryCityId = jiupiOrder.getCityId();
        Integer orderCityId = jiupiOrder.getFromCityId();
        AssertUtils.notNull(orderCityId, "下单城市不能为空");
        boolean isNeedTransferCityId =
            !orderCityId.equals(deliveryCityId) || (OtherConstTypes.ZHAOSHANG_CITYID.equals(orderCityId)
                && !OtherConstTypes.ZHAOSHANG_CITYID.equals(deliveryCityId));
        if (isNeedTransferCityId) {
            // 如果招商城市发货城市Id跟下单城市Id一致，需要进行转换
            if (OtherConstTypes.ZHAOSHANG_CITYID.equals(deliveryCityId)) {
                deliveryCityId = warehouseCityBL.getCityIdByWarehouseId(jiupiOrder.getWarehouseId());
            }

            List<Long> productSkuList = jiupiOrder.getItems().stream().filter(n -> n.getProductSkuId() != null)
                .map(InventoryDeliveryJiupiOrderItem::getProductSkuId).collect(Collectors.toList());
            Map<Long, Long> actualDeliverySkuIdMap =
                productSkuQueryBL.getActualDeliverySkuIdS(productSkuList, jiupiOrder.getWarehouseId(), deliveryCityId);

            for (InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem : jiupiOrder.getItems()) {
                Long oderSkuId = inventoryDeliveryJiupiOrderItem.getProductSkuId();
                Long actualDeliverySkuId = actualDeliverySkuIdMap.get(oderSkuId);
                if (actualDeliverySkuId == null) {
                    String errorMsg =
                        String.format("下单产品转实际发货产品失败:SKU不存在！ OrderId=%s OrderNo=%s 发货城市ID=%s 下单城市ID=%s SKUID=%s",
                            jiupiOrder.getOrderId(), jiupiOrder.getOrderNo(), deliveryCityId, orderCityId, oderSkuId);
                    throw new BusinessValidateException(errorMsg);
                }
                LOG.info("下单产品转实际发货城市产品: OrderId={} OrderNo={} 发货城市ID={} SKUID={} 下单城市ID={} SKUID={}",
                    jiupiOrder.getOrderId(), jiupiOrder.getOrderNo(), deliveryCityId, actualDeliverySkuId, orderCityId,
                    actualDeliverySkuId);
                inventoryDeliveryJiupiOrderItem.setProductSkuId(actualDeliverySkuIdMap.get(oderSkuId));
            }
            // 城市Id设置为发货城市Id，后边发消息给交易系统处理销售库存时会用到
            jiupiOrder.setCityId(deliveryCityId);
        }
        return jiupiOrder;
    }

    /**
     * 将普通skuid转换成招商skuid
     *
     * @param productSkuIds
     * @param cityId
     * @return
     */
    public Map<Long, Long> process2ZhaoShangSku(List<Long> productSkuIds, Integer cityId) {
        Map<Long, Long> map = new HashMap<>(16);
        for (Long productSkuId : productSkuIds) {
            map.put(productSkuId, productSkuQueryBL.process2ZhaoShangSku(productSkuId, cityId));
        }
        return map;
    }

}

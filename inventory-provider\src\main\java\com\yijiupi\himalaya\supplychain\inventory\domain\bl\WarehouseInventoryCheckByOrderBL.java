package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.BatchProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bo.ReturnOrderProductionDateCacheBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bo.ReturnOrderProductionDateTotalCacheBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ProductStoreChangeRecordConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ReturnOrderProductionDateTotalCacheBOConvertor;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStoreChangeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.batch.BatchProductStoreChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.BatchInventoryQueryRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryCheckByOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryCheckByOrderItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryCheckByOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.ReturnOrderProductionDateCacheBL;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.search.ProductStoreChangeRecordByOrderSO;

/**
 * 根据订单矫正仓库库存
 *
 * <AUTHOR>
 * @date 11/28/20 2:48 PM
 */
@Service
public class WarehouseInventoryCheckByOrderBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseInventoryCheckByOrderBL.class);

    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    @Autowired
    private ProductStoreChangeRecordBL productStoreChangeRecordBL;

    @Autowired
    private ProductStoreChangeRecordPOMapper productStoreChangeRecordPOMapper;

    @Autowired
    private ReturnOrderProductionDateCacheBL returnOrderProductionDateCacheBL;

    private static final int MIN_PRODUCT_DATE_YEAR = 1;

    /**
     * 根据订单矫正仓库库存
     */
    public void checkInventoryByOrder(List<InventoryCheckByOrderDTO> orderDTOList) {
        // 20个一组分别处理
        List<List<InventoryCheckByOrderDTO>> lists = splitList(orderDTOList, 20);
        for (List<InventoryCheckByOrderDTO> list : lists) {
            try {
                checkInventory(list);
            } catch (Exception e) {
                LOGGER.error("根据订单矫正仓库库存异常：" + JSON.toJSONString(list), e);
            }
        }
    }

    /**
     * 矫正仓库库存
     */
    private void checkInventory(List<InventoryCheckByOrderDTO> orderDTOList) {
        long startTime = System.currentTimeMillis();
        LOGGER.info("[根据订单矫正仓库库存]开始矫正：{}", JSON.toJSONString(orderDTOList));
        AssertUtils.notEmpty(orderDTOList, "订单不能为空");
        orderDTOList.forEach(order -> {
            AssertUtils.notNull(order.getCityId(), "城市id不能为空");
            AssertUtils.notNull(order.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(order.getOrderId(), "订单id不能为空");
            AssertUtils.notNull(order.getOrderNo(), "订单号不能为空");
            AssertUtils.notNull(order.getOrderType(), "单据类型不能为空");
            AssertUtils.notEmpty(order.getItemList(), "订单项不能为空");
            order.getItemList().forEach(item -> {
                AssertUtils.notNull(item.getItemId(), "订单项id不能为空");
                AssertUtils.notNull(item.getSkuId(), "skuId不能为空");
                AssertUtils.notEmpty(item.getDetailList(), "订单项detail不能为空");
                item.getDetailList().forEach(detail -> {
                    AssertUtils.notNull(detail.getSpecId(), "规格id不能为空");
                    AssertUtils.notNull(detail.getTotalCount(), "小单位总数量不能为空");
                });
            });
        });

        // 1、查询订单库存变更记录
        ProductStoreChangeRecordByOrderSO orderSO = new ProductStoreChangeRecordByOrderSO();
        List<String> orderNoList =
            orderDTOList.stream().map(p -> p.getOrderNo()).distinct().collect(Collectors.toList());
        orderSO.setOrderNoList(orderNoList);
        orderSO.setJiupiEventTypeList(
            Arrays.asList(JiupiEventType.仓库发货扣仓库库存.getType(), JiupiEventType.内配单出库减库存.getType()));
        PageList<ProductStoreChangeRecordByOrderDTO> pageList =
            productStoreChangeRecordBL.listProductStoreChangeRecordByOrder(orderSO);
        List<ProductStoreChangeRecordByOrderDTO> changeRecordList = pageList.getDataList();
        if (CollectionUtils.isEmpty(changeRecordList)) {
            LOGGER.info("[根据订单矫正仓库库存]找不到库存变更记录，直接跳过：{}", JSON.toJSONString(orderNoList));
            return;
        }

        // 2、根据订单项detail数量与已变更数量作比较，得到差异值去矫正库存
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        orderDTOList.forEach(order -> {
            if (CollectionUtils.isEmpty(order.getItemList())) {
                LOGGER.info("[根据订单矫正仓库库存]跳过订单项为空的订单：{}", order.getOrderNo());
                return;
            }
            order.getItemList().forEach(item -> {
                if (CollectionUtils.isEmpty(item.getDetailList())) {
                    LOGGER.info("[根据订单矫正仓库库存]跳过订单Detail为空的订单项：{}", item.getItemId());
                    return;
                }
                // 找出当前订单指定产品的库存变更记录
                List<ProductStoreChangeRecordByOrderDTO> productChangeList =
                    changeRecordList.stream().filter(p -> Objects.equals(p.getOrderNo(), order.getOrderNo())
                        && Objects.equals(p.getProductSkuId(), item.getSkuId())).collect(Collectors.toList());
                // (1) 遍历detail
                item.getDetailList().forEach(detail -> {
                    Optional<ProductStoreChangeRecordByOrderDTO> optional = productChangeList.stream()
                        .filter(p -> Objects.equals(p.getSecOwnerId(), detail.getSecOwnerId())).findFirst();
                    // 存在库存变更记录时
                    if (optional.isPresent()) {
                        // 需要处理差异库存
                        BigDecimal diffCount = optional.get().getTotalCount().add(detail.getTotalCount());
                        addWarehouseChangePOByChange(warehouseChangeList, optional.get(), diffCount.negate());
                        // 处理过就移除
                        productChangeList.remove(optional.get());
                    } else {
                        // 不存在库存变更记录时，新增一条库存变更
                        addWarehouseChangePOByOrder(warehouseChangeList, order, item, detail);
                    }
                });
                // (2) 存在多余的库存记录时，填充库存
                if (CollectionUtils.isNotEmpty(productChangeList)) {
                    productChangeList.forEach(change -> {
                        addWarehouseChangePOByChange(warehouseChangeList, change, change.getTotalCount().negate());
                    });
                }
            });
        });

        // 3、处理库存
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            LOGGER.info("[根据订单矫正仓库库存]处理库存参数：{}", JSON.toJSONString(warehouseChangeList));
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, false, true, true,
                true);
            warehouseInventoryManageBL.processSellInventory(warehouseChangeList, null);
        }

        long endTime = System.currentTimeMillis();
        LOGGER.info("[根据订单矫正仓库库存]结束，总耗时：{}ms", (endTime - startTime));
    }

    /**
     * 封装处理库存数据
     */
    private void addWarehouseChangePOByChange(List<WarehouseInventoryChangeBO> warehouseChangeList,
        ProductStoreChangeRecordByOrderDTO changeRecordDTO, BigDecimal count) {
        if (count.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        WarehouseInventoryChangeBO changeBO = getBaseChangeBO();
        changeBO.setCityId(changeRecordDTO.getCityId());
        changeBO.setWarehouseId(changeRecordDTO.getWarehouseId());
        changeBO.setOrderType(changeRecordDTO.getOrderType());
        changeBO.setOrderId(changeRecordDTO.getOrderId());
        changeBO.setOrderNo(changeRecordDTO.getOrderNo());
        changeBO.setOwnId(changeRecordDTO.getOwnerId());
        changeBO.setSecOwnerId(changeRecordDTO.getSecOwnerId());
        changeBO.setProductSpecificationId(changeRecordDTO.getSpecId());
        changeBO.setProductSkuId(changeRecordDTO.getProductSkuId());
        changeBO.setCount(count);
        warehouseChangeList.add(changeBO);
    }

    /**
     * 封装处理库存数据
     */
    private void addWarehouseChangePOByOrder(List<WarehouseInventoryChangeBO> warehouseChangeList,
        InventoryCheckByOrderDTO order, InventoryCheckByOrderItemDTO item, InventoryCheckByOrderItemDetailDTO detail) {
        if (detail.getTotalCount().compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        WarehouseInventoryChangeBO changeBO = getBaseChangeBO();
        changeBO.setCityId(order.getCityId());
        changeBO.setWarehouseId(order.getWarehouseId());
        changeBO.setOrderType(order.getOrderType());
        changeBO.setOrderId(order.getOrderId().toString());
        changeBO.setOrderNo(order.getOrderNo());
        changeBO.setOwnId(detail.getOwnerId());
        changeBO.setSecOwnerId(detail.getSecOwnerId());
        changeBO.setProductSpecificationId(detail.getSpecId());
        changeBO.setProductSkuId(item.getSkuId());
        changeBO.setCount(detail.getTotalCount().negate());
        warehouseChangeList.add(changeBO);
    }

    private WarehouseInventoryChangeBO getBaseChangeBO() {
        WarehouseInventoryChangeBO changeBO = new WarehouseInventoryChangeBO();
        changeBO.setJiupiEventType(JiupiEventType.仓库发货扣仓库库存.getType());
        changeBO.setErpEventType(ERPEventType.单据删除.getType());
        changeBO.setDescription(JiupiEventType.仓库发货扣仓库库存.name());
        changeBO.setAllocationCalculation(false);
        return changeBO;
    }

    /**
     * 拆分list
     *
     * @return
     */
    public <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }
        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 根据单号还原仓库库存
     */
    public void restoreInventoryByOrderNo(List<String> orderNos, Integer warehouseId) {
        LOGGER.info("[根据订单号还原仓库库存]开始还原参数orderNos：{}，warehouseId:{}", JSON.toJSONString(orderNos), warehouseId);
        ProductStoreChangeRecordByOrderSO orderSO = new ProductStoreChangeRecordByOrderSO();
        orderSO.setWarehouseId(warehouseId);
        orderSO.setOrderNoList(orderNos);
        List<ProductStoreChangeRecordByOrderDTO> recordDTOS =
            productStoreChangeRecordBL.listProductStoreChangeRecordByOrder(orderSO).getDataList();
        if (CollectionUtils.isEmpty(recordDTOS)) {
            LOGGER.info("没有找到库存变更记录:orderNos:{},warehouseId:{}", JSON.toJSONString(orderNos), warehouseId);
            return;
        }

        if (warehouseId == null) {
            List<String> errOrderNos = new ArrayList<>();
            recordDTOS.stream()
                .collect(Collectors.groupingBy(ProductStoreChangeRecordByOrderDTO::getOrderNo,
                    Collectors.mapping(ProductStoreChangeRecordByOrderDTO::getWarehouseId, Collectors.toSet())))
                .forEach((orderNo, warehouseIds) -> {
                    if (warehouseIds.size() > 1) {
                        errOrderNos.add(orderNo);
                    }
                });
            if (CollectionUtils.isNotEmpty(errOrderNos)) {
                throw new BusinessValidateException("下列单据在多个仓库有库存变更明细数据，请重新指定仓库处理:" + StringUtils.join(orderNos, ","));
            }
        }

        List<WarehouseInventoryChangeBO> changeBOS =
            ProductStoreChangeRecordConverter.productStoreChangeRecords2WarehouseInventoryChangeBOS(recordDTOS);
        if (CollectionUtils.isEmpty(changeBOS)) {
            LOGGER.info("过滤后没有找到库存变更记录:orderNos:{},warehouseId:{}", JSON.toJSONString(orderNos), warehouseId);
            return;
        }
        List<WarehouseInventoryChangeBO> lstZhengShuBOs =
            changeBOS.stream().filter(p -> p.getCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        List<WarehouseInventoryChangeBO> lstFuShuBOs =
            changeBOS.stream().filter(p -> p.getCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        changeBOS.clear();
        if (CollectionUtils.isNotEmpty(lstZhengShuBOs)) {
            processByChangeBOS(lstZhengShuBOs);
            lstZhengShuBOs.clear();
        }
        if (CollectionUtils.isNotEmpty(lstFuShuBOs)) {
            processByChangeBOS(lstFuShuBOs);
            lstFuShuBOs.clear();
        }
    }

    private void processByChangeBOS(List<WarehouseInventoryChangeBO> changeBOS) {
        changeBOS.forEach(bo -> {
            bo.setCount(bo.getCount().negate());
            bo.setJiupiEventType(JiupiEventType.erp库存同步.getType());
            bo.setErpEventType(ERPEventType.单据修改.getType());
            bo.setDescription(ERPEventType.单据修改.name());
            bo.setAllocationCalculation(false);
        });
        LOGGER.info("[根据订单号还原仓库库存]处理库存参数：{}", JSON.toJSONString(changeBOS));
        warehouseInventoryManageBL.validateAndProcessProductStore(changeBOS, false, false, true, true, true);
        warehouseInventoryManageBL.processSellInventory(changeBOS, null);
    }

    public List<String> findStoreChangeOrderNos(List<String> orderNos, String excludeUser) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        excludeUser = StringUtils.trimToNull(excludeUser);
        return productStoreChangeRecordPOMapper.findStoreChangeOrderNos(orderNos, excludeUser);
    }

    /**
     * 根据单号还原仓库批次库存
     */
    public void restoreBatchInventoryByOrderNo(List<String> orderNos) {
        LOGGER.info("[根据订单号还原仓库批次库存]开始还原参数orderNos：{}", JSON.toJSONString(orderNos));
        List<BatchProductStoreChangeRecordPO> batchProductStoreChangeRecordPOS =
            productStoreChangeRecordPOMapper.findBatchInventoryByOrderNos(orderNos);
        List<WarehouseInventoryChangeBO> changeBOS = ProductStoreChangeRecordConverter
            .batchProductStoreChangeRecordPOS2WarehouseChangeBOS(batchProductStoreChangeRecordPOS);

        if (CollectionUtils.isEmpty(changeBOS)) {
            LOGGER.info("没有找到批次库存变更记录:orderNos:{}", JSON.toJSONString(orderNos));
            return;
        }

        processByChangeBOS(changeBOS);
    }

    /**
     * 根据单号查询
     */
    public List<BatchProductStoreChangeRecordDTO> queryBatchInventoryByOrderNo(List<String> orderNos) {
        List<BatchProductStoreChangeRecordDTO> result = new ArrayList<>();
        List<BatchProductStoreChangeRecordPO> batchProductStoreChangeRecordPOS =
            productStoreChangeRecordPOMapper.findBatchInventoryByOrderNos(orderNos);
        batchProductStoreChangeRecordPOS.forEach(recordPO -> {
            BatchProductStoreChangeRecordDTO recordDTO = new BatchProductStoreChangeRecordDTO();
            BeanUtils.copyProperties(recordPO, recordDTO);
            result.add(recordDTO);
        });
        return result;
    }

    /**
     * 根据单号查询和规格id
     */
    public List<BatchProductStoreChangeRecordDTO> queryBatchInventory(BatchInventoryQueryRecordDTO queryRecordDTO) {
        List<BatchProductStoreChangeRecordDTO> result = new ArrayList<>();
        List<BatchProductStoreChangeRecordPO> batchProductStoreChangeRecordPOS =
            productStoreChangeRecordPOMapper.findBatchInventory(queryRecordDTO);
        batchProductStoreChangeRecordPOS.forEach(recordPO -> {
            BatchProductStoreChangeRecordDTO recordDTO = new BatchProductStoreChangeRecordDTO();
            BeanUtils.copyProperties(recordPO, recordDTO);
            result.add(recordDTO);
        });
        return result;
    }

    /**
     * 根据订单号查询订单数量
     *
     * @param orderNos
     * @return
     */
    public Long queryCountBatchInventoryByOrderNo(List<String> orderNos) {
        Long orderCount = productStoreChangeRecordPOMapper.queryCountBatchInventoryByOrderNo(orderNos);
        return orderCount;
    }

    /**
     * 查询生产日期
     * 
     * @param queryDTO
     * @return
     */
    public List<ReturnOrderProductDateDTO> queryProductDate(ReturnOrderProductDateQueryDTO queryDTO) {
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start("查询生产日期");
            // LOG.info("查询库存入参：{}", JSON.toJSONString(queryDTO));

            ReturnOrderProductionDateTotalCacheBO totalCacheBO =
                returnOrderProductionDateCacheBL.getProductionCache(queryDTO);
            if (CollectionUtils.isEmpty(totalCacheBO.getNotCacheBOList())) {
                stopWatch.stop();
                return ReturnOrderProductionDateTotalCacheBOConvertor
                    .convertToReturnOrderProductDateDTO(totalCacheBO.getCacheBOList());
            }

            List<ReturnOrderProductionDateCacheBO> notCacheBOList = totalCacheBO.getNotCacheBOList();
            List<String> orderNos = notCacheBOList.stream().map(ReturnOrderProductionDateCacheBO::getOrderNo).distinct()
                .collect(Collectors.toList());
            List<Long> productSpecIds = notCacheBOList.stream().map(ReturnOrderProductionDateCacheBO::getProductSpecId)
                .distinct().collect(Collectors.toList());
            BatchInventoryQueryRecordDTO queryRecordDTO = new BatchInventoryQueryRecordDTO();
            queryRecordDTO.setOrderNos(orderNos);
            queryRecordDTO.setSpecificationIds(productSpecIds);
            List<BatchProductStoreChangeRecordPO> productionDateList =
                productStoreChangeRecordPOMapper.findBatchInventoryProductionDate(queryRecordDTO);
            stopWatch.stop();
            if (CollectionUtils.isEmpty(productionDateList)) {
                return Collections.emptyList();
            }
            Map<String, List<BatchProductStoreChangeRecordPO>> productionDateMap =
                productionDateList.stream().collect(Collectors.groupingBy(BatchProductStoreChangeRecordPO::getOrderNo));
            stopWatch.start("计算生产日期");

            Map<String, List<BatchProductStoreChangeRecordPO>> productionDateKeyMap =
                productionDateList.stream().collect(Collectors
                    .groupingBy(k -> String.format("%s-%s-%s", k.getOrderNo(), k.getSpecId(), k.getOwnerId())));

            Map<String, ReturnOrderProductionDateCacheBO> notCacheBOMap = notCacheBOList.stream()
                .collect(Collectors.toMap(ReturnOrderProductionDateCacheBO::getDefaultKey, v -> v));

            for (Map.Entry<String, ReturnOrderProductionDateCacheBO> entry : notCacheBOMap.entrySet()) {
                List<BatchProductStoreChangeRecordPO> lstBatch = productionDateKeyMap.get(entry.getKey());
                ReturnOrderProductionDateCacheBO dateCacheBO = entry.getValue();
                if (CollectionUtils.isNotEmpty(lstBatch)) {
                    Map<String, List<BatchProductStoreChangeRecordPO>> batchMap = lstBatch.stream().collect(Collectors
                        .groupingBy(p -> String.format("%s-%s-%s", p.getSpecId(), p.getOwnerId(), p.getSecOwnerId())));

                    for (Map.Entry<String, List<BatchProductStoreChangeRecordPO>> recordEntry : batchMap.entrySet()) {
                        if (CollectionUtils.isEmpty(recordEntry.getValue())) {
                            continue;
                        }
                        List<BatchProductStoreChangeRecordPO> changeRecordList = recordEntry.getValue();
                        BatchProductStoreChangeRecordPO batchProductStoreChangeRecordDTO = changeRecordList.get(0);
                        ReturnOrderProductDateItemDTO productDateItemDTO = new ReturnOrderProductDateItemDTO();
                        productDateItemDTO.setOwnerId(batchProductStoreChangeRecordDTO.getOwnerId());
                        productDateItemDTO.setSecOwnerId(batchProductStoreChangeRecordDTO.getSecOwnerId());
                        productDateItemDTO.setSpecId(batchProductStoreChangeRecordDTO.getSpecId());
                        Date productDate = getProductionDateByRule(changeRecordList);
                        productDateItemDTO.setProductDate(productDate);
                        dateCacheBO.setItemDTO(productDateItemDTO);
                    }
                }
            }

            List<ReturnOrderProductionDateCacheBO> totalList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(totalCacheBO.getCacheBOList())) {
                totalList.addAll(totalCacheBO.getCacheBOList());
            }
            if (CollectionUtils.isNotEmpty(totalCacheBO.getNotCacheBOList())) {
                totalList.addAll(totalCacheBO.getNotCacheBOList());
            }

            List<ReturnOrderProductionDateCacheBO> filterTotalList =
                totalList.stream().filter(m -> Objects.nonNull(m.getItemDTO())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterTotalList)) {
                return ReturnOrderProductionDateTotalCacheBOConvertor
                    .convertToReturnOrderProductDateDefaultDTO(totalList);
            }

            // LOG.info(String.format("全量结果：%s", JSON.toJSONString(productInfoCodeDTOList)));
            stopWatch.stop();
            LOGGER.warn("查询生产日期时间入参: {} ； 时间: {}", JSON.toJSONString(queryDTO), stopWatch.prettyPrint());
            // LOG.info(String.format("过滤完结果：%s", JSON.toJSONString(productInfoCodeDTOList)));
            return ReturnOrderProductionDateTotalCacheBOConvertor.convertToReturnOrderProductDateDTO(filterTotalList);
        } catch (Exception e) {
            LOGGER.warn("查询生产日期报错，入参" + JSON.toJSONString(queryDTO), e);
            throw new BusinessValidateException("查询生产日期报错！");
        } finally {
            LOGGER.warn("查询生产日期时间入参: {} ； 时间: {}", JSON.toJSONString(queryDTO), stopWatch.prettyPrint());
        }
    }

    private Date getProductionDateByRule(List<BatchProductStoreChangeRecordPO> changeRecordList) {
        Date productDate = null;
        // 优先取生产日期最老的
        if (changeRecordList.stream()
            .anyMatch(p -> p.getProductionDate() != null && p.getProductionDate().getYear() > MIN_PRODUCT_DATE_YEAR)) {
            List<Date> lstProductionDate = changeRecordList.stream()
                .filter(p -> p.getProductionDate() != null && p.getProductionDate().getYear() > MIN_PRODUCT_DATE_YEAR)
                .map(p -> p.getProductionDate()).sorted().collect(Collectors.toList());
            // LOG.info(String.format("生产日期汇总：%s", JSON.toJSONString(lstProductionDate)));
            productDate = lstProductionDate.get(0);
        }
        // 其次取批次时间最老的
        if (productDate == null && changeRecordList.stream()
            .anyMatch(p -> p.getBatchTime() != null && p.getBatchTime().getYear() > MIN_PRODUCT_DATE_YEAR)) {
            List<Date> lstBatchTime = changeRecordList.stream()
                .filter(p -> p.getBatchTime() != null && p.getBatchTime().getYear() > MIN_PRODUCT_DATE_YEAR)
                .map(p -> p.getBatchTime()).sorted().collect(Collectors.toList());
            // LOG.info(String.format("批次时间汇总：%s", JSON.toJSONString(lstBatchTime)));
            productDate = lstBatchTime.get(0);
        }
        // 最后取批次入库时间最老的
        if (productDate == null && changeRecordList.stream().anyMatch(
            p -> p.getBatchCreatetime() != null && p.getBatchCreatetime().getYear() > MIN_PRODUCT_DATE_YEAR)) {
            List<Date> lstBatchCreatetime = changeRecordList.stream()
                .filter(p -> p.getBatchCreatetime() != null && p.getBatchCreatetime().getYear() > MIN_PRODUCT_DATE_YEAR)
                .map(p -> p.getBatchCreatetime()).sorted().collect(Collectors.toList());
            // LOG.info(String.format("批次创建时间汇总：%s", JSON.toJSONString(lstBatchCreatetime)));
            productDate = lstBatchCreatetime.get(0);
        }

        return productDate;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.promotion;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.promotion.ProductPromotionStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.ProductPromotionStoreBatchDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchResultDTO;

/**
 * 促销批次库存
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Mapper
public interface ProductPromotionStoreBatchMapper {
    int batchInsert(@Param("list") List<ProductPromotionStoreBatchPO> list);

    int batchDeleteByIds(@Param("ids") List<Long> ids);

    int batchUpdate(@Param("list") List<ProductPromotionStoreBatchPO> list);

    List<ProductPromotionStoreBatchPO> selectByIds(@Param("ids") List<Long> ids);

    List<ProductPromotionStoreBatchDTO> queryByCondition(PromotionStoreBatchQueryDTO queryDTO);

    List<PromotionStoreBatchResultDTO> listPromotionStoreBatchProduct(PromotionStoreBatchQueryDTO queryDTO);

    List<PromotionStoreBatchResultDTO> listProductMixedBatchFlag(PromotionStoreBatchQueryDTO queryDTO);

    Long getStoreBatchPromotionCount(@Param("warehouseId") Integer warehouseId, @Param("skuId") Long skuId,
        @Param("batchAttributeInfoNo") String batchAttributeInfoNo);

    List<PromotionStoreBatchResultDTO> listPromotionStoreBatchNoGroup(PromotionStoreBatchQueryDTO queryDTO);
}

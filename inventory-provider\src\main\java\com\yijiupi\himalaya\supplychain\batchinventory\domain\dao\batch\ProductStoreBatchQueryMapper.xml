<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductStoreBatchQueryMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="productstore_id" property="productStoreId" jdbcType="VARCHAR"/>
        <result column="totalcount_minunit" property="totalCount" jdbcType="VARCHAR"/>
        <result column="productiondate" property="productionDate" jdbcType="TIMESTAMP"/>
        <result column="location_id" property="locationId" jdbcType="BIGINT"/>
        <result column="location_name" property="locationName" jdbcType="VARCHAR"/>
        <result column="locationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="subcategory" property="subcategory" jdbcType="TINYINT"/>
        <result column="expiretime" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="batchtime" property="batchTime" jdbcType="TIMESTAMP"/>
        <result column="createuserId" property="createUserId" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="BatchAttributeInfoNo" property="batchAttributeInfoNo" jdbcType="VARCHAR"/>
        <result column="OwnerType" property="ownerType" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="findBatchInventorySql">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        psb.totalcount_minunit as storeTotalCount,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        loc.area as area,
        loc.sequence AS locationSequence,
        area.subcategory as areaSubcategory,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        own.OwnerName as secOwnerName,
        pc.ProductFeature,
        pc.MaxReplenishment,
        pc.MinReplenishment,
        psku.productBrand,
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) as shelfLifeUnit,
        psku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        LEFT JOIN Location area on loc.area_id = area.id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id
        <if test="so.limitSku == null or so.limitSku != 1">
            LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
        </if>
        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id =
            pc.Warehouse_Id
        </if>
        where 1=1
    </sql>

    <!--查询批次库存信息(根据货位或产品名称获取批次库存)-->
    <select id="findBatchInventoryList"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        ps.SecOwner_Id as secOwnerId,
        psb.totalcount_minunit as storeTotalCount,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        loc.area as area,
        loc.sequence AS locationSequence,
        area.subcategory as areaSubcategory,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        pc.ProductFeature,
        pc.MaxReplenishment,
        pc.MinReplenishment,
        psku.productBrand,
        IFNULL(pi.MonthOfShelfLife,psku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(pi.ShelfLifeUnit,psku.ShelfLifeUnit) as shelfLifeUnit,
        psku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount,
        psku.ProductInfo_Id as productInfoId
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        LEFT JOIN productinfo pi on pi.id = psku.ProductInfo_Id
        LEFT JOIN Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <if test="so.warehouseId!=null">
            AND loc.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null">
            AND loc.City_Id = #{so.cityId}
        </if>
        LEFT JOIN Location area on loc.area_id = area.id
        <if test="so.limitSku == null or so.limitSku != 1">
            LEFT JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
        </if>
        <if test="so.limitSku != null and so.limitSku == 1">
            INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id =
            pc.Warehouse_Id
        </if>
        where 1=1
        AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="so.productSkuId!=null and so.productSkuId!=''">
            AND psku.ProductSku_Id = #{so.productSkuId}
        </if>
        <if test="so.warehouseId!=null and so.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="so.cityId!=null and so.cityId!=''">
            AND ps.City_Id = #{so.cityId}
        </if>
        <if test="so.locationFullName!=null and so.locationFullName!=''">
            AND psb.location_name like concat(#{so.locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.source != null">
            AND psku.Source = #{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.subCategoryList!=null and so.subCategoryList.size()>0">
            AND loc.subcategory in
            <foreach collection="so.subCategoryList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="so.skuIds!=null and so.skuIds.size()>0">
            and psku.productsku_id in
            <foreach collection="so.skuIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        order by psb.productiondate desc,psb.totalcount_minunit desc
    </select>


    <select id="findLocationBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO">
        SELECT
        psb.BatchAttributeinfoNo as batchAttributeInfoNo,
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.NAME) AS
        productSkuName,
        ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId,
        ps.OwnerType as ownerType,
        ps.SecOwner_Id as secOwnerId,
        ps.Channel as channel,
        psku.specificationName,
        psku.unitName,
        psku.packageName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id as secOwnerId,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        psb.id as storeBatchId,
        psku.SaleModel as saleModel,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        psku.productBrand,
        psku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb on psb.productstore_id =ps.id
        INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id =
        pc.Warehouse_Id
        where 1=1
        AND ps.totalcount_minunit != 0 AND psb.totalcount_minunit != 0
        <if test="dto.warehouseId!=null and dto.warehouseId!=''">
            AND ps.Warehouse_Id = #{so.warehouseId}
        </if>
        <if test="dto.cityId!=null and dto.cityId!=''">
            AND ps.City_Id = #{so.cityId}
        </if>
        <if test="dto.locationFullName!=null and dto.locationFullName!=''">
            AND psb.location_name = #{dto.locationFullName,jdbcType=VARCHAR}
        </if>
        <if test="dto.locationIds != null and dto.locationIds > 0">
            AND psb.location_id in
            <foreach collection="dto.locationIds" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

</mapper>
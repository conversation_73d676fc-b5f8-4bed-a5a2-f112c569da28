package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.DeliveryCountChangeBL;
import com.yijiupi.himalaya.supplychain.inventory.service.IDeliveryCountProcessService;

/**
 * 处理已下单未发货数量
 */
@Service
public class DeliveryCountProcessServiceImpl implements IDeliveryCountProcessService {

    @Autowired
    private DeliveryCountChangeBL deliveryCountChangeBL;

    @Override
    public void processDeliveryCount(String msg) {
        // deliveryCountChangeBL.syncApply(msg);
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存批次库存
 *
 * <AUTHOR> 2018/1/25
 */
public class ProductStoreBatchPO implements Serializable {
    /**
     * 主键id
     */
    private String id;
    /**
     * store主键
     */
    private String productStoreId;
    /**
     * 库存小数量
     */
    private BigDecimal totalCount;
    /**
     * 批次入库时间
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期日期
     */
    private Date expireTime;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * '货区或货位类型：0:货位，1:货区',
     */
    private Integer locationCategory;
    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    private Integer subcategory;
    /**
     * 是否更新批次库存
     */
    private boolean needUpdateBatchInventory = true;
    /**
     * 创建人
     */
    private Integer createUserId;
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;

    /**
     * 包装规格系数
     */
    private BigDecimal packageQuantity;

    /**
     * skuId
     */
    private Long productSkuId;

    /**
     * 商品规格id
     */
    private Long productSpecificationId;

    /**
     * 货主id
     *
     * @return
     */
    private Long ownerId;

    /**
     * 二级货主id
     *
     * @return
     */
    private Long secOwnerId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 变更小单位总数量
     */
    private BigDecimal changeCount;

    /**
     * 货主类型
     */
    private Integer ownerType;
    /**
     * 关联的入库单号
     */
    private String inStockOrderNo;

    /**
     * 临期标识：0：正品，1：临期
     */
    private Long adventId;

    /**
     * 库存属性（0：默认，1：自动转入）
     */
    private Byte batchProperty;

    /**
     * 业务类型（0：默认，1：生产日期治理任务）
     */
    private Byte businessType;

    public Long getAdventId() {
        return adventId;
    }

    public void setAdventId(Long adventId) {
        this.adventId = adventId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public String getInStockOrderNo() {
        return inStockOrderNo;
    }

    public void setInStockOrderNo(String inStockOrderNo) {
        this.inStockOrderNo = inStockOrderNo;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity == null || packageQuantity.equals(BigDecimal.ZERO) ? BigDecimal.ONE : packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 主键id
     *
     * @return id 主键id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     *
     * @param id 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 store主键
     *
     * @return productStoreId store主键
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 store主键
     *
     * @param productStoreId store主键
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 库存小数量
     *
     * @return totalCount 库存小数量
     */
    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 库存小数量
     *
     * @param totalCount 库存小数量
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 批次入库时间
     *
     * @return batchTime 批次入库时间
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次入库时间
     *
     * @param batchTime 批次入库时间
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取
     *
     * @return needUpdateBatchInventory
     */
    public boolean isNeedUpdateBatchInventory() {
        return this.needUpdateBatchInventory;
    }

    /**
     * 设置
     *
     * @param needUpdateBatchInventory
     */
    public void setNeedUpdateBatchInventory(boolean needUpdateBatchInventory) {
        this.needUpdateBatchInventory = needUpdateBatchInventory;
    }

    /**
     * 获取 生产日期
     *
     * @return productionDate 生产日期
     */
    public Date getProductionDate() {
        return this.productionDate;
    }

    /**
     * 设置 生产日期
     *
     * @param productionDate 生产日期
     */
    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    /**
     * 获取 过期日期
     *
     * @return expireTime 过期日期
     */
    public Date getExpireTime() {
        return this.expireTime;
    }

    /**
     * 设置 过期日期
     *
     * @param expireTime 过期日期
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 获取 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 '货区或货位类型：0:货位，1:货区',
     */
    public Integer getLocationCategory() {
        return this.locationCategory;
    }

    /**
     * 设置 '货区或货位类型：0:货位，1:货区',
     */
    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public Integer getSubcategory() {
        return this.subcategory;
    }

    /**
     * 货区/货区类型， 货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3）， 货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）''
     */
    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    /**
     * 获取 创建人
     */
    public Integer getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取 批属性编号
     */
    public String getBatchAttributeInfoNo() {
        return this.batchAttributeInfoNo;
    }

    /**
     * 设置 批属性编号
     */
    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    /**
     * 重复项
     */
    public String getMergeProductStoreBatch() {
        return this.batchAttributeInfoNo + this.locationId + this.productStoreId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    /**
     * 未开启货位库存根据盘点生产日期新增批次信息唯一 key
     */
    public String getCheckAddBatchKey() {
        return String.format("%s-%s-%s-%s", getProductStoreId(), getLocationId(),
                getProductionDate() == null ? "" : getProductionDate().getTime(),
                getBatchTime() == null ? "" : getBatchTime().getTime());
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Byte getBatchProperty() {
        return batchProperty;
    }

    public void setBatchProperty(Byte batchProperty) {
        this.batchProperty = batchProperty;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.baseutil.ArrayUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.erp.ErpPurchaseReturnOutStockDTO;
import com.yijiupi.himalaya.supplychain.dto.product.*;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductSpecCityStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.*;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductStorePOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStorePO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockApplyPreemptDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockApplyPreemptQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyStateEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockApplyQueryService;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseInventoryReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseStoreBySupplierOpSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductSpecCityListStoreSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductWarehouseListStoreItemSO;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.DisposedProductInventorDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.ProductSkuInfoReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.*;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchOrderInfoQueryService;
import com.yijiupi.himalaya.supplychain.waves.dto.outstockorder.ProductAllotTypeDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 仓库库存查询BL
 *
 * <AUTHOR>
 * @since 2017/7/20
 */
@Service
public class WarehouseInventoryQueryBL {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseInventoryQueryBL.class);
    /**
     * 动销产品货位查询配置
     */
    private static final String DYNAMIC_PRODUCT_QUERY_CATEGORY = "DynamicProductQueryCategory";
    /**
     * 仓库停用校验-货主仓库库存下限
     */
    private static final String WAREHOUSE_DISABLE_OWNER_INVENTORY_FLOOR = "warehouse_disable_owner_inventory_floor";
    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;
    @Autowired
    private ProductStorePOMapper productStorePOMapper;
    @Autowired
    private TradingThirdUserBL tradingThirdUserBL;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;
    @Autowired
    private ProductSkuZhaoShangBL productSkuZhaoShangBL;
    // @Reference(timeout = 30000)
    // private IOrderQueryService iOrderQueryService;
    @Autowired
    private WarehouseInventoryCheckBL warehouseInventoryCheckBL;
    @Reference
    private IOrgService iOrgService;
    @Reference
    private IStoreWareHouseService iStoreWareHouseService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IContentConfigurationService iContentConfigurationService;
    @Reference
    private IProductRelationGroupService iProductRelationGroupService;
    @Reference
    private IBatchOrderInfoQueryService iBatchOrderInfoQueryService;
    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IOutStockApplyQueryService iOutStockApplyQueryService;
    @Reference
    private IVariableValueService iVariableValueService;
    @Autowired
    private InventoryProductSkuMapper inventoryProductSkuMapper;
    @Autowired
    private FindStoreConvertor findStoreConvertor;
    @Autowired
    private InventoryReportDTOConvertor inventoryReportDTOConvertor;
    @Reference
    private IProductSkuService iProductSkuService;
    private static final int MAX_PARAM_SIZE = 2000;
    public static final ExecutorService EXECUTOR =
        new ThreadPoolExecutor(10, 20, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(),
            new DefaultThreadFactory("inventoryQuery"), new ThreadPoolExecutor.CallerRunsPolicy());

    @Reference
    private OwnerService ownerService;

    /**
     * 通过skuid和仓库id查询出记录
     */
    // public List<ProductInventoryPO> findProductInventoryByProductSkuIdWarehouseId(List<Long> productSkuIdList,
    // Integer warehouseId, Integer channel) {
    // return productInventoryPOMapper.findProductInventoryByProductSkuIdWarehouseId(productSkuIdList, warehouseId,
    // channel);
    // }

    /**
     * 根据主键查找产品库存
     */
    public ProductInventoryPO selectInventoryByPrimaryKey(String id) {
        return productInventoryPOMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据主键集合查找产品库存集合
     */
    public List<ProductInventoryPO> selectInventoryListByPrimaryKey(List<String> idList) {
        return productInventoryPOMapper.selectInventoryListByPrimaryKey(idList);
    }

    /**
     * 获取产品信息对应城市库存
     *
     * @param so
     * @return
     */
    public List<ProductSpecCityStoreDTO> findProductSpecCityListStore(ProductSpecCityListStoreSO so) {
        return productStorePOMapper.findProductSpecCityListStore(so);
    }

    /**
     * 获取产品SKU信息对应仓库库存(条件productSkuId,warehouseId,channel)
     *
     * @param ProductWarehouseListStoreItemSOList
     * @return
     */
    public List<ProductWarehouseStoreDTO>
        findProductWarehouseListStore(List<ProductWarehouseListStoreItemSO> ProductWarehouseListStoreItemSOList) {
        List<ProductWarehouseStoreDTO> productWarehouseStoreDTOS = new ArrayList<>();
        // //以warehouseID，channel，source，secOwnerId进行分组
        Map<String, List<ProductWarehouseListStoreItemSO>> lstGroupItems = ProductWarehouseListStoreItemSOList.stream()
            .collect(Collectors.groupingBy(ProductWarehouseListStoreItemSO::getGroupKey));
        for (String key : lstGroupItems.keySet()) {
            List<ProductWarehouseListStoreItemSO> lstItemDtos = lstGroupItems.get(key);
            List<Long> lstSkuIds =
                lstItemDtos.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lstItemDtos) || CollectionUtils.isEmpty(lstSkuIds)) {
                continue;
            }
            ProductWarehouseListStoreItemSO itemSO = lstItemDtos.get(0);
            Integer warehouseId = itemSO.getWarehouseId();
            Integer channel = itemSO.getChannel();
            Long secOwnerId = itemSO.getSecOwnerId();
            Integer source = itemSO.getSource();
            List<ProductWarehouseStoreDTO> productWarehouseListStore =
                productStorePOMapper.findProductWarehouseBySku(warehouseId, lstSkuIds, channel, secOwnerId, source);
            if (!CollectionUtils.isEmpty(productWarehouseListStore)) {
                productWarehouseStoreDTOS.addAll(productWarehouseListStore);
            }
        }
        return productWarehouseStoreDTOS;
    }

    /**
     * 查询仓库库存信息
     */
    // public PageList<com.yijiupi.himalaya.supplychain.dto.product.ProductWarehouseStoreDTO>
    // findProductWarehouseStoreList(ProductWarehouseStoreSO so,
    // PagerCondition pagerCondition) {
    // // 改为先查询产品信息，再接入库存数量
    // Integer warehouseId = so.getWarehouseId();
    // Warehouse warehouse = tradingThirdUserBL.getWarehouse(warehouseId);
    // Integer shopId = warehouse.getShopId();//经销商id
    // PageResult<ProductStorePO> pageResult = productStorePOMapper.findProductWarehouseStoreList(so, shopId,
    // pagerCondition.getCurrentPage(), pagerCondition.getPageSize());
    // PageList<ProductStorePO> pageList = pageResult.toPageList();
    // List<ProductStorePO> poList = pageList.getDataList();
    //
    // List<Integer> ownerIds = poList.stream().filter(productStorePO -> productStorePO.getOwnerId() !=
    // null).map(ProductStorePO::getOwnerId).collect(Collectors.toList());
    // Map<Integer, Partner> partnerMap = tradingThirdUserBL.findPartnerByIds(ownerIds);
    // List<com.yijiupi.himalaya.supplychain.dto.product.ProductWarehouseStoreDTO> storeList = new ArrayList<>();
    // if (CollectionUtils.isNotEmpty(poList)) {
    // storeList = poList.stream().map(ProductStoreConverter::convertToProductWarehouseStoreDTO)
    // .collect(Collectors.toList());
    // storeList.stream().forEach(p -> {
    // Long ownerId = p.getOwnerId();
    // if (ownerId != null) {
    // Partner partner = partnerMap.get(ownerId);
    // String ownerName = partner.getName();
    // p.setOwnerName(ownerName);
    // }
    // });
    // }
    // PageList<com.yijiupi.himalaya.supplychain.dto.product.ProductWarehouseStoreDTO> resultList = new PageList<>();
    // resultList.setDataList(storeList);
    // resultList.setPager(pageResult.getPager());
    // return resultList;
    // }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存
     *
     * @param warehouseId
     * @param productSkuIdList
     * @return
     */
    public Map<Long, BigDecimal> getProductInventoryCountMap(Integer warehouseId, List<Long> productSkuIdList,
        Integer channel, Long secOwnerId) {
        Map<Long, BigDecimal> mapCount = new HashMap<>(16);
        Map<Long, List<ProductInventoryPO>> productInventoryPOMap =
            getLongProductInventoryPOMapBySkuId(warehouseId, productSkuIdList, channel, secOwnerId);
        for (Map.Entry<Long, List<ProductInventoryPO>> inventories : productInventoryPOMap.entrySet()) {
            BigDecimal totalCount = inventories.getValue().stream().map(ProductInventoryPO::getTotalCountMinUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            mapCount.put(inventories.getKey(), totalCount);
        }
        return mapCount;
    }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存
     *
     * @return
     */
    public List<ProductInventoryPO> getProductInventoryPOBySpecId(List<Long> productSpecIdList, Long ownerId,
        Integer warehouseId, Integer channel, Long secOwnerId) {
        List<ProductInventoryPO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productSpecIdList)) {
            result = productInventoryPOMapper.findProductInventoryByProductSpecIdWarehouseId(productSpecIdList, ownerId,
                warehouseId, channel, secOwnerId);
            // LOG.info(String.format("查询库存信息参数-1：warehouseId:%s,specIds:%s,ownerId:%s,channel:%s,secOnwerId:%s,Map:{}",
            // warehouseId, JSON.toJSONString(productSpecIdList), ownerId, channel, secOwnerId,
            // JSON.toJSONString(result)));
        }
        return result;
    }

    /**
     * 批量查询库存 key:-产品skuid value-对应库存（酒批和大宗）
     *
     * @return
     */
    public Map<Long, ProductStoreDTO> listProductInventoryBySkuIds(List<Long> productSkuIdList, Integer warehouseId) {
        Map<Long, ProductStoreDTO> storeMap = new HashMap<>(16);
        List<ProductInventoryPO> poList = productInventoryPOMapper
            .findProductInventoryByProductSkuIdWarehouseId(productSkuIdList, warehouseId, null, null, null);
        if (CollectionUtils.isNotEmpty(poList)) {
            for (ProductInventoryPO p : poList) {

                BigDecimal[] remainder = p.getTotalCountMinUnit().divideAndRemainder(p.getPackageQuantity());
                // 大件数量
                BigDecimal packageCount = p.getPackageQuantity().compareTo(BigDecimal.ZERO) == 0
                    || p.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : remainder[0];
                // 小件数量
                BigDecimal unitCount = p.getPackageQuantity().compareTo(BigDecimal.ZERO) == 0
                    || p.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : remainder[1];
                // 库存对象
                ProductStoreDTO productStoreDTO = new ProductStoreDTO();
                productStoreDTO.setPackageName(p.getPackageName());
                productStoreDTO.setUnitName(p.getUnitName());
                if (null == storeMap.get(p.getProductSkuId())) {
                    productStoreDTO.setUnitTotolCount(p.getTotalCountMinUnit());
                    productStoreDTO.setPackageCount(packageCount);
                    productStoreDTO.setUnitCount(unitCount);
                } else {
                    ProductStoreDTO oldProductStore = storeMap.get(p.getProductSkuId());
                    productStoreDTO
                        .setUnitTotolCount(oldProductStore.getUnitTotolCount().add(p.getTotalCountMinUnit()));
                    productStoreDTO.setPackageCount(oldProductStore.getPackageCount().add(packageCount));
                    productStoreDTO.setUnitCount(oldProductStore.getUnitCount().add(unitCount));
                }
                storeMap.put(p.getProductSkuId(), productStoreDTO);
            }
        }
        return storeMap;
    }

    /**
     * 批量查询库存（包含供应商） key:-产品skuid value - 自己与供应商库存实体
     *
     * @return
     */
    public Map<Long, List<ProductInventoryDTO>> findProductInventoryIncludeSupplierBySkuIds(List<Long> productSkuIdList,
        Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIdList, "产品SKU信息不能为空");
        List<ProductInventoryPO> poList = productInventoryPOMapper
            .findProductInventoryByProductSkuIdWarehouseId(productSkuIdList, warehouseId, null, null, null);
        if (CollectionUtils.isNotEmpty(poList)) {
            List<ProductInventoryDTO> productInventoryList =
                ProductInventoryConverter.productInventoryPO2ProductInventoryDTO(poList);
            return productInventoryList.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ProductInventoryDTO::getProductSkuId));
        }
        return Collections.emptyMap();
    }

    /**
     * 查询配送中数量
     */
    public List<ProductInventoryPO> getProductInventoryPODeliveryCount(List<Long> productSkuIdList, Integer warehouseId,
        Integer channel) {
        // return productInventoryPOMapper.findProductInventoryPODeliveryCount(productSkuIdList, warehouseId, channel);
        return new ArrayList<>();
    }

    /**
     * 根据销售规格Id及产品信息，查找对应库存（轻加盟用）
     *
     * @param productSpecificationId
     * @param warehouseId
     * @param ownerType
     * @param ownerId
     * @param channel
     * @return
     */
    // public ProductInventoryPO getProductInventoryBySepcId(Integer productSpecificationId, Integer warehouseId,
    // Integer ownerType, Long ownerId, Integer channel) {
    // return productInventoryPOMapper.getProductInventoryBySepcId(productSpecificationId, warehouseId, ownerType,
    // ownerId, channel);
    // }

    /**
     * 根据warehouseId和SKUID批量查询库存
     *
     * @return
     */
    public ProductInventoryPO getProductInventoryPO(Long productSkuId, Integer warehouseId, Integer channel,
        Long secOwnerId) {
        ProductInventoryPO productInventoryPO = null;
        List<ProductInventoryPO> productInventoryPOList =
            getProductInventoryPO(Collections.singletonList(productSkuId), warehouseId, channel, secOwnerId, null);
        if (productInventoryPOList.size() > 0) {
            productInventoryPO = productInventoryPOList.get(0);
        }
        return productInventoryPO;
    }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存
     *
     * @return
     */
    public List<ProductInventoryPO> getProductInventoryPO(List<Long> productSkuIdList, Integer warehouseId,
        Integer channel, Long secOwnerId, Integer eraseOwner) {
        return productInventoryPOMapper.findProductInventoryByProductSkuIdWarehouseId(productSkuIdList, warehouseId,
            channel, secOwnerId, eraseOwner);
    }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存
     *
     * @param warehouseId
     * @return
     */
    public Map<Long, List<ProductInventoryPO>> getLongProductInventoryPOMap(Integer warehouseId,
        List<Long> productSpecIdList, Long ownerId, Integer channel, Long secOwnerId) {
        Map<Long, List<ProductInventoryPO>> map = new HashMap<>(16);
        // 通过productSkuId,warehoustID查询出ownType,ownId.批量查询减少数据库交互.
        List<ProductInventoryPO> productInventoryPOList =
            getProductInventoryPOBySpecId(productSpecIdList, ownerId, warehouseId, channel, secOwnerId);
        // 1.可能会出现一个skuid对应上2个库存(productstore)记录,影响库存流程.
        // 2.也可能会出现2个一样的skuid对应一条库存记录(交易平台并发产生了2条一模一样的productsku记录,主键不同而已),不影响流程.

        // 同一个规格出现多个上架状态不同的sku，且库存指向同一个的数据去重
        productInventoryPOList.stream().filter(StreamUtils.distinctByKey(ProductInventoryPO::getId))
            .collect(Collectors.groupingBy(ProductInventoryPO::getProductSpecificationId))
            .forEach((specId, skuPOList) -> {
                long count =
                    skuPOList.stream().filter(StreamUtils.distinctByKey(ProductInventoryPO::getSkuSign)).count();
                if (skuPOList.size() > count) {
                    // 这里说明一个规格货主关联了2条以上了productStore数据.
                    // 存在超过1条库存记录
                    throw new BusinessException(
                        "同一个规格货主存在多条库存记录！specId:" + specId + ",详细信息：" + JSON.toJSONString(skuPOList));
                }
                map.put(specId, skuPOList);
            });

        return map;
    }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存
     *
     * @param warehouseId
     * @param productSkuIdList
     * @return
     */
    public Map<Long, List<ProductInventoryPO>> getLongProductInventoryPOMapBySkuId(Integer warehouseId,
        List<Long> productSkuIdList, Integer channel, Long secOwnerId) {
        Map<Long, List<ProductInventoryPO>> map = new HashMap<>(16);
        // 通过productSkuId,warehoustID查询出ownType,ownId.批量查询减少数据库交互.
        List<ProductInventoryPO> productInventoryPOList =
            getProductInventoryPO(productSkuIdList, warehouseId, channel, secOwnerId, null);
        // productInventoryPOList.stream().collect(Collectors.toMap(n -> n.getProductSkuId(), n ->
        // n.getTotalCountMinUnit()));
        // 1.可能会出现一个skuid对应上2个库存(productstore)记录,影响库存流程.
        // 2.也可能会出现2个一样的skuid对应一条库存记录(交易平台并发产生了2条一模一样的productsku记录,主键不同而已),不影响流程.
        // for (ProductInventoryPO productInventoryPO : productInventoryPOList) {
        // Long skuId = productInventoryPO.getProductSkuId();
        // if (map.containsKey(skuId)) {
        // continue;
        // }
        // List<ProductInventoryPO> skuPOList = productInventoryPOList.stream().filter(p ->
        // p.getProductSkuId().equals(skuId) && Objects.equals(p.getChannel(), channel)).collect(Collectors.toList());
        // if (skuPOList.size() > 1) {
        // Long poIdCount = skuPOList.stream().map(p -> p.getId()).filter(p ->
        // !p.equals(productInventoryPO.getId())).count();
        // if (poIdCount > 0) {
        // //这里说明一个skuid关联了2条以上了productstore数据.
        // //存在超过1条库存记录
        // throw new BusinessException("同一个SKU存在多条库存记录！skuId:" + skuId + ",详细信息：" + JSON.toJSONString(skuPOList));
        // }
        // }
        // map.put(skuId, productInventoryPO);
        // }
        // 校验库存
        productInventoryPOList.stream().collect(Collectors.groupingBy(ProductInventoryPO::getProductSpecificationId))
            .forEach((specId, skuPOList) -> {
                Map<String,
                    Set<String>> skuSignMap = skuPOList.stream().filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(ProductInventoryPO::getSkuSign,
                            Collectors.mapping(ProductInventoryPO::getId, Collectors.toSet())));
                skuSignMap.forEach((k, v) -> {
                    if (v.size() > 1) {
                        // 存在超过1条库存记录
                        throw new BusinessException(
                            String.format("同一个规格货主存在多条库存记录！货主信息:%s , 库存ID：%s ", k, JSON.toJSONString(v)));
                    }
                });
            });
        productInventoryPOList.stream().collect(Collectors.groupingBy(ProductInventoryPO::getProductSkuId))
            .forEach((skuId, skuPOList) -> {
                map.put(skuId, skuPOList);
            });
        return map;
    }

    /**
     * 查询多个商品仓库库存数.
     */
    public Map<Long, BigDecimal> getWarehouseInventory(List<Long> productSkuIds, Integer warehouseId, Integer channel,
        Long secOwnerId) {

        Map<Long, BigDecimal> map = getProductInventoryCountMap(warehouseId, productSkuIds, channel, secOwnerId);

        // 传过来一个skuid,如果这个skuid查不到库存. 应该返回 k(skuID)->v(0)
        for (Long productSkuId : productSkuIds) {
            if (!map.containsKey(productSkuId)) {
                map.put(productSkuId, BigDecimal.ZERO);
            }
        }
        return map;
    }
    //
    // /**
    // * 查询一个产品在多个仓库下的库存数量 k(仓库id)-v(库存数量)
    // */
    // public Map<Integer, BigDecimal> getWarehouseInventory(Long productSkuId, List<Integer> warehouseIdList, Integer
    // channel, Long secOwnerId) {
    // HashMap<Integer, BigDecimal> map = new HashMap<>(16);
    // for (Integer warehouseId : warehouseIdList) {
    // ProductInventoryPO inventoryPO = getProductInventoryPO(productSkuId, warehouseId, channel, secOwnerId);
    // if (inventoryPO == null) {
    // continue;
    // }
    // map.put(inventoryPO.getWarehouseId(), inventoryPO.getTotalCountMinUnit());
    // }
    // return map;
    // }

    // /**
    // * 查询单个商品仓库库存数.
    // */
    // public BigDecimal getWarehouseInventory(Long productSkuId, Integer warehouseId, Integer channel, Long secOwnerId)
    // {
    // return getWarehouseInventory(Collections.singletonList(productSkuId), warehouseId, channel,
    // secOwnerId).get(productSkuId);
    // }

    // /**
    // * 查询单个商品在对应城市下所有仓库库存数总和
    // */
    // public BigDecimal getSumWarehouseInventory(Long productSkuId, Integer cityId, Integer channel, Long secOwnerId) {
    // BigDecimal sumInventory = BigDecimal.ZERO;
    // //查询出该城市下的所有仓库
    // List<Warehouse> warehouseList = tradingThirdUserBL.listAllEnableWarehouseByCityId(cityId, null);
    // List<Integer> warehouseIdList = warehouseList.stream().map(Warehouse::getId).collect(Collectors.toList());
    // for (Integer warehouseId : warehouseIdList) {
    // sumInventory = sumInventory.add(getWarehouseInventory(productSkuId, warehouseId, channel, secOwnerId));
    // }
    // return sumInventory;
    // }

    // /**
    // * 查询多个商品在对应城市下的仓库库存数总和
    // */
    // public Map<Long, BigDecimal> getCityWarehouseInventory(Set<Long> productSkuIds, Integer channel, Long secOwnerId)
    // {
    // List<Map<Long, BigDecimal>> inventoryProductSkuMap = productSkuQueryBL.getInventoryProductSkuMap(productSkuIds,
    // channel, secOwnerId);
    // HashMap<Long, BigDecimal> map = new HashMap<>(16);
    // inventoryProductSkuMap.forEach(n -> {
    // BigDecimal productSkuId = n.get("productSkuId");
    // BigDecimal sumInventory = n.get("sumInventory");
    // map.put(productSkuId.longValue(), sumInventory);
    // });
    // return map;
    // }

    // /**
    // * 根据skuId和仓库Id获取库存信息
    // */
    // public WarehouseStoreDTO getProductInventoryDetail(Long productSkuId, Integer warehouseId, Integer channel, Long
    // secOwnerId) {
    // WarehouseStoreDTO warehouseStoreDTO = null;
    // ProductInventoryPO inventoryPO = getProductInventoryPO(productSkuId, warehouseId, channel, secOwnerId);
    // if (inventoryPO != null) {
    // warehouseStoreDTO = new WarehouseStoreDTO();
    // warehouseStoreDTO.setId(inventoryPO.getId());
    // warehouseStoreDTO.setCityId(inventoryPO.getCityId());
    // warehouseStoreDTO.setOwnerId(inventoryPO.getOwnerId());
    // warehouseStoreDTO.setOwnerType(inventoryPO.getOwnerType());
    // warehouseStoreDTO.setWarehouseTotalCount(inventoryPO.getTotalCountMinUnit());
    // warehouseStoreDTO.setWarehouseId(inventoryPO.getWarehouseId());
    // warehouseStoreDTO.setProductSpecId(inventoryPO.getProductSpecificationId());
    // warehouseStoreDTO.setChannel(inventoryPO.getChannel());
    // warehouseStoreDTO.setSource(inventoryPO.getSource());
    // warehouseStoreDTO.setPackageName(inventoryPO.getPackageName());
    // warehouseStoreDTO.setUnitName(inventoryPO.getUnitName());
    // warehouseStoreDTO.setSpecificationName(inventoryPO.getSpecificationName());
    // warehouseStoreDTO.setPackageQuantity(inventoryPO.getPackageQuantity());
    // warehouseStoreDTO.setSecOwnerId(inventoryPO.getSecOwnerId());
    // warehouseStoreDTO.setProductSkuId(inventoryPO.getProductSkuId());
    // }
    // return warehouseStoreDTO;
    // }

    /**
     * 根据skuId和city获取库存信息
     */
    public List<InventoryBySkuIdCityIdDTO> getProductInventoryBySkuIdCityId(List<ProductSkuForInventoryDTO> dtos) {
        ArrayList<InventoryBySkuIdCityIdDTO> inventoryBySkuIdCityIdDTOList = new ArrayList<>();
        for (ProductSkuForInventoryDTO dto : dtos) {
            InventoryBySkuIdCityIdDTO inventoryBySkuIdCityIdDTO = new InventoryBySkuIdCityIdDTO();
            List<ProductInventoryInfoDTO> productInventoryInfoDTOList = ProductInventoryConverter
                .productInventoryPOList2DTOList(productInventoryPOMapper.getProductInventoryBySkuIdCityId(dto));
            inventoryBySkuIdCityIdDTO.setProductInventoryPOList(productInventoryInfoDTOList);
            inventoryBySkuIdCityIdDTOList.add(inventoryBySkuIdCityIdDTO);
        }
        return inventoryBySkuIdCityIdDTOList;
    }

    /**
     * 查询 库存报表（供应链）
     */
    public PageList<InventoryReportDTO> findStoreReportPageByAuth(StockReportSO so, PagerCondition pagerCondition) {
        PageList<InventoryReportDTO> resultList = new PageList<>();
        if (CollectionUtils.isEmpty(so.getWarehouseIds())) {
            return resultList;
        }
        if (StringUtils.isEmpty(so.getInventoryPinProperty())) {
            so.setInventoryPinProperty(null);
        }
        StopWatch stopWatch = new StopWatch("查询库存信息接口");
        stopWatch.start("查询库存信息");
        PageResult<InventoryReportDTO> pageResult = productInventoryPOMapper.findStoreReportPageByAuth(so,
            pagerCondition.getCurrentPage(), pagerCondition.getPageSize());
        stopWatch.stop();
        PageList<InventoryReportDTO> inventoryReportDTOPageList = pageResult.toPageList();
        List<InventoryReportDTO> dataList = inventoryReportDTOPageList.getDataList();
        // 如果根据SKUID查询，过滤非本次传递的SKUID以外的数据
        if (CollectionUtils.isNotEmpty(dataList) && CollectionUtils.isNotEmpty(so.getProductSkuIds())) {
            dataList.removeIf(p -> !so.getProductSkuIds().contains(p.getProductSkuId()));
        }
        stopWatch.start("查询库存ABC属性");
        // 根据sku 仓库id 查询库存ABC属性
        List<Long> skuIdList =
            dataList.stream().map(InventoryReportDTO::getProductSkuId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            List<ProductSkuConfigDTO> productSkuConfigDTOList =
                inventoryProductSkuMapper.queryInventoryProperty(skuIdList, so.getWarehouseIds().get(0));
            for (InventoryReportDTO inventoryReport : dataList) {
                ProductSkuConfigDTO productSkuConfigDTO = productSkuConfigDTOList.stream()
                    .filter(p -> p.getProductSkuId().compareTo(inventoryReport.getProductSkuId()) == 0).findFirst()
                    .orElse(new ProductSkuConfigDTO());
                inventoryReport.setInventoryPinProperty(productSkuConfigDTO.getInventoryRatio());
            }
        }
        stopWatch.stop();
        stopWatch.start("第三步查询");
        findDeliveryedCountByProductStoreIds(dataList);
        stopWatch.stop();
        LOG.info("查询库存信息接口耗时信息：{}", stopWatch.prettyPrint());
        return inventoryReportDTOPageList;
    }

    public void findDeliveryedCountByProductStoreIds(List<InventoryReportDTO> dtoList) {
        if (dtoList == null || dtoList.size() <= 0) {
            return;
        }
        List<String> lstProductStoreIds =
            dtoList.stream().map(p -> p.getProductStoreId()).distinct().collect(Collectors.toList());
        List<InventoryReportDTO> deliveryedCountByProductStoreIds =
            productInventoryPOMapper.findDeliveryedCountByProductStoreIds(lstProductStoreIds);
        if (deliveryedCountByProductStoreIds == null || deliveryedCountByProductStoreIds.size() <= 0) {
            return;
        }
        dtoList.forEach(p -> {
            Optional<InventoryReportDTO> reportDTO = deliveryedCountByProductStoreIds.stream()
                .filter(q -> q.getProductStoreId().equals(p.getProductStoreId())).findAny();
            reportDTO.ifPresent(inventoryReportDTO -> p.setDeliveryedCount(inventoryReportDTO.getDeliveryedCount()));
        });
    }

    /**
     * 查询 库存报表（供应链）
     */
    public PageList<InventoryReportDTO> findStoreReportPageByProductSpecification(StockReportSO so,
        PagerCondition pagerCondition) {
        PageList<InventoryReportDTO> resultList = new PageList<>();
        if (CollectionUtils.isEmpty(so.getWarehouseIds())) {
            return resultList;
        }
        PageResult<InventoryReportDTO> pageResult = productInventoryPOMapper.findStoreReportPageByProductSpecification(
            so, pagerCondition.getCurrentPage(), pagerCondition.getPageSize());
        PageList<InventoryReportDTO> inventoryReportDTOPageList = pageResult.toPageList();
        return inventoryReportDTOPageList;
    }

    /**
     * 查询产品库存列表
     */
    public PageList<WarehouseInventoryDTO> listWarehouseInventory(WarehouseInventoryQueryDTO warehouseInventoryQuery,
        PagerCondition pager) {
        // 查询productSku productsotre
        PageResult<WarehouseInventoryDTO> pageResult = productInventoryPOMapper
            .getListWarehouseInventory(warehouseInventoryQuery, pager.getCurrentPage(), pager.getPageSize());
        PageList<WarehouseInventoryDTO> toPageList = pageResult.toPageList();
        return toPageList;

    }

    /**
     * 查询产品库存列表
     */
    public PageList<WarehouseInventoryDTO>
        listWarehouseInventoryBySpecInfo(WarehouseInventoryQueryDTO warehouseInventoryQuery, PagerCondition pager) {
        // 查询productSku productsotre
        PageResult<WarehouseInventoryDTO> pageResult = productInventoryPOMapper
            .getListWarehouseInventoryBySpecInfo(warehouseInventoryQuery, pager.getCurrentPage(), pager.getPageSize());
        PageList<WarehouseInventoryDTO> toPageList = pageResult.toPageList();
        return toPageList;

    }

    /**
     * 查询有货主的产品库存列表
     */
    public PageList<WarehouseInventoryDTO> getListWarehouseInventoryByOwner(Integer pageNum, Integer pageSize) {
        PageResult<WarehouseInventoryDTO> pageResult =
            productInventoryPOMapper.getListWarehouseInventoryByOwner(pageNum, pageSize);
        PageList<WarehouseInventoryDTO> warehouseStoreDTOS = pageResult.toPageList();
        return warehouseStoreDTOS;
    }

    /**
     * 查询城市SKU库存
     */
    public List<ProductSkuInventoryCountDTO> listProductSkuInventoyByCity(Integer cityId, Integer channel,
        Integer source, Long secOwnerId) {
        return productInventoryPOMapper.listProductSkuInventoyByCity(cityId, channel, source, secOwnerId);
    }

    /**
     * 获取供应商库存汇总信息
     */
    public StoreDTOBySupplierOp
        getProductStoreForSupplierOp(WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO) {
        StoreDTOBySupplierOp storeDTOBySupplierOp =
            productStorePOMapper.getProductStoreForSupplierOp(warehouseStoreBySupplierOpSO);
        return storeDTOBySupplierOp;
    }

    /**
     * 调拨系统产品查询库存用的
     */
    public PageList<StoreDTOBySupplierOp> findProductWarehouseStoreListForAllocation(
        WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO, PagerCondition pagerCondition) {
        PageResult<StoreDTOBySupplierOp> pageResult = productStorePOMapper.findProductWarehouseStoreListForAllocation(
            warehouseStoreBySupplierOpSO, pagerCondition.getCurrentPage(), pagerCondition.getPageSize());
        PageList<StoreDTOBySupplierOp> pageList = pageResult.toPageList();
        List<StoreDTOBySupplierOp> dataList = pageList.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return pageList;
        }
        return pageList;
    }

    /**
     * 查询配送中数量
     */
    // @Deprecated
    // public Map<Long, BigDecimal> getdeliveryCountList(List<Long> productSkuIds, Integer warehouseId, Integer channel)
    // {
    //// //招商城市产品SKUID跟warehouseid不一致，需要先转换后再查库存
    //// Map<Long, Long> mapZhaoShangSku = productSkuZhaoShangBL.getActualDeliverySkuIdMap(productSkuIds, warehouseId,
    // null);
    //// if (mapZhaoShangSku.size() > 0) {
    //// //Key：招商产品SKU->Value:酒批产品SKU.移除招商产品SKU
    //// for (Long zhaoShangSku : mapZhaoShangSku.keySet()) {
    //// productSkuIds.remove(zhaoShangSku);
    //// }
    //// //添加招商发货城市SKU
    //// for (Long jiuPiSku : mapZhaoShangSku.values()) {
    //// productSkuIds.add(jiuPiSku);
    //// }
    //// }
    // Map<Long, ProductInventoryPO> productInventoryPOMap = getLongProductInventoryPOList(warehouseId, productSkuIds,
    // channel);
    // Map<Long, BigDecimal> map = new HashMap<>(16);
    // for (Map.Entry<Long, ProductInventoryPO> skuId : productInventoryPOMap.entrySet()) {
    // map.put(skuId.getKey(), skuId.getValue().getDeliveryedCount());
    // }
    //// if (mapZhaoShangSku.size() > 0) {
    //// //Key：招商产品SKU->Value:酒批产品SKU
    //// for (Map.Entry<Long, Long> zhaoShangEntity : mapZhaoShangSku.entrySet()) {
    //// if (!map.containsKey(zhaoShangEntity.getKey())) {
    //// Integer nStore = ObjectUtils.defaultIfNull(map.get(zhaoShangEntity.getValue()), 0);
    //// //添加招商城市SKU，对应库存为酒批城市SKU的库存
    //// map.put(zhaoShangEntity.getKey(), nStore);
    //// productSkuIds.add(zhaoShangEntity.getKey());
    //// }
    //// }
    //// //移除酒批城市SKU
    //// for (Long jiuPiSku : mapZhaoShangSku.values()) {
    //// map.remove(jiuPiSku);
    //// productSkuIds.remove(jiuPiSku);
    //// }
    //// }
    //
    // //传过来一个skuid,如果这个skuid查不到库存. 应该返回 k(skuID)->v(0)
    // for (Long productSkuId : productSkuIds) {
    // if (!map.containsKey(productSkuId)) {
    // map.put(productSkuId, BigDecimal.ZERO);
    // }
    // }
    // return map;
    // }

    // public Map<Long, ProductInventoryPO> getLongProductInventoryPOList(Integer warehouseId, List<Long>
    // productSkuIdList, Integer channel) {
    // Map<Long, ProductInventoryPO> map = new HashMap<>(16);
    // //通过productSkuId,warehoustID查询出ownType,ownId.批量查询减少数据库交互.
    // List<ProductInventoryPO> ProductInventoryPOList = getProductInventoryPODeliveryCount(productSkuIdList,
    // warehouseId, channel);
    //// ProductInventoryPOList.stream().collect(Collectors.toMap(n -> n.getProductSkuId(), n ->
    // n.getTotalCountMinUnit()));
    // //1.可能会出现一个skuid对应上2个库存(productstore)记录,影响库存流程.
    // //2.也可能会出现2个一样的skuid对应一条库存记录(交易平台并发产生了2条一模一样的productsku记录,主键不同而已),不影响流程.
    // for (ProductInventoryPO productInventoryPO : ProductInventoryPOList) {
    // Long skuId = productInventoryPO.getProductSkuId();
    // if (map.containsKey(skuId)) {
    // continue;
    // }
    // List<ProductInventoryPO> skuPOList = ProductInventoryPOList.stream().filter(p ->
    // p.getProductSkuId().equals(skuId) && p.getChannel() == channel).collect(Collectors.toList());
    // if (skuPOList.size() > 1) {//这里只能说明一个skuid关联了多条数据.
    // Long poIdCount = skuPOList.stream().map(p -> p.getId()).filter(p ->
    // !p.equals(productInventoryPO.getId())).count();
    // if (poIdCount > 0) {//这里说明一个skuid关联了2条以上了productstore数据.
    // //超过1条库存记录
    // throw new BusinessException("同一个SKU存在多条库存记录，请联系后台技术人员！SkuId:" + skuId + ",详细信息：" + JSON.toJSONString(skuPOList));
    // }
    // //todo 同一个城市同一个仓库下，导入多个运营模式不一样的SKU
    // }
    // map.put(skuId, productInventoryPO);
    // }
    // return map;
    // }

    /**
     * 将普通skuid转换成招商skuid
     *
     * @param productSkuIds 普通skuid
     * @param cityId 招商城市一定是898
     * @return
     */
    public Map<Long, Long> process2ZhaoShangSku(List<Long> productSkuIds, Integer cityId) {
        return productSkuZhaoShangBL.process2ZhaoShangSku(productSkuIds, cityId);
    }

    // /**
    // * 根据产品信息查询storeId
    // *
    // * @return
    // */
    // public Map<Long, ProductShopStoreReturnDTO> getProductInventoryStoreId(ProductSkuForInventoryShopDTO
    // productSkuForInventoryShopDTO) {
    // PageResult<ProductShopStoreInventoryPO> poList =
    // productInventoryPOMapper.getProductInventoryStoreId(productSkuForInventoryShopDTO,
    // productSkuForInventoryShopDTO.getCurrentPage(), productSkuForInventoryShopDTO.getPageSize());
    // Map<Long, ProductShopStoreReturnDTO> map = new HashMap<>(16);
    // for (ProductShopStoreInventoryPO po : poList) {
    // ProductShopStoreReturnDTO dto = new ProductShopStoreReturnDTO();
    // BeanUtils.copyProperties(po, dto);
    // map.put(po.getProductSkuId(), dto);
    // }
    // return map;
    // }

    // /**
    // * 根据产品信息查询storeId
    // *
    // * @return
    // */
    // public Map<String, Object> getProductInventoryStoreIdNew(ProductSkuForInventoryShopDTO
    // productSkuForInventoryShopDTO) {
    // Map<String, Object> dataMap = new HashMap<>(16);
    // PageResult<ProductShopStoreInventoryPO> poList =
    // productInventoryPOMapper.getProductInventoryStoreId(productSkuForInventoryShopDTO,
    // productSkuForInventoryShopDTO.getCurrentPage(), productSkuForInventoryShopDTO.getPageSize());
    // Map<Long, ProductShopStoreReturnDTO> map = new HashMap<>(16);
    // for (ProductShopStoreInventoryPO po : poList) {
    // ProductShopStoreReturnDTO dto = new ProductShopStoreReturnDTO();
    // BeanUtils.copyProperties(po, dto);
    // map.put(po.getProductSkuId(), dto);
    // }
    // dataMap.put("map", map);
    // dataMap.put("totalCount", poList.getPager().getRecordCount());
    // return dataMap;
    // }

    /**
     * 根据规格Id+OwnerId查询仓库库存
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    public List<WarehouseStoreDTO> getProductInventorys(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        /* wareHoseInventoryQueryDTO.setPageSize(3000);
        int pageCount = 1;
        List<ProductInventoryPO> productInventoryPOS = new ArrayList<>();
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            wareHoseInventoryQueryDTO.setCurrentPage(pageNum);
            PageResult<ProductInventoryPO> pageResult = productInventoryPOMapper.pageListProductInventorys(wareHoseInventoryQueryDTO);
            List<ProductInventoryPO> result = pageResult.getResult();
            if (pageNum == 1) {
                pageCount = pageResult.getPager().getTotalPage();
            }
            if(!CollectionUtils.isEmpty(result)){
                productInventoryPOS.addAll(result);
            }
        }*/
        List<ProductInventoryPO> productInventoryPOS =
            productInventoryPOMapper.getProductInventorys(wareHoseInventoryQueryDTO);
        List<WarehouseStoreDTO> warehouseStoreDTOS =
            ProductStoreConverter.productStorePOS2WarehouseStoreDTOS(productInventoryPOS);
        return warehouseStoreDTOS;
    }

    /**
     * 通过城市id,所属人类型.查询某个城市下库存信息
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    public PageList<WarehouseStoreDTO>
        getProductInventorysByPager(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        PageResult<ProductInventoryPO> productInventoryPOS =
            productInventoryPOMapper.getProductInventorysByPager(wareHoseInventoryQueryDTO,
                wareHoseInventoryQueryDTO.getCurrentPage(), wareHoseInventoryQueryDTO.getPageSize());
        PageList<WarehouseStoreDTO> warehouseStoreDTOS =
            ProductStoreConverter.productStorePOS2WarehouseStoreDTOS(productInventoryPOS);
        return warehouseStoreDTOS;
    }

    /**
     * 手动分页返回
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    public PageList<WarehouseStoreDTO>
        getProductInventoryByHandPager(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        PageList<WarehouseStoreDTO> returnList = new PageList<>();
        final int totalCount =
            productInventoryPOMapper.pageFindProductInventoryByWarehouseIdCount(wareHoseInventoryQueryDTO);
        Pager pager =
            new Pager(wareHoseInventoryQueryDTO.getCurrentPage(), wareHoseInventoryQueryDTO.getPageSize(), totalCount);
        if (totalCount == 0) {
            returnList.setPager(pager);
            returnList.setDataList(Collections.emptyList());
            return returnList;
        }

        // 遍历剩余的页数
        final int totalPage = pager.getTotalPage();
        List<WarehouseStoreDTO> warehouseStoreDTOList = new ArrayList<>();

        for (int i = 1; i <= totalPage; i++) {
            wareHoseInventoryQueryDTO.setCurrentPage(i);

            List<ProductInventoryPO> pagerResult =
                productInventoryPOMapper.pageFindProductInventoryByWarehouseId(wareHoseInventoryQueryDTO,
                    wareHoseInventoryQueryDTO.getOffset(), wareHoseInventoryQueryDTO.getLimitnum());
            if (CollectionUtils.isNotEmpty(pagerResult)) {
                List<WarehouseStoreDTO> warehouseStoreDTOS =
                    ProductStoreConverter.productStorePOS2WarehouseStoreDTOS(pagerResult);
                warehouseStoreDTOList.addAll(warehouseStoreDTOS);
            }
        }
        returnList.setDataList(warehouseStoreDTOList);
        returnList.setPager(pager);
        return returnList;
    }

    /**
     * 通过城市id,所属人类型.查询某个城市下库存信息
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    public PageList<WarehouseStoreDTO>
        getProductInventorysFromProductInfoSpecificationByPager(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        PageResult<ProductInventoryPO> productInventoryPOS =
            productInventoryPOMapper.getProductInventorysByProductInfoSpecification(wareHoseInventoryQueryDTO,
                wareHoseInventoryQueryDTO.getCurrentPage(), wareHoseInventoryQueryDTO.getPageSize());
        PageList<WarehouseStoreDTO> warehouseStoreDTOS =
            ProductStoreConverter.productStorePOS2WarehouseStoreDTOS(productInventoryPOS);
        return warehouseStoreDTOS;
    }

    /**
     * 通过城市id,所属人类型.查询某个城市下库存信息（包含没有仓库库存的，即没有入过库的）
     *
     * @return
     */
    public List<WarehouseStoreDTO> getProductInventorysByAll(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        List<ProductInventoryPO> productInventoryPOS =
            productInventoryPOMapper.getProductInventorysByAll(wareHoseInventoryQueryDTO);
        List<WarehouseStoreDTO> warehouseStoreDTOS =
            ProductStoreConverter.productStorePOS2WarehouseStoreDTOS(productInventoryPOS);
        if (CollectionUtils.isNotEmpty(warehouseStoreDTOS)) {
            warehouseStoreDTOS.forEach(p -> {
                if (p.getWarehouseId() == null) {
                    p.setWarehouseId(wareHoseInventoryQueryDTO.getWarehouseId());
                }
            });
        }
        return warehouseStoreDTOS;
    }

    /**
     * 查找仓库库存（易经销）
     *
     * @param warehouseInventoryYJXQueryDTO
     * @return
     */
    public PageList<WarehouseInventoryYJXDTO>
        listWarehouseInventoryYJX(WarehouseInventoryYJXQueryDTO warehouseInventoryYJXQueryDTO) {
        AssertUtils.notNull(warehouseInventoryYJXQueryDTO.getFacilitatorId(), "服务商id不能为空");
        AssertUtils.notNull(warehouseInventoryYJXQueryDTO.getPageNum(), "页码不能为空");
        AssertUtils.notNull(warehouseInventoryYJXQueryDTO.getPageSize(), "每页大小不能为空");

        PageResult<WarehouseInventoryYJXDTO> pageResult =
            productInventoryPOMapper.listWarehouseInventoryYJX(warehouseInventoryYJXQueryDTO);
        PageList<WarehouseInventoryYJXDTO> pageList = pageResult.toPageList();
        if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
            pageList.getDataList().forEach(p -> {
                BigDecimal[] remainder = p.getUnitTotalCount().divideAndRemainder(p.getPackageQuantity());
                p.setPackageCount(
                    (p.getPackageQuantity() == null || p.getUnitTotalCount() == null) ? BigDecimal.ZERO : remainder[0]);
                p.setUnitCount(
                    (p.getPackageQuantity() == null || p.getUnitTotalCount() == null) ? BigDecimal.ZERO : remainder[1]);
            });
        }
        return pageList;
    }

    /**
     * 获取已下单未发货的数量（待出库数量） key: %s-%s（规格id + ownerId） value: 待出库数量
     */
    @Deprecated
    public Map<String, BigDecimal> getWaitDeliveryCountMap(Integer cityId, Integer warehouseId,
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIds) {
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 废弃了
        Map<String, BigDecimal> waitDeliveryCountMap = new HashMap<>(16);
        // List<List<ProductSpecAndOwnerIdDTO>> lists = InventoryConvertor.splitListNew(specAndOwnerIds, 100);
        // for (List<ProductSpecAndOwnerIdDTO> list : lists) {
        // List<CityMergeDTO> infoList = list.stream().map(p -> {
        // CityMergeDTO cityMergeDTO = new CityMergeDTO();
        // cityMergeDTO.setProductSpecificationId(p.getProductSpecId());
        // cityMergeDTO.setProductOwnerId(p.getOwnerId());
        // return cityMergeDTO;
        // }).collect(Collectors.toList());
        // List<CityMergeDTO> count = iOrderQueryService.getCityMergeCountBySkus(infoList, cityId, warehouseId);
        // if (CollectionUtils.isNotEmpty(count)) {
        // count.forEach(n -> {
        // waitDeliveryCountMap.put(
        // String.format("%s-%s", n.getProductSpecificationId(), n.getProductOwnerId()), n.getCount());
        // });
        // }
        // }
        return waitDeliveryCountMap;
    }

    /**
     * 获取待出库产品列表
     */
    public PageList<ProductWaitDeliveryDTO> listProductWaitDelivery(ProductWaitDeliverySO productWaitDeliverySO) {
        PageResult<ProductWaitDeliveryDTO> pageResult =
            productInventoryPOMapper.listProductWaitDelivery(productWaitDeliverySO);
        PageList<ProductWaitDeliveryDTO> waitDeliveryDTOList = pageResult.toPageList();
        if (CollectionUtils.isNotEmpty(waitDeliveryDTOList.getDataList())) {
            // 获取待出库数量Map
            List<ProductSpecAndOwnerIdDTO> specAndOwnerIdDTOS = waitDeliveryDTOList.getDataList().stream().map(p -> {
                ProductSpecAndOwnerIdDTO dto = new ProductSpecAndOwnerIdDTO();
                dto.setProductSpecId(p.getProductSpecificationId());
                dto.setOwnerId(p.getOwnerId());
                return dto;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> waitDeliveryCountMap = getWaitDeliveryCountMap(productWaitDeliverySO.getCityId(),
                productWaitDeliverySO.getWarehouseId(), specAndOwnerIdDTOS);

            // 获取产品分配波次的数量Map
            List<Long> productSkuIds = waitDeliveryDTOList.getDataList().stream().map(p -> p.getProductSkuId())
                .distinct().collect(Collectors.toList());
            Map<Long, ProductAllotTypeDTO> allotTypeDTOMap = iBatchOrderInfoQueryService.getProductAllotCountMap(
                productWaitDeliverySO.getCityId(), productWaitDeliverySO.getWarehouseId(), productSkuIds);

            // 查询指定货位上的库存（出库位、集货位）
            Map<Long, BigDecimal> locationStoreMap =
                getAssignLocationStoreMap(productWaitDeliverySO.getWarehouseId(), productSkuIds);

            waitDeliveryDTOList.getDataList().forEach(p -> {
                if (null != waitDeliveryCountMap) {
                    // 待出库数量
                    p.setDeliveryTotalUnitCount(waitDeliveryCountMap
                        .get(String.format("%s-%s", p.getProductSpecificationId(), p.getOwnerId())));
                }
                // 可用库存数量（仓库库存 - 已分配波次数量 - 指定货位[出库位、集货位]库存）
                BigDecimal enableCount = p.getStoreTotalUnitCount();

                if (null != allotTypeDTOMap) {
                    ProductAllotTypeDTO productAllotTypeDTO = allotTypeDTOMap.get(p.getProductSkuId());
                    if (null != productAllotTypeDTO) {
                        // 未分配波次数量
                        p.setNotAllotTotalUnitCount(productAllotTypeDTO.getNotAllotCount());
                        // 减去已分配波次数量
                        if (null != enableCount) {
                            enableCount = enableCount.subtract(productAllotTypeDTO.getAllotCount());
                        }
                    }
                }

                // 减去指定货位[出库位、集货位]库存
                if (null != enableCount && locationStoreMap.get(p.getProductSkuId()) != null) {
                    enableCount = enableCount.subtract(locationStoreMap.get(p.getProductSkuId()));
                }
                p.setEnableTotalUnitCount(enableCount);

                // 设置大小数量
                if (null != p.getPackageQuantity() && p.getPackageQuantity().compareTo(BigDecimal.ZERO) != 0) {
                    if (null != p.getDeliveryTotalUnitCount()) {
                        p.setDeliveryPackageCount(
                            p.getDeliveryTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[0]);
                        p.setDeliveryUnitCount(
                            p.getDeliveryTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[1]);
                    }
                    if (null != p.getStoreTotalUnitCount()) {
                        p.setStorePackageCount(
                            p.getStoreTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[0]);
                        p.setStoreUnitCount(p.getStoreTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[1]);
                    }
                    if (null != p.getEnableTotalUnitCount()) {
                        p.setEnablePackageCount(
                            p.getEnableTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[0]);
                        p.setEnableUnitCount(p.getEnableTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[1]);
                    }
                    if (null != p.getNotAllotTotalUnitCount()) {
                        p.setNotAllotPackageCount(
                            p.getNotAllotTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[0]);
                        p.setNotAllotUnitCount(
                            p.getNotAllotTotalUnitCount().divideAndRemainder(p.getPackageQuantity())[1]);
                    }
                }
            });
        }
        return waitDeliveryDTOList;
    }

    /**
     * 仓库库存报表
     *
     * @return
     */
    public PageList<WarehouseInventoryReportDTO>
        listWarehouseInventoryReport(WarehouseInventoryReportSO warehouseInventoryReportSO) {
        PageResult<WarehouseInventoryReportDTO> pageResult =
            productInventoryPOMapper.listWarehouseInventoryReport(warehouseInventoryReportSO);
        PageList<WarehouseInventoryReportDTO> pageList = pageResult.toPageList();
        if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
            // 获取城市名名称集合
            List<Integer> cityIdset = pageList.getDataList().stream().filter(p -> p.getCityId() != null)
                .map(p -> p.getCityId()).distinct().collect(Collectors.toList());
            Map<Integer, OrgDTO> orgByIds = iOrgService.findOrgByIds(cityIdset);
            // 获取仓库名称集合
            List<Integer> warehouseIds = pageList.getDataList().stream().filter(p -> p.getWarehouseId() != null)
                .map(p -> p.getWarehouseId()).distinct().collect(Collectors.toList());
            Map<Integer, String> warhouseNameMap = iStoreWareHouseService.findWareHouseNamesByIds(warehouseIds);
            // 填充
            pageList.getDataList().forEach(p -> {
                if (p.getProductSkuId() == null) {
                    p.setStatisticsClassName("--");
                }
                // 城市名称
                if (null != orgByIds) {
                    OrgDTO orgDTO = orgByIds.get(p.getCityId());
                    p.setCityName(orgDTO == null ? null : orgDTO.getOrgName());
                }
                // 当前产品有库存
                if (StringUtils.isNotEmpty(p.getStoreId())) {
                    // 仓库名称
                    if (null != warhouseNameMap) {
                        p.setWarehouseName(warhouseNameMap.get(p.getWarehouseId()));
                    }
                    // 产品渠道名称
                    p.setChannelName(getChannelName(p.getChannel()));
                    // 仓库库存
                    if (p.getTotalCountMinUnit() != null && p.getSpecQuantity() != null && p.getSpecQuantity() != 0) {
                        p.setPackageCount(
                            p.getTotalCountMinUnit().divideAndRemainder(new BigDecimal(p.getSpecQuantity()))[0]);
                        p.setUnitCount(
                            p.getTotalCountMinUnit().divideAndRemainder(new BigDecimal(p.getSpecQuantity()))[1]);
                        p.setWarehouseInventory(
                            p.getPackageCount().stripTrailingZeros().toPlainString() + p.getPackageCountName()
                                + p.getUnitCount().stripTrailingZeros().toPlainString() + p.getUnitCountName());
                    }
                }
            });
        }
        return pageList;
    }

    private String getChannelName(Integer channel) {
        String channelName = "";
        if (channel != null) {
            if (channel == ProductChannelType.JIUPI) {
                channelName = "零售产品";
            } else if (channel == ProductChannelType.LARGE) {
                channelName = "大宗产品";
            }
        }
        return channelName;
    }

    /**
     * 动销产品查询
     *
     * @param queryDTO
     * @return
     */
    public PageList<DynamicProductDTO> findDynamicProductList(DynamicProductQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "动销产品查询参数不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "动销产品查询仓库不能为空！");
        AssertUtils.notNull(queryDTO.getDynamicStartTime(), "动销时间-起始时间不能为空！");
        AssertUtils.notNull(queryDTO.getDynamicEndTime(), "动销时间-截止时间不能为空！");
        // 开启货位库存则查询，否则返回null
        PageList<DynamicProductDTO> defaultPageList = new PageList<>();
        defaultPageList.setDataList(Collections.EMPTY_LIST);
        defaultPageList.setPager((new Pager(queryDTO.getPageNum(), queryDTO.getPageSize(), 0)));
        Boolean isOpenStock = warehouseConfigService.isOpenLocationStock(queryDTO.getWarehouseId());
        if (!isOpenStock) {
            return defaultPageList;
        }
        // 查询配置货位
        String contentValue = iContentConfigurationService.getContentValue(DYNAMIC_PRODUCT_QUERY_CATEGORY, null, "");
        if (StringUtils.isEmpty(contentValue)) {
            LOG.warn("动销产品统计配置货位/货区为空！");
            return defaultPageList;
        }
        List<Integer> subCategorys = Lists.newArrayList(contentValue.split(",")).stream().filter(d -> d != null)
            .map(d -> Integer.valueOf(d)).collect(Collectors.toList());
        queryDTO.setSubCategorys(subCategorys);
        LOG.info("动销产品查询参数:{}", JSON.toJSONString(queryDTO));
        PageResult<DynamicProductDTO> productStoreList = productStorePOMapper.findDynamicProductList(queryDTO);
        List<DynamicProductDTO> result = productStoreList.getResult();
        if (CollectionUtils.isNotEmpty(result)) {
            result.stream().filter(d -> d != null).forEach(d -> {
                // 大小件转化
                BigDecimal unitTotalCount = ObjectUtils.defaultIfNull(d.getUnitTotalCount(), BigDecimal.ZERO);
                BigDecimal[] counts = unitTotalCount.divideAndRemainder(d.getPackageQuantity());
                d.setPackageCount(counts[0]);
                d.setUnitCount(counts[1]);
            });
        }
        PageList<DynamicProductDTO> pageList = new PageList<>();
        pageList.setDataList(result);
        pageList.setPager(productStoreList.getPager());
        return pageList;
    }

    /**
     * 获取产品关联的合并产品的库存总和（不包括当前查询产品本身的库存）
     *
     * @return
     */
    public Map<Long, BigDecimal> getRefProductInventoryMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return null;
        }

        // 1、获取产品的关联产品
        Map<ProductSkuDTO, List<ProductSkuDTO>> refProductMap =
            iProductRelationGroupService.findGroupTotalProductBySkuIds(warehouseId, productSkuIds);
        if (refProductMap == null || refProductMap.isEmpty()) {
            return null;
        }
        LOG.info("关联产品：{}", JSON.toJSONString(refProductMap));
        // 2、合并关联产品的skuId集合
        List<Long> refProductSkuIds = refProductMap.values().stream().filter(e -> CollectionUtils.isNotEmpty(e))
            .flatMap(e -> e.stream()).filter(e -> e != null && e.getProductSkuId() != null)
            .map(e -> e.getProductSkuId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refProductSkuIds)) {
            return null;
        }
        // 3、查询关联产品的库存
        Map<Long, ProductStoreDTO> productStoreMap = listProductInventoryBySkuIds(refProductSkuIds, warehouseId);
        LOG.info("查询库存结果：{}", JSON.toJSONString(productStoreMap));
        // 4、计算关联产品的库存总和
        Map<Long, BigDecimal> inventoryMap = new HashMap<>(16);
        refProductMap.forEach((skuId, refSkuDTOList) -> {
            BigDecimal sum = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(refSkuDTOList)) {
                List<Long> refSkuIds = refSkuDTOList.stream().filter(Objects::nonNull).map(q -> q.getProductSkuId())
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                for (Long refSkuId : refSkuIds) {
                    ProductStoreDTO productStore = productStoreMap.get(refSkuId);
                    if (productStore == null) {
                        continue;
                    }
                    sum = ObjectUtils.defaultIfNull(productStore.getUnitTotolCount(), BigDecimal.ZERO).add(sum);
                }
            }
            inventoryMap.put(skuId.getProductSkuId(), sum);
        });
        LOG.info("关联产品的库存总和：{}", JSON.toJSONString(inventoryMap));
        return inventoryMap;
    }

    /**
     * 获取产品的可用库存（仓库库存 - 已分波次数量 - 指定货位[出库位、集货位]库存）
     *
     * @return
     */
    public Map<Long, BigDecimal> getEnableStoreCountMap(Integer cityId, Integer warehouseId, List<Long> skuIds) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(skuIds, "产品sku集合不能为空");

        Map<Long, BigDecimal> enableMap = new HashMap<>(16);

        // 查询仓库库存
        Map<Long, BigDecimal> storeMap = getWarehouseInventory(skuIds, warehouseId, ProductChannelType.JIUPI, null);
        // 查询已分波次数量
        Map<Long, ProductAllotTypeDTO> allotTypeDTOMap =
            iBatchOrderInfoQueryService.getProductAllotCountMap(cityId, warehouseId, skuIds);
        // 查询指定货位上的库存（出库位、集货位）
        Map<Long, BigDecimal> locationStoreMap = getAssignLocationStoreMap(warehouseId, skuIds);

        storeMap.forEach((skuId, storeCount) -> {
            BigDecimal count = storeCount;
            // 减去已分波次数量
            ProductAllotTypeDTO productAllotTypeDTO = allotTypeDTOMap.get(skuId);
            if (productAllotTypeDTO != null) {
                count = count.subtract(productAllotTypeDTO.getAllotCount());
            }
            // 减去指定货位上的库存（出库位、集货位）
            if (locationStoreMap.get(skuId) != null) {
                count = count.subtract(locationStoreMap.get(skuId));
            }
            enableMap.put(skuId, count);
        });
        return enableMap;
    }

    /**
     * 获取产品在指定货位上的库存（出库位、集货位）
     *
     * @return
     */
    private Map<Long, BigDecimal> getAssignLocationStoreMap(Integer warehouseId, List<Long> skuIds) {
        // 查询货位库存
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        batchInventoryQueryDTO.setProductSkuIdList(skuIds);
        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationEnum.出库位.getType());
        subCategoryList.add(LocationEnum.集货位.getType());
        subCategoryList.add(LocationAreaEnum.周转区.getType());
        subCategoryList.add(LocationAreaEnum.集货区.getType());
        batchInventoryQueryDTO.setSubCategoryList(subCategoryList);
        List<BatchInventoryDTO> batchInventoryDTOS =
            iBatchInventoryQueryService.findInventoryLocationBySkuId(batchInventoryQueryDTO);
        // 根据skuId分组
        Map<Long, BigDecimal> resultMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(batchInventoryDTOS)) {
            Map<Long,
                List<BatchInventoryDTO>> batchInventoryMap = batchInventoryDTOS.stream()
                    .filter(
                        p -> p.getStoreTotalCount() != null && p.getStoreTotalCount().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.groupingBy(p -> p.getProductSkuId()));
            batchInventoryMap.forEach((skuId, batchInventoryList) -> {
                if (CollectionUtils.isNotEmpty(batchInventoryList)) {
                    resultMap.put(skuId, batchInventoryList.stream().map(p -> p.getStoreTotalCount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            });
        }
        return resultMap;
    }

    /**
     * 根据库存ID获取库存信息
     *
     * @return
     */
    public ProductStoreBaseDTO getProductStoreById(String id) {
        AssertUtils.notNull(id, "库存id不能为空");
        ProductStorePO productStorePO = productStorePOMapper.getProductStoreById(id);
        if (productStorePO == null) {
            return null;
        }
        ProductStoreBaseDTO storeBaseDTO = new ProductStoreBaseDTO();
        BeanUtils.copyProperties(productStorePO, storeBaseDTO);
        return storeBaseDTO;
    }

    /**
     * 查看产品及关联产品的销售库存
     *
     * @return
     */
    public List<ProductRelateSaleStoreDTO> getProductRelateSaleStoreMap(ProductRelateSaleStoreSO productRelateStoreSO) {
        AssertUtils.notNull(productRelateStoreSO, "参数不能为空");
        AssertUtils.notNull(productRelateStoreSO.getCityId(), "城市ID不能为空");
        AssertUtils.notNull(productRelateStoreSO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(productRelateStoreSO.getProductSkuIds(), "产品skuID不能为空");

        // 查skuid
        Map<Long, ProductSkuDTO> productSkuMap =
            iProductSkuQueryService.findBySkuWithMap(productRelateStoreSO.getProductSkuIds());
        if (productSkuMap == null || productSkuMap.isEmpty()) {
            return null;
        }

        // 1、获取产品的关联产品
        Map<Long, List<ProductSkuDTO>> refProductMap = iProductRelationGroupService
            .findSameGroupProductDirect(productRelateStoreSO.getWarehouseId(), productRelateStoreSO.getProductSkuIds());
        LOG.info("查询出关联产品：{}", JSON.toJSONString(refProductMap));
        if (refProductMap == null || refProductMap.isEmpty()) {
            return null;
        }
        // 2、查询销售库存
        ProductStoreQueryDTO productStoreQueryDTO = new ProductStoreQueryDTO();
        productStoreQueryDTO.setCityId(productRelateStoreSO.getCityId());
        productStoreQueryDTO.setWarehouseId(productRelateStoreSO.getWarehouseId());
        // 查询所有sku
        List<Long> productSkuIdAll = new ArrayList<>();
        productSkuIdAll.addAll(productRelateStoreSO.getProductSkuIds());
        refProductMap.forEach((k, list) -> {
            if (CollectionUtils.isNotEmpty(list)) {
                List<Long> skuIds = list.stream().filter(p -> p != null && p.getProductSkuId() != null)
                    .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
                productSkuIdAll.addAll(skuIds);
            }
        });
        productStoreQueryDTO.setProductSkuIds(productSkuIdAll);
        productStoreQueryDTO.setPrintLog(true);
        LOG.info("查看产品及关联产品的销售库存参数：{}", JSON.toJSONString(productStoreQueryDTO));
        List<WarehouseStoreDTO> storeDTOList = warehouseInventoryCheckBL.getSaleInventoryList(productStoreQueryDTO);
        if (CollectionUtils.isEmpty(storeDTOList)) {
            LOG.info("查看产品及关联产品的销售库存为空");
            return null;
        }

        // 3、筛选出待查询的产品
        List<ProductRelateSaleStoreDTO> resultList = new ArrayList<>();
        productRelateStoreSO.getProductSkuIds().forEach(skuId -> {
            ProductSkuDTO skuDTO = productSkuMap.get(skuId);
            if (skuDTO == null) {
                return;
            }

            // 找到关联产品的销售库存
            List<ProductSkuDTO> refProductList = refProductMap.get(skuId);
            if (CollectionUtils.isEmpty(refProductList)) {
                return;
            }
            List<Long> refSkuIds = refProductList.stream().filter(p -> p != null && p.getProductSkuId() != null)
                .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(refSkuIds)) {
                return;
            }
            List<ProductRelateSaleStoreDTO> relateList =
                storeDTOList.stream().filter(p -> refSkuIds.contains(p.getProductSkuId()))
                    .map(p -> convertProductRelateStoreDTO(p)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(relateList)) {
                return;
            }

            // 当前易款产品的销售库存
            ProductRelateSaleStoreDTO saleStoreDTO;
            Optional<ProductRelateSaleStoreDTO> optional =
                storeDTOList.stream().filter(p -> Objects.equals(skuId, p.getProductSkuId()))
                    .map(p -> convertProductRelateStoreDTO(p)).findFirst();
            if (optional.isPresent()) {
                saleStoreDTO = optional.get();
            } else {
                saleStoreDTO = new ProductRelateSaleStoreDTO();
                saleStoreDTO.setCityId(productRelateStoreSO.getCityId());
                saleStoreDTO.setWarehouseId(productRelateStoreSO.getWarehouseId());
                saleStoreDTO.setProductSkuId(skuDTO.getProductSkuId());
                saleStoreDTO.setProductSpecId(skuDTO.getProductSpecificationId());
                saleStoreDTO.setOwnerId(skuDTO.getCompany_Id());
                saleStoreDTO.setOwnerName(skuDTO.getOwnerName());
                saleStoreDTO.setSaleStoreTotalCount(BigDecimal.ZERO);
            }
            saleStoreDTO.setRefProductStoreList(relateList);

            resultList.add(saleStoreDTO);
        });
        LOG.info("查看产品及关联产品的销售库存结果：{}", JSON.toJSONString(resultList));
        return resultList;
    }

    /**
     * 根据sku获取点前产品总库存数量(包含产品和关联产品库存数)
     *
     * @param productSkuIdList 产品skuId
     * @param warehouseId 仓库ID
     * @return 产品对应的库存（库存 = 产品自身库存 + 关联产品库存）
     */
    public Map<Long, BigDecimal> genProductAndRefProductTotalStore(List<Long> productSkuIdList, Integer warehouseId) {
        LOG.info("仓库：[{}] 查询产品总库存参数：{}", warehouseId, JSON.toJSONString(productSkuIdList));
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIdList, "产品skuId不能为空");
        // 去重后skuId
        List<Long> newSkuIdList =
            productSkuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 根据sku的实时库存
        Map<Long, ProductStoreDTO> productStoreMap = listProductInventoryBySkuIds(newSkuIdList, warehouseId);
        if (productStoreMap == null) {
            productStoreMap = new HashMap<>(16);
        }
        //
        // 获取产品关联的合并产品的库存总和
        // Map<Long, BigDecimal> refInventory = getRefProductInventoryMap(null, warehouseId, newSkuIdList);
        // if (refInventory == null) {
        // refInventory = new HashMap<>(16);
        // }
        Map<Long, BigDecimal> result = new HashMap<>(16);
        for (int i = 0; i < newSkuIdList.size(); i++) {
            Long skuId = newSkuIdList.get(i);
            if (skuId == null) {
                continue;
            }
            // 记录当前 sku 总库存
            BigDecimal storeTotalCount = BigDecimal.ZERO;
            ProductStoreDTO productStoreDTO = productStoreMap.get(skuId);
            if (productStoreDTO != null && productStoreDTO.getUnitTotolCount() != null) {
                storeTotalCount = storeTotalCount.add(productStoreDTO.getUnitTotolCount());
            }
            // 增加关联产品库存
            // storeTotalCount = storeTotalCount.add(ObjectUtils.defaultIfNull(refInventory.get(skuId),
            // BigDecimal.ZERO));
            result.put(skuId, storeTotalCount);
        }
        return result;
    }

    private ProductRelateSaleStoreDTO convertProductRelateStoreDTO(WarehouseStoreDTO warehouseStoreDTO) {
        ProductRelateSaleStoreDTO productRelateStoreDTO = new ProductRelateSaleStoreDTO();
        BeanUtils.copyProperties(warehouseStoreDTO, productRelateStoreDTO);
        return productRelateStoreDTO;
    }

    public List<ProductStoreBaseDTO> findProductStoreBySpec(ProductStoreQueryDTO productStoreQueryDTO) {
        return productStorePOMapper.findProductStoreBySpec(productStoreQueryDTO);
    }

    /**
     * 查询经销商在指定仓库是否有库存
     *
     * @return
     */
    public Boolean isExistProductStoreByOwner(Integer warehouseId, Long ownerId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notNull(ownerId, "货主ID不能为空");
        ProductStorePO productStorePO =
            productStorePOMapper.getProductStoreByOwnerIdAndWarehouseId(warehouseId, ownerId);
        if (productStorePO != null) {
            return true;
        }
        return false;
    }

    /**
     * 分页查出动盘产品 - 开启货位库存
     *
     * @return
     */
    public PageList<DynamicProductDTO> findDynamicProductPageList(DynamicProductQueryDTO queryDTO) {
        LOG.info("动销产品查询参数:{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "动销产品查询参数不能为空！");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "动销产品查询仓库不能为空！");
        AssertUtils.notNull(queryDTO.getDynamicStartTime(), "动销时间-起始时间不能为空！");
        AssertUtils.notNull(queryDTO.getDynamicEndTime(), "动销时间-截止时间不能为空！");
        AssertUtils.notEmpty(queryDTO.getSubCategorys(), "动销产品货区/货区类型不能为空");
        // 查询动销产品
        PageResult<DynamicProductDTO> productStoreList = productStorePOMapper.findDynamicProductList(queryDTO);
        List<DynamicProductDTO> result = productStoreList.getResult();
        if (CollectionUtils.isNotEmpty(result)) {
            result.stream().filter(d -> d != null).forEach(d -> {
                // 大小件转化
                BigDecimal unitTotalCount = ObjectUtils.defaultIfNull(d.getUnitTotalCount(), BigDecimal.ZERO);
                BigDecimal[] counts = unitTotalCount.divideAndRemainder(d.getPackageQuantity());
                d.setPackageCount(counts[0]);
                d.setUnitCount(counts[1]);
            });
        }
        PageList<DynamicProductDTO> pageList = new PageList<>();
        pageList.setDataList(result);
        pageList.setPager(productStoreList.getPager());
        return pageList;
    }

    /**
     * 查询未开启货位库存动盘产品
     */
    public List<NotOpenStockDailyPickingProductDTO>
        findNotOpenStockDynamicProductList(NotOpenStockDailyPickingProductQueryDTO queryDTO) {
        LOG.info("查询未开启货位库存动盘产品参数:{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "每日拣货产品[未开启货位库存]查询参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(queryDTO.getDynamicStartTime(), "每日拣货起始时间不能为空");
        AssertUtils.notNull(queryDTO.getDynamicEndTime(), "每日拣货截止时间不能为空");
        return productStorePOMapper.findNotOpenStockDynamicProductList(queryDTO);
    }

    public List<WarehouseStoreDTO> getDisposedProductInventories(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        List<WarehouseStoreDTO> disposedProductInventories = new ArrayList<>();
        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationAreaEnum.残次品区.getType());
        subCategoryList.add(LocationEnum.残次品位.getType());
        List<WarehouseStoreDTO> warehouseStoreDTOS =
            productStorePOMapper.getDisposedProductInventories(wareHoseInventoryQueryDTO, subCategoryList);
        if (CollectionUtils.isNotEmpty(warehouseStoreDTOS)) {
            warehouseStoreDTOS.stream().collect(Collectors.groupingBy(WarehouseStoreDTO::getId))
                .forEach((storeId, warehouseStores) -> {
                    BigDecimal unitTotalCount = warehouseStores.stream().map(WarehouseStoreDTO::getWarehouseTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (unitTotalCount.compareTo(BigDecimal.ZERO) != 0) {
                        WarehouseStoreDTO disposedProductInventory = new WarehouseStoreDTO();
                        BeanUtils.copyProperties(warehouseStores.get(0), disposedProductInventory);
                        disposedProductInventory.setWarehouseTotalCount(unitTotalCount);

                        disposedProductInventories.add(disposedProductInventory);
                    }
                });
        }
        return disposedProductInventories;
    }

    public List<DisposedProductInventorDTO>
        findDisposedProductInventorDetails(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        List<Integer> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationAreaEnum.残次品区.getType());
        subCategoryList.add(LocationEnum.残次品位.getType());
        return productStorePOMapper.findDisposedProductInventorDetails(wareHoseInventoryQueryDTO, subCategoryList);
    }

    /**
     * 根据规格+货主查找对应的二级货主
     *
     * @return
     */
    public Map<String, List<Long>> getSecOwnerIdMap(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO) {
        List<ProductInventoryPO> productInventoryPOS =
            productInventoryPOMapper.listProductSecOwnerId(productSecOwnerIdQueryDTO);
        LOG.info("根据规格+货主查找对应的二级货主参数：{}", JSON.toJSONString(productSecOwnerIdQueryDTO));
        if (CollectionUtils.isEmpty(productInventoryPOS)) {
            return Collections.EMPTY_MAP;
        }
        Map<String, List<Long>> secOwnerIdMap = new HashMap<>(16);
        // 按规格+货主分组
        Map<String, List<ProductInventoryPO>> inventoryMap = productInventoryPOS.stream()
            .collect(Collectors.groupingBy(p -> String.format("%s-%s", p.getProductSpecificationId(), p.getOwnerId())));
        inventoryMap.forEach((key, list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            secOwnerIdMap.put(key, list.stream().map(p -> p.getSecOwnerId()).distinct().collect(Collectors.toList()));
        });
        LOG.info("根据规格+货主查找对应的二级货主结果：{}", JSON.toJSONString(secOwnerIdMap));
        return secOwnerIdMap;
    }

    /**
     * 查询预占库存
     */
    public List<WarehouseStoreDTO> getPreemptProductInventories(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        List<WarehouseStoreDTO> preemptProductInventories = new ArrayList<>();
        List<WarehouseStoreDTO> disposedProductInventories = getDisposedProductInventories(wareHoseInventoryQueryDTO);

        OutStockApplyPreemptQueryDTO outStockApplyPreemptQueryDTO = new OutStockApplyPreemptQueryDTO();
        outStockApplyPreemptQueryDTO.setOrgId(wareHoseInventoryQueryDTO.getCityId());
        outStockApplyPreemptQueryDTO.setWarehouseId(wareHoseInventoryQueryDTO.getWarehouseId());
        outStockApplyPreemptQueryDTO.setSkuIds(wareHoseInventoryQueryDTO.getProductSkuIds());
        List<Byte> outStockApplyStates = new ArrayList<>();
        outStockApplyStates.add(OutStockApplyStateEnum.待审核.getType());
        outStockApplyStates.add(OutStockApplyStateEnum.待出库.getType());
        outStockApplyStates.add(OutStockApplyStateEnum.部分出库.getType());
        outStockApplyPreemptQueryDTO.setStates(outStockApplyStates);

        List<Byte> nonBusinessTypes = new ArrayList<>();
        nonBusinessTypes.add(OutStockOrderBusinessType.团购差异.getType());
        nonBusinessTypes.add(OutStockOrderBusinessType.分销出库.getType());
        outStockApplyPreemptQueryDTO.setNonBusinessTypes(nonBusinessTypes);
        List<OutStockApplyPreemptDTO> outStockApplyPreempts =
            iOutStockApplyQueryService.findOutStockApplyPreempt(outStockApplyPreemptQueryDTO);

        List<WarehouseStoreDTO> outStockApplyStorePreempts =
            ProductInventoryConverter.outStockApplyPreempts2WarehouseStoreDTOS(outStockApplyPreempts,
                wareHoseInventoryQueryDTO.getCityId(), wareHoseInventoryQueryDTO.getWarehouseId());
        if (CollectionUtils.isNotEmpty(outStockApplyStorePreempts)) {
            disposedProductInventories.addAll(outStockApplyStorePreempts);
        }

        // 查询erp采购退出库预占销售库存
        List<WarehouseStoreDTO> erpWarehouseStoreDTOList = getErpPurchaseReturnOutStock(wareHoseInventoryQueryDTO);
        if (CollectionUtils.isNotEmpty(erpWarehouseStoreDTOList)) {
            disposedProductInventories.addAll(erpWarehouseStoreDTOList);
        }

        if (CollectionUtils.isNotEmpty(disposedProductInventories)) {
            disposedProductInventories.stream().collect(Collectors.groupingBy(
                item -> String.format("%s-%s-%s", item.getProductSpecId(), item.getOwnerId(), item.getSecOwnerId())))
                .forEach((skuSign, list) -> {
                    WarehouseStoreDTO warehouseStoreDTO = list.get(0);
                    BigDecimal unitTotalCount = list.stream().map(WarehouseStoreDTO::getWarehouseTotalCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    warehouseStoreDTO.setWarehouseTotalCount(unitTotalCount);

                    preemptProductInventories.add(warehouseStoreDTO);
                });
        }
        LOG.info("warehouseId:{},查询预占库存总结果:{},残次品结果：{}", wareHoseInventoryQueryDTO.getWarehouseId(),
            JSON.toJSONString(preemptProductInventories), JSON.toJSONString(disposedProductInventories));
        return preemptProductInventories;
    }

    public List<ProductStoreBaseDTO> findProductStoreByIds(List<String> ids) {
        List<ProductStorePO> productStorePOS = productStorePOMapper.findProductStoreByIds(ids);
        if (CollectionUtils.isEmpty(productStorePOS)) {
            return null;
        }

        List<ProductStoreBaseDTO> storeBaseDTOS = new ArrayList<>();
        productStorePOS.forEach(productStorePO -> {
            ProductStoreBaseDTO storeBaseDTO = new ProductStoreBaseDTO();
            BeanUtils.copyProperties(productStorePO, storeBaseDTO);

            storeBaseDTOS.add(storeBaseDTO);
        });
        return storeBaseDTOS;
    }

    /**
     * 在城市或全国中，根据产品信息查找在仓库库存中存在的产品信息
     */
    public PageList<WarehouseInventoryReportDTO> findProductInWarehouseInventory(WarehouseInventoryReportSO querySO) {
        PageResult<WarehouseInventoryReportDTO> pageResult =
            productInventoryPOMapper.findProductInWarehouseInventory(querySO);
        PageList<WarehouseInventoryReportDTO> pageList = pageResult.toPageList();
        if (CollectionUtils.isEmpty(pageList.getDataList())) {
            return pageList;
        }
        // 获取城市名名称集合
        List<Integer> cityIdSet = pageList.getDataList().stream().filter(p -> p.getCityId() != null)
            .map(WarehouseInventoryReportDTO::getCityId).distinct().collect(Collectors.toList());
        Map<Integer, OrgDTO> cityIdMap = iOrgService.findOrgByIds(cityIdSet);

        // 填充
        pageList.getDataList().forEach(p -> {
            // 城市名称
            if (cityIdMap != null) {
                OrgDTO orgDTO = cityIdMap.get(p.getCityId());
                p.setCityName(orgDTO == null ? null : orgDTO.getOrgName());
            }
        });
        return pageList;
    }

    public List<ProductStoreBaseDTO> findStoreBySpecOwner(ProductStoreQueryDTO productStoreQueryDTO) {
        return productStorePOMapper.findStoreBySpecOwner(productStoreQueryDTO);
    }

    /**
     * 根据中台Sku+仓库查询仓库库存
     *
     * @return
     */
    public List<ProductStoreBaseDTO> findProductStoreByUnifySkuId(ProductStoreQueryDTO productStoreQueryDTO) {
        List<ProductStoreBaseDTO> storeBaseDTOS =
            productStorePOMapper.findProductStoreByUnifySkuId(productStoreQueryDTO);
        return storeBaseDTOS;
    }

    /**
     * 根据货主仓库库存下限配置，获取仓库库存数量
     */
    public Boolean isHaveWarehouseInventory(Integer warehouseId) {
        VariableValueQueryDTO configQuery = new VariableValueQueryDTO();
        configQuery.setVariableKey(WAREHOUSE_DISABLE_OWNER_INVENTORY_FLOOR);
        configQuery.setWarehouseId(warehouseId);
        VariableDefAndValueDTO configValueDTO = iVariableValueService.detailVariable(configQuery);
        if (configValueDTO == null || org.springframework.util.StringUtils.isEmpty(configValueDTO.getVariableData())) {
            throw new BusinessValidateException(String.format("[仓库停用校验]获取不到仓库停用的货主仓库库存下限配置，仓库ID：%s，配置项：%s", warehouseId,
                WAREHOUSE_DISABLE_OWNER_INVENTORY_FLOOR));
        }
        return productInventoryPOMapper.isHaveWarehouseInventory(warehouseId,
            Double.valueOf(configValueDTO.getVariableData())) != null;
    }

    /**
     * 根据sku信息查询仓库库存
     */
    public PageList<WarehouseStoreDTO> listProductStoreBySkuInfo(WarehouseInventoryReportSO queryDTO) {
        PageResult<ProductInventoryPO> poPageResult = productInventoryPOMapper.listProductStoreBySkuInfo(queryDTO);
        return ProductStoreConverter.productStorePOS2WarehouseStoreDTOS(poPageResult);
    }

    public String queryAbcAttribute(List<Long> productSkuIdList, Integer warehouseId) {
        List<ProductSkuConfigDTO> productSkuConfigDTOList =
            inventoryProductSkuMapper.queryInventoryProperty(productSkuIdList, warehouseId);
        if (CollectionUtils.isNotEmpty(productSkuConfigDTOList)) {
            return productSkuConfigDTOList.get(0).getInventoryRatio();
        }
        return "";
    }

    /**
     * @see findStoreReportPageInfoByAuthNew 查询 库存报表（供应链）
     */
    // @Deprecated
    // public PageList<InventoryReportDTO> findStoreReportPageInfoByAuth(StockReportSO so, PagerCondition
    // pagerCondition) {
    // PageList<InventoryReportDTO> resultList = new PageList<>();
    // if (CollectionUtils.isEmpty(so.getWarehouseIds())) {
    // return resultList;
    // }
    // if (StringUtils.isEmpty(so.getInventoryPinProperty())) {
    // so.setInventoryPinProperty(null);
    // }
    // PageResult<InventoryReportDTO> pageResult = productInventoryPOMapper.findStoreReportPageInfoByAuth(so,
    // pagerCondition.getCurrentPage(), pagerCondition.getPageSize());
    // PageList<InventoryReportDTO> inventoryReportDTOPageList = pageResult.toPageList();
    // findDeliveryedCountByProductStoreIds(inventoryReportDTOPageList.getDataList());
    // return inventoryReportDTOPageList;
    // }
    public PageList<InventoryReportDTO> findStoreReportPageInfoByAuthNew(StockReportSO so,
        PagerCondition pagerCondition) {
        PageList<InventoryReportDTO> resultList = new PageList<>();
        if (CollectionUtils.isEmpty(so.getWarehouseIds())) {
            return resultList;
        }
        if (StringUtils.isEmpty(so.getInventoryPinProperty())) {
            so.setInventoryPinProperty(null);
        }
        so.setLimitSku((byte)1);

        PageResult<InventoryReportDTO> pageResult = productInventoryPOMapper.findStoreReportPageInfoByAuthNew(so,
            pagerCondition.getCurrentPage(), pagerCondition.getPageSize());

        PageList<InventoryReportDTO> inventoryReportDTOPageList = pageResult.toPageList();
        List<InventoryReportDTO> inventoryReportDTOList = inventoryReportDTOPageList.getDataList();
        if (CollectionUtils.isEmpty(inventoryReportDTOList)) {
            return inventoryReportDTOPageList;
        }
        inventoryReportDTOConvertor.initShopInfo(inventoryReportDTOList);
        List<String> productStoreIds = inventoryReportDTOList.stream().map(InventoryReportDTO::getProductStoreId)
            .distinct().collect(Collectors.toList());
        List<InventoryReportDTO> storeAverageList =
            productInventoryPOMapper.findStoreReportAverageStockAge(productStoreIds);
        inventoryReportDTOConvertor.initStockAverageInfo(inventoryReportDTOList, storeAverageList);

        findDeliveryedCountByProductStoreIds(inventoryReportDTOList);
        return inventoryReportDTOPageList;
    }

    /**
     * 库存报表
     */
    public PageList<FindStoreInfoDTO> findStorePage(FindStoreDTO findStoreQuery) {
        if (findStoreQuery.getWarehouseIds() == null || findStoreQuery.getWarehouseIds().size() <= 0) {
            WareHouseDTO wareHouseDTO = iStoreWareHouseService.getWareHouseByCityId(findStoreQuery.getCityId());
            findStoreQuery.setWarehouseIds(Arrays.asList(wareHouseDTO.getId()));
        }
        StockReportSO so = new StockReportSO();
        so.setProductSkuName(findStoreQuery.getProductSkuName());
        so.setWarehouseIds(findStoreQuery.getWarehouseIds());
        if (findStoreQuery.getSupplierId() != null && findStoreQuery.getSupplierId() > 0) {
            so.setSupplierId(findStoreQuery.getSupplierId());
        }
        if (findStoreQuery.getStoreOwnerType() != null && findStoreQuery.getStoreOwnerType() >= 0) {
            so.setStoreOwnerType(findStoreQuery.getStoreOwnerType());
        }
        so.setProductSkuIds(findStoreQuery.getProductSkuIds());
        so.setProductTypeList(findStoreQuery.getProductTypeList());

        if (CollectionUtils.isEmpty(so.getWarehouseIds())) {
            return new PageList<>();
        }
        PageResult<InventoryReportDTO> pageResult = productInventoryPOMapper.findStoreReportPageByAuth(so,
            findStoreQuery.getPageNum(), findStoreQuery.getPageSize());
        PageList<InventoryReportDTO> pageList = pageResult.toPageList();
        findDeliveryedCountByProductStoreIds(pageList.getDataList());
        List<InventoryReportDTO> dataList = pageList.getDataList();
        if (dataList != null && dataList.size() > 0) {
            // 如果根据SKUID查询，过滤非本次传递的SKUID以外的数据
            if (CollectionUtils.isNotEmpty(so.getProductSkuIds())) {
                dataList.removeIf(p -> !so.getProductSkuIds().contains(p.getProductSkuId()));
            }
            List<Integer> warehouseIds =
                dataList.stream().map(InventoryReportDTO::getWarhouseId).distinct().collect(Collectors.toList());
            // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 未处理
            // List<CityMergeDTO> lstParam = new ArrayList<>();
            // dataList.stream().forEach(p -> {
            // CityMergeDTO dto = new CityMergeDTO();
            // dto.setProductOwnerId(p.getOwnerId());
            // dto.setProductSpecificationId(p.getProductSpecificationId());
            // lstParam.add(dto);
            // });
            // List<CityMergeDTO> lstWaiting = new ArrayList<>();
            // if (CollectionUtils.isNotEmpty(lstParam)) {
            // Lists.partition(lstParam, 30).forEach(list -> {
            // lstWaiting.addAll(
            // iOrderQueryService.getWaitingDeliveryStateCountByProductSkuIds(list, warehouseIds.get(0)));
            // });
            // }
            // LOG.info("findStorePage mapWaiting:{}" + JSON.toJSONString(lstWaiting));
            WareHouseDTO wareHouseDTO = iStoreWareHouseService.findWareHouseById(warehouseIds.get(0));
            // 合作商库存不查
            List<InventoryReportDTO> dataListWithOutPartner = ArrayUtils.deepCopy(dataList);
            // dataListWithOutPartner.removeIf(t -> Objects.equals(OwnerTypeConst.合作商, t.getStoreOwnerType()));
            // 获取销售库存
            List<ProductSpecAndOwnerIdDTO> specAndOwnerIds = dataListWithOutPartner.stream().map(t -> {
                ProductSpecAndOwnerIdDTO dto = new ProductSpecAndOwnerIdDTO();
                dto.setProductSpecId(t.getProductSpecificationId());
                dto.setOwnerId(t.getOwnerId());
                return dto;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> saleInventoryMap = getSaleInventoryMap(specAndOwnerIds, warehouseIds.get(0),
                findStoreQuery.getCityId(), findStoreQuery.getPrintLog());
            Map<String, BigDecimal> mapWaiting = new HashMap<>(16);
            // if (!org.springframework.util.CollectionUtils.isEmpty(lstWaiting)) {
            // for (CityMergeDTO p : lstWaiting) {
            // mapWaiting.put(String.format("%s%s", p.getProductSpecificationId(), p.getProductOwnerId()),
            // p.getCount());
            // }
            // mapWaiting = lstWaiting.stream().collect(Collectors.toMap(p -> String.format("%s%s",
            // p.getProductSpecificationId(), p.getProductOwnerId()), q -> q.getCount()));
            // }
            LOG.info("findStorePage 销售库存：{}", JSON.toJSONString(saleInventoryMap));
            List<FindStoreInfoDTO> convert =
                findStoreConvertor.convert(dataList, wareHouseDTO, mapWaiting, saleInventoryMap);
            setBoxCodeInfo(convert, findStoreQuery.getCityId());
            PageList<FindStoreInfoDTO> resultList = new PageList<>();
            resultList.setDataList(convert);
            resultList.setPager(pageList.getPager());
            return resultList;
        }

        PageList<FindStoreInfoDTO> resultList = new PageList<>();
        resultList.setDataList(new ArrayList<FindStoreInfoDTO>());
        resultList.setPager(new Pager());
        return resultList;
    }

    /**
     * 查询销售库存
     *
     * @return
     */
    private Map<String, BigDecimal> getSaleInventoryMap(List<ProductSpecAndOwnerIdDTO> specAndOwnerIds,
        Integer warehouseId, Integer cityId, Boolean printLog) {
        Map<String, BigDecimal> saleMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(specAndOwnerIds)) {
            ProductStoreQueryDTO productStoreQueryDTO = new ProductStoreQueryDTO();
            productStoreQueryDTO.setCityId(cityId);
            productStoreQueryDTO.setWarehouseId(warehouseId);
            productStoreQueryDTO.setSpecAndOwnerIds(specAndOwnerIds);
            productStoreQueryDTO.setPrintLog(printLog);
            List<WarehouseStoreDTO> warehouseStoreDTOList =
                warehouseInventoryCheckBL.getSaleInventoryList(productStoreQueryDTO);
            if (!org.springframework.util.CollectionUtils.isEmpty(warehouseStoreDTOList)) {
                warehouseStoreDTOList.forEach(p -> {
                    String productSkuIdCityIDWarehouseId =
                        String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId());
                    saleMap.put(productSkuIdCityIDWarehouseId, p.getSaleStoreTotalCount());
                });
            }
        }
        return saleMap;
    }

    private void setBoxCodeInfo(List<FindStoreInfoDTO> lstItem, Integer cityId) {
        List<Long> lstSkuIds = lstItem.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(lstSkuIds)) {
            return;
        }
        Set<Long> skuIdSet = new HashSet<>(lstSkuIds);
        // 拿到瓶码箱码信息
        Map<Long, ProductCodeDTO> codeMap = iProductSkuService.getPackageAndUnitCode(skuIdSet, cityId);
        LOG.info("setBoxCodeInfo 获取瓶码箱码信息：{}", JSON.toJSONString(codeMap));
        Map<Long, ProductSkuInfoReturnDTO> productInfoBySkuId = iProductSkuService.getProductInfoBySkuId(lstSkuIds);
        lstItem.forEach(p -> {
            ProductSkuInfoReturnDTO dto = productInfoBySkuId.get(p.getProductSkuId());
            if (dto != null) {
                p.setUnpackage(dto.getUnpackage());
            }
            ProductCodeDTO productCodeDTO = codeMap.get(p.getProductSkuId());
            if (null != productCodeDTO) {
                // 箱码
                p.setPackageCode(productCodeDTO.getPackageCode());
                // 瓶码
                p.setUnitCode(productCodeDTO.getUnitCode());
            }
        });
    }

    private List<WarehouseStoreDTO> getErpPurchaseReturnOutStock(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO) {
        // 查询erp采购退出库预占销售库存
        List<ErpPurchaseReturnOutStockDTO> erpPurchaseList =
            InventoryConvertor.getErpPurchaseReturnOutStock(wareHoseInventoryQueryDTO.getWarehouseId());
        LOG.info("getErpPurchaseReturnOutStock erp查询采购退预占库存结果:{}", JSON.toJSONString(erpPurchaseList));
        if (CollectionUtils.isEmpty(erpPurchaseList)) {
            return Collections.emptyList();
        }

        List<String> secOwnerIdList = erpPurchaseList.stream().map(ErpPurchaseReturnOutStockDTO::getSecOwnerId)
                .filter(secOwnerId -> !StringUtils.isEmpty(secOwnerId)).distinct().collect(Collectors.toList());
        Map<String, Long> wmsOwnerIdMap = ownerService.getOwnerIdMap(secOwnerIdList);
        if (wmsOwnerIdMap == null || wmsOwnerIdMap.isEmpty()) {
            throw new BusinessException(String.format("采购退供应商[%s]不存在！", JSON.toJSONString(secOwnerIdList)));
        }

        Set<String> existSuppliers = wmsOwnerIdMap.keySet();
        if (!existSuppliers.containsAll(secOwnerIdList)) {
            secOwnerIdList.removeAll(existSuppliers);
            throw new BusinessException(String.format("采购退供应商[%s]不存在！", JSON.toJSONString(secOwnerIdList)));
        }

        List<WarehouseStoreDTO> erpWarehouseStoreDTOList = erpPurchaseList.stream().map(p -> {
            WarehouseStoreDTO warehouseStoreDTO = new WarehouseStoreDTO();
            BeanUtils.copyProperties(p, warehouseStoreDTO);
            warehouseStoreDTO.setCityId(wareHoseInventoryQueryDTO.getCityId());
            warehouseStoreDTO.setWarehouseId(wareHoseInventoryQueryDTO.getWarehouseId());
            Long supplierId = wmsOwnerIdMap.get(p.getSecOwnerId());
            warehouseStoreDTO.setSecOwnerId(supplierId);
            BigDecimal totalCount = p.getWarehouseTotalCount();
            BigDecimal includeDisposalCount = p.getIncludeDisposalCount();
            if (includeDisposalCount != null) {
                warehouseStoreDTO.setWarehouseTotalCount(totalCount.subtract(includeDisposalCount));
            }
            return warehouseStoreDTO;
        }).collect(Collectors.toList());

        LOG.info("getErpPurchaseReturnOutStock 预占库存结果:{}", JSON.toJSONString(erpWarehouseStoreDTOList));
        return erpWarehouseStoreDTOList;
    }
}
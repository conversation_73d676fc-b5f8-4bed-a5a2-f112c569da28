local info = ''
for _, v in pairs(ARGV) do
    local key1, value1 = string.match(v, '([_?%d+]+)%s*=%s*([-]?%d+)')
    local temp = redis.call('hget', KEYS[1], key1)
    local kc = (temp and tonumber(temp)) or tonumber('0')
    local ct = tonumber(value1);
    if (ct < 0 and (kc + ct) < 0) then
        info = info .. '{' .. key1 .. ':' .. kc .. ':' .. value1 .. '}'
    end
end
if (string.len(info) > 0) then
    return info
end
for _, v in pairs(ARGV) do
    local key1, value1 = string.match(v, '([_?%d+]+)%s*=%s*([-]?%d+)')
    redis.call('hincrby', KEYS[1], key1, tonumber(value1))
end
return 'OK'
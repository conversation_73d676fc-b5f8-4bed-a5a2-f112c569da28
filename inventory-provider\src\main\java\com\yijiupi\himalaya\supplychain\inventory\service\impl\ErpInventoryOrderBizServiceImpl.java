package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ErpInventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IErpInventoryOrderBizService;

/**
 * Erp单据库存业务处理服务.
 */
@Service(timeout = 60000)
public class ErpInventoryOrderBizServiceImpl implements IErpInventoryOrderBizService {

    private static final Logger LOG = LoggerFactory.getLogger(ErpInventoryOrderBizServiceImpl.class);

    @Autowired
    private ErpInventoryOrderBizBL erpOrderBizBL;

    @Override
    public void processInStockOrderInventory(ProcessInStockOrderInventoryDTO processDTO) {
        LOG.info("ERP单据库存操作 : {}", JSON.toJSONString(processDTO));
        AssertUtils.notNull(processDTO, "单据对象不能为空！");
        AssertUtils.notEmpty(processDTO.getInStockOrderDTOList(), "单据内容不能为空！");
        erpOrderBizBL.processInStockOrderInventory(processDTO);
    }

    @Override
    public void processOutStockOrderInventory(ProcessOutStockOrderInventoryDTO processDTO) {
        LOG.info("ERP单据出库存操作 : {}", JSON.toJSONString(processDTO));
        AssertUtils.notNull(processDTO, "单据对象不能为空！");
        AssertUtils.notEmpty(processDTO.getOutStockOrderDTOList(), "单据内容不能为空！");
        erpOrderBizBL.processOutStockOrderInventory(processDTO);
    }
}

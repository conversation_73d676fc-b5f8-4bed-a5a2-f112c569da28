package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeTemplateRelationReturnPO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 字典表主键
     */
    private Long dicId;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性类型
     */
    private Byte attributeType;
    /**
     * 有效位数
     */
    private Integer effectiveDigit;
    /**
     * 是否必须
     */
    private Boolean required;

    /**
     * 是否参与批属性计算,不参与(0),参与计算(1)
     */
    private Byte isCalculation;

    /**
     * 是否参与盘点,不参与(0),参与(1)
     */
    private Byte isStoreCheck;

    /**
     * 获取 字典表主键
     */
    public Long getDicId() {
        return this.dicId;
    }

    /**
     * 设置 字典表主键
     */
    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    /**
     * 获取 属性名称
     */
    public String getAttributeName() {
        return this.attributeName;
    }

    /**
     * 设置 属性名称
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    /**
     * 获取 属性类型
     */
    public Byte getAttributeType() {
        return this.attributeType;
    }

    /**
     * 设置 属性类型
     */
    public void setAttributeType(Byte attributeType) {
        this.attributeType = attributeType;
    }

    /**
     * 获取 有效位数
     */
    public Integer getEffectiveDigit() {
        return this.effectiveDigit;
    }

    /**
     * 设置 有效位数
     */
    public void setEffectiveDigit(Integer effectiveDigit) {
        this.effectiveDigit = effectiveDigit;
    }

    /**
     * 获取 是否必须
     */
    public Boolean getRequired() {
        return this.required;
    }

    /**
     * 设置 是否必须
     */
    public void setRequired(Boolean required) {
        this.required = required;
    }

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    public Byte getIsCalculation() {
        return isCalculation;
    }

    public void setIsCalculation(Byte isCalculation) {
        this.isCalculation = isCalculation;
    }

    public Byte getIsStoreCheck() {
        return isStoreCheck;
    }

    public void setIsStoreCheck(Byte isStoreCheck) {
        this.isStoreCheck = isStoreCheck;
    }
}

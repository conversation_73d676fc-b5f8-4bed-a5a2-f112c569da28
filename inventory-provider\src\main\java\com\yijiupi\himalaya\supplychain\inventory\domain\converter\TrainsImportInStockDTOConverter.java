package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.instockbatchnotify.TrainsImportInStockDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.instockbatchnotify.TrainsImportInStockDealerDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.instockbatchnotify.TrainsImportInStockOrderDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.instockbatchnotify.TrainsImportInStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStockOrderRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/3/16
 */
@Component
public class TrainsImportInStockDTOConverter {

    @Reference
    private OwnerService ownerService;

    public TrainsImportInStockDTO convert(List<ErpStockOrderRecordDTO> erpStoreOrderRecordList) {

        ErpStockOrderRecordDTO erpStockOrderRecordDTO = erpStoreOrderRecordList.get(0);

        Map<String, List<ErpStockOrderRecordDTO>> groupMap =
            erpStoreOrderRecordList.stream().collect(Collectors.groupingBy(ErpStockOrderRecordDTO::getReforderid));

        TrainsImportInStockDTO trainsImportInStockDTO = new TrainsImportInStockDTO();

        trainsImportInStockDTO.setCityId(erpStockOrderRecordDTO.getOrgId());
        trainsImportInStockDTO.setWarehouseId(erpStockOrderRecordDTO.getWarehouseId());
        trainsImportInStockDTO.setInStockTime(new Date());
        trainsImportInStockDTO.setOptUserId("1");

        List<TrainsImportInStockOrderDTO> orderList = new ArrayList<>();
        for (Map.Entry<String, List<ErpStockOrderRecordDTO>> entry : groupMap.entrySet()) {
            orderList.add(convertOrder(entry.getValue()));
        }
        trainsImportInStockDTO.setTrainsInStockOrderList(orderList);

        return trainsImportInStockDTO;
    }

    private TrainsImportInStockOrderDTO convertOrder(List<ErpStockOrderRecordDTO> recordList) {
        ErpStockOrderRecordDTO erpStockOrderRecordDTO = recordList.get(0);
        TrainsImportInStockOrderDTO orderDTO = new TrainsImportInStockOrderDTO();
        if (Objects.nonNull(erpStockOrderRecordDTO.getReforderid())) {
            orderDTO.setOrderId(Long.valueOf(erpStockOrderRecordDTO.getReforderid()));
        }
        orderDTO.setInStockOrderType((int)InStockOrderTypeEnum.盘盈入库.getType());
        orderDTO.setInStockOrderId(Long.valueOf(erpStockOrderRecordDTO.getReforderid()));
        orderDTO.setTrainsInStockOrderItemList(convertItem(recordList));

        return orderDTO;
    }

    private List<TrainsImportInStockOrderItemDTO> convertItem(List<ErpStockOrderRecordDTO> recordList) {
        List<TrainsImportInStockOrderItemDTO> itemList = new ArrayList<>();

        for (ErpStockOrderRecordDTO erpStockOrderRecordDTO : recordList) {
            TrainsImportInStockOrderItemDTO itemDTO = new TrainsImportInStockOrderItemDTO();
            itemDTO.setOrderItemId(Long.valueOf(erpStockOrderRecordDTO.getId()));
            itemDTO.setProductSkuId(erpStockOrderRecordDTO.getProductskuId());
            itemDTO.setUnitTotalCount(erpStockOrderRecordDTO.getUnittotalcount());
            itemDTO.setInStockOrderItemId(Long.valueOf(erpStockOrderRecordDTO.getId()));
            itemDTO.setTrainsInStockDealerList(convertItemDetail(erpStockOrderRecordDTO));

            itemList.add(itemDTO);
        }

        return itemList;
    }

    private List<TrainsImportInStockDealerDTO> convertItemDetail(ErpStockOrderRecordDTO erpStockOrderRecordDTO) {
        TrainsImportInStockDealerDTO dealerDTO = new TrainsImportInStockDealerDTO();
        dealerDTO.setUnitTotalCount(erpStockOrderRecordDTO.getUnittotalcount());
        dealerDTO.setSecOwnerId(erpStockOrderRecordDTO.getSecOwnerId());
        dealerDTO.setProductionDate(erpStockOrderRecordDTO.getProductionDate());
        dealerDTO.setOwnerId(erpStockOrderRecordDTO.getOwnerId());
        dealerDTO.setErpSecOwnerId(getErpSecOwnId(erpStockOrderRecordDTO.getSecOwnerId()));
        // dealerDTO.setExpirationDate();
        dealerDTO.setProductSpecificationId(erpStockOrderRecordDTO.getProductSpecificationId());

        return Collections.singletonList(dealerDTO);
    }

    private String getErpSecOwnId(Long wmsSecOwnerId) {
        OwnerDTO owner = ownerService.getOwnerById(wmsSecOwnerId);
        if (owner == null) {
            return null;
        }
        return owner.getRefPartnerId();
    }

}

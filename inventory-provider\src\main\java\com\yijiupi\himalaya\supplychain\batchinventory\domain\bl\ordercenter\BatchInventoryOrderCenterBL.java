package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.ordercenter;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.ServiceAbilityClientUtil;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.OrderApiResult;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
@Service
public class BatchInventoryOrderCenterBL {

    @Autowired
    private ServiceAbilityClientUtil serviceAbilityClientUtil;

    private static final Logger LOG = LoggerFactory.getLogger(BatchInventoryOrderCenterBL.class);

    /**
     * 查询销售库存
     */
    public List<OrderCenterSaleInventoryQueryResultDTO>
        findSaleInventoryList(List<OrderCenterSaleInventoryQueryDTO> queryDTOList) {
        // LOG.info("findSaleInventoryList，入参queryQuery:{}", JSON.toJSONString(queryDTOList));
        if (CollectionUtils.isEmpty(queryDTOList)) {
            return new ArrayList<>();
        }

        try {
            OrderApiResult<List<OrderCenterSaleInventoryQueryResultDTO>> result = serviceAbilityClientUtil.getInstance()
                .invoke("/saleinventory/SaleInventoryService/findSaleInventoryByKeys",
                    new TypeToken<OrderApiResult<List<OrderCenterSaleInventoryQueryResultDTO>>>() {}.getType(),
                    queryDTOList);
            if (result.getCode() != 200) {
                LOG.error("查询销售库存接口报错{}", result.getMsg());
            }

            LOG.info("查询销售库存findSaleInventoryList，查询结果:{}", JSON.toJSONString(result.getData()));
            return result.getData();

        } catch (Exception e) {
            LOG.warn("findSaleInventoryList查询销售库存", e);
            return new ArrayList<>();
        }
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStoreBatchPO;

/**
 * 批次库存
 *
 * <AUTHOR> 2018/1/25
 */
public interface InventoryProductStoreBatchMapper {
    /**
     * 根据storeId查询批次库存信息(>0的批次)
     */
    List<ProductStoreBatchPO> findProductStoreBatch(@Param("productStoreId") String productStoreId);

}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * Created by wa<PERSON><PERSON> on 2017-09-26
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryQueryTest {

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;

    @Test
    public void inventoryQueryTest() {
        // WarehouseStoreDTO warehouseStoreDTO = warehouseInventoryQueryBL.getProductInventoryDetail(99900040884324L,
        // 9991, null);
        // System.out.println(warehouseStoreDTO.getId());
        // System.out.println(warehouseStoreDTO.getWarehouseTotalCount());
    }
}

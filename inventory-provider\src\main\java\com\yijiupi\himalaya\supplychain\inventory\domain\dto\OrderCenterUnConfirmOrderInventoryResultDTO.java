package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: OrderCenterUnConfirmOrderInventoryResultDTO
 * @description:
 * @date 2023-03-09 16:15
 */
public class OrderCenterUnConfirmOrderInventoryResultDTO implements Serializable {
    /**
     * 出入库单类型 WMS出库单类型 1调拨单， 2销售订单， 3线上退货订单， 4线下退货订单， 5自提订单， 6快递直发订单， 7虚仓二次分拣订单， 8内配订单 入库单类型 1线上退货单退货入库， 2销售单回单入库， 3调拨单调拨入库， 4线下退货单退货入库， 5前置仓内配入库
     */
    private Integer orderType;
    /**
     * 小单位数量
     */
    private BigDecimal count;
    /**
     * 一级货主
     */
    private Long ownerId;
    /**
     * 规格Id
     */
    private Long productSpecificationId;
    /**
     * 二级货主
     */
    private Long secOwnerId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
    * 类型 1出库单， 2入库单
    */
    private Integer type;
    /**
     * 产品skuId
     */
    private Long skuId;

    /**
     * 获取 出入库单类型 WMS出库单类型 1调拨单， 2销售订单， 3线上退货订单， 4线下退货订单， 5自提订单， 6快递直发订单， 7虚仓二次分拣订单， 8内配订单 入库单类型 1线上退货单退货入库， 2销售单回单入库， 3调拨单调拨入库， 4线下退货单退货入库， 5前置仓内配入库
     *
     * @return orderType 出入库单类型 WMS出库单类型 1调拨单， 2销售订单， 3线上退货订单， 4线下退货订单， 5自提订单， 6快递直发订单， 7虚仓二次分拣订单， 8内配订单 入库单类型 1线上退货单退货入库， 2销售单回单入库， 3调拨单调拨入库， 4线下退货单退货入库， 5前置仓内配入库
     */
    public Integer getOrderType() {
        return this.orderType;
    }

    /**
     * 设置 出入库单类型 WMS出库单类型 1调拨单， 2销售订单， 3线上退货订单， 4线下退货订单， 5自提订单， 6快递直发订单， 7虚仓二次分拣订单， 8内配订单 入库单类型 1线上退货单退货入库， 2销售单回单入库， 3调拨单调拨入库， 4线下退货单退货入库， 5前置仓内配入库
     *
     * @param orderType 出入库单类型 WMS出库单类型 1调拨单， 2销售订单， 3线上退货订单， 4线下退货订单， 5自提订单， 6快递直发订单， 7虚仓二次分拣订单， 8内配订单 入库单类型 1线上退货单退货入库， 2销售单回单入库， 3调拨单调拨入库， 4线下退货单退货入库， 5前置仓内配入库
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**
     * 获取 小单位数量
     *
     * @return count 小单位数量
     */
    public BigDecimal getCount() {
        return this.count;
    }

    /**
     * 设置 小单位数量
     *
     * @param count 小单位数量
     */
    public void setCount(BigDecimal count) {
        this.count = count;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 类型 1出库单， 2入库单
     *
     * @return type 类型 1出库单， 2入库单
     */
    public Integer getType() {
        return this.type;
    }

    /**
     * 设置 类型 1出库单， 2入库单
     *
     * @param type 类型 1出库单， 2入库单
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取 一级货主
     *
     * @return ownerId 一级货主
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 一级货主
     *
     * @param ownerId 一级货主
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 规格Id
     *
     * @return productSpecificationId 规格Id
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 规格Id
     *
     * @param productSpecificationId 规格Id
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 二级货主
     *
     * @return secOwnerId 二级货主
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主
     *
     * @param secOwnerId 二级货主
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 产品skuId
     *
     * @return skuId 产品skuId
     */
    public Long getSkuId() {
        return this.skuId;
    }

    /**
     * 设置 产品skuId
     *
     * @param skuId 产品skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }
}

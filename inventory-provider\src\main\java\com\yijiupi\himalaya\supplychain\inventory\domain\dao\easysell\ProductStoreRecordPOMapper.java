package com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreReturnDTO;

/**
 * <AUTHOR>
 */
public interface ProductStoreRecordPOMapper {
    /**
     * 易经销根据仓库ID类目查询仓库库存
     * 
     * @param productStoreQueryDTO
     * @return
     */
    PageResult<ProductStoreReturnDTO> findProductStoreList(ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 根据仓库id集合得到经销商id集合
     * 
     * @param warehouseIdList
     * @return
     */
    List<Long> findShopIdList(@Param("list") List<Integer> warehouseIdList);
}
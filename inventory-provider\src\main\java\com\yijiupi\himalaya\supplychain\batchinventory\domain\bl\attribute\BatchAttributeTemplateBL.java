package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.AttributeRuleType;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchAttributeTemplateConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchAttributeTemplateRelationConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeTemplateMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeTemplateRelationMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplatePO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplateRelationPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductInfoDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 批属性模板管理
 *
 * <AUTHOR> 2018/4/9
 */
@Service
public class BatchAttributeTemplateBL {

    /**
     * 适用服务商顶级服务商ID
     */
    private static final int TOP_PARENT_ORG_ID = 1;

    @Autowired
    private BatchAttributeTemplateMapper batchAttributeTemplateMapper;
    @Autowired
    private BatchAttributeTemplateRelationMapper batchAttributeTemplateRelationMapper;

    @Reference
    private IOrgService iOrgService;

    /**
     * 新增
     *
     * @param batchAttributeTemplateDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatchAttributeTemplate(BatchAttributeTemplateDTO batchAttributeTemplateDTO) {
        BatchAttributeTemplatePO batchAttributeTemplatePO =
            BatchAttributeTemplateConvert.BatchAttributeTemplateDTO2PO(batchAttributeTemplateDTO);
        // 插入模板表
        batchAttributeTemplatePO.setId(UUIDGenerator.getUUID(batchAttributeTemplatePO.getClass().getName()));
        batchAttributeTemplateMapper.insert(batchAttributeTemplatePO);

        List<BatchAttributeTemplateRelationPO> batchAttributeTemplateRelationPOS = BatchAttributeTemplateRelationConvert
            .batchAttributeTemplateRelationDTOS2POS(batchAttributeTemplateDTO.getRelationList());
        // 关联赋值
        batchAttributeTemplateRelationPOS.forEach(n -> {
            n.setTemplateId(batchAttributeTemplatePO.getId());
        });
        // 插入生成模板关联表
        batchAttributeTemplateRelationPOS.forEach(p -> p.setId(UUIDGenerator.getUUID(p.getClass().getName())));
        batchAttributeTemplateRelationMapper.insertList(batchAttributeTemplateRelationPOS);
    }

    /**
     * 编辑
     * 
     * @param batchAttributeTemplateDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchAttributeTemplate(BatchAttributeTemplateDTO batchAttributeTemplateDTO) {
        BatchAttributeTemplatePO batchAttributeTemplatePO =
            BatchAttributeTemplateConvert.BatchAttributeTemplateDTO2PO(batchAttributeTemplateDTO);
        batchAttributeTemplateMapper.update(batchAttributeTemplatePO);
        batchAttributeTemplateRelationMapper.deleteByTemplateId(batchAttributeTemplateDTO.getId());
        List<BatchAttributeTemplateRelationPO> batchAttributeTemplateRelationPOS = BatchAttributeTemplateRelationConvert
            .batchAttributeTemplateRelationDTOS2POS(batchAttributeTemplateDTO.getRelationList());
        batchAttributeTemplateRelationPOS.forEach(n -> n.setTemplateId(batchAttributeTemplateDTO.getId()));
        // 插入生成模板关联表
        batchAttributeTemplateRelationPOS.forEach(p -> p.setId(UUIDGenerator.getUUID(p.getClass().getName())));
        batchAttributeTemplateRelationMapper.insertList(batchAttributeTemplateRelationPOS);
    }

    /**
     * 查询批属性模板管理列表
     *
     * @param dto
     * @return
     */
    public PageList<BatchAttributeTemplateReturnDTO>
        findBatchAttributeTemplateList(BatchAttributeTemplateQueryDTO dto) {
        PageResult<BatchAttributeTemplateReturnDTO> batchAttributeTemplateList =
            batchAttributeTemplateMapper.findBatchAttributeTemplateList(dto, dto.getPageNum(), dto.getPageSize());
        return batchAttributeTemplateList.toPageList();
    }

    /**
     * 停用,启用
     *
     * @param batchAttributeTemplateDTO
     */
    public void updateBatchAttributeTemplateState(BatchAttributeTemplateDTO batchAttributeTemplateDTO) {
        batchAttributeTemplateMapper.updateIsEnable(batchAttributeTemplateDTO);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        batchAttributeTemplateMapper.deleteById(id);
        // 删除relation表
        batchAttributeTemplateRelationMapper.deleteByTemplateId(id);
    }

    /**
     * 根据产品信息查询批属性应填属性
     *
     * @param batchProductInfoDTO
     * @return
     */
    public List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeTemplateRelation(BatchProductInfoDTO batchProductInfoDTO) {
        return batchAttributeTemplateRelationMapper.findBatchAttributeTemplateRelation(batchProductInfoDTO);
    }

    /**
     * 查询类目和仓库绑定的批属性字典
     *
     * @param batchAttributeEnableQueryDTO
     * @return
     */
    public List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeEnable(BatchAttributeEnableQueryDTO batchAttributeEnableQueryDTO) {
        return batchAttributeTemplateRelationMapper.findBatchAttributeEnable(batchAttributeEnableQueryDTO);
    }

    /**
     * 根据配置类型和属性值Id查询批属性字典
     */
    public List<BatchAttributeTemplateRelationReturnDTO>
        findAttributeTemplateByRuleType(BatchAttributeTemplateRuleTypeQueryDTO ruleTypeQueryDTO) {
        AssertUtils.notNull(ruleTypeQueryDTO, "查询参数不能为空");
        AssertUtils.notNull(ruleTypeQueryDTO.getRuleType(), "批属性配置类型不能为空");
        // 去掉空格
        ruleTypeQueryDTO.setAttributeValueId(StringUtils.trimToNull(ruleTypeQueryDTO.getAttributeValueId()));
        ruleTypeQueryDTO.setBatchAttributeInfoNo(StringUtils.trimToNull(ruleTypeQueryDTO.getBatchAttributeInfoNo()));
        AssertUtils.notNull(ruleTypeQueryDTO.getAttributeValueId(), "配置类型属性值ID不能为空");
        // 属性值ID
        String attributeValueId = ruleTypeQueryDTO.getAttributeValueId();
        // 如果是服务商则会转化成其上级服务商ID
        String parentOrgId = getParentOrgId(ruleTypeQueryDTO.getRuleType(), attributeValueId);
        // 重新设置服务商
        ruleTypeQueryDTO.setAttributeValueId(parentOrgId);
        List<BatchAttributeTemplateRelationReturnDTO> templateAttributes =
            batchAttributeTemplateRelationMapper.findAttributeTemplateByRuleTypeAndValueId(ruleTypeQueryDTO);
        if (CollectionUtils.isNotEmpty(templateAttributes)
            && StringUtils.isNotBlank(ruleTypeQueryDTO.getBatchAttributeInfoNo())) {
            // 如果 batchAttributeInfoNo 不为空则查询属性对应值: batchAttributeInfoNo
            BatchAttributeValueQueryDTO valueQueryDTO = new BatchAttributeValueQueryDTO();
            valueQueryDTO.setAttributeValueId(ruleTypeQueryDTO.getAttributeValueId());
            valueQueryDTO.setRuleType(ruleTypeQueryDTO.getRuleType());
            valueQueryDTO.setBatchNoList(Collections.singletonList(ruleTypeQueryDTO.getBatchAttributeInfoNo()));
            List<BatchAttributeTemplateRelationReturnDTO> attributeTemplateValues =
                findAttributeTemplateValue(valueQueryDTO);
            // 填充模板数据
            fillAttributeTemplateValue(templateAttributes, attributeTemplateValues);
        }
        return templateAttributes;
    }

    /**
     * 根据字典属性ID和批次编号查询对应属性值
     */
    public List<BatchAttributeTemplateRelationReturnDTO>
        findAttributeTemplateValue(BatchAttributeValueQueryDTO valueQueryDTO) {
        AssertUtils.notNull(valueQueryDTO, "查询参数不能为空");
        // 去掉空格
        valueQueryDTO.setAttributeValueId(StringUtils.trimToNull(valueQueryDTO.getAttributeValueId()));
        AssertUtils.notNull(valueQueryDTO.getAttributeValueId(), "批属性配置ID不能为空");
        // 默认查询服务商
        Byte ruleType =
            valueQueryDTO.getRuleType() == null ? AttributeRuleType.SERVICEPROVIDER : valueQueryDTO.getRuleType();
        // 顶级服务商ID
        String parentOrgId = getParentOrgId(ruleType, valueQueryDTO.getAttributeValueId());
        // 重新设置服务商
        valueQueryDTO.setAttributeValueId(parentOrgId);
        // 查询模板属性值
        List<BatchAttributeTemplateRelationReturnDTO> templateValues =
            batchAttributeTemplateRelationMapper.findAttributeTemplateValue(valueQueryDTO);
        if (CollectionUtils.isEmpty(templateValues)) {
            return Collections.emptyList();
        }
        List<BatchAttributeTemplateRelationReturnDTO> resultValues = new ArrayList<>(templateValues.size());
        Map<String, List<BatchAttributeTemplateRelationReturnDTO>> tmpValueMap =
            templateValues.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(e -> String.format("%s%s", e.getDicId(), e.getBatchAttributeInfoNo())));
        tmpValueMap.forEach((k, v) -> {
            if (CollectionUtils.isEmpty(v)) {
                return;
            }
            // 如果存在多个值则获取最新一条记录:按时间由近到远排序，null排在最后
            Collections.sort(v, Comparator.comparing(BatchAttributeTemplateRelationReturnDTO::getLastUpdateTime,
                Comparator.nullsFirst(Date::compareTo)).reversed());
            resultValues.add(v.get(0));
        });
        return resultValues;
    }

    /**
     * 适用服务商时查找服务商上级机构id
     */
    private String getParentOrgId(Byte ruleType, String attributeValueId) {
        if (Objects.equals(AttributeRuleType.SERVICEPROVIDER, ruleType)) {
            // 适用用服务需要转 ParentOrg_Id
            if (!StringUtils.isNumeric(attributeValueId)) {
                throw new BusinessValidateException(String.format("服务商ID应为数值！传入服务商ID:%s", attributeValueId));
            }
            int orgId = Integer.valueOf(attributeValueId);
            if (TOP_PARENT_ORG_ID == orgId) {
                return attributeValueId;
            } else {
                // 查找上级
                OrgDTO org = iOrgService.getOrg(orgId);
                return org == null || org.getParentOrgId() == null ? attributeValueId : org.getParentOrgId().toString();
            }
        }
        return attributeValueId;
    }

    /**
     * 填充模板数据
     */
    private void fillAttributeTemplateValue(List<BatchAttributeTemplateRelationReturnDTO> templateAttributes,
        List<BatchAttributeTemplateRelationReturnDTO> templateAttributeValues) {
        if (CollectionUtils.isEmpty(templateAttributes) || CollectionUtils.isEmpty(templateAttributeValues)) {
            return;
        }
        templateAttributes.stream().filter(Objects::nonNull).forEach(template -> {
            BatchAttributeTemplateRelationReturnDTO templateDicValue = templateAttributeValues.stream()
                .filter(e -> e != null && Objects.equals(template.getDicId(), e.getDicId())).findAny().orElse(null);
            if (templateDicValue == null) {
                return;
            }
            // 如果存在多个值则获取最新一条记录:按时间由近到远排序，null排在最后
            template.setDicAttributeValueId(templateDicValue.getDicAttributeValueId());
            template.setDicAttributeValueName(templateDicValue.getDicAttributeValueName());
            template.setBatchAttributeInfoNo(templateDicValue.getBatchAttributeInfoNo());
        });
    }
}

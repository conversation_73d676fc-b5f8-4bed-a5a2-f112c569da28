package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2018/5/11
 */
public interface ProductStoreMapper {
    /**
     * 通过库存主键id查找skuId
     *
     * @param storeId
     * @return
     */
    WarehouseInventoryTransferPO findSkuIdByStoreId(@Param("storeId") String storeId);

    /**
     * 通过库存主键id查找库存记录 limit1
     */
    ProductInventoryPO findInventoryPO(@Param("storeId") String storeId);

    /**
     * 通过SkuId查找库存记录
     */
    ProductInventoryPO findInventoryPOBySkuId(@Param("warehouseId") Integer warehouseId, @Param("skuId") Long skuId,
        @Param("channel") Integer channel);

    List<ProductInventoryPO> findNoBatchInventoryStoreIds(@Param("warehouseId") Integer warehouseId);

    /**
     * 通过库存主键id查找skuId
     *
     * @param storeIdList
     * @return
     */
    List<WarehouseInventoryTransferPO> listSkuIdByStoreIds(@Param("storeIdList") List<String> storeIdList);

    /**
     * 通过库存主键ids查找库存记录
     */
    List<ProductInventoryPO> findInventoryPOByStoreIds(@Param("storeIdList") List<String> storeIdList);
}

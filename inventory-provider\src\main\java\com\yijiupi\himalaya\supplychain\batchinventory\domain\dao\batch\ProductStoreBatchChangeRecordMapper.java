package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2018/1/26
 */
public interface ProductStoreBatchChangeRecordMapper {

    /**
     * 创建批次库存变更记录
     *
     * @param list
     */
    void insertProductStoreBatchChangeRecord(@Param("list") List<ProductStoreBatchChangeRecordPO> list);

    /**
     * 查询批次库存变更记录
     *
     * @return
     */
    PageResult<ProductStoreBatchChangeRecordDTO>
        listProductStoreBatchChangeRecord(ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO);

    /**
     * 根据库存变更历史storeId,orderNo查找上一次的批次库存变更历史
     */
    List<ProductStoreBatchChangeRecordPO> selectBatchInventoryByChangeRecordId(
        @Param("productStoreId") String productStoreId, @Param("orderNo") String orderNo);

    /**
     * 查询批次库存变更流水
     *
     * @return
     */
    PageResult<ProductStoreBatchChangeFlowDTO>
        listProductStoreBatchChangeFlow(ProductStoreBatchChangFlowQueryDTO productStoreBatchChangeRecordQueryDTO);

    /**
     * 查询批次库存变更记录
     *
     * @return
     */
    List<ProductStoreBatchChangeRecordDTO> selectProductStoreBatchChangeRecords(
        ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO);

    /**
     * 根据规格id和订单号查询记录信息 <br />
     * 订单号 和 规格信息不能为空
     * @param queryDTO
     * @return
     */
    List<ProductStoreBatchChangeInfoResultDTO> findChangeRecordInfoByOrderInfo(ProductStoreBatchChangeInfoQueryDTO queryDTO);
}

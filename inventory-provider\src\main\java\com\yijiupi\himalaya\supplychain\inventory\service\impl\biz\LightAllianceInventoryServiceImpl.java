package com.yijiupi.himalaya.supplychain.inventory.service.impl.biz;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.biz.LightAllianceInventoryBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.LightAllianceProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.biz.ILightAllianceInventoryService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;

@Service(timeout = 60000)
public class LightAllianceInventoryServiceImpl implements ILightAllianceInventoryService {

    @Autowired
    private LightAllianceInventoryBL lightAllianceInventoryBL;

    /**
     * 获取单个商品轻加盟仓库库存.
     */
    @Override
    public LightAllianceProductInventoryDTO getProductInventoryMap(Long productSkuId, Integer warehouseId) {
        Integer channel = ProductChannelType.JIUPI;
        return lightAllianceInventoryBL.getProductInventory(productSkuId, warehouseId, channel);
    }

    /**
     * 获取多个商品轻加盟仓库库存.
     */
    @Override
    public Map<Long, LightAllianceProductInventoryDTO> getProductInventoryMap(List<Long> productSkuIds,
        Integer warehouseId) {
        Integer channel = ProductChannelType.JIUPI;
        return lightAllianceInventoryBL.getProductInventoryMap(productSkuIds, warehouseId, channel);
    }

    /**
     * 调整产品库存.
     */
    @Override
    public void adjustProductInventory(Long productSkuId, Integer warehouseId, BigDecimal count) {
        lightAllianceInventoryBL.adjustProductInventory(productSkuId, warehouseId, count);
    }

    /**
     * 复制仓库库存到轻加盟仓库
     */
    @Override
    public void copyInventoryForLightAlliance(Integer cityId, Integer cityWarehouseId, Integer lightWarehouseId,
        Integer opUserId, Integer channel) {
        lightAllianceInventoryBL.syncCopyInventory(cityId, cityWarehouseId, lightWarehouseId, opUserId, channel);
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl.biz;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class LightAllianceInventoryBLTest {

    @Autowired
    private LightAllianceInventoryBL lightAllianceInventoryBL;

    @Test
    public void getProductInventory() throws Exception {
        // LightAllianceProductInventoryDTO inventory = lightAllianceInventoryBL.getProductInventory(51200000003875L,
        // 5121,null);
    }

    @Test
    public void getProductInventoryMap() throws Exception {}

    @Test
    public void adjustProductInventory() throws Exception {
        // lightAllianceInventoryBL.adjustProductInventory(51200000003875L, 5121, 100);
    }

}
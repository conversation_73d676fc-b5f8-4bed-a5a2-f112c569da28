package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProcessLocationInventoryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/22
 */
@Service
public class ProcessLocationInventoryBL {

    @Autowired
    private List<ProcessLocationInventoryBaseBL> baseServiceList;
    protected final static Logger LOGGER = LoggerFactory.getLogger(ProcessLocationInventoryBL.class);

    @Transactional(rollbackFor = RuntimeException.class)
    public ProductInventoryChangeRecordPO processLocationInventory(ProcessLocationInventoryBO bo) {
        LOGGER.info("重构版本的货位库存处理: {}", JSON.toJSONString(bo));
        Optional<ProcessLocationInventoryBaseBL> serviceOptional =
            baseServiceList.stream().filter(m -> m.support(bo)).findAny();
        if (serviceOptional.isPresent()) {
            return serviceOptional.get().processLocationInventory(bo);
        }

        LOGGER.warn("未找到合适的服务，入参：{}", JSON.toJSONString(bo));
        return bo.getProductInventoryChangeRecordPO();
    }

}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.StoreDTOBySupplierOp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryListQueryService;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseStoreBySupplierOpSO;

@Service(timeout = 30000)
public class InventoryListQueryServiceImpl implements IInventoryListQueryService {

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;

    /**
     * 查询仓库库存列表
     */
    @Override
    public PageList<WarehouseInventoryDTO> listWarehouseInventory(WarehouseInventoryQueryDTO warehouseInventoryQuery,
        PagerCondition pager) {
        return warehouseInventoryQueryBL.listWarehouseInventory(warehouseInventoryQuery, pager);
    }

    /**
     * 查询城市SKU库存.
     *
     * @param cityId 城市ID
     * @return 城市SKU库存
     */
    @Override
    public List<ProductSkuInventoryCountDTO> listProductSkuInventoyByCity(Integer cityId, Integer channel,
        Integer source, Long secOwnerId) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        return warehouseInventoryQueryBL.listProductSkuInventoyByCity(cityId, channel, source, secOwnerId);
    }

    /**
     * 用户范围库存报表
     */
    @Override
    public PageList<InventoryReportDTO> findStoreReportPageByAuth(StockReportSO so, PagerCondition pager) {
        return warehouseInventoryQueryBL.findStoreReportPageByAuth(so, pager);
    }

    /**
     * 用户范围库存报表(正常库存调用,也可以只查询经销商库存)
     */
    @Override
    public PageList<InventoryReportDTO> findStoreReportPageByProductSpecification(StockReportSO so,
        PagerCondition pager) {
        return warehouseInventoryQueryBL.findStoreReportPageByProductSpecification(so, pager);
    }

    /**
     * 查询单个仓库库存列表（op仓库管理）
     */
    // @Override
    // public PageList<ProductWarehouseStoreDTO> findProductWarehouseStoreList(ProductWarehouseStoreSO so,
    // PagerCondition pager) {
    // return warehouseInventoryQueryBL.findProductWarehouseStoreList(so, pager);
    // }

    /**
     * 调拨系统产品查询库存用的
     */
    @Override
    public PageList<StoreDTOBySupplierOp> findProductWarehouseStoreListForAllocation(
        WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO, PagerCondition pagerCondition) {
        return warehouseInventoryQueryBL.findProductWarehouseStoreListForAllocation(warehouseStoreBySupplierOpSO,
            pagerCondition);
    }

    @Override
    public String queryAbcAttribute(List<Long> productSkuIdList, Integer warehouseId) {
        return warehouseInventoryQueryBL.queryAbcAttribute(productSkuIdList, warehouseId);
    }
    //
    // /**
    // * 库存信息汇总(供应商门户)
    // */
    // @Override
    // public StoreDTOBySupplierOp getProductStoreForSupplierOp(
    // WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO) {
    // AssertUtils.notNull(warehouseStoreBySupplierOpSO.getProductInfoSpecId(), "产品信息规格ID不能为空");
    // return warehouseInventoryQueryBL.getProductStoreForSupplierOp(warehouseStoreBySupplierOpSO);
    // }

    /**
     * 用户范围库存报表查询（包含平均库龄）
     */
    @Override
    public PageList<InventoryReportDTO> findStoreReportPageInfoByAuth(StockReportSO so, PagerCondition pager) {
        return warehouseInventoryQueryBL.findStoreReportPageInfoByAuthNew(so, pager);
    }

    /**
     * 用户范围库存报表
     */
    @Override
    public PageList<FindStoreInfoDTO> findStorePage(FindStoreDTO findStoreQuery) {
        return warehouseInventoryQueryBL.findStorePage(findStoreQuery);
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"></result>
        <result column="TotalCount_MinUnit" property="totalCountMinUnit" jdbcType="DECIMAL"></result>
        <result column="OwnerType" property="ownerType" jdbcType="INTEGER"></result>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"></result>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"></result>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"></result>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"></result>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="Channel" property="channel" jdbcType="INTEGER"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="INTEGER"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="warehouseCustodyFee" property="warehouseCustodyFee" jdbcType="DECIMAL"/>
        <result column="DeliveryFee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="DeliveryPayType" property="deliveryPayType" jdbcType="INTEGER"/>
        <result column="deliveryedCount" property="deliveryedCount" jdbcType="DECIMAL"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>

    </resultMap>
    <sql id="Base_Column_List">
        Id, City_Id, Warehouse_Id, TotalCount_MinUnit,
        OwnerType, Owner_Id, CreateTime, LastUpdateTime,
        ProductSpecification_Id,secOwner_id,channel
    </sql>
    <sql id="Base_Column_ByProductSku">
        ps.Id, ps.City_Id, ps.Warehouse_Id, ps.TotalCount_MinUnit,
        ps.OwnerType, ps.Owner_Id, ps.CreateTime, ps.LastUpdateTime,
        ps.ProductSpecification_Id,ps.secOwner_id,ps.channel
    </sql>
    <select id="insertInventoryPOList" parameterType="java.util.List">
        insert into productstore (Id, City_Id, Warehouse_Id, OwnerType, Owner_Id,
        CreateTime, LastUpdateTime,TotalCount_MinUnit,
        ProductSpecification_Id,secOwner_id,channel,UnifySkuId)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.id}, #{item.cityId},
            #{item.warehouseId},
            #{item.ownerType}, #{item.ownerId},
            #{item.createTime},
            #{item.lastUpdateTime}, #{item.totalCountMinUnit},
            #{item.productSpecificationId},
            #{item.secOwnerId}, #{item.channel},
            #{item.unifySkuId}
            )
        </foreach>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select ps.Id,
        ps.City_Id,
        ps.Warehouse_Id,
        ps.TotalCount_MinUnit,
        ps.OwnerType,
        ps.Owner_Id,
        ps.CreateTime,
        ps.LastUpdateTime,
        ps.ProductSpecification_Id,
        ps.secOwner_id,
        ps.channel,
        spec.packageQuantity,
        spec.packageName,
        spec.unitName,
        spec.name
        as specificationName
        from productstore ps
        inner join productinfospecification spec on spec.id = ps.ProductSpecification_Id
        where ps.Id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectInventoryListByPrimaryKey" resultMap="BaseResultMap">
        select
        ps.Id, ps.City_Id, ps.Warehouse_Id, ps.TotalCount_MinUnit,
        ps.OwnerType, ps.Owner_Id, ps.CreateTime, ps.LastUpdateTime,
        ps.ProductSpecification_Id,ps.secOwner_id,ps.channel,spec.packageQuantity,spec.packageName,spec.unitName,spec.name
        as specificationName
        from productstore ps
        inner join productinfospecification spec on spec.id = ps.ProductSpecification_Id
        where ps.Id IN
        <foreach collection="idList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getProductInventoryBySkuIdCityId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_ByProductSku"/>
        ,psku.ProductSku_Id,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        psku.OwnerName
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null and ps.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where psku.ProductSku_Id = #{dto.productSkuId,jdbcType=BIGINT}
        and psku.City_Id = #{dto.cityId,jdbcType=INTEGER}
        <if test="dto.channel!=null">
            and ps.Channel=#{dto.channel,jdbcType=INTEGER}
        </if>
    </select>


    <update id="increaseWarehouseCountBatchById" parameterType="list">
        update productstore
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="TotalCount_MinUnit =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then TotalCount_MinUnit + #{item.changeCount}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="listProductSkuInventoyByCity" parameterType="java.lang.Integer"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductSkuInventoryCountDTO">
        SELECT
        k.ProductSku_Id AS productSkuId,
        p.warehouse_id AS warehouseId,
        p.TotalCount_MinUnit AS count,
        p.Channel as channel,
        p.SecOwner_Id as secOwnerId,
        k.specificationName,
        k.packageName,
        k.unitName,
        k.packageQuantity,
        k.Source
        FROM productstore p, productsku k
        WHERE
        p.City_Id = #{cityId,jdbcType=INTEGER}
        AND p.productSpecification_id = k.productSpecification_id
        AND p.City_Id = k.City_Id
        AND ((k.Company_Id is null and p.Owner_Id is null) or (k.Company_Id = p.Owner_Id))
        AND ((k.secOwner_Id is null and p.secOwner_Id is null) or (k.secOwner_Id = p.secOwner_Id))
        <if test="channel!=null">
            AND p.channel = #{channel,jdbcType=INTEGER}
        </if>
        <if test="source!=null">
            AND k.Source = #{source,jdbcType=INTEGER}
        </if>
        <if test="secOwnerId!=null">
            AND p.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="findStoreReportPageByAuth"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO">
        SELECT
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSku_Id as productId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.Name) as
        productSkuName,
        ps.Warehouse_Id as warhouseId,
        ps.OwnerType as storeOwnerType,
        ps.Owner_Id as ownerId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.Channel as channel,
        ps.SecOwner_Id as secOwnerId,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.OwnerType as ownerType,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        owner.OwnerName as secOwnerName,
        psku.ProductState,
			<if test="so.productSkuIds == null or so.productSkuIds.size() == 0">
        pc.storageAttribute warehouseAllocationType,
      </if>
        psku.ProductType
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        <if test="so.eraseOwnerId == null or so.eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        LEFT JOIN owner on ps.SecOwner_Id is not null and owner.id = ps.SecOwner_Id
        <if test="so.productSkuIds == null or so.productSkuIds.size() == 0">
            inner join productskuconfig pc on psku.ProductSku_Id = pc.ProductSku_Id and pc.Warehouse_Id = ps.Warehouse_Id
            <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
                and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
            </if>
        </if>
        where 1=1
        <if test="(so.productSkuIds == null or so.productSkuIds.size() == 0) and so.limitSku != null and so.limitSku == 1">
            and pc.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="(so.productSkuIds == null or so.productSkuIds.size() == 0) and so.inventoryPinProperty != null">
            AND pc.inventoryPinProperty like concat('%',#{so.inventoryPinProperty,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.source!=null">
            AND psku.Source=#{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.warehouseIds!=null">
            and ps.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
        </if>
        <if test="so.storeOwnerType!=null">
            and ps.OwnerType=#{so.storeOwnerType,jdbcType=INTEGER}
        </if>
        <if test="so.productSkuIds != null and so.productSkuIds.size() > 0">
            and psku.ProductSku_Id in
            <foreach item="item" collection="so.productSkuIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == 1">
            and ps.TotalCount_MinUnit <![CDATA[ > ]]>  0
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == 0">
            and ps.TotalCount_MinUnit <![CDATA[ <= ]]>  0
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == -1">
            and ps.TotalCount_MinUnit <![CDATA[ < ]]>  0
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and psku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.productTypeList != null and so.productTypeList.size() > 0">
            and psku.ProductType in
            <foreach item="item" collection="so.productTypeList" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
    </select>

    <select id="findDeliveryedCountByProductStoreIds"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO">
        SELECT
        dc.Id as productStoreId,
        IFNULL(dc.DeliveryedCount_MinUnit,0) as deliveryedCount
        FROM deliverystorerecord dc
        where dc.id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findStoreReportPageByProductSpecification"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO">
        SELECT
        ps.Id as productStoreId,
        info.ProductName as productSkuName,
        ps.Warehouse_Id as warhouseId,
        ps.OwnerType as storeOwnerType,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.Channel as channel,
        0 as deliveryedCount,
        spec.Name as specificationName,
        spec.packageName,
        spec.unitName,
        spec.packageQuantity,
        ps.OwnerType as ownerType,
        spec.id as productSpecificationId,
        owner.OwnerName as OwnerName,
        secOwner.OwnerName as secOwnerName
        FROM
        productstore ps
        INNER JOIN productinfospecification spec ON ps.ProductSpecification_Id=spec.id
        INNER JOIN productinfo info ON info.id=spec.ProductInfo_Id
        LEFT JOIN owner owner on ps.Owner_Id is not null and owner.id = ps.Owner_Id
        LEFT JOIN owner secOwner on ps.SecOwner_Id is not null and secOwner.id = ps.SecOwner_Id
        where 1=1
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND info.ProductName like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.warehouseIds!=null">
            and ps.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
        </if>
        <if test="so.storeOwnerType!=null">
            and ps.OwnerType=#{so.storeOwnerType,jdbcType=INTEGER}
        </if>
    </select>

    <select id="findProductInventoryByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM productstore
        WHERE Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="channel!=null">
            and Channel = #{channel,jdbcType=INTEGER}
        </if>
    </select>
    <!--通过skuid和仓库id查询出记录-->
    <select id="findProductInventoryByProductSkuIdWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_ByProductSku"/>,
        psku.ProductSku_Id,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        psku.warehouseCustodyFee,
        psku.DeliveryFee,
        psku.DeliveryPayType,
        secOwner.OwnerName as secOwnerName
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="eraseOwner == null or eraseOwner == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND psku.City_Id = ps.City_Id
        LEFT JOIN owner secOwner on ps.SecOwner_Id is not null and secOwner.id = ps.SecOwner_Id
        WHERE
        psku.ProductSku_Id in
        <foreach collection="productSkuIdList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        AND ps.Warehouse_Id =#{warehouseId}
        <if test="channel!=null">
            AND ps.Channel=#{channel,jdbcType=INTEGER}
        </if>
        <if test="secOwnerId!=null">
            and ps.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}
        </if>
    </select>

    <!--通过skuid和仓库id查询出记录-->
    <select id="findProductInventoryByProductSpecIdWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_ByProductSku"/>,
        psku.ProductSku_Id,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        psku.warehouseCustodyFee,
        psku.DeliveryFee,
        psku.DeliveryPayType
        FROM
        productstore ps
        left JOIN productsku psku ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        WHERE
        ps.ProductSpecification_Id in
        <foreach collection="productSpecIdList" index="index" item="item" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND ps.Warehouse_Id =#{warehouseId}
        <if test="channel!=null">
            AND ps.Channel=#{channel,jdbcType=INTEGER}
        </if>
        <if test="secOwnerId!=null">
            and ps.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="ownerId==null">
            and ps.Owner_Id is null
        </if>
        <if test="ownerId!=null">
            and ps.Owner_Id = #{ownerId,jdbcType=BIGINT}
        </if>
    </select>

    <resultMap id="ListWarehouseInventoryMap"
               type="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryDTO">
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="Name" property="productName" jdbcType="VARCHAR"/>
        <result column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="ProductSpecification_Id" property="productSpecId" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="TIMESTAMP"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="OwnerType" property="ownerType" jdbcType="INTEGER"/>
        <result column="TotalCount_MinUnit" property="warehouseTotalCount" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="INTEGER"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="INTEGER"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="deliveryedCount" property="deliveryedCount" jdbcType="DECIMAL"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>

    </resultMap>

    <!-- 查询仓库是否有库存 -->
    <select id="isHaveWarehouseInventory" resultType="byte">
        select 1
        from productstore ps
        where ps.TotalCount_MinUnit > #{inventoryFloor,jdbcType=DECIMAL}
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        limit 1
    </select>

    <!--查询仓库库存-->
    <select id="getListWarehouseInventoryBySpecInfo" resultMap="ListWarehouseInventoryMap">
        select
        IFNULL(psku.Name,info.ProductName) as Name,
        ps.Id,
        ps.City_Id,
        ps.ProductSpecification_Id,
        ps.Warehouse_Id,
        ps.Owner_Id,
        ps.OwnerType,
        ps.TotalCount_MinUnit,
        ps.channel,
        0 as deliveryedCount,
        IFNULL(spec.`Name`,psku.specificationName) AS specificationName,
        IFNULL(spec.PackageQuantity,psku.packageQuantity) AS packageQuantity,
        IFNULL(spec.PackageName,psku.packageName) AS packageName,
        IFNULL(spec.UnitName,psku.unitName) AS unitName,
        ps.SecOwner_Id,
        psku.ProductSku_Id,
        own.OwnerName
        FROM productstore ps
        INNER JOIN productinfospecification spec on spec.id = ps.ProductSpecification_Id
        INNER JOIN productinfo info on info.id = spec.ProductInfo_Id
        LEFT JOIN owner own on ps.Owner_Id is not null and ps.Owner_Id = own.id
        LEFT JOIN productsku psku ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        WHERE 1=1
        <if test="dto.cityId!=null">
            and ps.City_Id = #{dto.cityId}
        </if>
        <if test="dto.warehouseId!=null">
            AND (ps.Warehouse_Id =#{dto.warehouseId} or ps.Warehouse_Id is null)
        </if>
        <if test="dto.channel!=null">
            AND ps.Channel=#{dto.channel,jdbcType=INTEGER}
        </if>
        <if test="dto.secOwnerId!=null ">
            and ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="dto.productName!=null and dto.productName!='' ">
            and info.ProductName like concat('%',#{dto.productName,jdbcType=VARCHAR},'%')
        </if>
    </select>
    <select id="getListWarehouseInventory" resultMap="ListWarehouseInventoryMap">

        select
        psku.`Name`,
        ps.Id,
        psku.City_Id,
        psku.ProductSpecification_Id,
        ps.Warehouse_Id,
        ps.Owner_Id,
        ps.OwnerType,
        ps.TotalCount_MinUnit,
        ps.channel,
        0 as deliveryedCount,
        psku.ProductSku_Id,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id,
        psku.OwnerName
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        <if test="dto.channel!=null">
            AND ps.Channel=#{dto.channel,jdbcType=INTEGER}
        </if>
        <if test="dto.warehouseId!=null">
            AND (ps.Warehouse_Id =#{dto.warehouseId} or ps.Warehouse_Id is null)
        </if>
        <if test="dto.secOwnerId!=null ">
            and ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=BIGINT}
        </if>
        WHERE 1=1
        <if test="dto.productSkuId!=null">
            and psku.productsku_id =#{dto.productSkuId}
        </if>
        <if test="dto.cityId!=null">
            and psku.City_Id = #{dto.cityId}
        </if>
        <if test="dto.productName!=null and dto.productName!='' ">
            and psku.`Name` like concat('%',#{dto.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.source!=null ">
            and psku.Source = #{dto.source,jdbcType=INTEGER}
        </if>

    </select>
    <!--查询仓库库存-->
    <select id="getListWarehouseInventoryByOwner" resultMap="ListWarehouseInventoryMap">
        select psku.`Name`,
        ps.Id,
        psku.City_Id,
        psku.ProductSpecification_Id,
        ps.Warehouse_Id,
        ps.Owner_Id,
        ps.OwnerType,
        ps.TotalCount_MinUnit,
        ps.channel,
        0 as deliveryedCount,
        psku.ProductSku_Id,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id,
        psku.OwnerName
        FROM productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null and ps.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        WHERE ps.Owner_Id > 0
        and ps.SecOwner_Id is null
    </select>

    <resultMap id="BaseResultShopMap"
               type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductShopStoreInventoryPO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"></result>
        <result column="TotalCount_MinUnit" property="totalCountMinUnit" jdbcType="DECIMAL"></result>
        <result column="OwnerType" property="ownerType" jdbcType="INTEGER"></result>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"></result>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"></result>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"></result>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"></result>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="Name" property="productSkuName" jdbcType="VARCHAR"/>
        <result column="Channel" property="channel" jdbcType="INTEGER"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="INTEGER"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="warehouseCustodyFee" property="warehouseCustodyFee" jdbcType="DECIMAL"/>
        <result column="DeliveryFee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="DeliveryPayType" property="deliveryPayType" jdbcType="INTEGER"/>
        <result column="deliveryedCount" property="deliveryedCount" jdbcType="DECIMAL"/>

    </resultMap>
    <!--    <select id="getProductInventoryStoreId" resultMap="BaseResultShopMap">-->
    <!--        select store.Id,store.TotalCount_MinUnit,sku.specificationName,sku.Name,sku.packageQuantity,-->
    <!--        sku.packageName,sku.unitName,sku.ProductSku_Id-->
    <!--        from productstore store-->
    <!--        inner JOIN productsku sku on store.ProductSpecification_Id = sku.ProductSpecification_Id-->
    <!--        AND sku.City_Id = store.City_Id-->
    <!--        AND ((sku.Company_Id is null and store.Owner_Id is null) or (sku.Company_Id = store.Owner_Id))-->
    <!--        AND ((sku.secOwner_Id is null and store.secOwner_Id is null) or (sku.secOwner_Id = store.secOwner_Id))-->
    <!--        and store.OwnerType = #{productSkuForInventoryShopDTO.ownerType}-->
    <!--        where-->
    <!--        store.Warehouse_Id = #{productSkuForInventoryShopDTO.warehouseId}-->
    <!--        and store.Owner_Id = #{productSkuForInventoryShopDTO.ownerId}-->
    <!--        and store.Channel=#{productSkuForInventoryShopDTO.channel}-->
    <!--        and sku.Source=#{productSkuForInventoryShopDTO.source}-->
    <!--        <if test="productSkuForInventoryShopDTO.searchKey!=null">-->
    <!--            and sku.Name like concat('%',#{productSkuForInventoryShopDTO.searchKey},'%')-->
    <!--        </if>-->
    <!--        <if test="productSkuForInventoryShopDTO.productSkuIdList!=null and productSkuForInventoryShopDTO.productSkuIdList.size>0">-->
    <!--            and sku.ProductSku_Id IN-->
    <!--            <foreach item="item" collection="productSkuForInventoryShopDTO.productSkuIdList" separator="," open="("-->
    <!--                     close=")" index="">-->
    <!--                #{item,jdbcType=INTEGER}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--    </select>-->

    <sql id="getProductInventorys">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.Name AS productName,
        psku.ProductSpecification_Id AS productSpecificationId,
        psku.SaleModel AS saleModel,
        psku.Company_Id AS ownerId,
        psku.OwnerName AS ownerName,
        psku.specificationName AS specificationName,
        psku.packageQuantity AS packageQuantity,
        psku.packageName AS packageName,
        psku.unitName AS unitName,
        psku.Source AS source,
        psku.ProductInfo_Id AS productInfoId,
        pc.StatisticsClass AS statisticsClass,
        pc.StatisticsClassName AS statisticsClassName,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Channel as channel,
        ps.lastupdatetime as lastUpdateTime,
        ps.secOwner_Id as secOwnerId,
        ps.Id as id
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="dto.eraseOwnerId == null or dto.eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND psku.City_Id = ps.City_Id
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        <where>1=1
            <if test="dto.cityId != null">
                AND ps.City_Id = #{dto.cityId,jdbcType=INTEGER}
            </if>
            <if test="dto.warehouseId != null">
                AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="dto.ownId != null">
                AND ps.Owner_Id = #{dto.ownId,jdbcType=BIGINT}
            </if>
            <if test="dto.secOwnerId!=null">
                and ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="dto.source != null">
                AND psku.Source = #{dto.source,jdbcType=TINYINT}
            </if>
            <if test="dto.channel != null">
                AND ps.Channel =#{dto.channel,jdbcType=TINYINT}
            </if>
            <if test="dto.productSkuIds != null and dto.productSkuIds.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="dto.productSkuIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.specAndOwnerIds != null and dto.specAndOwnerIds.size() > 0">
                AND
                <foreach collection="dto.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                    <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
            <if test="dto.productSpecIds != null and dto.productSpecIds.size() > 0">
                AND psku.ProductSpecification_Id in
                <foreach collection="dto.productSpecIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.statisticsClassList != null and dto.statisticsClassList.size() > 0">
                AND pc.StatisticsClass in
                <foreach collection="dto.statisticsClassList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="getProductInventorysByPager"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        <include refid="getProductInventorys"/>
    </select>

    <select id="pageFindProductInventoryByWarehouseId"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.Name AS productName,
        psku.ProductSpecification_Id AS productSpecificationId,
        psku.SaleModel AS saleModel,
        psku.Company_Id AS ownerId,
        psku.OwnerName AS ownerName,
        psku.specificationName AS specificationName,
        psku.packageQuantity AS packageQuantity,
        psku.packageName AS packageName,
        psku.unitName AS unitName,
        psku.Source AS source,
        psku.ProductInfo_Id AS productInfoId,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Channel as channel,
        ps.lastupdatetime as lastUpdateTime,
        ps.secOwner_Id as secOwnerId,
        ps.Id as id
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="dto.eraseOwnerId == null or dto.eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND psku.City_Id = ps.City_Id
        <where>1=1
            <if test="dto.cityId != null">
                AND ps.City_Id = #{dto.cityId,jdbcType=INTEGER}
            </if>
            <if test="dto.warehouseId != null">
                AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="dto.ownId != null">
                AND ps.Owner_Id = #{dto.ownId,jdbcType=BIGINT}
            </if>
            <if test="dto.secOwnerId!=null">
                and ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="dto.source != null">
                AND psku.Source = #{dto.source,jdbcType=TINYINT}
            </if>
            <if test="dto.channel != null">
                AND ps.Channel =#{dto.channel,jdbcType=TINYINT}
            </if>
            <if test="dto.productSkuIds != null and dto.productSkuIds.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="dto.productSkuIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.specAndOwnerIds != null and dto.specAndOwnerIds.size() > 0">
                AND
                <foreach collection="dto.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                    <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
            <if test="dto.productSpecIds != null and dto.productSpecIds.size() > 0">
                AND psku.ProductSpecification_Id in
                <foreach collection="dto.productSpecIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.statisticsClassList != null and dto.statisticsClassList.size() > 0">
                AND pc.StatisticsClass in
                <foreach collection="dto.statisticsClassList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        limit #{offset,jdbcType=INTEGER},#{limitnum,jdbcType=INTEGER}
    </select>

    <select id="pageFindProductInventoryByWarehouseIdCount"
            resultType="java.lang.Integer">
        select
        count(0)
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="dto.eraseOwnerId == null or dto.eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND psku.City_Id = ps.City_Id
        <where>1=1
            <if test="dto.cityId != null">
                AND ps.City_Id = #{dto.cityId,jdbcType=INTEGER}
            </if>
            <if test="dto.warehouseId != null">
                AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="dto.ownId != null">
                AND ps.Owner_Id = #{dto.ownId,jdbcType=BIGINT}
            </if>
            <if test="dto.secOwnerId!=null">
                and ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="dto.source != null">
                AND psku.Source = #{dto.source,jdbcType=TINYINT}
            </if>
            <if test="dto.channel != null">
                AND ps.Channel =#{dto.channel,jdbcType=TINYINT}
            </if>
            <if test="dto.productSkuIds != null and dto.productSkuIds.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="dto.productSkuIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.specAndOwnerIds != null and dto.specAndOwnerIds.size() > 0">
                AND
                <foreach collection="dto.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                    <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
            <if test="dto.productSpecIds != null and dto.productSpecIds.size() > 0">
                AND psku.ProductSpecification_Id in
                <foreach collection="dto.productSpecIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.statisticsClassList != null and dto.statisticsClassList.size() > 0">
                AND pc.StatisticsClass in
                <foreach collection="dto.statisticsClassList" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getProductInventorysByProductInfoSpecification"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        select
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        info.ProductName AS productName,
        spec.Id AS productSpecificationId,
        ps.Owner_Id AS ownerId,
        ps.SecOwner_Id AS secOwnerId,
        own.OwnerName AS ownerName,
        secOwn.OwnerName AS secOwnerName,
        spec.Name AS specificationName,
        spec.PackageQuantity AS packageQuantity,
        spec.PackageName AS packageName,
        spec.UnitName AS unitName,
        spec.ProductInfo_Id AS productInfoId,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        ps.Channel as channel,
        ps.lastupdatetime as lastUpdateTime
        FROM productinfospecification spec
        INNER JOIN productinfo info ON info.id = spec.ProductInfo_Id
        INNER JOIN productstore ps ON spec.id = ps.ProductSpecification_Id
        LEFT JOIN owner own ON own.id = ps.Owner_Id
        LEFT JOIN OWNER secOwn ON secOwn.id = ps.SecOwner_Id

        <if test="dto.warehouseAllocationType != null and dto.warehouseAllocationType != ''">
        INNER JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
            AND ps.City_Id = psku.City_Id
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productskuconfig pc on pc.ProductSku_Id = psku.ProductSku_Id and ps.Warehouse_Id = pc.Warehouse_Id
            and (pc.storageAttribute = #{dto.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
        </if>

        <where>1=1
            <if test="dto.cityId != null">
                AND ps.City_Id = #{dto.cityId,jdbcType=INTEGER}
            </if>
            <if test="dto.warehouseId != null">
                AND ps.Warehouse_Id = #{dto.warehouseId,jdbcType=INTEGER}
            </if>
            <if test="dto.ownId != null">
                AND ps.Owner_Id = #{dto.ownId,jdbcType=BIGINT}
            </if>
            <if test="dto.secOwnerId!=null">
                and ps.SecOwner_Id = #{dto.secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="dto.channel != null">
                AND ps.Channel =#{dto.channel,jdbcType=TINYINT}
            </if>
            <if test="dto.specAndOwnerIds != null and dto.specAndOwnerIds.size() > 0">
                AND
                <foreach collection="dto.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                    <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
            <if test="dto.productSpecIds != null and dto.productSpecIds.size() > 0">
                AND ps.ProductSpecification_Id in
                <foreach collection="dto.productSpecIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getProductInventorys"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.Name AS productName,
        psku.ProductSpecification_Id AS productSpecificationId,
        psku.SaleModel AS saleModel,
        psku.Company_Id AS ownerId,
        psku.OwnerName AS ownerName,
        psku.specificationName AS specificationName,
        psku.packageQuantity AS packageQuantity,
        psku.packageName AS packageName,
        psku.unitName AS unitName,
        psku.Source AS source,
        psku.ProductInfo_Id AS productInfoId,
        pc.StatisticsClass AS statisticsClass,
        pc.StatisticsClassName AS statisticsClassName,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Channel as channel,
        ps.lastupdatetime as lastUpdateTime,
        ps.secOwner_Id as secOwnerId,
        ps.Id as id
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND psku.City_Id = ps.City_Id
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        <where>1=1
            <if test="cityId != null">
                AND ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="ownId != null">
                AND ps.Owner_Id = #{ownId,jdbcType=BIGINT}
            </if>
            <if test="secOwnerId!=null">
                and ps.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=TINYINT}
            </if>
            <if test="channel != null">
                AND ps.Channel =#{channel,jdbcType=TINYINT}
            </if>
            <if test="productSkuIds != null and productSkuIds.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="productSkuIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="specAndOwnerIds != null and specAndOwnerIds.size() > 0">
                AND
                <foreach collection="specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                    <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
            <if test="productSpecIds != null and productSpecIds.size() > 0">
                AND psku.ProductSpecification_Id in
                <foreach collection="productSpecIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="specAndOwnerIdAndSecOwnerIds != null and specAndOwnerIdAndSecOwnerIds.size() > 0">
                AND
                <foreach collection="specAndOwnerIdAndSecOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">
                        AND ps.Owner_Id is null
                    </if>
                    <if test="item.ownerId != null">
                        AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                    </if>
                    <if test="item.secOwnerId == null">
                        AND ps.SecOwner_Id is null
                    </if>
                    <if test="item.secOwnerId != null">
                        AND ps.SecOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}
                    </if>
                    )
                </foreach>
            </if>
        </where>
        order by psku.id
    </select>

    <select id="getProductInventorysByAll"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.Name AS productName,
        psku.ProductSpecification_Id AS productSpecificationId,
        psku.SaleModel AS saleModel,
        psku.Company_Id AS ownerId,
        psku.OwnerName AS ownerName,
        psku.specificationName AS specificationName,
        psku.packageQuantity AS packageQuantity,
        psku.packageName AS packageName,
        psku.unitName AS unitName,
        psku.Source AS source,
        psku.ProductInfo_Id AS productInfoId,
        pc.StatisticsClass AS statisticsClass,
        pc.StatisticsClassName AS statisticsClassName,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        psku.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Channel as channel,
        ps.lastupdatetime as lastUpdateTime
        FROM
        productsku psku
        LEFT JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null and ps.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        <if test="channel != null">
            AND ps.Channel =#{channel,jdbcType=TINYINT}
        </if>
        <if test="warehouseId != null">
            AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        <where>
            <if test="source != null">
                psku.Source = #{source,jdbcType=TINYINT}
            </if>
            <if test="ownId != null">
                AND psku.Company_Id = #{ownId,jdbcType=BIGINT}
            </if>
            <if test="cityId != null">
                AND psku.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productSkuIds != null and productSkuIds.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="productSkuIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="specAndOwnerIds != null and specAndOwnerIds.size() > 0">
                AND
                <foreach collection="specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    psku.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND psku.Company_Id is null</if>
                    <if test="item.ownerId != null">AND psku.Company_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
        </where>
    </select>

    <select id="listWarehouseInventoryYJX"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.WarehouseInventoryYJXDTO">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.Name as productName,
        psku.productBrand as productBrand,
        psku.specificationName as specificationName,
        psku.packageQuantity as packageQuantity,
        psku.packageName as packageName,
        psku.unitName as unitName,
        psku.OwnerName as ownerName,
        ps.OwnerType as ownerType,
        ps.TotalCount_MinUnit AS unitTotalCount,
        own.OwnerName as secOwnerName
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null and ps.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id
        <where>
            ps.Owner_Id in (select dc.Dealer_Id from dealerchargeconfig dc where dc.Facilitator_Id =
            #{facilitatorId,jdbcType=BIGINT})
            and ps.Warehouse_Id in (select wh.Id from warehouse wh where wh.City_Id = #{facilitatorId,jdbcType=BIGINT})
            <if test="cityId != null">
                and ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="ownerId != null">
                and ps.Owner_Id = #{ownerId,jdbcType=BIGINT}
            </if>
            <if test="ownerType != null">
                and ps.OwnerType = #{ownerType,jdbcType=TINYINT}
            </if>
            <if test="productName != null">
                and psku.Name like concat('%', #{productName,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

    <select id="listWarehouseInventoryReport"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryReportDTO">
        select
        ps.City_Id AS cityId,
        ps.Warehouse_Id AS warehouseId,
        psku.ProductSku_Id AS productSkuId,
        ps.Id AS storeId,
        IFNULL(info.ProductName,psku.Name) AS productName,
        IFNULL(own.Id,psku.Company_Id) AS ownerId,
        IFNULL(own.OwnerName,psku.OwnerName) AS ownerName,
        secOwn.OwnerName AS secOwnerName,
        ps.SecOwner_Id AS secOwnerId,
        spec.ProductInfo_Id AS productInfoId,
        spec.Id AS productInfoSpecificationId,
        pc.StatisticsClassName AS statisticsClassName,
        ps.Channel AS channel,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        IFNULL(spec.`Name`,psku.specificationName) AS specName,
        IFNULL(spec.PackageQuantity,psku.packageQuantity) AS specQuantity,
        IFNULL(spec.PackageName,psku.packageName) AS packageCountName,
        IFNULL(spec.UnitName,psku.unitName) AS unitCountName
        FROM productstore ps
        INNER join productinfospecification spec on spec.id = ps.ProductSpecification_Id
        inner join productinfo info on info.id = spec.ProductInfo_Id
        left join owner own on ps.Owner_Id is not null and ps.Owner_Id = own.Id
        left join owner secOwn on ps.SecOwner_Id is not null and ps.SecOwner_Id = secOwn.Id
        LEFT JOIN productsku psku ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        <where>
            <if test="cityId != null">
                ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="productName != null">
                and info.ProductName like concat('%', #{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="ownerId != null and ownerId == 1">and ps.Owner_Id is null</if>
            <if test="ownerId != null and ownerId != 1">and ps.Owner_Id = #{ownerId,jdbcType=BIGINT}</if>
            <if test="secOwnerId != null">and ps.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}</if>
            <if test="hasRealStoreType != null and hasRealStoreType == 1">
                and ps.TotalCount_MinUnit <![CDATA[ > ]]>  0
            </if>
            <if test="hasRealStoreType != null and hasRealStoreType == 0">
                and ps.TotalCount_MinUnit <![CDATA[ <= ]]>  0
            </if>
            <if test="hasRealStoreType != null and hasRealStoreType == -1">
                and ps.TotalCount_MinUnit <![CDATA[ < ]]>  0
            </if>
        </where>
    </select>

    <select id="listProductWaitDelivery"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductWaitDeliveryDTO">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.ProductSpecification_Id AS productSpecificationId,
        psku.Company_Id AS ownerId,
        psku.Name AS productName,
        psku.specificationName AS specificationName,
        psku.packageQuantity AS packageQuantity,
        psku.packageName AS packageName,
        psku.unitName AS unitName,
        psku.ProductFeature AS productFeature,
        psku.IsPick AS pick,
        psku.IsSow AS sow,
        psku.StorageType AS storageType,
        ps.TotalCount_MinUnit AS storeTotalUnitCount,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null and ps.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id AND ps.TotalCount_MinUnit > 0
        WHERE
        ps.City_Id = #{cityId,jdbcType=INTEGER}
        AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="skuIds != null and skuIds.size() > 0">
            and psku.ProductSku_Id in
            <foreach collection="skuIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="specAndOwnerIds != null and specAndOwnerIds.size() > 0">
            AND
            <foreach collection="specAndOwnerIds" item="item" open="(" close=")" separator="or">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                )
            </foreach>
        </if>
        <if test="productName != null">
            AND psku.Name like concat('%', #{productName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="productFeature != null">
            AND psku.ProductFeature = #{productFeature,jdbcType=TINYINT}
        </if>
        <if test="storageType != null">
            AND psku.StorageType = #{storageType,jdbcType=TINYINT}
        </if>
    </select>

    <select id="listProductSecOwnerId"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        select
        Owner_Id as ownerId,
        ProductSpecification_Id as productSpecificationId,
        SecOwner_Id AS secOwnerId,
        Warehouse_Id AS warehouseId
        FROM productstore
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="specAndOwnerIds != null and specAndOwnerIds.size() > 0">
            AND
            <foreach collection="specAndOwnerIds" item="item" open="(" close=")" separator="or">
                (
                ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">AND Owner_Id is null</if>
                <if test="item.ownerId != null">AND Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                )
            </foreach>
        </if>
    </select>

    <select id="listProductStore" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM productstore
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="specAndOwnerIds != null and specAndOwnerIds.size() > 0">
            AND
            <foreach collection="specAndOwnerIds" item="item" open="(" close=")" separator="or">
                (
                ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">AND Owner_Id is null</if>
                <if test="item.ownerId != null">AND Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                <if test="item.secOwnerId == null">and SecOwner_Id is null</if>
                <if test="item.secOwnerId != null">and SecOwner_Id = #{item.secOwnerId,jdbcType=BIGINT}</if>
                )
            </foreach>
        </if>
    </select>


    <select id="findProductInWarehouseInventory"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryReportDTO">
        select
        distinct
        ps.City_Id AS cityId,
        psku.ProductSku_Id AS productSkuId,
        IFNULL(info.ProductName,psku.Name) AS productName,
        IFNULL(info.Brand,psku.productBrand) AS productBrand,
        spec.Id AS productInfoSpecificationId,
        IFNULL(spec.`Name`,psku.specificationName) AS specName,
        pc.StatisticsClass as statisticsClass,
        pc.StatisticsClassName AS statisticsClassName,
        pc.SecondStatisticsClass as secondStatisticsClass,
        pc.SecondStatisticsClassName AS secondStatisticsClassName
        FROM productstore ps
        INNER join productinfospecification spec on spec.id = ps.ProductSpecification_Id
        inner join productinfo info on info.id = spec.ProductInfo_Id
        LEFT JOIN productsku psku ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND psku.City_Id = ps.City_Id
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        <where>
            <if test="cityId != null">
                ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="productName != null">
                and info.ProductName like concat('%', #{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productBrand != null">
                and info.Brand like concat('%', #{productBrand,jdbcType=VARCHAR}, '%')
            </if>
            <if test="firstCategoryId != null">and pc.StatisticsClass = #{firstCategoryId,jdbcType=BIGINT}</if>
            <if test="secondCategoryId != null">and pc.SecondStatisticsClass = #{secondCategoryId,jdbcType=BIGINT}</if>
        </where>
    </select>

    <select id="listProductStoreBySkuInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_ByProductSku"/>
        ,psku.ProductSku_Id,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        psku.OwnerName
        from productstore ps
        INNER JOIN productsku psku
        ON
        ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join productinfocategory cat on psku.ProductInfoCategory_Id=cat.Id
        where
        ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="ownerId == null">
            and ps.Owner_Id is null
        </if>
        <if test="ownerId != null">
            and ps.Owner_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        <if test="skuIds != null and skuIds.size() > 0">
            and psku.ProductSku_Id in
            <foreach collection="skuIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="storeLimit != null">
            <if test="storeLimit == 0">
                and ps.TotalCount_MinUnit = 0
            </if>
            <if test="storeLimit == 1">
                and ps.TotalCount_MinUnit != 0
            </if>
            <if test="storeLimit == 2 and startTime != null">
                and (ps.TotalCount_MinUnit != 0 or ps.lastupdatetime <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP})
            </if>
        </if>
        <if test="endTime != null">
            and ps.lastupdatetime <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="firstCategoryIdList != null and firstCategoryIdList.size() > 0">
            and cat.StatisticsClass in
            <foreach collection="firstCategoryIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findStoreReportPageInfoByAuth"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO">
        SELECT
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSku_Id as productId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.Name) as
        productSkuName,
        ps.Warehouse_Id as warhouseId,
        ps.OwnerType as storeOwnerType,
        ps.Owner_Id as ownerId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.Channel as channel,
        ps.SecOwner_Id as secOwnerId,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.OwnerType as ownerType,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        owner.OwnerName as secOwnerName,
        psku.ProductState,
        (select round(SUM(DATEDIFF(now(), psb.createtime) * psb.totalcount_minunit) / ps.TotalCount_MinUnit) from
        productstorebatch psb where psb.productstore_id = ps.Id and psb.totalcount_minunit != 0 and
        ps.TotalCount_MinUnit != 0 group by psb.productstore_id) as averageStockAge
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        <if test="so.eraseOwnerId == null or so.eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        LEFT JOIN owner on ps.SecOwner_Id is not null and owner.id = ps.SecOwner_Id
        inner join productskuconfig pc on psku.ProductSku_Id = pc.ProductSku_Id and pc.Warehouse_Id = ps.Warehouse_Id
        where 1=1
        <if test="so.limitSku != null and so.limitSku == 1">
            and pc.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.inventoryPinProperty != null">
            AND pc.inventoryPinProperty like concat('%',#{so.inventoryPinProperty,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.source!=null">
            AND psku.Source=#{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.warehouseIds!=null">
            and ps.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
        </if>
        <if test="so.storeOwnerType!=null">
            and ps.OwnerType=#{so.storeOwnerType,jdbcType=INTEGER}
        </if>
        <if test="so.productSkuIds != null and so.productSkuIds.size() > 0">
            and psku.ProductSku_Id in
            <foreach item="item" collection="so.productSkuIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == 1">
            and ps.TotalCount_MinUnit <![CDATA[ > ]]>  0
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == 0">
            and ps.TotalCount_MinUnit <![CDATA[ <= ]]>  0
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == -1">
            and ps.TotalCount_MinUnit <![CDATA[ < ]]>  0
        </if>
        <if test="so.startStockAge != null or so.endStockAge != null">
            and exists
            (select round(SUM(DATEDIFF(now(), psb.createtime) * psb.totalcount_minunit) / ps.TotalCount_MinUnit) as
            stockAge from productstorebatch psb where psb.productstore_id = ps.Id
            group by psb.productstore_id having
            <if test="so.startStockAge != null">
                <![CDATA[ stockAge >= #{so.startStockAge,jdbcType=BIGINT} ]]>
            </if>
            <if test="so.startStockAge != null and so.endStockAge != null">
                and
            </if>
            <if test="so.endStockAge != null">
                <![CDATA[ stockAge <= #{so.endStockAge,jdbcType=BIGINT} ]]>
            </if>
            )
        </if>
    </select>

    <select id="pageListProductInventorys"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO">
        select
        psku.ProductSku_Id AS productSkuId,
        psku.Name AS productName,
        psku.ProductSpecification_Id AS productSpecificationId,
        psku.SaleModel AS saleModel,
        psku.Company_Id AS ownerId,
        psku.OwnerName AS ownerName,
        psku.specificationName AS specificationName,
        psku.packageQuantity AS packageQuantity,
        psku.packageName AS packageName,
        psku.unitName AS unitName,
        psku.Source AS source,
        psku.ProductInfo_Id AS productInfoId,
        pc.StatisticsClass AS statisticsClass,
        pc.StatisticsClassName AS statisticsClassName,
        ps.TotalCount_MinUnit AS totalCountMinUnit,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.Channel as channel,
        ps.lastupdatetime as lastUpdateTime,
        ps.secOwner_Id as secOwnerId
        FROM
        productsku psku
        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND psku.City_Id = ps.City_Id
        LEFT JOIN productinfocategory pc on psku.ProductInfoCategory_Id = pc.Id
        <where>1=1
            <if test="cityId != null">
                AND ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="ownId != null">
                AND ps.Owner_Id = #{ownId,jdbcType=BIGINT}
            </if>
            <if test="secOwnerId!=null">
                and ps.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=TINYINT}
            </if>
            <if test="channel != null">
                AND ps.Channel =#{channel,jdbcType=TINYINT}
            </if>
            <if test="productSkuIds != null and productSkuIds.size() > 0">
                AND psku.ProductSku_Id in
                <foreach collection="productSkuIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="specAndOwnerIds != null and specAndOwnerIds.size() > 0">
                AND
                <foreach collection="specAndOwnerIds" item="item" open="(" close=")" separator="or">
                    (
                    ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                    <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                    <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                    )
                </foreach>
            </if>
            <if test="productSpecIds != null and productSpecIds.size() > 0">
                AND psku.ProductSpecification_Id in
                <foreach collection="productSpecIds" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by psku.id
    </select>

    <select id="findStoreReportAverageStockAge"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO">
        select round(SUM(DATEDIFF(now(), psb.createtime) * psb.totalcount_minunit) / ps.TotalCount_MinUnit)
        as averageStockAge,psb.productstore_id as productStoreId
        from sc_product.productstorebatch psb
        inner join sc_product.productstore ps
        on psb.productstore_id = ps.id
        where psb.totalcount_minunit != 0 and
        ps.TotalCount_MinUnit != 0
        and psb.productstore_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        group by psb.productstore_id
    </select>

    <select id="findStoreReportPageInfoByAuthNew"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO">
        SELECT
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSku_Id as productId,
        CONCAT(case psku.ProductState when 0 then '[下架]' when 1 then '[作废]' else '' end, psku.Name) as
        productSkuName,
        ps.Warehouse_Id as warhouseId,
        ps.OwnerType as storeOwnerType,
        ps.Owner_Id as ownerId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.Channel as channel,
        ps.SecOwner_Id as secOwnerId,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.OwnerType as ownerType,
        psku.ProductSpecification_Id as productSpecificationId,
        psku.OwnerName,
        psku.ProductState,
        pc.storageAttribute warehouseAllocationType
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        <if test="so.eraseOwnerId == null or so.eraseOwnerId == 0">
            AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
            AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        </if>
        inner join productskuconfig pc on psku.ProductSku_Id = pc.ProductSku_Id and pc.Warehouse_Id = ps.Warehouse_Id
        <if test="so.warehouseAllocationType != null and so.warehouseAllocationType != ''">
            and (pc.storageAttribute = #{so.warehouseAllocationType,jdbcType=INTEGER} or pc.StorageAttribute = 0 or pc.StorageAttribute is null)
        </if>

        where 1=1
        <if test="so.cityId!=null">
            AND psku.City_Id=#{so.cityId,jdbcType=INTEGER}
        </if>
        <if test="so.limitSku != null and so.limitSku == 1">
            and pc.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.inventoryPinProperty != null">
            AND pc.inventoryPinProperty like concat('%',#{so.inventoryPinProperty,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.source!=null">
            AND psku.Source=#{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.warehouseIds!=null">
            and ps.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
        </if>
        <if test="so.agencyId!=null">
            and ps.Owner_Id=#{so.agencyId,jdbcType=BIGINT}
        </if>
        <if test="so.storeOwnerType!=null">
            and ps.OwnerType=#{so.storeOwnerType,jdbcType=INTEGER}
        </if>
        <if test="so.deleted != null and so.deleted.size > 0">
            and psku.IsDelete in
            <foreach item="item" collection="so.deleted" separator="," open="(" close=")" index="">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="so.productSkuIds != null and so.productSkuIds.size() > 0">
            and psku.ProductSku_Id in
            <foreach item="item" collection="so.productSkuIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == 1">
            and ps.TotalCount_MinUnit <![CDATA[ > ]]>  0
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == 0">
            and ps.TotalCount_MinUnit <![CDATA[ <= ]]>  0
        </if>
        <if test="so.hasRealStoreType != null and so.hasRealStoreType == -1">
            and ps.TotalCount_MinUnit <![CDATA[ < ]]>  0
        </if>
        <if test="so.startStockAge != null or so.endStockAge != null">
            and exists
            (select round(SUM(DATEDIFF(now(), psb.createtime) * psb.totalcount_minunit) / ps.TotalCount_MinUnit) as
            stockAge from productstorebatch psb where psb.productstore_id = ps.Id
            group by psb.productstore_id having
            <if test="so.startStockAge != null">
                <![CDATA[ stockAge >= #{so.startStockAge,jdbcType=BIGINT} ]]>
            </if>
            <if test="so.startStockAge != null and so.endStockAge != null">
                and
            </if>
            <if test="so.endStockAge != null">
                <![CDATA[ stockAge <= #{so.endStockAge,jdbcType=BIGINT} ]]>
            </if>
            )
        </if>
    </select>


</mapper>
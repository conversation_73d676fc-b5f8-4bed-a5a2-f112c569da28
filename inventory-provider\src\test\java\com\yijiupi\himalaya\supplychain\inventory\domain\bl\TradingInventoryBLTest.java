package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * 交易库存UT Created by Lifeng on 2017/6/27.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class TradingInventoryBLTest {
    //
    // @Autowired
    // private TradingInventoryServiceImpl tradingInventoryServiceImpl;
    //
    // @Test
    // public void getWarehouseInventoryMapWithPre(){
    // Map<Long, Integer> productSkuIdAndWarehouseIdMap = new HashMap<>(16);
    // productSkuIdAndWarehouseIdMap.put(99900000014360L,9991);
    // Map<Long, ProductInventoryWithPreDTO> map =
    // tradingInventoryServiceImpl.getWarehouseInventoryMapWithPre(productSkuIdAndWarehouseIdMap);
    // }

}
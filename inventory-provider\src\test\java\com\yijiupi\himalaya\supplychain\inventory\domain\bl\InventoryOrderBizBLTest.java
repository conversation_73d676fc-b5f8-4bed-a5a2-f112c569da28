package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;

/**
 * <AUTHOR> 2017/11/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryOrderBizBLTest {

    @Autowired
    private InventoryOrderBizBL inventoryOrderBiz;
    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;

    /**
     * 批量配送
     */
    @Test
    public void batchDeliver() {
        ArrayList<InventoryDeliveryJiupiOrder> list = new ArrayList<>();
        InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = initDeliveryJiupiOrder();
        list.add(inventoryDeliveryJiupiOrder);
        InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder2 = initDeliveryJiupiOrder3();
        list.add(inventoryDeliveryJiupiOrder2);
        // inventoryOrderBiz.orderDeliveryNew(Collections.singletonList(initDeliveryJiupiOrder()), true);
        // Collections.singletonList(initDeliveryJiupiOrder())用这个会出问题. java.lang.UnsupportedOperationException
        // private static class SingletonList<E>
        inventoryOrderBiz.orderDeliveryNew(list, true, true);
    }

    /**
     * 配送失败.
     */
    @Test
    public void deliveryFail() {
        // InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = initDeliveryJiupiOrder();
        // InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = initDeliveryJiupiOrder2();

        Gson gson = new Gson();
        String json =
            "{\"cityId\":999,\"createUserId\":\"11002\",\"createUserName\":\"张裔譞\",\"deliveryState\":4,\"items\":[{\"buyCount\":20,\"deliverCount\":20,\"orderItem_Id\":169610302101406456,\"productSkuId\":99900000482969,\"saleSpecQuantity\":12,\"takeCount\":0}],\"jiupiOrderType\":3,\"orderId\":999018022710040852,\"orderNo\":\"9998058000121\",\"orderType\":0,\"warehouseId\":9991}";
        InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder =
            gson.fromJson(json, InventoryDeliveryJiupiOrder.class);
        ArrayList<InventoryDeliveryJiupiOrder> list = new ArrayList<>();
        list.add(inventoryDeliveryJiupiOrder);
        // inventoryOrderBizBL.orderDeliveryFailNew(list);
    }

    /**
     * 配送完成确认.
     */
    @Test
    public void deliveryComplete() {
        InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = initDeliveryJiupiOrder();
        // InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = initDeliveryJiupiOrder2();
        ArrayList<InventoryDeliveryJiupiOrder> list = new ArrayList<>();
        list.add(inventoryDeliveryJiupiOrder);
        // inventoryOrderBizBL.orderCompleteNew(list);
    }

    // /**
    // * 退货单
    // */
    // @Test
    // public void deliveryReturnOrderList() {
    // List<DeliveryReturnOrder> deliveryReturnOrderList = new ArrayList<>();
    // DeliveryReturnOrder deliveryReturnOrder = new DeliveryReturnOrder();
    // deliveryReturnOrder.setCityId(999);
    // deliveryReturnOrder.setWarehouseId(9991);
    // deliveryReturnOrder.setReturnOrderId(1100021321495L);
    // deliveryReturnOrder.setId(987152211L);
    // deliveryReturnOrder.setCreateUserId("125");
    // deliveryReturnOrder.setCreateUserName("liufei");
    // deliveryReturnOrder.setIsReject(false);
    //
    // ArrayList<DeliveryReturnOrderItem> list = new ArrayList<>();
    // DeliveryReturnOrderItem inventoryDeliveryJiupiOrderItem = new DeliveryReturnOrderItem();
    // inventoryDeliveryJiupiOrderItem.setSaleSpecQuantity(10);
    // inventoryDeliveryJiupiOrderItem.setReturnCount(5);
    // inventoryDeliveryJiupiOrderItem.setProductSkuId(99900050733641L);
    // list.add(inventoryDeliveryJiupiOrderItem);
    //
    // deliveryReturnOrder.setItems(list);
    // deliveryReturnOrderList.add(deliveryReturnOrder);
    //
    //// inventoryOrderBizBL.returnOrderCompleteNew(deliveryReturnOrderList);
    //
    // }

    private InventoryDeliveryJiupiOrder initDeliveryJiupiOrder() {
        InventoryDeliveryJiupiOrder d = new InventoryDeliveryJiupiOrder();
        d.setOrderId(127566453123323211L);
        d.setCityId(999);
        d.setOrderNo("6453312312312sdf331");
        d.setWarehouseId(9991);
        d.setId(1269856712345222211L);
        d.setCreateUserId("125");
        d.setCreateUserName("liufei");
        d.setJiupiOrderType(0);

        ArrayList<InventoryDeliveryJiupiOrderItem> list = new ArrayList<>();
        InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem1 = new InventoryDeliveryJiupiOrderItem();
        inventoryDeliveryJiupiOrderItem1.setDeliverCount(BigDecimal.valueOf(8));// 配送件数
        inventoryDeliveryJiupiOrderItem1.setBuyCount(BigDecimal.valueOf(3));// 买的件数
        inventoryDeliveryJiupiOrderItem1.setTakeCount(BigDecimal.valueOf(3));// 取货件数
        inventoryDeliveryJiupiOrderItem1.setSaleSpecQuantity(BigDecimal.valueOf(10));
        // inventoryDeliveryJiupiOrderItem1.setProductSkuId(99900050733641L);
        inventoryDeliveryJiupiOrderItem1.setProductSkuId(99900049216014L);
        // inventoryDeliveryJiupiOrderItem1.setProductSkuId(10000000010468L);

        InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem2 = new InventoryDeliveryJiupiOrderItem();
        inventoryDeliveryJiupiOrderItem2.setDeliverCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem2.setSaleSpecQuantity(BigDecimal.valueOf(10));
        inventoryDeliveryJiupiOrderItem2.setBuyCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem2.setTakeCount(BigDecimal.valueOf(10));
        inventoryDeliveryJiupiOrderItem2.setProductSkuId(999000507223891L);
        // inventoryDeliveryJiupiOrderItem2.setProductSkuId(10000000013870L);

        list.add(inventoryDeliveryJiupiOrderItem1);
        // list.add(inventoryDeliveryJiupiOrderItem2);
        d.setItems(list);
        return d;
    }

    private InventoryDeliveryJiupiOrder initDeliveryJiupiOrder3() {
        InventoryDeliveryJiupiOrder d = new InventoryDeliveryJiupiOrder();
        d.setCityId(999);
        d.setWarehouseId(9991);
        d.setOrderId(2346512322344365465L);
        d.setId(234652342341235465L);
        d.setOrderNo("6845674112341132434");
        d.setCreateUserId("125");
        d.setCreateUserName("liufei");
        d.setJiupiOrderType(0);

        ArrayList<InventoryDeliveryJiupiOrderItem> list = new ArrayList<>();
        InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem1 = new InventoryDeliveryJiupiOrderItem();
        inventoryDeliveryJiupiOrderItem1.setDeliverCount(BigDecimal.valueOf(8));// 配送件数
        inventoryDeliveryJiupiOrderItem1.setBuyCount(BigDecimal.valueOf(3));// 买的件数
        inventoryDeliveryJiupiOrderItem1.setTakeCount(BigDecimal.valueOf(3));// 取货件数
        inventoryDeliveryJiupiOrderItem1.setSaleSpecQuantity(BigDecimal.valueOf(10));
        // inventoryDeliveryJiupiOrderItem1.setProductSkuId(99900050733641L);
        inventoryDeliveryJiupiOrderItem1.setProductSkuId(99900049216014L);
        // inventoryDeliveryJiupiOrderItem1.setProductSkuId(10000000010468L);

        InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem2 = new InventoryDeliveryJiupiOrderItem();
        inventoryDeliveryJiupiOrderItem2.setDeliverCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem2.setSaleSpecQuantity(BigDecimal.valueOf(10));
        inventoryDeliveryJiupiOrderItem2.setBuyCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem2.setTakeCount(BigDecimal.valueOf(10));
        inventoryDeliveryJiupiOrderItem2.setProductSkuId(999000507223891L);
        // inventoryDeliveryJiupiOrderItem2.setProductSkuId(10000000013870L);

        list.add(inventoryDeliveryJiupiOrderItem1);
        // list.add(inventoryDeliveryJiupiOrderItem2);
        d.setItems(list);
        return d;
    }

    private InventoryDeliveryJiupiOrder initDeliveryJiupiOrder2() {
        InventoryDeliveryJiupiOrder d = new InventoryDeliveryJiupiOrder();
        d.setOrderId(10000000201318L);
        d.setCityId(100);
        d.setOrderNo("134141525");
        d.setWarehouseId(9991);
        d.setId(987152211L);
        d.setCreateUserId("125");
        d.setCreateUserName("liufei");
        d.setJiupiOrderType(0);// 3是大宗

        ArrayList<InventoryDeliveryJiupiOrderItem> list = new ArrayList<>();
        InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem1 = new InventoryDeliveryJiupiOrderItem();
        inventoryDeliveryJiupiOrderItem1.setDeliverCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem1.setSaleSpecQuantity(BigDecimal.valueOf(10));
        inventoryDeliveryJiupiOrderItem1.setBuyCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem1.setTakeCount(BigDecimal.valueOf(8));
        inventoryDeliveryJiupiOrderItem1.setProductSkuId(99900050599817L);
        list.add(inventoryDeliveryJiupiOrderItem1);

        InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem2 = new InventoryDeliveryJiupiOrderItem();
        inventoryDeliveryJiupiOrderItem2.setDeliverCount(BigDecimal.valueOf(6));
        inventoryDeliveryJiupiOrderItem2.setSaleSpecQuantity(BigDecimal.valueOf(10));
        inventoryDeliveryJiupiOrderItem2.setBuyCount(BigDecimal.valueOf(6));
        inventoryDeliveryJiupiOrderItem2.setTakeCount(BigDecimal.valueOf(6));
        inventoryDeliveryJiupiOrderItem2.setProductSkuId(99900050599817L);
        list.add(inventoryDeliveryJiupiOrderItem2);

        d.setItems(list);
        return d;
    }

    /**
     * 延迟配送
     */
    @Test
    public void orderDeliveryDelayNew() {
        Gson gson = new Gson();
        String s3 =
            "[{\"cityId\":999,\"id\":89811802271402783,\"inventoryOptKey\":\"99911802271502832\",\"items\":[{\"buyCount\":2,\"deliverCount\":2,\"productSkuId\":89800000427693,\"saleSpecQuantity\":6,\"takeCount\":0}],\"jiupiOrderType\":1,\"orderId\":898118022714041145,\"orderNo\":\"************\",\"orderType\":0,\"warehouseId\":9991},{\"cityId\":898,\"id\":89811802271502857,\"inventoryOptKey\":\"89811802271502857\",\"items\":[{\"buyCount\":5,\"deliverCount\":5,\"productSkuId\":89800000427693,\"saleSpecQuantity\":6,\"takeCount\":0}],\"jiupiOrderType\":1,\"orderId\":898118022715041192,\"orderNo\":\"************\",\"orderType\":0,\"warehouseId\":9991}]";
        List<InventoryDeliveryJiupiOrder> list =
            gson.fromJson(s3, new TypeToken<List<InventoryDeliveryJiupiOrder>>() {}.getType());
        // inventoryOrderBizBL.orderDeliveryDelayNew(list);
    }
}

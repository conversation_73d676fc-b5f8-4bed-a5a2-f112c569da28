package com.yijiupi.himalaya.supplychain.inventory.controller.convertor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/19 10:04
 * @Version 1.0
 */
public class ProductOwnerDTOConvertor {

	public static List<ProductOwnerInfoDTO> convertByInventoryReport(List<InventoryReportDTO> inventoryReportDTOList) {
		if (CollectionUtils.isEmpty(inventoryReportDTOList)) {
			return Collections.emptyList();
		}
		return inventoryReportDTOList.stream().map(m -> {
			ProductOwnerInfoDTO productOwnerDTO = new ProductOwnerInfoDTO();
			productOwnerDTO.setOwnerId(m.getOwnerId());
			productOwnerDTO.setProductSpecId(m.getProductSpecificationId());
			return productOwnerDTO;
		}).collect(Collectors.toList());

	}


}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * 分仓残次品金额配置
 * 
 */
public class CCPConfigByAllocationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分仓类型，1、酒饮；2、休食
     */
    private String key;

    /**
     * 分仓残次品配置的限额
     */
    private BigDecimal value;

    /**
     * 包含的事业部
     * （酒类
     * 粮调副食
     * 日化百货
     * 乳啤
     * 休食
     * 饮料）
     */
    private List<String> dept;

    public List<String> getDept() {
        return dept;
    }

    public void setDept(List<String> dept) {
        this.dept = dept;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}

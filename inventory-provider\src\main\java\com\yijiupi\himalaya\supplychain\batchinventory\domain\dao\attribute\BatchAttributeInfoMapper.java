package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeInfoPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface BatchAttributeInfoMapper {
    /**
     * 新增
     *
     * @param batchAttributeInfoPO
     * @return
     */
    int insert(@Param("batchAttributeInfoPO") BatchAttributeInfoPO batchAttributeInfoPO);

    /**
     * 批量新增
     *
     * @param batchAttributeInfoPOs
     * @return
     */
    int insertList(@Param("batchAttributeInfoPOs") List<BatchAttributeInfoPO> batchAttributeInfoPOs);

    int update(@Param("batchAttributeInfoPO") BatchAttributeInfoPO batchAttributeInfoPO);

    /**
     * 根据批属性编号查找详情
     *
     * @param batchAttributeInfoNo
     * @return
     */
    List<BatchAttributeInfoDTO> findAttributeInfoByNo(@Param("batchAttributeInfoNo") String batchAttributeInfoNo);

    /**
     * 通过BatchAttributeinfoNo查询info
     *
     * @param batchAttributeInfoNo
     * @return
     */
    List<BatchAttributeInfoPO> findByBatchAttributeInfoNo(@Param("batchAttributeInfoNo") String batchAttributeInfoNo);

    /**
     * 通过批次编号集合查询info
     * 
     * @param batchNOList
     * @return
     */
    List<BatchAttributeInfoPO> findByBatchAttributeInfoNoList(@Param("batchAttrNoList") Collection<String> batchNOList);

}

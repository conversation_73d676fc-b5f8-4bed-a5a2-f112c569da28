package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.core.RabbitMessagingTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * Created by wang<PERSON> on 2017-10-13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class, properties = {"dubbo.port=40025"})
public class RabbitMQTest {
    @Autowired
    private RabbitMessagingTemplate rabbitMessagingTemplate;
    @Value("${inventory.order-redis-deducted-async}")
    private String orderInventoryDeductAsync;
    // @Autowired
    // private OrderInventoryDeductBL orderInventoryDeductBL;

    @Test
    public void sendMQ() {
        // List<RedisInventoryDeduct> deductItems = new ArrayList<>();
        // RedisInventoryDeduct redisInventoryDeduct = new RedisInventoryDeduct();
        // redisInventoryDeduct.setCount(10);
        // redisInventoryDeduct.setName("111");
        // redisInventoryDeduct.setOrderId(111L);
        // redisInventoryDeduct.setCityId(999);
        // redisInventoryDeduct.setWarehouseId(9991);
        // deductItems.add(redisInventoryDeduct);
        // orderInventoryDeductBL.deductDbInventoryAsync(deductItems);
    }
}

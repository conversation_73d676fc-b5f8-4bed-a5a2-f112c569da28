package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.biz.LightAllianceInventoryBL;

/**
 * Created by wang<PERSON> on 2017-09-20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class LightInventoryTest {

    @Autowired
    private LightAllianceInventoryBL lightAllianceInventoryBL;

    @Test
    public void lightInventoryTest() {

    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.PromotionStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.TrdPromotionSyncMessage;

/**
 * 交易促销活动同步.
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Component
public class PromotionStoreBatchListener {
    private static final Logger LOG = LoggerFactory.getLogger(PromotionStoreBatchListener.class);

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    @RabbitListener(queues = "${mq.supplychain.batchinventory.synTrdPromotion}")
    public void synTrdPromotion(Message message) {
        try {
            String json = new String(message.getBody(), "UTF-8");
            LOG.info("交易促销活动同步>>>【mq.supplychain.batchinventory.synTrdPromotion】" + json);
            // 处理sku
            TrdPromotionSyncMessage syncMessage = JSON.parseObject(json, TrdPromotionSyncMessage.class);
            promotionStoreBatchBL.synTrdPromotion(syncMessage);
        } catch (Exception e) {
            LOG.error("交易促销活动同步失败", e);
        }
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl.easysell;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductSkuPOMapper;

/**
 * 根据条码，经销商id,仓库查询商品信息
 * 
 * @author: lidengfeng
 * @date 2018/8/30 19:07
 */
@Service
public class EasySellProductDetailsBL {

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;

    /**
     * 规格Id集合,经销商id,仓库查询商品信息参数
     * 
     * @param productDetailsQueryDTO
     * @return
     */
    public PageList<ProductDetailsDTO> findProductDetailsList(ProductDetailsQueryDTO productDetailsQueryDTO) {
        PageList<ProductDetailsDTO> pageList = new PageList<ProductDetailsDTO>();
        PageResult<ProductDetailsDTO> poList = productSkuPOMapper.findProductDetailsList(productDetailsQueryDTO);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;
    }
}

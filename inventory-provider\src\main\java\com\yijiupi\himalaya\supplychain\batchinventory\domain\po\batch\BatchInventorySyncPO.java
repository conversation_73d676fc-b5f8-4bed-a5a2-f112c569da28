package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 货位库存同步PO
 *
 * <AUTHOR>
 * @date 2019/2/23 18:19
 */
public class BatchInventorySyncPO implements Serializable {

    private static final long serialVersionUID = -3502281087433356441L;

    /**
     * 产品库存ID
     */
    private String productStoreId;

    /**
     * 商品sku
     */
    private Long productSkuId;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 货位名称
     */
    private String locationName;

    /**
     * 货位/货区
     */
    private Integer locationCategory;

    /**
     * 货位类别
     */
    private Integer subcategory;

    /**
     * 库存数量
     */
    private BigDecimal totalCount;

    private String productStoreBatchId;

    /**
     * 库存与货位库存差异数量
     */
    private BigDecimal diffCount;
    /**
     * 货主 id
     */
    private Long ownerId;
    /**
     * 二级货主 id
     */
    private Long secOwnerId;
    /**
     * 仓库 id
     */
    private Integer warehouseId;
    /**
     * 
     */
    private Long productSpecificationId;

    public BigDecimal getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(BigDecimal diffCount) {
        this.diffCount = diffCount;
    }

    public String getProductStoreBatchId() {
        return productStoreBatchId;
    }

    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getLocationCategory() {
        return locationCategory;
    }

    public void setLocationCategory(Integer locationCategory) {
        this.locationCategory = locationCategory;
    }

    public Integer getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(Integer subcategory) {
        this.subcategory = subcategory;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 货主 id
     *
     * @return ownerId 货主 id
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 货主 id
     *
     * @param ownerId 货主 id
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 二级货主 id
     *
     * @return secOwnerId 二级货主 id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主 id
     *
     * @param secOwnerId 二级货主 id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 仓库 id
     *
     * @return warehouseId 仓库 id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库 id
     *
     * @param warehouseId 仓库 id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取
     *
     * @return productSpecificationId
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置
     *
     * @param productSpecificationId
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }
}

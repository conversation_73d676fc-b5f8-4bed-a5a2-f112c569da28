package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;

public class ChangeInventoryConvert {
    public static List<InventoryDeliveryJiupiOrder> convert(List<OrderDTO> orderDTOList) {
        List<InventoryDeliveryJiupiOrder> deliveryJiupiOrderS = new ArrayList<>();
        for (OrderDTO orderDTO : orderDTOList) {
            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
            inventoryDeliveryJiupiOrder.setOrderId(orderDTO.getId());
            inventoryDeliveryJiupiOrder.setOrderNo(orderDTO.getRefOrderNo());
            inventoryDeliveryJiupiOrder.setOmsOrderId(
                orderDTO.getBusinessId() == null ? orderDTO.getId() : Long.valueOf(orderDTO.getBusinessId()));
            inventoryDeliveryJiupiOrder.setRelationOrderId(orderDTO.getRelationOrderId());
            inventoryDeliveryJiupiOrder.setCityId(orderDTO.getOrgId());
            inventoryDeliveryJiupiOrder.setFromCityId(orderDTO.getFromCityId());
            inventoryDeliveryJiupiOrder.setWarehouseId(orderDTO.getWarehouseId());
            inventoryDeliveryJiupiOrder.setItems(convertItem(orderDTO.getItems()));

            int orderType = orderDTO.getOrderType() == null ? 0 : orderDTO.getOrderType();
            // jiuPiEventType转换
            inventoryDeliveryJiupiOrder.setJiupiEventType(JiupiEventType.仓管确认入库.getType());

            inventoryDeliveryJiupiOrder.setJiupiOrderType(orderType);
            inventoryDeliveryJiupiOrder.setOrderType(orderType);
            // 记录订单能力类型
            inventoryDeliveryJiupiOrder.setCapabilityType(orderDTO.getCapabilityType());

            deliveryJiupiOrderS.add(inventoryDeliveryJiupiOrder);
        }
        return deliveryJiupiOrderS;
    }

    private static List<InventoryDeliveryJiupiOrderItem> convertItem(List<OrderItemDTO> orderItemDTOList) {
        List<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            if (CollectionUtils.isNotEmpty(orderItemDTO.getItemDetailList())) {
                orderItemDTO.getItemDetailList().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem =
                        new InventoryDeliveryJiupiOrderItem();
                    inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItemDTO.getSkuId());
                    inventoryDeliveryJiupiOrderItem.setTakeCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItemDTO.getId());
                    inventoryDeliveryJiupiOrderItem.setOmsOrderItemId(orderItemDTO.getBusinessItemId() == null
                        ? orderItemDTO.getId() : Long.valueOf(orderItemDTO.getBusinessItemId()));
                    inventoryDeliveryJiupiOrderItem.setOrderItemDetailId(detail.getId());
                    inventoryDeliveryJiupiOrderItem.setRelationOrderItemId(orderItemDTO.getRelationOrderItemId());
                    inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(detail.getProductSpecificationId());
                    inventoryDeliveryJiupiOrderItem.setOwnerId(detail.getOwnerId());
                    inventoryDeliveryJiupiOrderItem.setSecOwnerId(detail.getSecOwnerId());
                    inventoryDeliveryJiupiOrderItem.setLocationId(detail.getLocationId());
                    inventoryDeliveryJiupiOrderItem.setLocationName(detail.getLocationName());
                    if (null != detail.getProductionDate()) {
                        inventoryDeliveryJiupiOrderItem.setProductionDate(detail.getProductionDate());
                    }
                    // 记录是否残次品
                    if (detail.getDefective() != null && detail.getDefective()) {
                        inventoryDeliveryJiupiOrderItem.setDefective(detail.getDefective());
                    }
                    inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
                });
            } else {
                InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
                inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItemDTO.getSkuId());
                inventoryDeliveryJiupiOrderItem.setTakeCount(orderItemDTO.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItemDTO.getId());
                inventoryDeliveryJiupiOrderItem.setOmsOrderItemId(orderItemDTO.getBusinessItemId() == null
                    ? orderItemDTO.getId() : Long.valueOf(orderItemDTO.getBusinessItemId()));
                inventoryDeliveryJiupiOrderItem.setRelationOrderItemId(orderItemDTO.getRelationOrderItemId());
                inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(orderItemDTO.getProductSpecificationId());
                inventoryDeliveryJiupiOrderItem.setOwnerId(orderItemDTO.getOwnerId());
                inventoryDeliveryJiupiOrderItem.setSecOwnerId(orderItemDTO.getSecOwnerId());
                inventoryDeliveryJiupiOrderItem.setLocationId(orderItemDTO.getLocationId());
                inventoryDeliveryJiupiOrderItem.setLocationName(orderItemDTO.getLocationName());
                if (null != orderItemDTO.getProductionDate()) {
                    inventoryDeliveryJiupiOrderItem.setProductionDate(orderItemDTO.getProductionDate());
                }
                inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
            }
        }
        return inventoryDeliveryJiupiOrderItems;
    }
}

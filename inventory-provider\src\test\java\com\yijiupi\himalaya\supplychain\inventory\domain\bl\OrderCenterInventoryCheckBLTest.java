package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.check.OrderCenterInventoryCheckBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterUnConfirmOrderInventoryResultDTO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class OrderCenterInventoryCheckBLTest {

    @Autowired
    private OrderCenterInventoryCheckBL orderCenterInventoryCheckBL;

    @Test
    public void test() {

        String json = "{\"code\":200, \"data\":[{\"orderType\":20,\"count\":-21.000000,\"warehouseId\":7141,\"type\":1,\"ownerId\":null,\"productSpecificationId\":560294,\"secOwnerId\":null},{\"orderType\":0,\"count\":70.000000,\"warehouseId\":7141,\"type\":2,\"ownerId\":null,\"productSpecificationId\":560262,\"secOwnerId\":1},{\"orderType\":0,\"count\":66.000000,\"warehouseId\":7141,\"type\":2,\"ownerId\":null,\"productSpecificationId\":560062,\"secOwnerId\":1},{\"orderType\":0,\"count\":56.000000,\"warehouseId\":7141,\"type\":2,\"ownerId\":null,\"productSpecificationId\":560283,\"secOwnerId\":1},{\"orderType\":0,\"count\":28.000000,\"warehouseId\":7141,\"type\":2,\"ownerId\":null,\"productSpecificationId\":560261,\"secOwnerId\":1}]}";
        OrderCenterInventoryResultDTO<List<OrderCenterUnConfirmOrderInventoryResultDTO>> resultDTO = JSON.parseObject(json, new TypeReference<OrderCenterInventoryResultDTO<List<OrderCenterUnConfirmOrderInventoryResultDTO>>>() {});


        Assertions.assertThat(resultDTO).isNotNull();
    }


}

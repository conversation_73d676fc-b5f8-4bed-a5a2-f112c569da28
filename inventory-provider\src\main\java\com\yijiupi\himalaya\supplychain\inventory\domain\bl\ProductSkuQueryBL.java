/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.DataValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoSO;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ActualSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.SpecificationInfoPO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuService;
import com.yijiupi.himalaya.supplychain.wmsdubbop.constant.ProductSaleMode;

/**
 * 产品信息查询BL
 */
@Service
public class ProductSkuQueryBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSkuQueryBL.class);

    @Reference
    private IProductSkuService iProductSkuService;

    @Autowired
    private InventoryProductSkuMapper inventoryProductSkuMapper;

    @Reference
    private IInStockQueryService iInStockQueryService;

    @Autowired
    private WarehouseCityBL warehouseCityBL;

    // /**
    // * 根据产品skuId获取产品信息规格（含其箱码、所属产品信息和包装规格信息）
    // *
    // * @param skuIdSet 产品skuId集合
    // */
    // public Map<Long, ProductInfoSpecificationDTO> getProductInfoSpecificationMap(Set<Long> skuIdSet) {
    // DataResult<Map<Long, ProductInfoSpecificationDTO>> dataResult = HttpUtil.httpGet(ConfigUtil.tradingAPIUrl +
    // "product/productInfoSpecitication/batch?skuIdSet=" + setParam(skuIdSet),
    // new TypeToken<DataResult<Map<Long, ProductInfoSpecificationDTO>>>() {
    // }.getType());
    // if (dataResult == null) {
    // throw new BusinessException("远程调用失败");
    // }
    // if (!dataResult.getSuccess()) {
    // throw new BusinessException(dataResult.getError());
    // }
    // return dataResult.getData();
    // }
    //
    // private String setParam(Set<Long> skuIdSet) {
    // if (CollectionUtils.isEmpty(skuIdSet)) {
    // return "";
    // }
    // StringBuilder s = new StringBuilder();
    // skuIdSet.forEach(n -> {
    // s.append(n + ",");
    // });
    // return s.substring(0, s.length() - 1);
    // }

    /**
     * 根据skuId查询productSku表
     */
    public ProductSkuPO getProductSkuBySkuId(Long productSkuId) {
        return inventoryProductSkuMapper.getProductSkuBySkuId(productSkuId);
    }

    // /**
    // * 根据产品skuId列表获取产品sku信息列表.
    // *
    // * @param skuIdList 多个产品SkuId
    // * @return 产品sku信息列表.
    // */
    // public Map<Long, ProductSku> getProductSkuListMap(List<Long> skuIdList) {
    // return iProductSkuQueryService.getProductSkuListMap(skuIdList);
    // }

    /**
     * 根据skuId查询productSku表(批量)
     */
    public List<ProductSkuPO> getProductSkuListByIds(List<Long> productSkuIdList) {
        return inventoryProductSkuMapper.getProductSkuListByIds(productSkuIdList);
    }

    // /**
    // * 招商订单，根据SKUId，查找发货城市SKUID
    // *
    // * @param productSkuId 招商城市skuId
    // * @param cityId 发货城市id
    // * @return
    // */
    // public Long getAnotherCityProductSkuId(Long productSkuId, Integer cityId) {
    // return productSkuMapper.getAnotherCityProductSkuId(productSkuId, cityId, ProductSourceType.易酒批);
    // }

    private static Integer invalidProductState = 1;

    /**
     * 查询实际发货skuId
     */
    public List<ActualSkuPO> getActualDeliverySkuIdS(List<Long> productSkuIds, Integer cityId) {
        List<ActualSkuPO> lstTmp = inventoryProductSkuMapper.getActualDeliverySkuIdS(productSkuIds, cityId);
        productSkuIds.forEach(p -> {
            // 如果同时存在作废与没作废的产品，移除作废产品
            boolean isHasInvalidSku = lstTmp.stream().anyMatch(
                q -> Objects.equals(p, q.getOrderSkuId()) && Objects.equals(q.getProductState(), invalidProductState))
                && lstTmp.stream().anyMatch(q -> Objects.equals(p, q.getOrderSkuId())
                    && !Objects.equals(q.getProductState(), invalidProductState));
            if (isHasInvalidSku) {
                LOGGER.info(String.format("%s产品存在已作废的SKU", p));
                lstTmp.removeIf(q -> Objects.equals(p, q.getOrderSkuId())
                    && Objects.equals(q.getProductState(), invalidProductState));
            }
        });
        return lstTmp;
    }

    /**
     * 查询实际发货skuId
     */
    public Map<Long, Long> getActualDeliverySkuIdS(List<Long> productSkuIds, Integer warehouseId,
        Integer deliveryCityId) {
        if (deliveryCityId == null) {
            if (warehouseId == null) {
                throw new DataValidateException("仓库Id不能为空！");
            }
            deliveryCityId = warehouseCityBL.getCityIdByWarehouseId(warehouseId);
        }
        productSkuIds.removeIf(Objects::isNull);
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.EMPTY_MAP;
        }
        List<ActualSkuPO> actualSkuList = getActualDeliverySkuIdS(productSkuIds, deliveryCityId);
        if (CollectionUtils.isEmpty(actualSkuList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, Long> result = new HashMap<>(16);
        actualSkuList.forEach(p -> {
            if (!result.containsKey(p.getOrderSkuId())) {
                result.put(p.getOrderSkuId(), p.getActualDeliverySkuId());
            }
        });
        return result;
    }

    /**
     * 查询实际发货skuId
     */
    public List<ActualSkuPO> getNormalProductStateSkuIdS(List<Long> productSkuIds) {
        return inventoryProductSkuMapper.getNormalProductStateSkuIdS(productSkuIds);
    }

    // /**
    // * 批量查询 需要加上城市过滤,不同 城市下的ProductSpecification_Id有可能一样
    // */
    // List<Map<Long, BigDecimal>> getInventoryProductSkuMap(Set<Long> productSkuIds, Integer channel, Long secOwnerId)
    // {
    // return productSkuMapper.getInventoryProductSkuMap(productSkuIds, channel, secOwnerId);
    // }
    //
    // /**
    // * 根据产品信息规格查询转换系数、包装规格
    // */
    // public SpecificationInfoPO getSpecificationInfo(Integer prodcutInfoSpecId) {
    // return productSkuMapper.getSpecificationInfo(prodcutInfoSpecId);
    // }

    /**
     * 根据storeId查询转换系数、包装规格
     *
     * @param productStoreID
     * @return
     */
    public SpecificationInfoPO getSpecificationInfoByStoreId(String productStoreID) {
        return inventoryProductSkuMapper.getSpecificationInfoByStoreId(productStoreID);
    }

    /**
     * 将发货城市skuId转换成招商skuId
     *
     * @param productSkuId 普通城市skuId
     * @param cityId 招商城市id
     * @return
     */
    public Long process2ZhaoShangSku(Long productSkuId, Integer cityId) {
        return inventoryProductSkuMapper.getAnotherCityProductSkuId(productSkuId, cityId);
    }

    /**
     * 产品查询
     */
    public PageList<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO) {
        AssertUtils.notNull(productSkuInfoSO.getWarehouseId(), "仓库id不能为空");
        // SCM2-8596 店仓多货主调整: 店仓不抹货主
        // Boolean eraseOwnerConfig =
        // iInStockQueryService.findOwnerInfoEraseWarehouseConfig((productSkuInfoSO.getWarehouseId()));
        // productSkuInfoSO.setEraseOwnerId(eraseOwnerConfig ? 1 : 0);
        PageResult<ProductSkuInfoDTO> pageResult = inventoryProductSkuMapper.listProductSkuInfo(productSkuInfoSO);
        PageList<ProductSkuInfoDTO> pageList = pageResult.toPageList();
        if (!CollectionUtils.isEmpty(pageList.getDataList())) {
            pageList.getDataList().forEach(dto -> {
                // 获取销售模式名称
                if (null != dto.getSaleModel()) {
                    dto.setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(dto.getSaleModel())));
                }
            });
        }
        return pageList;
    }

    /**
     * 查询产品SKU基本信息
     */
    public PageList<ProductSkuInfoDTO> findProductBaseInfo(ProductSkuInfoSO productSkuInfoSO) {
        AssertUtils.notNull(productSkuInfoSO.getWarehouseId(), "仓库id不能为空");
        PageResult<ProductSkuInfoDTO> pageResult = inventoryProductSkuMapper.findProductBaseInfo(productSkuInfoSO);
        PageList<ProductSkuInfoDTO> pageList = pageResult.toPageList();
        if (!CollectionUtils.isEmpty(pageList.getDataList())) {
            pageList.getDataList().forEach(dto -> {
                // 获取销售模式名称
                if (null != dto.getSaleModel()) {
                    dto.setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(dto.getSaleModel())));
                }
            });
        }
        return pageList;
    }

    /**
     * 查询产品SKU基本信息
     */
    public PageList<ProductSkuInfoDTO> findProductBaseInfoByStoreCheck(ProductSkuInfoSO productSkuInfoSO) {
        AssertUtils.notNull(productSkuInfoSO.getWarehouseId(), "仓库id不能为空");
        PageResult<ProductSkuInfoDTO> pageResult = inventoryProductSkuMapper.findProductBaseInfoByStoreCheck(productSkuInfoSO);
        PageList<ProductSkuInfoDTO> pageList = pageResult.toPageList();
        if (!CollectionUtils.isEmpty(pageList.getDataList())) {
            pageList.getDataList().forEach(dto -> {
                // 获取销售模式名称
                if (null != dto.getSaleModel()) {
                    dto.setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(dto.getSaleModel())));
                }
            });
        }
        return pageList;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-10-27 17:41
 **/
public class OrderCenterOrderItemOwner {

    private Long ownerId;

    private Long secOwnerId;

    private Integer warehouseId;

    private BigDecimal count;

    private BigDecimal costPrice;

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }
}

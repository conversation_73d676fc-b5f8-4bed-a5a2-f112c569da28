package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.product;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.ApplyProductStorageAgeConvertor;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ApplyProductStorageAgeDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockApplyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyStateEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.StorageAgeOutStockApplyOrderQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockApplyManageService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockApplyQueryService;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/16
 */
@Service
public class ProductSkuStorageApplyBL {

    @Reference
    private IProductSkuConfigService iProductSkuConfigService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IOutStockApplyManageService iOutStockApplyManageService;
    @Reference
    private IOutStockApplyQueryService iOutStockApplyQueryService;
    @Reference
    private ILocationService iLocationService;

    @Autowired
    private ApplyProductStorageAgeConvertor convertor;
    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;
    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;

    /**
     * 申请延长库龄
     * 
     * @param dto
     */
    public void applyProductStorageAge(ApplyProductStorageAgeDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空");
        AssertUtils.notNull(dto.getSkuId(), "sku信息不能为空");
        AssertUtils.notNull(dto.getCurrentStorageAge(), "当前库龄信息不能为空");
        AssertUtils.notNull(dto.getApplyStorageAge(), "申请库龄信息不能为空");
        AssertUtils.notNull(dto.getStoreBatchId(), "批次库存信息不能为空");
        AssertUtils.notNull(dto.getReason(), "原因不能为空");
        AssertUtils.notNull(dto.getOptUserId(), "操作人信息不能为空");

        ProductSkuDTO productSkuDTO = iProductSkuQueryService.selectBySkuId(dto.getSkuId());

        List<ProductSkuConfigDTO> skuConfigDTOList = iProductSkuConfigService
            .findSkuConfigBySkuIdsAndWarehouseId(Collections.singletonList(dto.getSkuId()), dto.getWarehouseId());
        ProductSkuConfigDTO productSkuConfigDTO =
            skuConfigDTOList.stream().filter(m -> m.getProductSkuId().equals(dto.getSkuId())).findFirst().get();

        // 查询该产品是否存在未审核的申请单
        StorageAgeOutStockApplyOrderQueryDTO queryDTO = new StorageAgeOutStockApplyOrderQueryDTO();
        queryDTO.setWarehouseId(dto.getWarehouseId());
        queryDTO.setStateList(Collections.singletonList(OutStockApplyStateEnum.待审核.getType()));
        queryDTO.setOrderType(OutStockApplyTypeEnum.库龄审核.getType());
        queryDTO.setProductSkuId(dto.getSkuId());
        List<OutStockApplyDTO> outStockApplyDTOList =
            iOutStockApplyQueryService.findStorageAgeOutStockApplyOrder(queryDTO);

        if (!CollectionUtils.isEmpty(outStockApplyDTOList)) {
            String orderNo = outStockApplyDTOList.stream().findAny().get().getOrderNo();
            throw new BusinessValidateException("存在未审核的申请单" + orderNo + "，请勿重复申请！");
        }

        ProductStoreBatchPO productStoreBatchPO =
            batchInventoryProductStoreBatchMapper.getProductStoreBatchById(dto.getStoreBatchId());

        validateIsDefectiveGoods(productStoreBatchPO);

        // 转换申请单
        OutStockApplyDTO outStockApplyDTO =
            convertor.convertOutStockApplyDTO(dto, productSkuDTO, productSkuConfigDTO, productStoreBatchPO);

        iOutStockApplyManageService.createOutStockApplyByOA(outStockApplyDTO);
    }

    private void validateIsDefectiveGoods(ProductStoreBatchPO productStoreBatchPO) {
        if (Objects.isNull(productStoreBatchPO)) {
            throw new BusinessValidateException("批次库存信息不存在！");
        }
        Long locationId = productStoreBatchPO.getLocationId();
        List<LoactionDTO> locationDTOList = iLocationService.findLocationByIds(Collections.singletonList(locationId));
        if (CollectionUtils.isEmpty(locationDTOList)) {
            throw new BusinessValidateException("批次库存对应货位 " + productStoreBatchPO.getLocationName() + "不存在！");
        }

        LoactionDTO locationDTO = locationDTOList.get(0);

        if (Objects.equals(LocationEnum.残次品位.getType().byteValue(), locationDTO.getSubcategory())
            || Objects.equals(LocationAreaEnum.残次品区.getType().byteValue(), locationDTO.getSubcategory())) {
            return;
        }
        throw new BusinessValidateException("货位" + productStoreBatchPO.getLocationName() + "不是残次品位，也不是残次品区。不能申请！");
    }

}

local deduct = tonumber(ARGV[3]);
local sell = redis.call('hget', KEYS[1], ARGV[1]);
local gift = redis.call('hget', KEYS[1], ARGV[2]);
local sellcount = (sell and tonumber(sell)) or tonumber('0');
local giftcount = (gift and tonumber(gift)) or tonumber('0');
local count = tonumber(math.min(deduct,sellcount,giftcount))

if((sellcount > 0) and (giftcount > 0)) then
    redis.call('hincrby', KEYS[1], ARGV[1], -count);
    redis.call('hincrby', KEYS[1], ARGV[2], -count);
    return count
else
    return 0
end


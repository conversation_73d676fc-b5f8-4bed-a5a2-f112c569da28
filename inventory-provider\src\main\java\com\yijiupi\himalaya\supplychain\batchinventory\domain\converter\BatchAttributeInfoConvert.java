package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeInfoPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeInfoDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeInfoConvert {

    public static BatchAttributeInfoPO BatchAttributeInfoDTO2PO(BatchAttributeInfoDTO batchAttributeInfoDTO) {
        if (batchAttributeInfoDTO == null) {
            return null;
        }
        BatchAttributeInfoPO batchAttributeInfoPO = new BatchAttributeInfoPO();
        batchAttributeInfoPO.setId(batchAttributeInfoDTO.getId());
        batchAttributeInfoPO.setBatchAttributeInfoNo(batchAttributeInfoDTO.getBatchAttributeInfoNo());
        batchAttributeInfoPO.setAttributeId(batchAttributeInfoDTO.getAttributeId());
        batchAttributeInfoPO.setAttributeName(batchAttributeInfoDTO.getAttributeName());
        batchAttributeInfoPO.setAttributeValueId(batchAttributeInfoDTO.getAttributeValueId());
        batchAttributeInfoPO.setAttributeValueName(batchAttributeInfoDTO.getAttributeValueName());
        batchAttributeInfoPO.setRemark(batchAttributeInfoDTO.getRemark());
        batchAttributeInfoPO.setCreateUser(batchAttributeInfoDTO.getCreateUser());
        return batchAttributeInfoPO;
    }

    public static List<BatchAttributeInfoPO>
        BatchAttributeInfoDTOS2POS(List<BatchAttributeInfoDTO> batchAttributeInfoDTOS) {
        ArrayList<BatchAttributeInfoPO> batchAttributeInfoPOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(batchAttributeInfoDTOS)) {
            return batchAttributeInfoPOS;
        }
        for (BatchAttributeInfoDTO batchAttributeInfoDTO : batchAttributeInfoDTOS) {
            batchAttributeInfoPOS.add(BatchAttributeInfoDTO2PO(batchAttributeInfoDTO));
        }
        return batchAttributeInfoPOS;
    }
}

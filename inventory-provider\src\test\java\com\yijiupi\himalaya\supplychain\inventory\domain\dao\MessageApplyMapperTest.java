package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.UUID;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * 消息消费记录Mapper - UT
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class MessageApplyMapperTest {

    @Autowired
    private MessageApplyMapper messageApplyMapper;

    @Test
    public void countMessageApply() throws Exception {
        String messageId = UUID.randomUUID().toString();
        messageApplyMapper.insertMessageApply(messageId);

        int i = messageApplyMapper.countMessageApply(messageId);
        Assert.assertTrue(i == 1);
    }

}
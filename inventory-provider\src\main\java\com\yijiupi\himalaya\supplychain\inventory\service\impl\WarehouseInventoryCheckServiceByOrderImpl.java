package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.BatchProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryCheckByOrderBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.BatchInventoryQueryRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryCheckByOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckByOrderService;

/**
 * 根据订单矫正仓库库存
 *
 * <AUTHOR>
 * @date 11/28/20 3:06 PM
 */
@Service(timeout = 300000)
public class WarehouseInventoryCheckServiceByOrderImpl implements IWarehouseInventoryCheckByOrderService {

    @Autowired
    private WarehouseInventoryCheckByOrderBL warehouseInventoryCheckByOrderBL;

    /**
     * 根据订单矫正仓库库存
     */
    @Override
    public void checkInventoryByOrder(List<InventoryCheckByOrderDTO> orderDTOList) {
        warehouseInventoryCheckByOrderBL.checkInventoryByOrder(orderDTOList);
    }

    /**
     * 根据单号还原仓库库存
     */
    @Override
    public void restoreInventoryByOrderNo(List<String> orderNos, Integer warehouseId) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        warehouseInventoryCheckByOrderBL.restoreInventoryByOrderNo(orderNos, warehouseId);
    }

    @Override
    public List<String> findStoreChangeOrderNos(List<String> orderNos, String excludeUser) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        return warehouseInventoryCheckByOrderBL.findStoreChangeOrderNos(orderNos, excludeUser);
    }

    /**
     * 根据单号还原仓库批次库存
     */
    @Override
    public void restoreBatchInventoryByOrderNo(List<String> orderNos) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        warehouseInventoryCheckByOrderBL.restoreBatchInventoryByOrderNo(orderNos);
    }

    /**
     * 根据单号查询批次变更明细
     */
    @Override
    public List<BatchProductStoreChangeRecordDTO> queryBatchInventoryByOrderNo(List<String> orderNos) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        return warehouseInventoryCheckByOrderBL.queryBatchInventoryByOrderNo(orderNos);
    }

    /**
     * 根据单号查询批次变更明细
     *
     * @param queryRecordDTO
     */
    @Override
    public List<BatchProductStoreChangeRecordDTO> queryBatchInventory(BatchInventoryQueryRecordDTO queryRecordDTO) {
        AssertUtils.notEmpty(queryRecordDTO.getOrderNos(), "单号不能为空");
        return warehouseInventoryCheckByOrderBL.queryBatchInventory(queryRecordDTO);
    }

    /**
     * 根据单号查询批次详情数量
     *
     * @param orderNos
     * @return
     */
    @Override
    public Long queryCountBatchInventoryByOrderNo(List<String> orderNos) {
        AssertUtils.notEmpty(orderNos, "单号不能为空");
        return warehouseInventoryCheckByOrderBL.queryCountBatchInventoryByOrderNo(orderNos);
    }

    /**
     * 查询生产日期
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ReturnOrderProductDateDTO> queryProductDate(ReturnOrderProductDateQueryDTO queryDTO) {
        return warehouseInventoryCheckByOrderBL.queryProductDate(queryDTO);
    }
}

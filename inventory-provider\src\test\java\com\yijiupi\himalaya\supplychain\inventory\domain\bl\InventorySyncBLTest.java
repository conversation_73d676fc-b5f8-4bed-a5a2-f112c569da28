package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * Created by Lifeng on 2017/7/19.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventorySyncBLTest {

    // @Autowired
    // private InventoryAdjustBL inventorySyncBL;

    // @Test
    // public void syncAllSellRedisAsync() throws Exception {
    // inventorySyncBL.syncAllSellRedisAsync();
    // }

}
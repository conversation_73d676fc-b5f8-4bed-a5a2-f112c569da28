package com.yijiupi.himalaya.supplychain.inventory.domain.bo;

import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateItemDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
public class ReturnOrderProductionDateCacheBO {

    private String key;

    private String orderNo;

    private Long productSpecId;

    private Long ownerId;

    private ReturnOrderProductDateItemDTO itemDTO;

    /**
     * 获取
     *
     * @return key
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 设置
     *
     * @param key
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 获取
     *
     * @return orderNo
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置
     *
     * @param orderNo
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取
     *
     * @return productSpecId
     */
    public Long getProductSpecId() {
        return this.productSpecId;
    }

    /**
     * 设置
     *
     * @param productSpecId
     */
    public void setProductSpecId(Long productSpecId) {
        this.productSpecId = productSpecId;
    }

    /**
     * 获取
     *
     * @return ownerId
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置
     *
     * @param ownerId
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取
     *
     * @return itemDTO
     */
    public ReturnOrderProductDateItemDTO getItemDTO() {
        return this.itemDTO;
    }

    /**
     * 设置
     *
     * @param itemDTO
     */
    public void setItemDTO(ReturnOrderProductDateItemDTO itemDTO) {
        this.itemDTO = itemDTO;
    }

    public String getDefaultKey() {
        return String.format("%s-%s-%s", orderNo, productSpecId, ownerId);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeRuleMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRulePO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="Template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="TemplateName" property="templateName" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        Template_id,
        TemplateName,
        Remark,
        CreateUser
    </sql>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="batchAttributeRulePO.id">
        INSERT INTO batchattributerule (
        id,
        Template_id,
        TemplateName,
        Remark,
        CreateUser
        ) VALUES (
        #{batchAttributeRulePO.id},
        #{batchAttributeRulePO.templateId,jdbcType=BIGINT},
        #{batchAttributeRulePO.templateName,jdbcType=VARCHAR},
        #{batchAttributeRulePO.remark,jdbcType=VARCHAR},
        #{batchAttributeRulePO.createUser,jdbcType=VARCHAR}
        )
    </insert>


    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchattributerule (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="batchAttributeRulePOs" item="batchAttributeRulePO" index="index" separator=",">
            (
            #{batchAttributeRulePO.id,jdbcType=BIGINT},
            #{batchAttributeRulePO.templateId,jdbcType=BIGINT},
            #{batchAttributeRulePO.templateName,jdbcType=VARCHAR},
            #{batchAttributeRulePO.remark,jdbcType=VARCHAR},
            #{batchAttributeRulePO.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE batchattributerule
        <set>
            <if test="batchAttributeRulePO.id != null">id= #{batchAttributeRulePO.id,jdbcType=BIGINT},</if>
            <if test="batchAttributeRulePO.templateId != null">Template_id=
                #{batchAttributeRulePO.templateId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeRulePO.templateName != null">TemplateName=
                #{batchAttributeRulePO.templateName,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeRulePO.remark != null">Remark= #{batchAttributeRulePO.remark,jdbcType=VARCHAR},</if>
            <if test="batchAttributeRulePO.createUser != null">CreateUser=
                #{batchAttributeRulePO.createUser,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{batchAttributeRulePO.id,jdbcType=BIGINT}
    </update>

    <!--auto generated by codehelper on 2018-04-09 21:59:29-->
    <delete id="deleteById">
        delete from batchattributerule
        where id=#{id,jdbcType=BIGINT}
    </delete>

    <resultMap id="BaseResultMap1"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleReturnPO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="Template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="TemplateName" property="templateName" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="VARCHAR"/>
        <collection property="relationList"
                    ofType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationReturnPO">
            <id column="brrid" property="id"/>
            <result column="RuleId" property="ruleId"/>
            <result column="RuleType" property="ruleType"/>
            <result column="AttributeValueName" property="attributeValueName"/>
            <result column="AttributeValue_Id" property="attributeValueId"/>
        </collection>
    </resultMap>

    <select id="findBatchAttributeRuleList" resultMap="BaseResultMap1">
        SELECT
        b.id,
        b.Template_id,
        b.TemplateName,
        b.Remark,
        DATE_FORMAT(b.CreateTime,'%Y-%m-%d %H:%i:%s') as CreateTime,
        b.CreateUser,
        brr.id as brrid,
        brr.RuleId,
        brr.RuleType,
        brr.AttributeValueName,
        brr.AttributeValue_Id
        FROM
        batchattributerule b
        INNER JOIN batchattributerulerelation brr ON b.id = brr.RuleId
        where 1=1
        <if test="batchAttributeRuleQueryDTO.templateName != null and batchAttributeRuleQueryDTO.templateName!=''">
            and TemplateName like concat(concat('%',#{batchAttributeRuleQueryDTO.templateName,jdbcType=VARCHAR}),'%')
        </if>
    </select>
</mapper>


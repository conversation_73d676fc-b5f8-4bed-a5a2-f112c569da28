<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.DealerChargeConfigPOMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Dealer_Id" property="dealerId" jdbcType="BIGINT"/>
        <result column="DealerName" property="dealerName" jdbcType="VARCHAR"/>
        <result column="IsGetWarehouseCharge" property="isGetWarehouseCharge" jdbcType="TINYINT"/>
        <result column="BusinessType" property="businessType" jdbcType="TINYINT"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
        <result column="Facilitator_Id" property="facilitatorId" jdbcType="BIGINT"/>
        <result column="FacilitatorName" property="facilitatorName" jdbcType="VARCHAR"/>
        <result column="CreateUser" property="createUser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="MobileNo" property="mobileNo" jdbcType="BIGINT"/>
        <result column="FirstServiceTime" property="firstServiceTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Dealer_Id, DealerName, IsGetWarehouseCharge, BusinessType, Status, Facilitator_Id,
        FacilitatorName, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime, MobileNo,
        FirstServiceTime
    </sql>

    <update id="updateDealerConfigStatus"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO">
        update dealerchargeconfig
        set
        Status = #{po.status,jdbcType=TINYINT},
        LastUpdateUser = #{po.lastUpdateUser,jdbcType=BIGINT},
        LastUpdateTime = now()
        where Dealer_Id = #{po.dealerId}
        and Facilitator_Id=#{po.facilitatorId}
    </update>

    <select id="selectDealerChargeConfigById"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigDTO">
        select
        dcc.Id as id,
        dcc.Dealer_Id as dealerId ,
        dcc.DealerName as dealerName,
        dcc.IsGetWarehouseCharge as isGetWarehouseCharge,
        dcc.BusinessType as businessType,
        dcc.Status as status,
        dcc.Facilitator_Id as facilitatorId,
        dcc.FacilitatorName as facilitatorname,
        dcc.CreateUser as createUser,
        dcc.CreateTime as createTime,
        dcc.LastUpdateUser as lastUpdateUser,
        dcc.LastUpdateTime as lastUpdateTime,
        dcc.MobileNo as mobileNo,
        dcc.FirstServiceTime as firstServiceTime
        from dealerchargeconfig dcc
        where dcc.Dealer_Id = #{dealerId}
        and Facilitator_Id=#{facilitatorId}
        and dcc.Status=1
    </select>

    <select id="selectCountByDealerId" resultType="java.lang.Integer">
        select
        count(1) as count
        from dealerchargeconfig
        where Dealer_Id = #{dealerId}
        and Facilitator_Id=#{facilitatorId}
    </select>

    <update id="updateDealerChargeConfig"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO">
        update dealerchargeconfig
        <set>
            <if test="po.isGetWarehouseCharge != null">
                IsGetWarehouseCharge = #{po.isGetWarehouseCharge,jdbcType=TINYINT},
            </if>
            <if test="po.businessType != null">
                BusinessType = #{po.businessType,jdbcType=TINYINT},
            </if>
            <if test="po.lastUpdateUser != null">
                LastUpdateUser = #{po.lastUpdateUser,jdbcType=BIGINT},
            </if>
            LastUpdateTime = now()

        </set>
        where Dealer_Id = #{po.dealerId}
        and Facilitator_Id=#{po.facilitatorId}
    </update>

    <insert id="insertDealerChargeConfig"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO">
        insert into
        dealerchargeconfig (
        Id,
        Dealer_Id,
        DealerName,
        IsGetWarehouseCharge,
        BusinessType,
        Status,
        Facilitator_Id,
        FacilitatorName,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime,
        MobileNo,
        FirstServiceTime
        )
        values (
        #{po.id,jdbcType=BIGINT},
        #{po.dealerId,jdbcType=BIGINT},
        #{po.dealerName,jdbcType=VARCHAR},
        #{po.isGetWarehouseCharge,jdbcType=TINYINT},
        #{po.businessType,jdbcType=TINYINT},
        #{po.status,jdbcType=TINYINT},
        #{po.facilitatorId,jdbcType=TINYINT},
        #{po.facilitatorName,jdbcType=VARCHAR},
        #{po.createUser,jdbcType=BIGINT},
        now(),
        #{po.createUser,jdbcType=BIGINT},
        now(),
        #{po.mobileNo,jdbcType=BIGINT},
        #{po.firstServiceTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <select id="selectDealerChargeList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigDTO">
        select
        dcc.Id as id,
        dcc.Dealer_Id as dealerId ,
        dcc.DealerName as dealerName,
        dcc.IsGetWarehouseCharge as isGetWarehouseCharge,
        dcc.BusinessType as businessType,
        dcc.Status as status,
        dcc.Facilitator_Id as facilitatorId,
        dcc.FacilitatorName as facilitatorname,
        dcc.CreateUser as createUser,
        dcc.CreateTime as createTime,
        dcc.LastUpdateUser as lastUpdateUser,
        dcc.LastUpdateTime as lastUpdateTime,
        dcc.MobileNo as mobileNo,
        dcc.FirstServiceTime as firstServiceTime
        from dealerchargeconfig dcc
        <where>

            <if test="dealerId != null">
                and dcc.Dealer_Id=#{dealerId,jdbcType=BIGINT}
            </if>
            <if test="facilitatorIdList != null">
                and dcc.Facilitator_Id in
                <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isGetWarehouseCharge != null">
                and dcc.IsGetWarehouseCharge=#{isGetWarehouseCharge,jdbcType=TINYINT}
            </if>
            <if test="businessType != null">
                and dcc.BusinessType=#{businessType,jdbcType=TINYINT}
            </if>
            <if test="mobileNo != null and mobileNo !=''">
                and dcc.MobileNo like concat('%', #{mobileNo,jdbcType=VARCHAR}, '%')
            </if>

        </where>
    </select>


    <select id="selectDealerList" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigDTO">
        select
        dcc.Id as id,
        dcc.Dealer_Id as dealerId ,
        dcc.DealerName as dealerName,
        dcc.IsGetWarehouseCharge as isGetWarehouseCharge,
        dcc.BusinessType as businessType,
        dcc.Status as status,
        dcc.Facilitator_Id as facilitatorId,
        dcc.FacilitatorName as facilitatorname,
        dcc.CreateUser as createUser,
        dcc.CreateTime as createTime,
        dcc.LastUpdateUser as lastUpdateUser,
        dcc.LastUpdateTime as lastUpdateTime,
        dcc.MobileNo as mobileNo,
        dcc.FirstServiceTime as firstServiceTime
        from dealerchargeconfig dcc
        <where>

            <if test="dealerId != null">
                and dcc.Dealer_Id=#{dealerId,jdbcType=BIGINT}
            </if>
            <if test="facilitatorIdList != null">
                and dcc.Facilitator_Id in
                <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="isGetWarehouseCharge != null">
                and dcc.IsGetWarehouseCharge=#{isGetWarehouseCharge,jdbcType=TINYINT}
            </if>
            <if test="businessType != null">
                and dcc.BusinessType=#{businessType,jdbcType=TINYINT}
            </if>
            <if test="mobileNo != null and mobileNo !=''">
                and dcc.MobileNo like concat('%', #{mobileNo,jdbcType=VARCHAR}, '%')
            </if>

        </where>
    </select>


    <select id="selectDealerWarehouseList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.DealerWarehouseDTO">

        select
        distinct
        dcc.Dealer_Id as dealerId ,
        dcc.IsGetWarehouseCharge as isGetWarehouseCharge,
        dcc.BusinessType as businessType,
        dcc.Status as status,
        dcc.MobileNo as mobileNo,
        dcc.FirstServiceTime as firstServiceTime,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        wh.Name as warehouseName,
        wh.DetailAddress as detailAddress,
        wh.City as city
        from dealerchargeconfig dcc inner join productstore ps on ps.Owner_Id=dcc.Dealer_Id
        inner join warehouse wh on ps.Warehouse_Id= wh.Id
        and dcc.Dealer_Id =#{dealerId}
        <if test="city != null and city != ''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>
        <if test="facilitatorIdList != null">
            and dcc.Facilitator_Id in
            <foreach collection="facilitatorIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="warehouseId != null">
            and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        and ps.OwnerType = 2

    </select>


    <select id="selectDealerInfoList" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigDTO">
        select
        dcc.Id as id,
        dcc.Dealer_Id as dealerId ,
        dcc.DealerName as dealerName,
        dcc.IsGetWarehouseCharge as isGetWarehouseCharge,
        dcc.BusinessType as businessType,
        dcc.Status as status,
        dcc.Facilitator_Id as facilitatorId,
        dcc.FacilitatorName as facilitatorname,
        dcc.CreateUser as createUser,
        dcc.CreateTime as createTime,
        dcc.LastUpdateUser as lastUpdateUser,
        dcc.LastUpdateTime as lastUpdateTime,
        dcc.MobileNo as mobileNo,
        dcc.FirstServiceTime as firstServiceTime
        from dealerchargeconfig dcc inner join warehouse wh on wh.ShopId=dcc.Dealer_Id
        <where>

            <if test="facilitatorId != null">
                and dcc.Facilitator_Id=#{facilitatorId,jdbcType=BIGINT}
            </if>
            <if test="warehouseId != null">
                and wh.id=#{warehouseId,jdbcType=INTEGER}
            </if>
        </where>

    </select>

</mapper>
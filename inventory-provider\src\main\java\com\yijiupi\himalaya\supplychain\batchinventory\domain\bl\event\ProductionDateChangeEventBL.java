package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.config.BatchInventoryMQProperties;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryInfoUpdateDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductionDateChangeMessageDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateQueryDTO;

/**
 * 批次库存生产日期变更息发送
 */
@Service
public class ProductionDateChangeEventBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(ProductionDateChangeEventBL.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ProductStoreMapper productStoreMapper;

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;

    public void sendProductionDateChangeEvent(List<BatchInventoryInfoUpdateDTO> batchInventoryInfoUpdateDTOS) {
        LOGGER.info("ProductionDateChangeEventBL.sendProductionDateChangeEvent 批次库存生产日期变更息发送,入参：{}",
            JSON.toJSONString(batchInventoryInfoUpdateDTOS));

        if (CollectionUtils.isEmpty(batchInventoryInfoUpdateDTOS)) {
            return;
        }

        List<BatchInventoryInfoUpdateDTO> list = batchInventoryInfoUpdateDTOS.stream()
            .filter(info -> info.getProductionDate() != null && !StringUtils.isBlank(info.getProductStoreId())
                && info.getProductionDateChangeNotify() != null && info.getProductionDateChangeNotify())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, Date> map = list.stream()
            .collect(Collectors.toMap(p -> p.getProductStoreId(), p -> p.getProductionDate(), (v1, v2) -> v1));
        List<String> storeIds =
            list.stream().map(BatchInventoryInfoUpdateDTO::getProductStoreId).distinct().collect(Collectors.toList());
        List<ProductInventoryPO> inventoryPOS = productStoreMapper.findInventoryPOByStoreIds(storeIds);
        if (CollectionUtils.isEmpty(inventoryPOS)) {
            return;
        }

        // 重新计算最新的生产日期
        List<ProductionDateQueryDTO> productionDateQueryDTOS = new ArrayList<>();
        for (ProductInventoryPO dto : inventoryPOS) {
            ProductionDateQueryDTO queryDTO = new ProductionDateQueryDTO();
            queryDTO.setWarehouseId(dto.getWarehouseId());
            queryDTO.setProductSpecificationId(dto.getProductSpecificationId());
            productionDateQueryDTOS.add(queryDTO);
        }
        List<ProductionDateDTO> productionDateDTOS = batchInventoryQueryBL.findProductionDate(productionDateQueryDTOS);;

        List<ProductionDateChangeMessageDTO> messageDTOList = new ArrayList<>();
        productionDateDTOS.stream().forEach(i -> {
            ProductionDateChangeMessageDTO messageDTO = new ProductionDateChangeMessageDTO();
            messageDTO.setWarehouseId(i.getWarehouseId());
            messageDTO.setProductSpecificationId(i.getProductSpecificationId());
            messageDTO.setOwnerId(i.getOwnerId());
            messageDTO.setSecOwnerId(i.getSecOwnerId());
            messageDTO.setProductionDate(i.getProductionDate());
            messageDTOList.add(messageDTO);
        });

        LOGGER.info("ProductionDateChangeEventBL.sendProductionDateChangeEvent 批次库存生产日期变更息发送,messageDTOList：{}",
            JSON.toJSONString(messageDTOList));
        if (CollectionUtils.isNotEmpty(messageDTOList)) {
            rabbitTemplate.convertAndSend(BatchInventoryMQProperties.SYN_PRODUCTION_DATE_EXCHANGE, null, messageDTOList);
        }
    }
}

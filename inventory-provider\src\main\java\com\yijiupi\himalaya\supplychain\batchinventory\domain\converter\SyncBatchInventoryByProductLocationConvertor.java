package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventorySyncQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchTaskQueryService;
import com.yijiupi.himalaya.supplychain.waves.enums.LocationTypeEnum;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/27
 */
@Component
public class SyncBatchInventoryByProductLocationConvertor {

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;

    @Reference
    private IBatchTaskQueryService iBatchTaskQueryService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private ILocationService iLocationService;

    public List<ProductStoreBatchPO> splitByPickOccupy(List<ProductStoreBatchPO> productStoreBatchPOList,
        BatchInventorySyncQueryDTO queryDTO) {
        boolean isOpenLocationGroup = false;
        boolean isOpenStock = false;
        if (queryDTO.getWarehouseId() != null) {
            isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(queryDTO.getWarehouseId());;
            isOpenStock = warehouseConfigService.isOpenLocationStock(queryDTO.getWarehouseId());
        }
        if (BooleanUtils.isFalse(isOpenLocationGroup) || BooleanUtils.isFalse(isOpenStock)) {
            return productStoreBatchPOList;
        }

        Map<Long, BigDecimal> pickOccupyMap = new HashMap<>();
        Lists.partition(productStoreBatchPOList, 100).forEach(m -> {
            List<Long> skuIds =
                m.stream().map(ProductStoreBatchPO::getProductSkuId).distinct().collect(Collectors.toList());
            Map<Long, BigDecimal> occupyMap = iBatchTaskQueryService
                .findPickedCountBySkuIdForSCM25(queryDTO.getCityId(), queryDTO.getWarehouseId(), skuIds, Boolean.FALSE);
            if (!org.springframework.util.CollectionUtils.isEmpty(occupyMap)) {
                pickOccupyMap.putAll(occupyMap);
            }
        });

        List<ProductStoreBatchPO> occupyStoreList = productStoreBatchPOList.stream()
            .filter(m -> Objects.nonNull(pickOccupyMap.get(m.getProductSkuId()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(occupyStoreList)) {
            return productStoreBatchPOList;
        }

        List<ProductStoreBatchPO> notOccupyStoreList = productStoreBatchPOList.stream()
            .filter(m -> Objects.isNull(pickOccupyMap.get(m.getProductSkuId()))).collect(Collectors.toList());

        LoactionDTO loactionDTO = setOutStockLocationArea(queryDTO);

        List<ProductStoreBatchPO> splitOccupyStoreList = splitStore(occupyStoreList, pickOccupyMap, loactionDTO);

        List<ProductStoreBatchPO> totalOccupyStoreList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(notOccupyStoreList)) {
            totalOccupyStoreList.addAll(notOccupyStoreList);
        }
        if (!CollectionUtils.isEmpty(splitOccupyStoreList)) {
            totalOccupyStoreList.addAll(splitOccupyStoreList);
        }

        return totalOccupyStoreList;
    }

    private List<ProductStoreBatchPO> splitStore(List<ProductStoreBatchPO> occupyStoreList,
        Map<Long, BigDecimal> pickOccupyMap, LoactionDTO loactionDTO) {
        List<ProductStoreBatchPO> splitStoreBatchList =
            occupyStoreList.stream().map(m -> split(m, pickOccupyMap.get(m.getProductSkuId()), loactionDTO))
                .flatMap(Collection::stream).collect(Collectors.toList());

        return splitStoreBatchList;
    }

    private List<ProductStoreBatchPO> split(ProductStoreBatchPO productStoreBatchPO, BigDecimal occupyCount,
        LoactionDTO loactionDTO) {
        if (occupyCount.compareTo(BigDecimal.ZERO) == 0) {
            return Collections.singletonList(productStoreBatchPO);
        }

        BigDecimal leftCount = productStoreBatchPO.getTotalCount().subtract(occupyCount);
        if (BigDecimal.ZERO.compareTo(leftCount) == 0) {
            productStoreBatchPO.setLocationId(loactionDTO.getId());
            productStoreBatchPO.setLocationName(loactionDTO.getName());
            productStoreBatchPO.setLocationCategory(loactionDTO.getCategory().intValue());
            productStoreBatchPO.setSubcategory(loactionDTO.getSubcategory().intValue());
            return Collections.singletonList(productStoreBatchPO);
        }

        ProductStoreBatchPO notOccupyStoreBatchPO = createProductStoreBatchPO(productStoreBatchPO, leftCount);
        ProductStoreBatchPO occupyStoreBatchPO = createProductStoreBatchPO(productStoreBatchPO, occupyCount);
        occupyStoreBatchPO.setLocationId(loactionDTO.getId());
        occupyStoreBatchPO.setLocationName(loactionDTO.getName());
        occupyStoreBatchPO.setLocationCategory(loactionDTO.getCategory().intValue());
        occupyStoreBatchPO.setSubcategory(loactionDTO.getSubcategory().intValue());

        return Arrays.asList(notOccupyStoreBatchPO, occupyStoreBatchPO);
    }

    private ProductStoreBatchPO createProductStoreBatchPO(ProductStoreBatchPO storeBatchPO, BigDecimal count) {
        ProductStoreBatchPO newStoreBatchPO = new ProductStoreBatchPO();
        BeanUtils.copyProperties(storeBatchPO, newStoreBatchPO);
        newStoreBatchPO.setId(UUIDUtil.getUUID().replaceAll("-", ""));
        newStoreBatchPO.setTotalCount(count);

        return newStoreBatchPO;
    }

    private LoactionDTO setOutStockLocationArea(BatchInventorySyncQueryDTO queryDTO) {
        LocationQueryDTO dto = new LocationQueryDTO();
        dto.setSubcategory(LocationTypeEnum.AREA.byteValue());
        dto.setLocSubcategory(LocationAreaEnum.周转区.getType().byteValue());
        dto.setCityId(queryDTO.getCityId());
        dto.setWarehouseId(queryDTO.getWarehouseId());
        List<LoactionDTO> loactionDTOList = iLocationService.findLocationListByIdAndCategory(dto);
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return null;
        }
        loactionDTOList = loactionDTOList.stream().filter(m -> m.getState().byteValue() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(loactionDTOList)) {
            return null;
        }

        return loactionDTOList.stream().findFirst().get();
    }

    public void fillProductionDate(List<ProductStoreBatchPO> productStoreBatchPOList) {
        // [{\"ownerId\":9048741546093508319,\"productSpecificationId\":867351,\"warehouseId\":9000901}]
        List<ProductionDateQueryDTO> productionDateQueryDTOS = productStoreBatchPOList.stream().map(m -> {
            ProductionDateQueryDTO queryDTO = new ProductionDateQueryDTO();
            queryDTO.setProductSpecificationId(m.getProductSpecificationId());
            queryDTO.setOwnerId(m.getOwnerId());
            queryDTO.setWarehouseId(m.getWarehouseId());
            return queryDTO;
        }).collect(Collectors.toList());
        List<ProductionDateDTO> productionDateDTOList =
            batchInventoryQueryBL.findProductionDate(productionDateQueryDTOS);
        Map<String, ProductionDateDTO> productionDateDTOMap = productionDateDTOList.stream().collect(Collectors.toMap(
            k -> getKey(k.getWarehouseId(), k.getProductSpecificationId(), k.getOwnerId()), v -> v, (v1, v2) -> v1));

        productStoreBatchPOList.forEach(m -> {
            ProductionDateDTO productionDateDTO =
                productionDateDTOMap.get(getKey(m.getWarehouseId(), m.getProductSpecificationId(), m.getOwnerId()));
            if (Objects.nonNull(productionDateDTO)) {
                m.setProductionDate(productionDateDTO.getProductionDate());
            }
        });

    }

    private String getKey(Integer warehouseId, Long productSpecificationId, Long ownerId) {
        return String.format("%s-%s-%s", warehouseId, productSpecificationId, ownerId);
    }

}

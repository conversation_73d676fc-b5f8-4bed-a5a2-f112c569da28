package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.stocknotify.OrderCenterTrainsImportOutStockDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.stocknotify.OrderCenterTrainsImportOutStockDealerDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.stocknotify.OrderCenterTrainsImportOutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.stocknotify.OrderCenterTrainsImportOutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStockOrderRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * <AUTHOR>
 * @title: OrderCenterTrainsImportOutStockDTOConverter
 * @description:
 * @date 2023-03-16 10:50
 */
@Component
public class OrderCenterTrainsImportOutStockDTOConverter {

    @Reference
    private OwnerService ownerService;

    public OrderCenterTrainsImportOutStockDTO convert(List<ErpStockOrderRecordDTO> erpStoreOrderRecordList) {

        ErpStockOrderRecordDTO erpStockOrderRecordDTO = erpStoreOrderRecordList.get(0);

        Map<String, List<ErpStockOrderRecordDTO>> groupMap =
            erpStoreOrderRecordList.stream().collect(Collectors.groupingBy(ErpStockOrderRecordDTO::getReforderid));

        OrderCenterTrainsImportOutStockDTO trainsImportOutStockDTO = new OrderCenterTrainsImportOutStockDTO();

        trainsImportOutStockDTO.setCityId(Long.valueOf(erpStockOrderRecordDTO.getOrgId()));
        trainsImportOutStockDTO.setWarehouseId(Long.valueOf(erpStockOrderRecordDTO.getWarehouseId()));
        trainsImportOutStockDTO.setOutStockTime(new Date());
        trainsImportOutStockDTO.setOptUserId("1");

        List<OrderCenterTrainsImportOutStockOrderDTO> orderList = new ArrayList<>();
        for (Map.Entry<String, List<ErpStockOrderRecordDTO>> entry : groupMap.entrySet()) {
            orderList.add(convertOrder(entry.getValue()));
        }
        trainsImportOutStockDTO.setTrainsImportOutStockOrders(orderList);

        return trainsImportOutStockDTO;
    }

    private OrderCenterTrainsImportOutStockOrderDTO convertOrder(List<ErpStockOrderRecordDTO> recordList) {
        ErpStockOrderRecordDTO erpStockOrderRecordDTO = recordList.get(0);
        OrderCenterTrainsImportOutStockOrderDTO orderDTO = new OrderCenterTrainsImportOutStockOrderDTO();
        if (Objects.nonNull(erpStockOrderRecordDTO.getReforderid())) {
            orderDTO.setOrderId(erpStockOrderRecordDTO.getReforderid());
        }
        orderDTO.setOutStockOrderType((int)OutStockOrderTypeEnum.盘亏出库.getType());
        orderDTO.setOutStockOrderId(Long.valueOf(erpStockOrderRecordDTO.getReforderid()));
        orderDTO.setTrainsImportOutStockOrderItems(convertItem(recordList));

        return orderDTO;
    }

    private List<OrderCenterTrainsImportOutStockOrderItemDTO> convertItem(List<ErpStockOrderRecordDTO> recordList) {
        List<OrderCenterTrainsImportOutStockOrderItemDTO> itemList = new ArrayList<>();

        for (ErpStockOrderRecordDTO erpStockOrderRecordDTO : recordList) {
            OrderCenterTrainsImportOutStockOrderItemDTO itemDTO = new OrderCenterTrainsImportOutStockOrderItemDTO();
            itemDTO.setOrderItemId(Long.valueOf(erpStockOrderRecordDTO.getId()));
            itemDTO.setProductSkuId(erpStockOrderRecordDTO.getProductskuId());
            itemDTO.setUnitTotalCount(erpStockOrderRecordDTO.getUnittotalcount());
            itemDTO.setOutStockOrderItemId(Long.valueOf(erpStockOrderRecordDTO.getId()));
            itemDTO.setTrainsImportOutStockDealers(convertItemDetail(erpStockOrderRecordDTO));

            itemList.add(itemDTO);
        }

        return itemList;
    }

    private List<OrderCenterTrainsImportOutStockDealerDTO>
        convertItemDetail(ErpStockOrderRecordDTO erpStockOrderRecordDTO) {
        OrderCenterTrainsImportOutStockDealerDTO dealerDTO = new OrderCenterTrainsImportOutStockDealerDTO();
        dealerDTO.setUnitTotalCount(erpStockOrderRecordDTO.getUnittotalcount());
        dealerDTO.setSecOwnerId(erpStockOrderRecordDTO.getSecOwnerId());
        dealerDTO.setProductionDate(erpStockOrderRecordDTO.getProductionDate());
        dealerDTO.setOwnerId(erpStockOrderRecordDTO.getOwnerId());
        String erpSecOwnId = getErpSecOwnId(erpStockOrderRecordDTO.getSecOwnerId());
        if (StringUtils.isNotEmpty(erpSecOwnId)) {
            dealerDTO.setErpSecOwnerId(erpSecOwnId);
        }
        // dealerDTO.setExpirationDate();
        dealerDTO.setProductSpecificationId(erpStockOrderRecordDTO.getProductSpecificationId());

        return Collections.singletonList(dealerDTO);
    }

    private String getErpSecOwnId(Long wmsSecOwnerId) {
        OwnerDTO owner = ownerService.getOwnerById(wmsSecOwnerId);
        if (owner == null) {
            return null;
        }
        return owner.getRefPartnerId();
    }

}

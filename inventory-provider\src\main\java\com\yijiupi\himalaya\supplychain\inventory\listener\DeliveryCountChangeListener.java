package com.yijiupi.himalaya.supplychain.inventory.listener;

import org.springframework.stereotype.Component;

/**
 * 供应链仓库库存变更事件.
 *
 * <AUTHOR> 2018/1/29
 */
@Component
public class DeliveryCountChangeListener {
    // private static final Logger LOG = LoggerFactory.getLogger(DeliveryCountChangeListener.class);
    //
    // @Autowired
    // private DeliveryStoreRecordMapper deliveryStoreRecordMapper;
    // @Autowired
    // private InventoryChangeEventFireBL inventoryChangeEventFireBL;
    //
    //// @RabbitListener(queues = "${mq.supplychain.inventory.deliverycountchange}")
    //// public void syncApply(ArrayList<ProductInventoryPO> msgs, Message message) {
    //// String syncData = JSON.toJSONString(msgs);
    //// LOG.info("发货数量变更消息：{}", syncData);
    //// processMsg(msgs);
    //// LOG.info("发货数量变更处理成功: {}", syncData);
    //// }
    //
    //
    // //批量处理发货中数量
    // private void processMsg(ArrayList<ProductInventoryPO> lstMsg) {
    // if (lstMsg != null && lstMsg.size() > 0) {
    // ArrayList<ProductInventoryPO> lstRecord = new ArrayList<>();
    // try {
    // //根据StoreId，合并发货总量
    // for (ProductInventoryPO po : lstMsg) {
    // if (lstRecord.stream().anyMatch(p -> p.getId().equals(po.getId()))) {
    // continue;
    // }
    // BigDecimal totalCount = lstMsg.stream().filter(p -> p.getId().equals(po.getId())).map(p ->
    // p.getChangeCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
    // po.setChangeCount(totalCount);
    // lstRecord.add(po);
    // }
    // if (!CollectionUtils.isEmpty(lstRecord)) {
    // deliveryStoreRecordMapper.insertOrUpdateDeliveryRecordBatch(lstRecord);
    // }
    // } catch (Exception oe) {
    // oe.printStackTrace();
    // LOG.info("处理发货数量失败:" + oe.getMessage());
    // if (!CollectionUtils.isEmpty(lstRecord)) {
    // inventoryChangeEventFireBL.deliveryCountInventoryChangeEvent(lstRecord);
    // LOG.info("发货数量失败消息(塞回队列重新处理)：" + JSON.toJSONString(lstRecord));
    // }
    // }
    // }
    // }

}

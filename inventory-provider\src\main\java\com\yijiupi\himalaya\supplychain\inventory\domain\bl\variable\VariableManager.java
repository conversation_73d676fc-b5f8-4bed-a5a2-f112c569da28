package com.yijiupi.himalaya.supplychain.inventory.domain.bl.variable;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-10-11 10:30
 **/
@Component
public class VariableManager {

    @Reference
    private IVariableValueService variableValueService;
    @Reference
    private IContentConfigurationService contentConfigurationService;
    private static final String NP_NOT_NEED_PICKING = "NP_NOT_NEED_PICKING";

    /**
     * 内配单二级仓是否不拣货, 优先取 wms 后台的配置, 找不到就取 scop 上的配置<br/>
     * <b>wms 后台配置不能设置默认值, 不然兜底策略将会失效</b>
     *
     * @param warehouseId 目标仓库 id
     * @return 内配单二级仓是否不拣货
     */
    public boolean isNotNeedPicking(Integer warehouseId) {
        if (warehouseId == null) {
            return false;
        }
        Optional<String> data = getVariableByKey(NP_NOT_NEED_PICKING, warehouseId)
                .map(VariableDefAndValueDTO::getVariableData).filter(StringUtils::hasText);
        if (data.isPresent()) {
            return data.map(Boolean::valueOf).filter(it -> it).isPresent();
        }
        return checkContentConfig(warehouseId, NP_NOT_NEED_PICKING);
    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key 参数 key
     * @return 查询结果, 可能为空
     */
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key) {
        return getVariableByKey(key, null);
    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @return 查询结果, 可能为空
     */
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key, Integer warehouseId) {
        return getVariableByKey(key, warehouseId, null);
    }

    /**
     * 查询 wms 网站上配置的参数
     *
     * @param key         参数 key
     * @param warehouseId 仓库 id
     * @param orgId       城市 id
     * @return 查询结果, 可能为空
     */
    @NotNull
    public Optional<VariableDefAndValueDTO> getVariableByKey(String key, Integer warehouseId, Integer orgId) {
        VariableValueQueryDTO queryDTO = new VariableValueQueryDTO();
        queryDTO.setVariableKey(key);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setOrgId(orgId);
        return Optional.ofNullable(variableValueService.detailVariable(queryDTO));
    }

    /**
     * 根据key检查内配配置
     */
    public boolean checkContentConfig(Integer warehouseId, String key) {
        if (warehouseId == null) {
            return false;
        }
        String contentValue = contentConfigurationService.getContentValue(key, null, "");
        String[] warehouseIds = contentValue.split("、");
        String targetWarehouseId = warehouseId.toString();
        return Arrays.asList(warehouseIds).contains(targetWarehouseId);
    }

}

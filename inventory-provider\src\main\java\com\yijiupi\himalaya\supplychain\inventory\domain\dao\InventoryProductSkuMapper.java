package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoSO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ActualSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.SpecificationInfoPO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;

/**
 * <AUTHOR> 2017/11/17
 */
public interface InventoryProductSkuMapper {

    /**
     * 根据skuId、source查询productSku表(skuId现在不唯一了,一个skuId可以对应多个产品来源,产品来源，0:酒批，1:微酒（贷款/抵押）)
     *
     * @param productSkuId skuid
     * @return
     */
    ProductSkuPO getProductSkuBySkuId(@Param("productSkuId") Long productSkuId);

    /**
     * 招商订单，根据SKUId，查找发货城市SKUID limit 1
     */
    Long getAnotherCityProductSkuId(@Param("productSkuId") Long productSkuId, @Param("cityId") Integer cityId);

    /**
     * 查询实际发货skuId
     *
     * @param productSkuIdS
     * @param cityId
     * @param source
     * @return
     */
    List<ActualSkuPO> getActualDeliverySkuIdS(@Param("list") List<Long> productSkuIdS, @Param("cityId") Integer cityId);

    /**
     * 查询作废产品对应的正常产品
     *
     * @param productSkuIdS
     * @return
     */
    List<ActualSkuPO> getNormalProductStateSkuIdS(@Param("list") List<Long> productSkuIdS);

    /**
     * 查询存在的SKUID
     *
     * @param productSkuIdS
     * @return
     */
    List<Long> getExitsProductSkuIds(@Param("list") List<Long> productSkuIdS);

    /**
     * 根据skuId查询productSku表(批量)
     */
    List<ProductSkuPO> getProductSkuListByIds(@Param("productSkuIdList") List<Long> productSkuIdList);
    //
    // /**
    // * 批量查询 需要加上城市过滤,不同 城市下的ProductSpecification_Id有可能一样
    // */
    // List<Map<Long, BigDecimal>> getInventoryProductSkuMap(@Param("set") Set<Long> productSkuIds,
    // @Param("channel") Integer channel,
    // @Param("secOwnerId") Long secOwnerId);

    /**
     * 根据产品信息规格查询转换系数、包装规格
     */
    SpecificationInfoPO getSpecificationInfo(@Param("prodcutInfoSpecId") Integer prodcutInfoSpecId);

    /**
     * 根据产品信息规格查询转换系数、包装规格
     */
    SpecificationInfoPO getSpecificationInfoByStoreId(@Param("productStoreId") String productStoreId);

    // /**
    // * 根据sku相关信息查询货位相关信息
    // *
    // * @param batchLocationInfoQueryDTO
    // * @return
    // */
    // List<BatchLocationInfoDTO> findBatchHuoWeiDTOBySku(@Param("dto") BatchLocationInfoQueryDTO
    // batchLocationInfoQueryDTO);
    //
    // /**
    // * 根据sku相关信息查询货区相关信息
    // *
    // * @param batchLocationInfoQueryDTO
    // * @return
    // */
    // List<BatchLocationInfoDTO> findBatchHuoQuDTOBySku(@Param("dto") BatchLocationInfoQueryDTO
    // batchLocationInfoQueryDTO);
    //
    // /**
    // * 根据sku相关信息查询货区或者货位相关信息
    // *
    // * @param batchLocationInfoQueryDTO
    // * @return
    // */
    // List<BatchLocationInfoDTO> findBatchDTOBySku(@Param("dto") BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);

    /**
     * 产品查询
     */
    PageResult<ProductSkuInfoDTO> listProductSkuInfo(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 查询产品SKU基本信息
     */
    PageResult<ProductSkuInfoDTO> findProductBaseInfo(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 查询产品SKU基本信息
     */
    PageResult<ProductSkuInfoDTO> findProductBaseInfoByStoreCheck(ProductSkuInfoSO productSkuInfoSO);

    /**
     * 查询sku config
     *
     * @param productSkuIdList
     * @param warehouseId
     * @return
     */
    List<ProductSkuConfigDTO> queryInventoryProperty(@Param("productSkuIdList") List<Long> productSkuIdList,
        @Param("warehouseId") Integer warehouseId);
}

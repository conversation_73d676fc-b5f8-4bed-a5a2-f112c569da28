package com.yijiupi.himalaya.supplychain.batchinventory.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产日期审核申请表
 *
 * <AUTHOR>
 * @since 2025-03-14 16:07
 **/
public class ProductionDateAuditPO implements Serializable {
    /**
     * 主键 id
     */
    private Long id;

    /**
     * 仓库 id
     */
    private Integer warehouseId;

    /**
     * 产品 skuId
     */
    private Long skuId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 审核状态, 0=审核中 1=审核通过 2=审核拒绝
     */
    private Integer state;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 批次库存小件总数
     */
    private BigDecimal unitTotalCount;

    /**
     * 关联任务号
     */
    private String refTodoTaskNo;

    /**
     * 关联任务 id
     */
    private Long refTodoTaskId;

    /**
     * 申请人名称
     */
    private String applyUser;

    /**
     * 申请人电话
     */
    private String mobileNo;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人 id
     */
    private Integer createUser;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 最后更新人
     */
    private Integer lastUpdateUser;

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型: 0：无，1：促销
     */
    private Byte businessType;

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }

    public String getRefTodoTaskNo() {
        return refTodoTaskNo;
    }

    public void setRefTodoTaskNo(String refTodoTaskNo) {
        this.refTodoTaskNo = refTodoTaskNo;
    }

    public Long getRefTodoTaskId() {
        return refTodoTaskId;
    }

    public void setRefTodoTaskId(Long refTodoTaskId) {
        this.refTodoTaskId = refTodoTaskId;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(Integer lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }
}
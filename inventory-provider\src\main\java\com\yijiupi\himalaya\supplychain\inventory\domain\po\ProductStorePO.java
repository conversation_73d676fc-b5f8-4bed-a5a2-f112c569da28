/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductStorePO implements Serializable {
    /**
     * productstore主键id
     */
    private String id;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 库存数量
     */
    private BigDecimal totalCountMinUnit;
    /**
     * 规格id
     */
    private Long productSpecificationId;
    /**
     * 类型
     */
    private Integer ownerType;
    /**
     * 所属人id
     */
    private Long ownerId;
    private Date createTime;
    private Date lastUpdateTime;
    /**
     * 发货中数量
     */
    private BigDecimal deliveryedCount;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 中台skuId
     */
    private Long unifySkuId;

    public Long getUnifySkuId() {
        return unifySkuId;
    }

    public void setUnifySkuId(Long unifySkuId) {
        this.unifySkuId = unifySkuId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(BigDecimal totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public BigDecimal getDeliveryedCount() {
        return deliveryedCount;
    }

    public void setDeliveryedCount(BigDecimal deliveryedCount) {
        this.deliveryedCount = deliveryedCount;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}
package com.yijiupi.himalaya.supplychain.inventory.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.BaseResult;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.ROResult;
import com.yijiupi.himalaya.supplychain.inventory.controller.model.WebConstants;
import com.yijiupi.himalaya.supplychain.query.LastTwoBatchQueryDTO;
import com.yijiupi.himalaya.supplychain.service.batchinventory.ITidbBatchInventoryQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/7/4 11:31
 * @Version 1.0
 */
@RestController
public class BatchInventoryController {
	private static final Logger LOG = LoggerFactory.getLogger(BatchInventoryController.class);
	@Reference
	private ITidbBatchInventoryQueryService tidbBatchInventoryQueryService;
	@Autowired
	private BatchInventoryQueryBL batchInventoryQueryBL;

	/**
	 * 查询批次库存信息列表
	 */
	@RequestMapping(value = "/batchInventory/findBatchInventoryInfo", method = RequestMethod.POST)
	public BaseResult findBatchInventoryInfo(@RequestBody BatchInventoryQueryDTO batchInventoryQueryDTO) {
		LOG.info("批次库存新:迁移到微服务:");
		AssertUtils.notNull(batchInventoryQueryDTO.getWarehouseId(), "仓库id不能为空");
		PageList<BatchInventoryDTO> batchInventoryList =
				batchInventoryQueryBL.findBatchInventoryInfo(batchInventoryQueryDTO);

		// SCM-19604 休百生产日期问题专项治理
		processLastTwoBatch(batchInventoryList);
		ROResult<List<BatchInventoryDTO>> listROResult = new ROResult<>(batchInventoryList.getDataList());
		listROResult.setTotalCount(batchInventoryList.getPager().getRecordCount());
		listROResult.setResult(WebConstants.RESULT_SUCCESS);
		return listROResult;
	}

	/**
	 * 处理批次是否为最近2个采购/调拨
	 *
	 * @param batchInventoryList
	 */
	private void processLastTwoBatch(PageList<BatchInventoryDTO> batchInventoryList) {
		if (batchInventoryList.getDataList() != null && !batchInventoryList.getDataList().isEmpty()) {
			Integer warehouseId = batchInventoryList.getDataList().stream().findAny().get().getWarehouseId();
			List<String> lstBatchId = batchInventoryList.getDataList().stream().map(BatchInventoryDTO::getStoreBatchId)
					.distinct().collect(Collectors.toList());
			LastTwoBatchQueryDTO queryDTO = new LastTwoBatchQueryDTO();
			queryDTO.setWarehouseId(warehouseId);
			queryDTO.setLstBatchId(lstBatchId);
			List<String> lastTwoBatchById = tidbBatchInventoryQueryService.findLastTwoBatchById(queryDTO);
			batchInventoryList.getDataList().forEach(p -> {
				if (lastTwoBatchById.contains(p.getStoreBatchId())) {
					p.setLastTwoBatch(true);
				}
			});
		}
	}
}

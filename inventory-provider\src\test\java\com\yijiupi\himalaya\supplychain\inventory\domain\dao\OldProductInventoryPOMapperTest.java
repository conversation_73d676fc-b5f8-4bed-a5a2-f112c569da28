
package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.search.ProductWarehouseStoreSO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseStoreBySupplierOpSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductSpecCityListStoreSO;

/**
 * Created by wang<PERSON> on 2017-09-14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class OldProductInventoryPOMapperTest {
    @Autowired
    private ProductStorePOMapper productStorePOMapper;

    @Test
    public void findProductSpecCityListStore() {
        ProductSpecCityListStoreSO so = new ProductSpecCityListStoreSO();
        List<Integer> cityIds = new ArrayList<>();
        cityIds.add(999);
        so.setCityIds(cityIds);
        so.setProductSpecId(95L);
        productStorePOMapper.findProductSpecCityListStore(so);
    }

    @Test
    public void findProductWarehouseListStore() {
        List<Long> productSkuIds = new ArrayList<>();
        productSkuIds.add(99900000176907L);
        // productStorePOMapper.findProductWarehouse(99916,productSkuIds,0);
    }

    // @Test
    // public void select(){
    // productStorePOMapper.selectByPrimaryKey("000069f63c114bcfaee19a134f773726");
    // }

    // @Test
    // public void findStockWarningProductSkuList(){
    // StockWarningByOpSO searchCon = new StockWarningByOpSO();
    // PagerCondition pager = new PagerCondition();
    // pager.setPageSize(20);
    // pager.setCurrentPage(1);
    // productStorePOMapper.findStockWarningProductSkuList(searchCon,pager);
    // }

    @Test
    public void findProductWarehouseStoreList() {
        ProductWarehouseStoreSO so = new ProductWarehouseStoreSO();
        PagerCondition pager = new PagerCondition();
        pager.setPageSize(20);
        pager.setCurrentPage(1);
    }

    @Test
    public void findProductWarehouseStoreListForAllocation() {
        WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO = new WarehouseStoreBySupplierOpSO();
        PagerCondition pager = new PagerCondition();
        pager.setPageSize(20);
        pager.setCurrentPage(1);
        // productStorePOMapper.findProductWarehouseStoreListForAllocation(warehouseStoreBySupplierOpSO,pager);
    }

    @Test
    public void getProductStoreForSupplierOp() {
        WarehouseStoreBySupplierOpSO warehouseStoreBySupplierOpSO = new WarehouseStoreBySupplierOpSO();
        productStorePOMapper.getProductStoreForSupplierOp(warehouseStoreBySupplierOpSO);
    }

    @Test
    public void findStoreReportPageByAuth() {
        StockReportSO so = new StockReportSO();
        PagerCondition pager = new PagerCondition();
        pager.setPageSize(20);
        pager.setCurrentPage(1);
        // productStorePOMapper.findStoreReportPageByAuth(so,pager);
    }
}
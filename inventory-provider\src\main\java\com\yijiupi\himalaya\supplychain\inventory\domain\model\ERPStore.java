package com.yijiupi.himalaya.supplychain.inventory.domain.model;

/**
 * <AUTHOR>
 */
public class ERPStore {

    private String productSkuId;

    private String warehouseId;

    private Integer erpDisplayCount;

    private Integer buyCount;

    private Integer buyReturnCount;

    private Integer saleCount;

    private Integer saleReturnCount;

    private Integer erpRealCount;

    public String getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(String productSkuId) {
        this.productSkuId = productSkuId;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getErpDisplayCount() {
        return erpDisplayCount;
    }

    public void setErpDisplayCount(Integer erpDisplayCount) {
        this.erpDisplayCount = erpDisplayCount;
    }

    public Integer getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(Integer buyCount) {
        this.buyCount = buyCount;
    }

    public Integer getBuyReturnCount() {
        return buyReturnCount;
    }

    public void setBuyReturnCount(Integer buyReturnCount) {
        this.buyReturnCount = buyReturnCount;
    }

    public Integer getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount) {
        this.saleCount = saleCount;
    }

    public Integer getSaleReturnCount() {
        return saleReturnCount;
    }

    public void setSaleReturnCount(Integer saleReturnCount) {
        this.saleReturnCount = saleReturnCount;
    }

    public Integer getErpRealCount() {
        return erpRealCount;
    }

    public void setErpRealCount(Integer erpRealCount) {
        this.erpRealCount = erpRealCount;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.util;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {

    /**
     * 获取两个时间间隔天数
     * 
     * @param startTime
     * @param endTime
     * @return
     */
    public static Long betweenDays(Date startTime, Date endTime) {
        LocalDate startLocalDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        return ChronoUnit.DAYS.between(startLocalDate, endLocalDate);
    }

    /**
     * 日期计算
     * 
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static Date add(Date date, Integer field, Integer amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, amount);
        return calendar.getTime();
    }
}

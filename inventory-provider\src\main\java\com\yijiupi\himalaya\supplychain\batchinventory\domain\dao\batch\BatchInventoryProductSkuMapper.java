package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchLocationInfoQueryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.productSku.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> 2017/11/17
 */
public interface BatchInventoryProductSkuMapper {

    /**
     * 查找所有可用的货位和货区(城市id过滤,去掉psku.cityid=ps.cityid关联条件)
     */
    List<BatchLocationInfoDTO>
        findBatchAllDTOBySkuAndCityId(@Param("dto") BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);

    /**
     * 查找所有可用的货位和货区
     */
    List<BatchLocationInfoDTO> findBatchAllDTOBySku(@Param("dto") BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);
    //
    // /**
    // * 根据货区查可用货位
    // *
    // * @param batchLocationInfoQueryDTO
    // * @return
    // */
    // List<BatchLocationInfoDTO> findBatchHuoQuDTOBySku(@Param("dto") BatchLocationInfoQueryDTO
    // batchLocationInfoQueryDTO);

    /**
     * 根据货区查可用货位（只查询库存信息存在的货位）
     *
     * @param batchLocationInfoQueryDTO
     * @return
     */
    List<BatchLocationInfoDTO>
        findBatchHuoWeiDTOBySku(@Param("dto") BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);

    /**
     * 根据货位查可用货位（所有货位，库存信息可能不存在）
     *
     * @param batchLocationInfoQueryDTO
     * @return
     */
    List<BatchLocationInfoDTO>
        findBatchHuoQuDTOBySkuAndLocation(@Param("dto") BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);

    /**
     * 根据货区查可用货位（所有货位，库存信息可能不存在）
     *
     * @param batchLocationInfoQueryDTO
     * @return
     */
    List<BatchLocationInfoDTO>
        findBatchHuoWeiDTOBySkuAndArea(@Param("dto") BatchLocationInfoQueryDTO batchLocationInfoQueryDTO);

    /**
     * 根据sku相关信息查询货区或者货位相关信息
     *
     * @param batchLocationInfoQueryPO
     * @return
     */
    List<BatchLocationInfoDTO> findBatchDTOBySku(@Param("po") BatchLocationInfoQueryPO batchLocationInfoQueryPO);

    /**
     * 根据locationId查询货位信息
     *
     * @param locationId
     * @return
     */
    List<BatchLocationInfoDTO> findBatchLocationDTOByLocationId(@Param("locationId") Long locationId,
        @Param("channel") Integer channel);

    /**
     * 查询批次库存,货位信息,货区类别必填
     */
    List<BatchLocationInfoDTO> findBatchDTOBySkuListAndCategory(
            @Param("list") Collection<BatchLocationInfoQueryPO> batchLocationInfoQueryPOS,
        @Param("subcategory") Integer subcategory, @Param("warehouseId") Integer warehouseId);

    /**
     * 查询批次库存,货位信息
     */
    List<BatchLocationInfoDTO> findBatchDTOBySkuList(
            @Param("list") Collection<BatchLocationInfoQueryPO> batchLocationInfoQueryPOS,
        @Param("warehouseId") Integer warehouseId);

    /**
     * 根据skuId查询productSku表(批量)
     */
    List<ProductSkuPO> getProductSkuListByIds(@Param("productSkuIdList") Collection<Long> productSkuIdList);

    /**
     * 查询批次库存,货位信息,货区类别必填
     */
    List<BatchLocationInfoDTO> findBatchBySkuListAndCategory(
            @Param("list") Collection<BatchLocationInfoQueryPO> batchLocationInfoQueryPOS,
        @Param("subcategorys") List<Integer> subcategorys, @Param("warehouseId") Integer warehouseId);
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleReturnPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRulePO;

@Mapper
public interface BatchAttributeRuleMapper {
    /**
     * 新增
     *
     * @param batchAttributeRulePO
     * @return
     */
    int insert(@Param("batchAttributeRulePO") BatchAttributeRulePO batchAttributeRulePO);

    int insertList(@Param("batchAttributeRulePOs") List<BatchAttributeRulePO> batchAttributeRulePOs);

    int update(@Param("batchAttributeRulePO") BatchAttributeRulePO batchAttributeRulePO);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    int deleteById(@Param("id") Long id);

    /**
     * 列表
     *
     * @param batchAttributeRuleQueryDTO
     * @return
     */
    PageResult<BatchAttributeRuleReturnPO> findBatchAttributeRuleList(
        @Param("batchAttributeRuleQueryDTO") BatchAttributeRuleQueryDTO batchAttributeRuleQueryDTO,
        @Param("pageSum") Integer pageSum, @Param("pageSize") Integer pageSize);
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl.easysell;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.easysell.EasySellProductDetailsBL;
import com.yijiupi.himalaya.supplychain.inventory.service.easysell.IEasySellProductDetailsService;

/**
 * @author: lidengfeng
 * @date 2018/8/30 19:23
 */
@Service
public class EasySellProductDetailsServiceImpl implements IEasySellProductDetailsService {

    @Autowired
    private EasySellProductDetailsBL easySellProductDetailsBL;

    /**
     * 根据规格Id集合,经销商id,仓库查询商品信息
     * 
     * @param query
     * @return
     */
    @Override
    public PageList<ProductDetailsDTO> findProductDetailsList(ProductDetailsQueryDTO query) {
        AssertUtils.notNull(query.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(query.getShopId(), "经销商id不能为空");
        return easySellProductDetailsBL.findProductDetailsList(query);
    }
}

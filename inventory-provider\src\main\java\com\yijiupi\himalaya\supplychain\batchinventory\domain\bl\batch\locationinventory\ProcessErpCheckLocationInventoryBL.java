package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory;

import java.math.BigDecimal;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProcessLocationInventoryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Service
public class ProcessErpCheckLocationInventoryBL extends ProcessLocationInventoryBaseBL {
    @Override
    protected boolean doSupport(ProcessLocationInventoryBO bo) {
        if (Objects.isNull(bo.getProductInventoryChangeRecordPO())) {
            return Boolean.FALSE;
        }
        if (Objects.equals(ERPType.库存盘点单.getType(), bo.getProductInventoryChangeRecordPO().getOrderType())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    protected void setProductInventoryChangeRecordPOInfo(ProductInventoryChangeRecordPO changeRecordPO) {
        super.setProductInventoryChangeRecordPOInfo(changeRecordPO);
        BigDecimal changeCount = changeRecordPO.getTotalCount();
        if (changeCount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 如果是盘点单据：盘盈单据则根据库存ID查询对应产品是否库龄管控产品,如果是库龄产品则查询批次是需要带上批次时间
        ProductInventoryPO inventoryPO = productStoreMapper.findInventoryPO(changeRecordPO.getProductStoreId());
        if (Objects.isNull(inventoryPO)) {
            return;
        }
        Boolean storeAgeControlProduct = iStockAgeStrategyService.isProductStockAgeControlsBySpec(
            inventoryPO.getCityId(), inventoryPO.getWarehouseId(), inventoryPO.getProductSpecificationId(),
            inventoryPO.getOwnerId(), inventoryPO.getSecOwnerId());
        // 设置是否库龄管控产品
        changeRecordPO.setStoreAgeControlProduct(storeAgeControlProduct);
    }
}

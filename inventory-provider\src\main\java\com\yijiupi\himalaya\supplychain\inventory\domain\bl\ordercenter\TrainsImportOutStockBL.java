package com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.OrderCenterTrainsImportOutStockDTOConverter;
import com.yijiupi.himalaya.supplychain.ordercenter.dto.stocknotify.OrderCenterTrainsImportOutStockDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStockOrderRecordDTO;
import com.yijiupi.himalaya.supplychain.storecheck.domain.enums.StoreCheckTypeConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: TrainsImportOutStockBL
 * @description:
 * @date 2023-03-16 11:16
 */
@Service
public class TrainsImportOutStockBL {

    @Autowired
    private InventoryOrderCenterBL inventoryOrderCenterBL;
    @Autowired
    private OrderCenterTrainsImportOutStockDTOConverter converter;
    private static final Logger LOG = LoggerFactory.getLogger(TrainsImportOutStockBL.class);

    /**
     * 通知订单中台盘亏正向出库、盘盈反审核出库
     *
     * @param erpStoreOrderRecord
     * @param erpEventType
     */
    public void notifyOrderCenterOut(List<ErpStockOrderRecordDTO> erpStoreOrderRecord, Integer erpEventType) {
        if (CollectionUtils.isEmpty(erpStoreOrderRecord)) {
            return;
        }

        List<ErpStockOrderRecordDTO> needSyncList = erpStoreOrderRecord.stream().filter(m -> needNotifyOrderCenterOut(m.getReforderid(), erpEventType)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needSyncList)) {
            LOG.warn("出库单同步，订单类型不匹配，不通知订单中台， {} ;  erpEventType : {}", JSON.toJSONString(erpStoreOrderRecord), erpEventType);
            return;
        }

        try {
            OrderCenterTrainsImportOutStockDTO outStockDTO = converter.convert(needSyncList);
            inventoryOrderCenterBL.batchImportOutStock(outStockDTO);
        } catch (Exception e) {
            LOG.warn("出库单同步，订单类型不匹配，不通知订单中台， {}", JSON.toJSONString(needSyncList));
        }
    }

    private boolean needNotifyOrderCenterOut(String orderNo, Integer erpEventType) {
        if (StringUtils.isEmpty(orderNo)) {
            return Boolean.FALSE;
        }

        if (orderNo.contains(StoreCheckTypeConstants.STORE_CHECK_RESULT_LACK) && ERPEventType.单据审核.getType().intValue() == erpEventType) {
            return Boolean.TRUE;
        }

        if (orderNo.contains(StoreCheckTypeConstants.STORE_CHECK_RESULT_MORE) && ERPEventType.单据反审核.getType().intValue() == erpEventType) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.WarehouseInventoryReportQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.erp.ERPStoreVO;
import com.yijiupi.himalaya.supplychain.enums.CheckDiffEnum;
import com.yijiupi.himalaya.supplychain.enums.CheckStateEnum;
import com.yijiupi.himalaya.supplychain.enums.StoreTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.check.OrderCenterInventoryCheckBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventorySyncEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.InventoryConvertor;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ProductStoreConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterUnConfirmOrderInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.check.CheckStoreInventoryByWarehouseInfoDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductTypeEnums;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoSpecificationSerivce;
import com.yijiupi.himalaya.supplychain.service.IWarehouseInventoryReportQueryService;
import com.yijiupi.himalaya.supplychain.storecheck.utils.BigDecimalUtils;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;
import com.yijiupi.himalaya.supplychain.wmsdubbop.dto.easychain.ShopWarehouseInventoryDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 库存对账 【销售库存】 = 仓库库存 - 已下单未发货库存 - 处理品库存 - 待上架数量 【仓库库存】 = ERP库存 - 已发货未完成库存
 *
 * <AUTHOR>
 * @date 2019/1/8 15:20
 */
@Service
public class WarehouseInventoryCheckByWarehouseBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseInventoryCheckByWarehouseBL.class);
    
    // 存储ERP二级货主ID到久批货主ID的映射关系
    private Map<String, List<String>> erpToWmsOwnerMap = new HashMap<>(16);

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private DeliveryStoreRecordBL deliveryStoreRecordBL;
    @Autowired
    private InventorySyncRecordBL inventorySyncRecordBL;

    // @ReferGateway(path = ServerPath.EASY_CHAIN)
    // private ThirdShopWarehouseInventoryQueryService thirdShopWarehouseInventoryQueryService;

    @Reference(timeout = 30000)
    private OwnerService ownerService;

    @Autowired
    private InventorySyncEventFireBL inventorySyncEventFireBL;

    @Autowired
    private WarehouseInventoryCheckBL warehouseInventoryCheckBL;

    @Autowired
    private OrderCenterInventoryCheckBL orderCenterInventoryCheckBL;

    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;

    @Reference
    private IWarehouseInventoryReportQueryService warehouseInventoryReportQueryService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Reference
    private IProductInfoSpecificationSerivce productInfoSpecificationSerivce;

    /**
     * 根据城市id，库存对账（只保存库存对比记录，不矫正实际库存） （仓库库存 = ERP库存 - 已发货未完成库存）
     */
    public void checkStoreInventoryByWarehouseId(CheckStoreInventoryByWarehouseInfoDTO dto) {
        Integer warehouseId = dto.getWarehouseId();
        Integer opUserId = dto.getOpUserId();
        boolean isSnap = dto.getIsIsSnap();
        LOGGER.info("开始库存对账warehouseId:{}", warehouseId);
        Long dtStart = System.currentTimeMillis();
        // 1、查询仓库库存
        List<WarehouseInventoryDTO> warehouseInventoryDTOList = getWarehouseInventoryDTOList(warehouseId);

        if (warehouseInventoryDTOList != null && warehouseInventoryDTOList.size() > 0) {
            Long dtFirst = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账warehouseId: %s, -第一步查库存，耗时：%sms，共%s条", warehouseId,
                    (dtFirst - dtStart), warehouseInventoryDTOList.size()));

            processSyncRecordByList(dto, warehouseInventoryDTOList, dtFirst);
            warehouseInventoryDTOList.clear();
        }
        LOGGER.info("结束库存对账warehouseId:{}", warehouseId);
    }

    private void processSyncRecordByList(CheckStoreInventoryByWarehouseInfoDTO dto,
                                         List<WarehouseInventoryDTO> warehouseInventoryDTOList, Long dtFirst) {
        Integer warehouseId = dto.getWarehouseId();
        Integer opUserId = dto.getOpUserId();
        boolean isSnap = dto.getIsIsSnap();
        if (warehouseInventoryDTOList != null && !CollectionUtils.isEmpty(warehouseInventoryDTOList)) {

            Integer cityId = warehouseInventoryDTOList.get(0).getCityId();

            // 2、将仓库库存中的大宗库存合并到酒批库存中
            mergeWarehouseInventoryOfLarge(warehouseInventoryDTOList);
            Long dtSecond = System.currentTimeMillis();
            LOGGER
                    .info(String.format("根据ERP库存对账-第二步查合并大宗库存warehouseId: %s，耗时：%sms", warehouseId, (dtSecond - dtFirst)));

            Map<Integer, Boolean> openCenterMap = orderCenterInventoryCheckBL
                    .openOrderCenterMap(Collections.singletonList(warehouseId), dto.getVersion());
            Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount =
                    orderCenterInventoryCheckBL.getOpenCenterCount(Collections.singletonList(warehouseId), openCenterMap);

            LOGGER.info("获取已发货未完成的数量 openCenterMap : {}", JSON.toJSONString(openCenterMap));
            LOGGER.info("获取已发货未完成的数量 openCenterCount : {}", JSON.toJSONString(openCenterCount));
            // 3-1、获取已发货未完成的数量
            Map<String, BigDecimal> deliveryCountMap =
                    isSnap ? new HashMap<>(16) : getDeliveryCountMap(cityId, warehouseId, openCenterCount);
            LOGGER.info("获取已发货未完成的数量 getDeliveryCountMap: {}", JSON.toJSONString(deliveryCountMap));
            Long dtThird_1 = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第三-1步查已发货未完成数量warehouseId: %s，耗时：%sms，共%s条", warehouseId,
                    (dtThird_1 - dtSecond), deliveryCountMap.size()));

            // 3-2、获取已收货的数量
            Map<String, BigDecimal> getDeliveryedReturnCountMap =
                    isSnap ? new HashMap<>(16) : getDeliveryedReturnCountMap(cityId, openCenterMap, openCenterCount);
            LOGGER.info("获取已发货未完成的数量 getDeliveryedReturnCountMap : {}", JSON.toJSONString(getDeliveryedReturnCountMap));
            Long dtThird_2 = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第三-2步查已入库未收款退货单数量warehouseId: %s，耗时：%sms，共%s条", warehouseId,
                    (dtThird_2 - dtThird_1), getDeliveryedReturnCountMap.size()));

            // 3-3、获取在途库存数量
            Map<String, BigDecimal> allotDeliveryingCountMap =
                    isSnap ? new HashMap<>(16) : getAllotDeliveryingCountMap(warehouseId, warehouseInventoryDTOList);
            Long dtThird_3 = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第三-3步获取在途库存数量warehouseId: %s，耗时：%sms，共%s条", warehouseId,
                    (dtThird_3 - dtThird_2), allotDeliveryingCountMap.size()));

            // 4、查询ERP库存
            Map<String, ERPStoreVO> erpStoreMap = isSnap ? new HashMap<>(16) : getERPStoreVOMap(warehouseId);
            Long dtFourth = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第四步查ERP库存warehouseId: %s，耗时：%sms，共%s条", warehouseId,
                    (dtFourth - dtThird_3), erpStoreMap.size()));

            // 5、获取仓库库存对比记录
            List<InventorySyncRecordDTO> inventorySyncRecordDTOS =
                    getModWarehouseInventoryDTOS(warehouseInventoryDTOList, deliveryCountMap, getDeliveryedReturnCountMap,
                            allotDeliveryingCountMap, erpStoreMap, opUserId, isSnap);
            Long dtFive = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第五步获取库存对比记录cityId: %s，数量：%s，原始库存数量：%s,耗时：%sms", cityId,
                    inventorySyncRecordDTOS.size(), warehouseInventoryDTOList.size(), (dtFive - dtFourth)));

            deliveryCountMap.clear();
            getDeliveryedReturnCountMap.clear();
            erpStoreMap.clear();

            // 6、新增库存对账记录
            insertInventorySyncRecord(inventorySyncRecordDTOS);
            Long dtSix = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第六步新增库存对账记录warehouseId: %s，数量：%s,耗时：%sms", warehouseId,
                    inventorySyncRecordDTOS.size(), (dtSix - dtFive)));

            warehouseInventoryDTOList.clear();
            inventorySyncRecordDTOS.clear();
        }
    }

    /**
     * 获取已发货未完成的数量
     *
     * @param cityId
     * @return
     */
    private Map<String, BigDecimal> getDeliveryedReturnCountMap(Integer cityId, Map<Integer, Boolean> openCenterMap,
                                                                Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount) {
        // LOGGER.info("获取已发货未完成的数量: {}", JSON.toJSONString(lstDeliveryedCount));
        Map<String, BigDecimal> deliveryedReturnCountMap = new HashMap<>(16);
        if (CollectionUtils.isEmpty(openCenterCount)) {
            return deliveryedReturnCountMap;
        }

        List<OrderCenterUnConfirmOrderInventoryResultDTO> list = new ArrayList<>();
        for (Map.Entry<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> entry : openCenterCount.entrySet()) {
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                list.addAll(entry.getValue());
            }
        }

        // list = list.stream().filter(m ->
        // OrderCenterInventoryOrderTypeConstants.TYPE_IN.equals(m.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return deliveryedReturnCountMap;
        }
        list.forEach(n -> {
            deliveryedReturnCountMap.put(String.format("%s-%s-%s-%s", n.getProductSpecificationId(), n.getOwnerId(),
                    n.getWarehouseId(), n.getSecOwnerId()), BigDecimal.ZERO);
        });

        return deliveryedReturnCountMap;
    }

    /**
     * 获取在途库存数量
     *
     * @return
     */
    private Map<String, BigDecimal> getAllotDeliveryingCountMap(Integer warehouseId,
                                                                List<WarehouseInventoryDTO> warehouseStoreDTOList) {
        Map<String, BigDecimal> allotDeliveryingCountMap = new HashMap<>(16);
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包
        return allotDeliveryingCountMap;
    }

    /**
     * 新增校正库存记录
     */
    private void insertInventorySyncRecord(List<InventorySyncRecordDTO> inventorySyncRecordDTOS) {
        if (CollectionUtils.isEmpty(inventorySyncRecordDTOS)) {
            return;
        }
        List<Long> lstSecOwnerIds = inventorySyncRecordDTOS.stream().filter(p -> p.getSecOwnerId() != null)
                .map(p -> p.getSecOwnerId()).distinct().collect(Collectors.toList());
        List<OwnerDTO> ownerDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lstSecOwnerIds)) {
            ownerDTOS = ownerService.listOwnerByIds(lstSecOwnerIds);
        }
        List<List<InventorySyncRecordDTO>> lists =
                InventoryConvertor.splitInventorySyncRecordList(inventorySyncRecordDTOS, 200);
        for (List<InventorySyncRecordDTO> list : lists) {
            try {
                inventorySyncRecordBL.saveInventorySyncRecordList(list);
                inventorySyncEventFireBL.inventorySyncEvent(list, ownerDTOS);
                // list.clear();
            } catch (Exception e) {
                LOGGER.error("新增校正库存记录异常：", e);
                LOGGER.error("新增校正库存记录异常List: {}", JSON.toJSONString(list));
            }
        }
        lists.clear();
        inventorySyncRecordDTOS.clear();
    }

    // private void updateDeliveryStoreRecord(List<WarehouseInventoryDTO> lstResult) {
    // List<List<WarehouseInventoryDTO>> lists = InventoryConvertor.splitInventoryList(lstResult, 500);
    // for (List<WarehouseInventoryDTO> list : lists) {
    // try {
    // //批量更新已发货总数量
    // updateDeliveryStoreRecordByDTOS(list);
    // list.clear();
    // } catch (Exception oe) {
    // LOGGER.info(String.format("更新已发货数量出错:%s，错误信息:%s", JSON.toJSONString(list), oe.getMessage()));
    // }
    // }
    // }

    /**
     * 获取有差异的仓库库存 （全量数据存档）
     *
     * @return
     */
    private List<InventorySyncRecordDTO> getModWarehouseInventoryDTOS(
            List<WarehouseInventoryDTO> warehouseInventoryDTOList, Map<String, BigDecimal> deliveryCountMap,
            Map<String, BigDecimal> getDeliveryedReturnCountMap, Map<String, BigDecimal> allotDeliveryingCountMap,
            Map<String, ERPStoreVO> erpStoreMap, Integer opUserId, boolean isSnap) {
        Map<String, InventorySyncRecordDTO> lstResult = new HashMap<>(16);
        Date now = new Date();
        // 创建久批库存的键映射，用于稍后检查ERP中有但久批中没有的情况
        Map<String, WarehouseInventoryDTO> warehouseInventoryMap = new HashMap<>(16);

        warehouseInventoryDTOList.forEach(p -> {
            // 已存在相同库存ID的跳过，避免重复处理
            if (lstResult.containsKey(p.getId())) {
                return;
            }
            String remark = "ERP库存对账";
            
            // 为久批库存对象设置ERP二级货主ID
            if (p.getSecOwnerId() != null) {
                String wmsOwnerId = p.getSecOwnerId().toString();
                p.setErpSecOwnerId(erpToWmsOwnerMap.entrySet().stream()
                        .filter(entry -> entry.getValue().contains(wmsOwnerId))
                        .map(Map.Entry::getKey)
                        .findFirst()
                        .orElse(null));

                // 07-24 线上问题，久批存在1,2两条二级货主的库存，erp存在货主=2的库存，久批存在货主1的ERP货主ID=2，最终库存对账产生2条记录，都匹配到了ERP库存
                // 这种情况不应该处理兼容逻辑，没匹配到二级货主的，直接跳过
                if (StringUtils.isEmpty(p.getErpSecOwnerId())) {
                    LOGGER.info("为久批库存对象设置ERP二级货主ID失败，wmsOwnerId:{}", wmsOwnerId);
                    return;
                }
            }
            
            String identityKeyByErpSecOwnerId = String.format("%s-%s-%s-%s", p.getProductSpecId(), p.getOwnerId(),
                    p.getWarehouseId(), p.getErpSecOwnerId());
            String identityKeyByScmSecOwnerId = String.format("%s-%s-%s-%s", p.getProductSpecId(), p.getOwnerId(),
                    p.getWarehouseId(), p.getSecOwnerId());
            // 将久批库存加入映射
            warehouseInventoryMap.put(identityKeyByErpSecOwnerId, p);

            if (!erpStoreMap.containsKey(identityKeyByErpSecOwnerId) && !isSnap) {
                if (!Objects.equals(p.getOwnerId(), null)
                        || BigDecimal.ZERO.compareTo(p.getWarehouseTotalCount()) == 0) {
                    return;
                }
                remark += "-ERP不存在";
            }
            ERPStoreVO erpStoreVO = erpStoreMap.get(identityKeyByErpSecOwnerId);
            BigDecimal erpRealCount = erpStoreVO == null ? BigDecimal.ZERO : erpStoreVO.getErpRealCount();
            BigDecimal orderUnDeliveryCount = deliveryCountMap.get(identityKeyByScmSecOwnerId) == null ? BigDecimal.ZERO
                    : deliveryCountMap.get(identityKeyByScmSecOwnerId);
            BigDecimal orderUnReturnCount = getDeliveryedReturnCountMap.get(identityKeyByScmSecOwnerId) == null
                    ? BigDecimal.ZERO : getDeliveryedReturnCountMap.get(identityKeyByScmSecOwnerId);
            BigDecimal allotDeliveryingCount = allotDeliveryingCountMap.get(identityKeyByScmSecOwnerId) == null
                    ? BigDecimal.ZERO : allotDeliveryingCountMap.get(identityKeyByScmSecOwnerId);
            BigDecimal realStoreCount =
                    erpRealCount.subtract(orderUnDeliveryCount).add(orderUnReturnCount).subtract(allotDeliveryingCount);
            // LOGGER.info(String.format(
            // "WarehouseId:
            // %s,Sku：%s,Store:%s,TMSDeliveryedCount:%s,TMSUnfinishedReturnCount:%s,allotDeliveryingCount:%s,erpCount:%s,realCount:%s,ERPDetail:%s",
            // p.getWarehouseId(), p.getProductSkuId(), p.getWarehouseTotalCount(), orderUnDeliveryCount,
            // orderUnReturnCount, allotDeliveryingCount, erpRealCount, realStoreCount,
            // JSON.toJSONString(erpStoreVO)));

            if (!isSnap) {
                // 仓库库存(差异值)
                p.setDiffTotalCount(realStoreCount.subtract(p.getWarehouseTotalCount()));
                // 假如差异绝对值小于0.01，设置为0
                if (p.getDiffTotalCount().abs().compareTo(new BigDecimal(MIN_DIFF_NUM)) < 0) {
                    p.setDiffTotalCount(BigDecimal.ZERO);
                }
            } else {
                p.setDiffTotalCount(BigDecimal.ZERO);
            }

            // 校正记录
            InventorySyncRecordDTO inventorySyncRecordDTO = getInventorySyncRecordDTO(p, erpRealCount,
                    orderUnDeliveryCount, opUserId, now, remark, allotDeliveryingCount);
            if (isSnap) {
                if (inventorySyncRecordDTO.getRemark() == null) {
                    inventorySyncRecordDTO.setRemark("库存快照");
                } else {
                    inventorySyncRecordDTO.setRemark(inventorySyncRecordDTO.getRemark() + "-库存快照");
                }
            }
            lstResult.put(p.getId(), inventorySyncRecordDTO);
        });

        // 处理ERP库存中存在但久批库存中不存在的情况
        if (!isSnap) {
            Map<String, InventorySyncRecordDTO> lstNoResult = new HashMap<>(16);
            
            for (Map.Entry<String, ERPStoreVO> entry : erpStoreMap.entrySet()) {
                String key = entry.getKey();
                ERPStoreVO erpStoreVO = entry.getValue();
                
                // 直接检查久批库存中是否存在该ERP记录
                // 不需要再检查不同的映射，因为现在久批库存的key也是用ERP的二级货主ID构建的
                boolean existsInWarehouse = warehouseInventoryMap.containsKey(key);

                // 如果确实不存在于久批库存中，且ERP库存不为零，则创建差异记录
                if (!existsInWarehouse &&
                        erpStoreVO.getErpRealCount() != null &&
                        erpStoreVO.getErpRealCount().compareTo(BigDecimal.ZERO) > 0) {

                    // 直接从ERPStoreVO中获取值
                    WarehouseInventoryDTO virtualDTO = new WarehouseInventoryDTO();
                    virtualDTO.setId(String.valueOf(UUIDGenerator.getUUID("WarehouseInventoryDTO")));

                    // 从ERPStoreVO中获取值，如果为null则设为默认值
                    virtualDTO.setProductSpecId(erpStoreVO.getProductSpecificationId());
                    virtualDTO.setOwnerId(erpStoreVO.getOwnerId());
                    virtualDTO.setWarehouseId(erpStoreVO.getWarehouseId());
                    // 设置ERP原始的二级货主ID
                    virtualDTO.setErpSecOwnerId(erpStoreVO.getSecOwnerId());
                    
                    // 优先使用映射的久批货主ID，如果有的话
                    if (!StringUtils.isEmpty(erpStoreVO.getSecOwnerId()) && 
                            erpToWmsOwnerMap.containsKey(erpStoreVO.getSecOwnerId()) && 
                            !CollectionUtils.isEmpty(erpToWmsOwnerMap.get(erpStoreVO.getSecOwnerId()))) {
                        // 选择第一个映射的久批货主ID
                        String firstWmsOwnerId = erpToWmsOwnerMap.get(erpStoreVO.getSecOwnerId()).get(0);
                        virtualDTO.setSecOwnerId(StringUtils.isEmpty(firstWmsOwnerId) ? null : Long.parseLong(firstWmsOwnerId));
                    } else {
                        virtualDTO.setSecOwnerId(null);
                    }

                    virtualDTO.setWarehouseTotalCount(BigDecimal.ZERO); // 久批库存为0
                    virtualDTO.setPackageQuantity(erpStoreVO.getPackageQuantity());
                    virtualDTO.setProductSkuId(erpStoreVO.getProductSkuId());

                    // 使用横线替代未知的值
                    virtualDTO.setProductName(erpStoreVO.getProductName());
                    virtualDTO.setSpecificationName("-");

                    // 计算差异值 = ERP实际库存 - 0
                    BigDecimal orderUnDeliveryCount = deliveryCountMap.get(key) == null ?
                            BigDecimal.ZERO : deliveryCountMap.get(key);
                    BigDecimal orderUnReturnCount = getDeliveryedReturnCountMap.get(key) == null ?
                            BigDecimal.ZERO : getDeliveryedReturnCountMap.get(key);
                    BigDecimal allotDeliveryingCount = allotDeliveryingCountMap.get(key) == null ?
                            BigDecimal.ZERO : allotDeliveryingCountMap.get(key);
                    BigDecimal realStoreCount = erpStoreVO.getErpRealCount()
                            .subtract(orderUnDeliveryCount)
                            .add(orderUnReturnCount)
                            .subtract(allotDeliveryingCount);

                    // 差异=ERP库存-0
                    virtualDTO.setDiffTotalCount(realStoreCount);
                    // 假如差异绝对值小于0.01，设置为0
                    if (virtualDTO.getDiffTotalCount().abs().compareTo(new BigDecimal(MIN_DIFF_NUM)) < 0) {
                        virtualDTO.setDiffTotalCount(BigDecimal.ZERO);
                    }

                    // 创建校正记录
                    String remark = "按仓库库存对账-久批不存在";
                    InventorySyncRecordDTO inventorySyncRecordDTO = getInventorySyncRecordDTO(
                            virtualDTO, erpStoreVO.getErpRealCount(), orderUnDeliveryCount,
                            opUserId, now, remark, allotDeliveryingCount);

                    // 添加到结果中
                    lstNoResult.put(virtualDTO.getId(), inventorySyncRecordDTO);
                }
            }

            //补充缺失数据
            if (!CollectionUtils.isEmpty(lstNoResult)) {

                // 补充城市ID信息
                List<Integer> lstWarehouseIds = lstNoResult.values().stream()
                        .filter(p -> p.getWarehouseId() != null)
                        .map(InventorySyncRecordDTO::getWarehouseId)
                        .distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(lstWarehouseIds)) {
                    List<Warehouse> warehouses = warehouseQueryService.listWarehouseByIds(lstWarehouseIds);
                    warehouses.forEach(warehouse -> {
                        lstNoResult.values().forEach(p -> {
                            if (Objects.equals(p.getWarehouseId(), warehouse.getId())) {
                                p.setOrgId(warehouse.getCityId());
                            }
                        });
                    });
                }

                // 补充SKU信息
                List<Long> lstSpecId = lstNoResult.values().stream()
                        .map(InventorySyncRecordDTO::getProductSpecificationId)
                        .distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(lstSpecId)) {
                    List<ProductInfoSpecificationDTO> lstProductInfo = productInfoSpecificationSerivce.findByProductInfoIds(lstSpecId);
                    lstProductInfo.forEach(productInfoSpecificationDTO -> {
                        lstNoResult.values().forEach(p -> {
                            if (Objects.equals(p.getProductSpecificationId(), productInfoSpecificationDTO.getId())) {
                                p.setProductName(productInfoSpecificationDTO.getProductName());
                                p.setPackageQuantity(productInfoSpecificationDTO.getPackageQuantity());
                                p.setSpecName(productInfoSpecificationDTO.getName());
                            }
                        });
                    });
                }
            }
            lstResult.putAll(lstNoResult);
        }

        return new ArrayList<>(lstResult.values());
    }

    public static final String MIN_DIFF_NUM = "0.01";

    /**
     * 获取校正记录
     *
     * @return
     */
    private InventorySyncRecordDTO getInventorySyncRecordDTO(WarehouseInventoryDTO p, BigDecimal erpRealCount,
                                                             BigDecimal orderUnDeliveryCount, Integer opUserId, Date now, String remark, BigDecimal allotDeliveryingCount) {
        InventorySyncRecordDTO inventorySyncRecordDTO = new InventorySyncRecordDTO();
        inventorySyncRecordDTO.setId(UUIDGenerator.getUUID("InventorySyncRecord"));
        inventorySyncRecordDTO.setOrgId(p.getCityId());
        inventorySyncRecordDTO.setWmsDeliveryedCount(BigDecimal.ZERO);
        inventorySyncRecordDTO.setProductStoreId(p.getId());
        inventorySyncRecordDTO.setWarehouseId(p.getWarehouseId());
        inventorySyncRecordDTO.setTmsDeliveryedCount(orderUnDeliveryCount);
        inventorySyncRecordDTO.setErpRealCount(erpRealCount);
        inventorySyncRecordDTO.setStoreCountMinUnit(p.getWarehouseTotalCount());
        // 差异数量绝对值小于0.01的忽略掉
        if (p.getDiffTotalCount().abs().compareTo(new BigDecimal(MIN_DIFF_NUM)) < 0) {
            inventorySyncRecordDTO.setDiffTotalCount(BigDecimal.ZERO);
        } else {
            inventorySyncRecordDTO.setDiffTotalCount(p.getDiffTotalCount());
        }
        BigDecimal[] diffCountInfo = BigDecimalUtils.divideAndRemainderByDecimalScale6AndRoundUpWithZero(
                inventorySyncRecordDTO.getDiffTotalCount(), p.getPackageQuantity());
        inventorySyncRecordDTO.setDiffMaxCount(diffCountInfo[0]);
        inventorySyncRecordDTO.setDiffMinCount(diffCountInfo[1]);
        inventorySyncRecordDTO.setProductSkuId(p.getProductSkuId());
        inventorySyncRecordDTO.setProductSpecificationId(p.getProductSpecId());
        inventorySyncRecordDTO.setProductName(p.getProductName());
        inventorySyncRecordDTO.setSpecName(p.getSpecificationName());
        inventorySyncRecordDTO.setPackageQuantity(p.getPackageQuantity());
        inventorySyncRecordDTO.setOwnerId(p.getOwnerId());
        inventorySyncRecordDTO.setSecOwnerId(p.getSecOwnerId());
        inventorySyncRecordDTO.setStoreType(StoreTypeEnum.仓库库存.getType());
        inventorySyncRecordDTO.setCreateTime(now);
        inventorySyncRecordDTO.setCreateUserId(opUserId);
        if (inventorySyncRecordDTO.getDiffTotalCount().compareTo(BigDecimal.ZERO) != 0) {
            inventorySyncRecordDTO.setDiff(CheckDiffEnum.有差异.getType());
            inventorySyncRecordDTO.setState(CheckStateEnum.未处理.getType());
        } else {
            inventorySyncRecordDTO.setDiff(CheckDiffEnum.无差异.getType());
            inventorySyncRecordDTO.setState(CheckStateEnum.不需要处理.getType());
        }
        inventorySyncRecordDTO.setRemark(remark);
        if (allotDeliveryingCount != null && allotDeliveryingCount.compareTo(BigDecimal.ZERO) != 0) {
            inventorySyncRecordDTO.setRemark(inventorySyncRecordDTO.getRemark() + "-在途库存：" + allotDeliveryingCount);
        }
        return inventorySyncRecordDTO;
    }

    /**
     * 查询ERP库存
     *
     * @param warehouseId
     * @return
     */
    private Map<String, ERPStoreVO> getERPStoreVOMap(Integer warehouseId) {
        List<ERPStoreVO> lstErpStore_YJP = InventoryConvertor.getErpStoreCountByWarehouse(warehouseId, 0);
        // LOGGER.info("获取ERP库存_YJP: {}", JSON.toJSONString(lstErpStore_YJP));
        List<ERPStoreVO> lstErpStore_YK = InventoryConvertor.getErpStoreCountByWarehouse(warehouseId, 1);
        // LOGGER.info("获取ERP库存_YK: {}", JSON.toJSONString(lstErpStore_YK));
        List<ERPStoreVO> lstErpStore = new ArrayList<>();
        lstErpStore.addAll(lstErpStore_YJP);
        lstErpStore.addAll(lstErpStore_YK);
        Map<String, ERPStoreVO> erpStoreMap = new HashMap<>(16);
        // 存储ERP二级货主ID到久批货主ID的映射关系，用于后续检查
        Map<String, List<String>> erpToWmsOwnerMap = new HashMap<>(16);
        
        if (!CollectionUtils.isEmpty(lstErpStore)) {
            List<String> secOwnerIdList = lstErpStore.stream().filter(p -> !StringUtils.isEmpty(p.getSecOwnerId()))
                    .map(p -> p.getSecOwnerId()).collect(Collectors.toList());
            Map<String, Long> secOwnerIdMap = new HashMap<>(16);
            if (!CollectionUtils.isEmpty(secOwnerIdList)) {
                secOwnerIdMap = ownerService.getOwnerIdMap(secOwnerIdList);
                
                // 构建ERP二级货主ID到久批货主ID的映射关系
                for (Map.Entry<String, Long> entry : secOwnerIdMap.entrySet()) {
                    if (entry.getKey() != null && entry.getValue() != null) {
                        String erpSecOwnerId = entry.getKey();
                        if (!erpToWmsOwnerMap.containsKey(erpSecOwnerId)) {
                            erpToWmsOwnerMap.put(erpSecOwnerId, new ArrayList<>());
                        }
                        erpToWmsOwnerMap.get(erpSecOwnerId).add(entry.getValue().toString());
                    }
                }
            }
            
            for (ERPStoreVO p : lstErpStore) {
                // 直接使用ERP原始的二级货主ID作为key的一部分
                String key = String.format("%s-%s-%s-%s", p.getProductSpecificationId(), p.getOwnerId(),
                        p.getWarehouseId(), p.getSecOwnerId());
                
                if (erpStoreMap.containsKey(key)) {
                    ERPStoreVO vo = erpStoreMap.get(key);
                    if (null != vo) {
                        p.setErpRealCount(p.getErpRealCount().add(vo.getErpRealCount()));
                    }
                }
                erpStoreMap.put(key, p);
            }
            lstErpStore_YJP.clear();
            lstErpStore_YK.clear();
            lstErpStore.clear();
        }
        // 将映射关系保存在一个静态变量或类属性中，供getModWarehouseInventoryDTOS方法使用
        this.erpToWmsOwnerMap = erpToWmsOwnerMap;
        return erpStoreMap;
    }

    /**
     * 获取已发货未完成的数量
     *
     * @param cityId
     * @return
     */
    private Map<String, BigDecimal> getDeliveryCountMap(Integer cityId, Integer warehouseId,
                                                        Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount) {
        Map<String, BigDecimal> deliveryCountMap = new HashMap<>(16);

        if (CollectionUtils.isEmpty(openCenterCount)) {
            return deliveryCountMap;
        }
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包
        List<OrderCenterUnConfirmOrderInventoryResultDTO> list = Stream.of(warehouseId).map(openCenterCount::get)
                .filter(m -> !CollectionUtils.isEmpty(m)).flatMap(m -> m.stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return deliveryCountMap;
        }

        // list = list.stream().filter(m ->
        // OrderCenterInventoryOrderTypeConstants.TYPE_OUT.equals(m.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return deliveryCountMap;
        }
        list.forEach(n -> {
            deliveryCountMap.put(String.format("%s-%s-%s-%s", n.getProductSpecificationId(), n.getOwnerId(),
                    n.getWarehouseId(), n.getSecOwnerId()), n.getCount());
        });

        return deliveryCountMap;
    }

    /**
     * 将仓库库存中的大宗库存合并到酒批库存中
     *
     * @param warehouseInventoryDTOList
     */
    private void mergeWarehouseInventoryOfLarge(List<WarehouseInventoryDTO> warehouseInventoryDTOList) {
        for (Iterator<WarehouseInventoryDTO> it = warehouseInventoryDTOList.iterator(); it.hasNext(); ) {
            WarehouseInventoryDTO p = it.next();
            if (Objects.equals(p.getChannel(), 1)) {
                String productSkuIdWarehouseId =
                        String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId());
                Optional<WarehouseInventoryDTO> inventoryDTO = warehouseInventoryDTOList.stream()
                        .filter(q -> Objects.equals(q.getChannel(), 0)
                                && String.format("%s-%s-%s", q.getProductSpecId(), q.getOwnerId(), q.getWarehouseId())
                                .equals(productSkuIdWarehouseId))
                        .findAny();
                inventoryDTO.ifPresent(warehouseInventoryDTO -> warehouseInventoryDTO.setWarehouseTotalCount(
                        p.getWarehouseTotalCount().add(warehouseInventoryDTO.getWarehouseTotalCount())));
                // 移除大宗库存
                it.remove();
            }
        }
    }

    /**
     * 将仓库库存中同规格同货主的供应商库存合并
     */
    private List<WarehouseInventoryDTO>
    mergeWarehouseStoreOfSecOwnerId(List<WarehouseInventoryDTO> warehouseStoreDTOList) {
        List<WarehouseInventoryDTO> mergeWarehouseStoreList = new ArrayList<>();

        // 按规格id+货主id+仓库id分组
        Map<String, List<WarehouseInventoryDTO>> warehouseStoreMap = warehouseStoreDTOList.stream().collect(Collectors
                .groupingBy(p -> String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId())));
        warehouseStoreMap.forEach((str, list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            WarehouseInventoryDTO warehouseInventoryDTO = list.get(0);
            BigDecimal count =
                    list.stream().map(p -> p.getWarehouseTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            warehouseInventoryDTO.setWarehouseTotalCount(count);
            warehouseInventoryDTO.setSecOwnerId(null);
            mergeWarehouseStoreList.add(warehouseInventoryDTO);
        });
        return mergeWarehouseStoreList;
    }

    /**
     * 根据城市idc查询仓库库存
     *
     * @param warehouseId
     * @return
     */
    private List<WarehouseInventoryDTO> getWarehouseInventoryDTOList(Integer warehouseId) {
        // WarehouseInventoryQueryDTO queryDTO = new WarehouseInventoryQueryDTO();
        // queryDTO.setWarehouseId(warehouseId);
        // PageList<WarehouseInventoryDTO> pageList =
        // warehouseInventoryQueryBL.listWarehouseInventoryBySpecInfo(queryDTO, new PagerCondition(1,
        // Integer.MAX_VALUE));
        // return pageList.getDataList();
        List<WarehouseInventoryDTO> lstResult = new ArrayList<>();
        WarehouseInventoryReportQueryDTO queryDTO = new WarehouseInventoryReportQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        // 遍历剩余的页数
        Integer totalPage = 1;
        int pageSize = 5000;
        for (int i = 1; i <= totalPage; i++) {
            // if (i == 1) {
            // PageHelper.startPage(i, pageSize);
            // } else {
            // PageHelper.startPage(i, pageSize, false);
            // }
            queryDTO.setPageNum(i);
            queryDTO.setPageSize(pageSize);
            queryDTO.setProductTypeList(Arrays.asList((int) ProductTypeEnums.包装材料.getType(),
                    (int) ProductTypeEnums.半成品.getType(), (int) ProductTypeEnums.成品.getType()));
            PageList<WarehouseInventoryDTO> pagerResult = ProductStoreConverter.warehouseInventoryListToReportPageList(
                    warehouseInventoryReportQueryService.listWarehouseInventoryByWarehouseId(queryDTO));

            if (pagerResult != null && pagerResult.getDataList() != null && pagerResult.getDataList().size() > 0) {
                if (i == 1) {
                    totalPage = pagerResult.getPager().getTotalPage();
                }
                lstResult.addAll(pagerResult.getDataList());
                pagerResult.getDataList().clear();
            }
        }
        return lstResult;
    }

// /**
// * 批量更新已发货总数量
// *
// * @param cityMergeDTOList
// */
// private void updateDeliveryStoreRecordByDTOS(List<WarehouseInventoryDTO> cityMergeDTOList) {
// if (!CollectionUtils.isEmpty(cityMergeDTOList)) {
// try {
// List<DeliveryStoreRecordDTO> lstTmp = cityMergeDTOList.stream().map(p -> {
// DeliveryStoreRecordDTO dto = new DeliveryStoreRecordDTO();
// dto.setId(p.getId());
// dto.setDeliveryedCount(p.getDeliveryedCount());
// return dto;
// }).collect(Collectors.toList());
// deliveryStoreRecordBL.updateDeliveryStoreRecordBatch(lstTmp);
// } catch (Exception oe) {
// LOGGER.error("更新已发货未完成数量出错！" + oe.getMessage(), oe);
// }
// }
// }

    /**
     * 根据易款店仓库存，存库存对账（只保存库存对比记录，不矫正实际库存）
     */
    public void checkStoreInventoryByEasyChain(Integer warehouseId, Integer opUserId) {
        LOGGER.info("[根据易款店仓库存]开始库存对账warehouseId:{}", warehouseId);
        Long dtStart = System.currentTimeMillis();
        // 1、查询仓库库存
        List<WarehouseInventoryDTO> warehouseInventoryDTOList = getWarehouseInventoryDTOList(warehouseId);
        Long dtFirst = System.currentTimeMillis();
        LOGGER.info(String.format("[根据易款店仓库存]库存对账-第一步查库存warehouseId: %s，耗时：%sms，共%s条", warehouseId, (dtFirst - dtStart),
                warehouseInventoryDTOList != null ? warehouseInventoryDTOList.size() : 0));

        if (warehouseInventoryDTOList != null && !warehouseInventoryDTOList.isEmpty()) {
            // 将仓库库存中的大宗库存合并到酒批库存中
            mergeWarehouseInventoryOfLarge(warehouseInventoryDTOList);
            // 将仓库库存中同规格同货主的供应商库存合并
            warehouseInventoryDTOList = mergeWarehouseStoreOfSecOwnerId(warehouseInventoryDTOList);

            // 2、查询易款店仓库存
            Map<String, BigDecimal> easychainStoreMap = getEasyChainStoreMap(warehouseId);
            Long dtSecond = System.currentTimeMillis();
            LOGGER.info(String.format("[根据易款店仓库存]库存对账-第二步查易款店仓库存warehouseId: %s，耗时：%sms，共%s条", warehouseId,
                    (dtSecond - dtFirst), easychainStoreMap.size()));

            // 3、获取仓库库存对比记录
            List<InventorySyncRecordDTO> inventorySyncRecordDTOS =
                    getInventorySyncRecordDTOS(warehouseInventoryDTOList, easychainStoreMap, opUserId);
            Long dtThird = System.currentTimeMillis();
            LOGGER.info(String.format("[根据易款店仓库存]库存对账-第三步获取库存对比记录warehouseId: %s，耗时：%sms，共%s条", warehouseId,
                    (dtThird - dtSecond), inventorySyncRecordDTOS.size()));

            warehouseInventoryDTOList.clear();
            easychainStoreMap.clear();

            // 4、新增库存对账记录
            insertInventorySyncRecord(inventorySyncRecordDTOS);
            inventorySyncRecordDTOS.clear();

            Long dtFourth = System.currentTimeMillis();
            LOGGER.info(
                    String.format("[根据易款店仓库存]库存对账-第四步新增库存对账记录warehouseId: %s，耗时：%sms", warehouseId, (dtFourth - dtThird)));
        }
        LOGGER.info("[根据易款店仓库存]结束库存对账warehouseId:{}", warehouseId);
    }

    /**
     * 获取易款店仓库存
     *
     * @return
     */
    private Map<String, BigDecimal> getEasyChainStoreMap(Integer warehouseId) {
        Map<String, BigDecimal> storeMap = new HashMap<>(16);
        // 每页条数
        Integer pageSize = 50;
        Integer pageNum = 1;
        for (int i = 1; i <= pageNum; i++) {
            PageList<ShopWarehouseInventoryDTO> pageList = getEasyChainStore(warehouseId, i, pageSize);
            if (pageList != null && pageList.getDataList() != null) {
                pageList.getDataList().forEach(p -> {
                    String key = String.format("%s-%s-%s", p.getYjpProductInfoSpecId(),
                            Objects.equals(p.getOwnerId(), 0L) ? null : p.getOwnerId(), warehouseId);
                    BigDecimal count = p.getInventoryCount();
                    if (storeMap.containsKey(key)) {
                        count = count.add(storeMap.get(key));
                    }
                    storeMap.put(key, count);
                });
                pageNum = pageList.getPager().getTotalPage();
                pageList.getDataList().clear();
            }
        }
        return storeMap;
    }

    private PageList<ShopWarehouseInventoryDTO> getEasyChainStore(Integer warehouseId, Integer page, Integer size) {
        // ThirdShopWarehouseInventoryQuery query = new ThirdShopWarehouseInventoryQuery();
        // query.setWarehouseId(warehouseId.longValue());
        // query.setPageIndex(page);
        // query.setPageSize(size);
        // PageList<ShopWarehouseInventoryDTO> pageList =
        // thirdShopWarehouseInventoryQueryService.listShopWarehouseInventory(query);
        // // LOGGER.info("获取易款店仓库存：{}, 参数：{}", JSON.toJSONString(pageList), JSON.toJSONString(query));
        // if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
        // return null;
        // }
        // return pageList;
        return null;
    }

    /**
     * 获取有差异的仓库库存 （全量数据存档）
     *
     * @return
     */
    private List<InventorySyncRecordDTO> getInventorySyncRecordDTOS(
            List<WarehouseInventoryDTO> warehouseInventoryDTOList, Map<String, BigDecimal> easychainStoreMap,
            Integer opUserId) {
        if (easychainStoreMap == null || easychainStoreMap.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        List<InventorySyncRecordDTO> lstResult = new ArrayList<>();
        Date now = new Date();
        warehouseInventoryDTOList.forEach(p -> {
            // 已存在相同库存ID的跳过，避免重复处理
            if (lstResult.stream().anyMatch(q -> q.getProductStoreId().equals(p.getId()))) {
                return;
            }
            String productSkuIdWarehouseId =
                    String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId());
            if (!easychainStoreMap.containsKey(productSkuIdWarehouseId)) {
                return;
            }
            BigDecimal easychainCount = easychainStoreMap.get(productSkuIdWarehouseId);
            LOGGER.info(String.format("WarehouseId: %s,Sku：%s,Store:%s,easychainCount:%s", p.getWarehouseId(),
                    p.getProductSkuId(), p.getWarehouseTotalCount(), easychainCount));
            // 仓库库存(差异值)
            p.setDiffTotalCount(easychainCount.subtract(p.getWarehouseTotalCount()));
            // 校正记录
            InventorySyncRecordDTO inventorySyncRecordDTO =
                    getInventorySyncRecordDTO(p, easychainCount, BigDecimal.ZERO, opUserId, now, "易款库存对账", null);
            lstResult.add(inventorySyncRecordDTO);
        });
        return lstResult;
    }

    /**
     * 仓库停用校验
     */
    public Boolean checkWarehouseDisable(Integer cityId, Integer warehouseId) {
        // 是否有库存
        Boolean isHaveInventory = warehouseInventoryQueryBL.isHaveWarehouseInventory(warehouseId);
        if (isHaveInventory) {
            LOGGER.info("[仓库停用校验]仓库有库存，城市id:{}，仓库id:{}", cityId, warehouseId);
            return false;
        }

        Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount =
                orderCenterInventoryCheckBL.getOpenCenterCount(Collections.singletonList(warehouseId));

        if (exist(openCenterCount)) {
            LOGGER.info("[仓库停用校验]仓库存在已发货未完成的，城市id:{}，仓库id:{}", cityId, warehouseId);
            return false;
        }
        return true;
    }

    private boolean exist(Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount) {
        if (CollectionUtils.isEmpty(openCenterCount)) {
            return Boolean.FALSE;
        }

        return openCenterCount.values().stream().filter(m -> !CollectionUtils.isEmpty(m))
                .anyMatch(m -> m.stream().map(OrderCenterUnConfirmOrderInventoryResultDTO::getCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) != 0);
    }
}

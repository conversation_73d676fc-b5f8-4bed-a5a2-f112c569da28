package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.framework.rabbit.delay.DelayMessageTemplate;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.DefectiveBatchInventorySyncDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/17
 */
@Component
public class DefectiveStoreBatchEventBL {
    private final static Logger LOGGER = LoggerFactory.getLogger(DefectiveStoreBatchEventBL.class);

    @Value("${ex.supplychain.defectiveInventoryChange.sync}")
    private String defectiveSync;

    @Autowired
    private DelayMessageTemplate delayMessageTemplate;

    /**
     * 残次品批次库存变动通知
     */
    public void defectiveStoreBatchChangeEvent(List<DefectiveBatchInventorySyncDTO> syncDTOS) {
        if (CollectionUtils.isEmpty(syncDTOS)) {
            return;
        }

        try {
            String payload = JSON.toJSONString(syncDTOS);
            LOGGER.info("残次品批次库存变动通知消息：{}", payload);
            delayMessageTemplate.convertAndSend(defectiveSync, null, syncDTOS,
                    Duration.ofSeconds(10));
        } catch (Exception ex) {
            LOGGER.error("延迟发送残次品批次库存变动通知消息失败！", ex);
        }
    }

}

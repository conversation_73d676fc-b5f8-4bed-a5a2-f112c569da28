package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory;

import java.math.BigDecimal;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
public class ProductStoreBatchShareBO {

    /**
     * productStoreBatch的id
     */
    private String productStoreBatchId;
    /**
     * 批次库存数量
     */
    private BigDecimal count;
    /**
     * store主键
     */
    private String productStoreId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 是否发生变更
     */
    private boolean hasChanged = Boolean.FALSE;

    public ProductStoreBatchShareBO() {}

    public ProductStoreBatchShareBO(String productStoreBatchId, BigDecimal count) {
        this.productStoreBatchId = productStoreBatchId;
        this.count = count;
    }

    /**
     * 获取 productStoreBatch的id
     *
     * @return productStoreBatchId productStoreBatch的id
     */
    public String getProductStoreBatchId() {
        return this.productStoreBatchId;
    }

    /**
     * 设置 productStoreBatch的id
     *
     * @param productStoreBatchId productStoreBatch的id
     */
    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    /**
     * 获取 批次库存数量
     *
     * @return count 批次库存数量
     */
    public BigDecimal getCount() {
        return this.count;
    }

    /**
     * 设置 批次库存数量
     *
     * @param count 批次库存数量
     */
    public void setCount(BigDecimal count) {
        this.count = count;
    }

    /**
     * 获取 store主键
     *
     * @return productStoreId store主键
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 store主键
     *
     * @param productStoreId store主键
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 货位id
     *
     * @return locationId 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     *
     * @param locationId 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     *
     * @return locationName 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     *
     * @param locationName 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 是否发生变更
     *
     * @return hasChanged 是否发生变更
     */
    public boolean isHasChanged() {
        return this.hasChanged;
    }

    /**
     * 设置 是否发生变更
     *
     * @param hasChanged 是否发生变更
     */
    public void setHasChanged(boolean hasChanged) {
        this.hasChanged = hasChanged;
    }

    public static ProductStoreBatchShareBO convert(ProductStoreBatchPO batchPO) {
        ProductStoreBatchShareBO shareBO = new ProductStoreBatchShareBO();
        shareBO.setCount(batchPO.getTotalCount());
        shareBO.setProductStoreBatchId(batchPO.getId());
        shareBO.setLocationId(batchPO.getLocationId());
        shareBO.setLocationName(batchPO.getLocationName());
        shareBO.setProductStoreId(batchPO.getProductStoreId());

        return shareBO;
    }

}

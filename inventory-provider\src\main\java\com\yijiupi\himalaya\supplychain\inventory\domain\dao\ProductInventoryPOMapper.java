package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.WarehouseInventoryYJXDTO;
import com.yijiupi.himalaya.supplychain.dto.product.WarehouseInventoryYJXQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.search.WarehouseInventoryReportSO;

@Mapper
public interface ProductInventoryPOMapper {

    void insertInventoryPOList(@Param("list") List<ProductInventoryPO> productInventoryPO);

    /**
     * 根据主键查找产品库存
     */
    ProductInventoryPO selectByPrimaryKey(String id);

    /**
     * 根据主键集合查找产品库存集合
     *
     * @param idList storeId集合
     * @return
     */
    List<ProductInventoryPO> selectInventoryListByPrimaryKey(@Param("idList") List<String> idList);

    /**
     * 通过产品SkuId和cityId集合查询库存信息
     */
    List<ProductInventoryPO> getProductInventoryBySkuIdCityId(@Param("dto") ProductSkuForInventoryDTO dto);

    /**
     * 通过skuid和仓库id查询库存记录
     */
    List<ProductInventoryPO> findProductInventoryByProductSkuIdWarehouseId(
        @Param("productSkuIdList") List<Long> productSkuIdList, @Param("warehouseId") Integer warehouseId,
        @Param("channel") Integer channel, @Param("secOwnerId") Long secOwnerId,
        @Param("eraseOwner") Integer eraseOwner);

    /**
     * 通过skuid和仓库id查询库存记录
     */
    List<ProductInventoryPO> findProductInventoryByProductSpecIdWarehouseId(
        @Param("productSpecIdList") List<Long> productSpecIdList, @Param("ownerId") Long ownerId,
        @Param("warehouseId") Integer warehouseId, @Param("channel") Integer channel,
        @Param("secOwnerId") Long secOwnerId);

    /**
     * 批量修改库存数量
     */
    void increaseWarehouseCountBatchById(@Param("list") List<ProductInventoryPO> params);

    /**
     * 查询某城市下所有SKU库存.
     *
     * @param cityId
     * @param channel 渠道
     * @param source 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    List<ProductSkuInventoryCountDTO> listProductSkuInventoyByCity(@Param("cityId") Integer cityId,
        @Param("channel") Integer channel, @Param("source") Integer source, @Param("secOwnerId") Long secOwnerId);

    /**
     * 库存报表
     *
     * @param so
     * @param pageNum 当前页
     * @param pageSize 每页显示数
     * @return
     */
    PageResult<InventoryReportDTO> findStoreReportPageByAuth(@Param("so") StockReportSO so,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询配送中数量
     *
     * @param productStoreIdList
     * @return
     */
    List<InventoryReportDTO> findDeliveryedCountByProductStoreIds(@Param("list") List<String> productStoreIdList);

    /**
     * 库存报表
     *
     * @param so
     * @param pageNum 当前页
     * @param pageSize 每页显示数
     * @return
     */
    PageResult<InventoryReportDTO> findStoreReportPageByProductSpecification(@Param("so") StockReportSO so,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 通过仓库id,查询该仓库下的所有库存信息 todo
     *
     * @param warehouseId
     * @param channel 渠道
     * @return
     */
    List<ProductInventoryPO> findProductInventoryByWarehouseId(@Param("warehouseId") Integer warehouseId,
        @Param("channel") Integer channel);

    /**
     * 查询仓库库存(分页)
     */
    PageResult<WarehouseInventoryDTO> getListWarehouseInventory(@Param("dto") WarehouseInventoryQueryDTO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询仓库库存(分页)
     */
    PageResult<WarehouseInventoryDTO> getListWarehouseInventoryBySpecInfo(@Param("dto") WarehouseInventoryQueryDTO dto,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询仓库库存(分页)
     */
    PageResult<WarehouseInventoryDTO> getListWarehouseInventoryByOwner(@Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 根据规格Id+OwnerId查询仓库库存
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    List<ProductInventoryPO> getProductInventorys(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 过城市id,所属人类型.查询某个城市下库存信息
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    PageResult<ProductInventoryPO> getProductInventorysByPager(
        @Param("dto") WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    List<ProductInventoryPO> pageFindProductInventoryByWarehouseId(
        @Param("dto") WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO, @Param("offset") Integer offset,
        @Param("limitnum") Integer limitnum);

    int pageFindProductInventoryByWarehouseIdCount(@Param("dto") WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 过城市id,所属人类型.查询某个城市下库存信息
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    PageResult<ProductInventoryPO> getProductInventorysByProductInfoSpecification(
        @Param("dto") WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 过城市id,所属人类型.查询某个城市下库存信息（包含没有仓库库存的，即没有入过库的）
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    List<ProductInventoryPO> getProductInventorysByAll(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 查找仓库库存（易经销）
     *
     * @return
     */
    PageResult<WarehouseInventoryYJXDTO>
        listWarehouseInventoryYJX(WarehouseInventoryYJXQueryDTO warehouseInventoryYJXQueryDTO);

    /**
     * 仓库库存报表
     *
     * @return
     */
    PageResult<WarehouseInventoryReportDTO>
        listWarehouseInventoryReport(WarehouseInventoryReportSO warehouseInventoryReportSO);

    /**
     * 获取待出库产品列表
     *
     * @return
     */
    PageResult<ProductWaitDeliveryDTO> listProductWaitDelivery(ProductWaitDeliverySO productWaitDeliverySO);

    /**
     * 货主查找对应的二级货主
     *
     * @return
     */
    List<ProductInventoryPO> listProductSecOwnerId(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO);

    /**
     * 货主查找对应的二级货主
     *
     * @return
     */
    List<ProductInventoryPO> listProductStore(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO);

    /**
     * 查询仓库是否有库存
     *
     * @return
     */
    Byte isHaveWarehouseInventory(@Param("warehouseId") Integer warehouseId,
        @Param("inventoryFloor") Double inventoryFloor);

    /**
     * 查询仓库库存中的产品信息
     */
    PageResult<WarehouseInventoryReportDTO> findProductInWarehouseInventory(WarehouseInventoryReportSO querySO);

    /**
     * 根据sku信息，查询仓库库存
     */
    PageResult<ProductInventoryPO> listProductStoreBySkuInfo(WarehouseInventoryReportSO queryDTO);

    /**
     * 库存报表
     *
     * @param so 查询条件
     * @param pageNum 当前页
     * @param pageSize 每页显示数
     * @return 查询结果
     */
    @Deprecated
    PageResult<InventoryReportDTO> findStoreReportPageInfoByAuth(@Param("so") StockReportSO so,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 根据规格Id+OwnerId查询仓库库存
     *
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    PageResult<ProductInventoryPO> pageListProductInventorys(WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO);

    /**
     * 将上面的查询findStoreReportPageInfoByAuth拆开
     *
     * @param so
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<InventoryReportDTO> findStoreReportPageInfoByAuthNew(@Param("so") StockReportSO so,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询平均库龄
     *
     * @param productStoreId
     * @return
     */
    List<InventoryReportDTO> findStoreReportAverageStockAge(@Param("list") List<String> productStoreId);

}

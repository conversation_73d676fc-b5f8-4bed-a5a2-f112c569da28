#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 264241152 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=19340, tid=11496
#
# JRE version:  (21.0.7+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://git.yijiupidev.com:81': 

Host: 12th Gen Intel(R) Core(TM) i5-12500, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Aug 21 01:53:47 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.007174 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000029f385898a0):  JavaThread "Unknown thread" [_thread_in_vm, id=11496, stack(0x000000b2b4300000,0x000000b2b4400000) (1024K)]

Stack: [0x000000b2b4300000,0x000000b2b4400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0x6e28c5]
V  [jvm.dll+0x6d634a]
V  [jvm.dll+0x36388b]
V  [jvm.dll+0x36b456]
V  [jvm.dll+0x3bd7e6]
V  [jvm.dll+0x3bdab8]
V  [jvm.dll+0x335fdc]
V  [jvm.dll+0x336ccb]
V  [jvm.dll+0x88b7a9]
V  [jvm.dll+0x3ca9b8]
V  [jvm.dll+0x8747f8]
V  [jvm.dll+0x45f3ce]
V  [jvm.dll+0x4610b1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffdd65cb148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x0000029f385ee210 WorkerThread "GC Thread#0"                     [id=27116, stack(0x000000b2b4400000,0x000000b2b4500000) (1024K)]
  0x0000029f385fedc0 ConcurrentGCThread "G1 Main Marker"            [id=21972, stack(0x000000b2b4500000,0x000000b2b4600000) (1024K)]
  0x0000029f385fff70 WorkerThread "G1 Conc#0"                       [id=1540, stack(0x000000b2b4600000,0x000000b2b4700000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffdd5cb90b7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffdd663fab8] Heap_lock - owner thread: 0x0000029f385898a0

Heap address: 0x0000000704e00000, size: 4018 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x0000029f4cd50000,0x0000029f4d530000] _byte_map_base: 0x0000029f49529000

Marking Bits: (CMBitMap*) 0x0000029f385ee820
 Bits: [0x0000029f4d530000, 0x0000029f513f8000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.004 Loaded shared library C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff71d070000 - 0x00007ff71d07a000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\java.exe
0x00007fff27ed0000 - 0x00007fff280e7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fff27020000 - 0x00007fff270e4000 	C:\Windows\System32\KERNEL32.DLL
0x00007fff25160000 - 0x00007fff25532000 	C:\Windows\System32\KERNELBASE.dll
0x00007fff25540000 - 0x00007fff25651000 	C:\Windows\System32\ucrtbase.dll
0x00007ffef4050000 - 0x00007ffef4068000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\jli.dll
0x00007fff09210000 - 0x00007fff0922b000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\VCRUNTIME140.dll
0x00007fff278d0000 - 0x00007fff27a81000 	C:\Windows\System32\USER32.dll
0x00007fff13100000 - 0x00007fff1339b000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5415_none_270d77d17387d7da\COMCTL32.dll
0x00007fff25130000 - 0x00007fff25156000 	C:\Windows\System32\win32u.dll
0x00007fff272f0000 - 0x00007fff27397000 	C:\Windows\System32\msvcrt.dll
0x00007fff267d0000 - 0x00007fff267f9000 	C:\Windows\System32\GDI32.dll
0x00007fff25a30000 - 0x00007fff25b53000 	C:\Windows\System32\gdi32full.dll
0x00007fff25660000 - 0x00007fff256fa000 	C:\Windows\System32\msvcp_win.dll
0x00007fff26c90000 - 0x00007fff26cc1000 	C:\Windows\System32\IMM32.DLL
0x0000000054aa0000 - 0x0000000054aad000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fff276b0000 - 0x00007fff27761000 	C:\Windows\System32\ADVAPI32.dll
0x00007fff25b60000 - 0x00007fff25c08000 	C:\Windows\System32\sechost.dll
0x00007fff25700000 - 0x00007fff25728000 	C:\Windows\System32\bcrypt.dll
0x00007fff264c0000 - 0x00007fff265d4000 	C:\Windows\System32\RPCRT4.dll
0x00007fff1d460000 - 0x00007fff1d565000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fff25c10000 - 0x00007fff264b2000 	C:\Windows\System32\SHELL32.dll
0x00007fff25730000 - 0x00007fff2586f000 	C:\Windows\System32\wintypes.dll
0x00007fff27a90000 - 0x00007fff27e23000 	C:\Windows\System32\combase.dll
0x00007fff27640000 - 0x00007fff276a9000 	C:\Windows\System32\SHLWAPI.dll
0x00007fff249d0000 - 0x00007fff249da000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fff1c060000 - 0x00007fff1c06c000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\vcruntime140_1.dll
0x00007ffe943e0000 - 0x00007ffe9446d000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\msvcp140.dll
0x00007ffdd5970000 - 0x00007ffdd6731000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\server\jvm.dll
0x00007fff27770000 - 0x00007fff277e1000 	C:\Windows\System32\WS2_32.dll
0x00007fff23d90000 - 0x00007fff23ddd000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007fff199d0000 - 0x00007fff19a04000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fff23d70000 - 0x00007fff23d83000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fff24030000 - 0x00007fff24048000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fff1c050000 - 0x00007fff1c05a000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\jimage.dll
0x00007fff226b0000 - 0x00007fff228e3000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fff277f0000 - 0x00007fff278c7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffeeb3f0000 - 0x00007ffeeb422000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fff25930000 - 0x00007fff259ab000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fff080e0000 - 0x00007fff08100000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5415_none_270d77d17387d7da;C:\Program Files (x86)\360\360Safe\safemon;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://git.yijiupidev.com:81': 
java_class_path (initial): C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2025.1.1.1/plugins/vcs-git/lib/git4idea-rt.jar;C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2025.1.1.1/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4213178368                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4213178368                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\TortoiseSVN\bin;D:\AndroidNDK\android-ndk-r23;C:\Program Files\SafeNet\Authentication\SAC\x64;C:\Program Files\SafeNet\Authentication\SAC\x32;C:\Program Files\Git\cmd;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Program Files\nodejs\;D:\apache-maven\bin;C:\Program Files\Java\jdk1.8.0_202\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\AppData\Local\Programs\CodeBuddy\bin;C:\Users\<USER>\.dotnet\tools
USERNAME=smart
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13284K (0% of 16456492K total physical memory with 5423736K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 9 days 13:45 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 151 stepping 5 microcode 0x37, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for the first 12 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 16070M (5296M free)
TotalPageFile size 46173M (AvailPageFile size 227M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 65M, peak: 317M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

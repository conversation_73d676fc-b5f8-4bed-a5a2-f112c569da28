package com.yijiupi.himalaya.supplychain.batchinventory.domain.manager;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInventoryQueryParam;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.InventoryOrderCenterBL;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.OmsInventoryInfo;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.OmsInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.omsorderquery.service.IOmsInventoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024-06-11 14:26
 **/
@Service
public class OmsInventoryManager {

    @Reference
    private IOmsInventoryService omsInventoryService;

    @Resource
    private InventoryOrderCenterBL inventoryOrderCenterBL;

    private static final Logger logger = LoggerFactory.getLogger(OmsInventoryManager.class);

    public List<OmsInventoryInfo> querySecSaleInventoryInternalList(List<OmsInventoryQueryDTO> list) {
        return runCaching(() -> omsInventoryService.querySecSaleInventoryInternalList(list), Collections::emptyList);
    }

    public List<SaleInventoryInfoDTO> findInventoryByProductOwners(Integer cityId, Integer warehouseId, List<ProductOwnerInfoDTO> productOwners) {
        ProductOwnerInventoryQueryParam param = ProductOwnerInventoryQueryParam.of(cityId, warehouseId, productOwners);
        return runCaching(() -> inventoryOrderCenterBL.findInventoryByProductOwners(param), Collections::emptyList);
    }

    private <T> T runCaching(Supplier<T> supplier, Supplier<T> defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.warn("出现异常", e);
        }
        return defaultValue.get();
    }

}

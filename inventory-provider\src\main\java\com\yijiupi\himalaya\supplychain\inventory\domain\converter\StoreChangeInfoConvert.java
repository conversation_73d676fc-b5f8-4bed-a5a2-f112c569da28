package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.StoreChangeInfoDTO;
import com.yijiupi.supplychain.serviceutils.constant.ERPEventType;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

@Component
public class StoreChangeInfoConvert extends ConvertUtils<ProductStoreChangeRecordDTO, StoreChangeInfoDTO> {

    @Override
    public StoreChangeInfoDTO convert(ProductStoreChangeRecordDTO m) {
        StoreChangeInfoDTO StoreChangeInfoDTO = new StoreChangeInfoDTO();
        if (m != null) {
            StoreChangeInfoDTO.setOrderId(null);
            StoreChangeInfoDTO.setOrderNo(m.getOrderNo());
            if (m.getAddStoreCountDTO() != null) {
                StoreChangeInfoDTO.setChangeCount(m.getAddStoreCountDTO().getTotalCountMinUnit());
                StoreChangeInfoDTO.setChangeCountMax(m.getAddStoreCountDTO().getStoreCountMaxUnit());
                StoreChangeInfoDTO.setChangeCountMin(m.getAddStoreCountDTO().getStoreCountMinUnit());
            }
            if (m.getSourceStoreCountDTO() != null) {
                StoreChangeInfoDTO.setSourceStoreCountMax(m.getSourceStoreCountDTO().getStoreCountMaxUnit());
                StoreChangeInfoDTO.setSourceStoreCountMin(m.getSourceStoreCountDTO().getStoreCountMinUnit());
            }
            if (m.getNewStoreCountDTO() != null) {
                StoreChangeInfoDTO.setNewStoreCountMax(m.getNewStoreCountDTO().getStoreCountMaxUnit());
                StoreChangeInfoDTO.setNewStoreCountMin(m.getNewStoreCountDTO().getStoreCountMinUnit());
            }
            StoreChangeInfoDTO.setPackageName(m.getMaxUnit());
            StoreChangeInfoDTO.setUnitName(m.getMinUnit());
            StoreChangeInfoDTO.setOrderTypeName(null);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            StoreChangeInfoDTO.setCreateTime(sdf.format(m.getCreateTime()));
            StoreChangeInfoDTO.setDescription(m.getDes());
            StoreChangeInfoDTO.setCreateUser(m.getCreateUser());
        }
        return StoreChangeInfoDTO;
    }

    @Override
    public List<StoreChangeInfoDTO> convert(List<ProductStoreChangeRecordDTO> mList) {
        if (mList == null) {
            return null;
        }
        List<StoreChangeInfoDTO> nList = new ArrayList<>(mList.size());
        for (ProductStoreChangeRecordDTO m : mList) {
            StoreChangeInfoDTO vo = convert(m);
            if (m.getOrderType() != null) {
                vo.setOrderTypeName(OrderConstant.getOrderTypeName(Byte.valueOf(m.getOrderType().toString())));
                vo.setOrderType(m.getOrderType());
            }
            JiupiEventType jiupiEventType = JiupiEventType.getEnum(m.getJiupiEventType());
            if (jiupiEventType != null) {
                vo.setJiupiEventTypeName(jiupiEventType.name());
            }
            ERPEventType erpEventType = ERPEventType.getEnum(m.getErpEventType());
            if (erpEventType != null) {
                vo.setErpEventTypeName(erpEventType.name());
            }
            nList.add(vo);
        }
        return nList;
    }

    @Override
    public ProductStoreChangeRecordDTO reverseConvert(StoreChangeInfoDTO n) {
        return null;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.fee.InventoryFeeBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryStorageDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryFeeQueryService;

/**
 * 库存费用相关查询
 *
 * <AUTHOR> 2018/2/2
 */
@Service
public class InventoryFeeQueryServiceImpl implements IInventoryFeeQueryService {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryFeeQueryServiceImpl.class);

    @Autowired
    private InventoryFeeBL inventoryFeeBL;

    /**
     * 配送费查询,托管费查询
     *
     * @param inventoryFeeQueryDTOS
     * @return
     */
    @Override
    public InventoryFeeDTO findInventoryFee(List<InventoryFeeQueryDTO> inventoryFeeQueryDTOS) {
        for (InventoryFeeQueryDTO inventoryFeeQueryDTO : inventoryFeeQueryDTOS) {
            AssertUtils.notNull(inventoryFeeQueryDTO.getProductSkuId(), "产品SKUID不能为空");
            AssertUtils.notNull(inventoryFeeQueryDTO.getWarehouseId(), "仓库ID不能为空");
            AssertUtils.notNull(inventoryFeeQueryDTO.getChannel(), "渠道ID不能为空");
            AssertUtils.notNull(inventoryFeeQueryDTO.getSource(), "产品来源不能为空");
            AssertUtils.notNull(inventoryFeeQueryDTO.getGetGoodsTime(), "取货时间不能为空");
        }
        return inventoryFeeBL.findInventoryFeeSum(inventoryFeeQueryDTOS);
    }

    /**
     * 通过订单号查询存储天数
     */
    @Override
    public Map<String, List<InventoryStorageDTO>> findInventoryStorage(List<String> orderNos) {
        AssertUtils.notEmpty(orderNos, "订单编号不能为空");
        // LOG.info("存储天数查询参数:{}", orderNos);
        return inventoryFeeBL.findInventoryStorage(orderNos);
    }
}

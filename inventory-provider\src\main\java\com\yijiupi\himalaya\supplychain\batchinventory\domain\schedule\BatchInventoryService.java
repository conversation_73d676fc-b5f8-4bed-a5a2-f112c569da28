package com.yijiupi.himalaya.supplychain.batchinventory.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryManageService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-19 16:32
 **/
@Service
public class BatchInventoryService {

    @Reference(timeout = 60000)
    private IWarehouseQueryService warehouseQueryService;
    @Reference(timeout = 120000)
    private IBatchInventoryManageService iBatchInventoryManageService;

    private static final Logger logger = LoggerFactory.getLogger(BatchInventoryService.class);

    /**
     * 更新残次品相关额度到redis，每天执行一次
     */
    @XxlJob("updateDefectiveProductPrice")
    public void updateDefectiveProductPrice() {
        logger.info("[更新残次品额度]开始");

        // 已启用仓库
        // 城市仓库((byte) 0),
        // 集货点((byte) 5),
        // 店仓合一((byte) 6),
        // 前置仓((byte) 8)
        List<Warehouse> warehouseList = warehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0, 1, 5, 6, 8));
        if (CollectionUtils.isEmpty(warehouseList)) {
            logger.info("[更新残次品额度]没有查询到任何仓库");
            return;
        }
        for (Warehouse warehouseElem : warehouseList) {
            if (warehouseElem.getCityId() == null || warehouseElem.getCityId() < 100) {
                continue;
            }
            try {
                logger.info("[更新残次品额度]当前仓库：{}，仓库ID：{}", warehouseElem.getName(), warehouseElem.getId());
                iBatchInventoryManageService.saveCcpPriceByInventory(warehouseElem.getCityId(), warehouseElem.getId());
            } catch (Exception ex) {
                logger.info("[更新残次品额度]仓库名：{}，仓库ID：{}，异常", warehouseElem.getName(), warehouseElem.getId(), ex);
            }
        }
        logger.info("[更新残次品额度]结束");
    }

}

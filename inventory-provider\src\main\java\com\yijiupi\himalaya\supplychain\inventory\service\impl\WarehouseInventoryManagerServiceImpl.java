package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryManageBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ProductInventoryConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockOperateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockProductDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.WJStockOperateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.WJStockProductDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryManageService;

/**
 * 库存调整（Scop，轻加盟，手动）
 */
@Service(timeout = 30000)
public class WarehouseInventoryManagerServiceImpl implements IWarehouseInventoryManageService {
    private final static Logger LOGGER = LoggerFactory.getLogger(WarehouseInventoryManagerServiceImpl.class);

    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    /**
     * 修改仓库库存数（根据库存对账记录变更，可以自定义库存变更明细）
     */
    @Override
    public void modWarehouseInventoryByCheckRecord(WarehouseInventoryCheckRecordDTO warehouseInventoryCheckRecordDTO) {
        AssertUtils.notNull(warehouseInventoryCheckRecordDTO, "修改仓库库存参数不能为空");
        if (warehouseInventoryCheckRecordDTO.getProductSpecificationId() == null) {
            AssertUtils.notNull(warehouseInventoryCheckRecordDTO.getProductSkuId(), "产品skuId不能为空");
        }
        AssertUtils.notNull(warehouseInventoryCheckRecordDTO.getWarehouseId(), "仓库Id不能为空");
        AssertUtils.notNull(warehouseInventoryCheckRecordDTO.getJiupiEventType(), "酒批事件类型不能为空");
        warehouseInventoryManageBL.modWarehouseInventoryByCheckRecord(warehouseInventoryCheckRecordDTO);
    }

    /**
     * 修改仓库库存数.（轻加盟+Scop手动）
     *
     * @param warehouseInventoryModDTO 设置仓库库存数量DTO
     * @param opUserId 操作用户ID
     */
    @Override
    public void modWarehouseInventory(WarehouseInventoryModDTO warehouseInventoryModDTO, Integer opUserId) {
        AssertUtils.notNull(warehouseInventoryModDTO, "设置仓库库存数量DTO为空");
        if (warehouseInventoryModDTO.getProductStoreId() == null) {
            AssertUtils.notNull(warehouseInventoryModDTO.getProductSkuId(), "产品skuId为空");
            AssertUtils.notNull(warehouseInventoryModDTO.getWarehouseId(), "仓库Id为空");
        }
        AssertUtils.notNull(opUserId, "操作人员id为空");
        // warehouseInventoryManageBL.modWarehouseInventory(warehouseInventoryModDTO, opUserId);
        warehouseInventoryManageBL.modWarehouseInventory(Collections.singletonList(warehouseInventoryModDTO), opUserId,
            InventoryChangeTypeEnum.设置仓库库存数量);
    }

    /**
     * SAAS用库存修改接口
     *
     * @param warehouseInventoryModDTO 设置仓库库存数量DTO
     */
    @Override
    public void modWarehouseInventoryBySaas(WarehouseInventoryModDTO warehouseInventoryModDTO) {
        AssertUtils.notNull(warehouseInventoryModDTO, "设置仓库库存数量DTO为空");
        if (warehouseInventoryModDTO.getProductStoreId() == null) {
            AssertUtils.notNull(warehouseInventoryModDTO.getProductSkuId(), "产品skuId为空");
            AssertUtils.notNull(warehouseInventoryModDTO.getWarehouseId(), "仓库Id为空");
        }
        warehouseInventoryManageBL.modWarehouseInventory(Collections.singletonList(warehouseInventoryModDTO), null,
            InventoryChangeTypeEnum.SAAS变更库存);
    }

    /**
     * SAAS用库存修改接口
     */
    @Override
    public void modWarehouseInventoryBatchBySaas(List<WarehouseInventoryModDTO> lstModDTO) {
        AssertUtils.notEmpty(lstModDTO, "参数不能为空");
        lstModDTO.forEach(warehouseInventoryModDTO -> {
            if (warehouseInventoryModDTO.getProductStoreId() == null) {
                AssertUtils.notNull(warehouseInventoryModDTO.getProductSkuId(), "产品skuId为空");
                AssertUtils.notNull(warehouseInventoryModDTO.getWarehouseId(), "仓库Id为空");
            }
        });
        warehouseInventoryManageBL.modWarehouseInventory(lstModDTO, null, InventoryChangeTypeEnum.SAAS变更库存);
    }

    /**
     * 批量修改仓库库存数.（轻加盟+Scop）
     *
     * @param lstModDTO 设置仓库库存数量DTO
     * @param opUserId 操作用户ID
     */
    @Override
    public void modWarehouseInventoryBatch(List<WarehouseInventoryModDTO> lstModDTO, Integer opUserId) {
        AssertUtils.notEmpty(lstModDTO, "参数不能为空");
        lstModDTO.forEach(warehouseInventoryModDTO -> {
            if (warehouseInventoryModDTO.getProductStoreId() == null) {
                AssertUtils.notNull(warehouseInventoryModDTO.getProductSkuId(), "产品skuId为空");
                AssertUtils.notNull(warehouseInventoryModDTO.getWarehouseId(), "仓库Id为空");
            }
        });
        warehouseInventoryManageBL.modWarehouseInventory(lstModDTO, opUserId, InventoryChangeTypeEnum.设置仓库库存数量);
    }

    /**
     * 修改经销商仓库库存数（经销商）
     *
     * @param warehouseInventoryModDTO 设置仓库库存数量DTO
     * @param opUserId 操作用户ID
     */
    @Override
    public void modShopWarehouseInventory(WarehouseInventoryModDTO warehouseInventoryModDTO, Integer opUserId) {
        AssertUtils.notNull(warehouseInventoryModDTO, "设置仓库库存数量DTO为空");
        AssertUtils.notNull(warehouseInventoryModDTO.getProductSkuId(), "产品skuId为空");
        AssertUtils.notNull(warehouseInventoryModDTO.getWarehouseId(), "仓库Id为空");
        AssertUtils.notNull(opUserId, "操作人员id为空");
        // warehouseInventoryManageBL.modShopWarehouseInventory(warehouseInventoryModDTO, opUserId);
        warehouseInventoryManageBL.modWarehouseInventory(Collections.singletonList(warehouseInventoryModDTO), opUserId,
            InventoryChangeTypeEnum.设置经销商库存);
    }

    /**
     * 仓库库存渠道转移（SCOP和供应链客户端）
     */
    @Override
    public void modTransfersWarehouseInventory(WarehouseInventoryTransfersDTO warehouseInventoryTransfersDTO,
        Integer opUserId) {
        LOGGER.info(" *** modTransfersWarehouseInventory 移库入参{}", JSON.toJSONString(warehouseInventoryTransfersDTO));
        AssertUtils.notNull(warehouseInventoryTransfersDTO, "库存DTO为空");
        AssertUtils.notNull(warehouseInventoryTransfersDTO.getProductSkuId(), "产品skuId为空");
        AssertUtils.notNull(warehouseInventoryTransfersDTO.getWarehouseId(), "仓库Id为空");
        AssertUtils.notNull(warehouseInventoryTransfersDTO.getBeChannel(), "被转移渠道为空");
        AssertUtils.notNull(warehouseInventoryTransfersDTO.getChannel(), "转移渠道为空");
        AssertUtils.notNull(opUserId, "操作人员id为空");
        warehouseInventoryManageBL.modTransfersWarehouseInventory(warehouseInventoryTransfersDTO, opUserId);
    }

    /**
     * 批量仓库库存转移（SCOP和供应链客户端）
     */
    @Override
    public void batchModTransfersWarehouseInventory(List<WarehouseInventoryTransfersDTO> warehouseInventoryTransfersDTO,
        Integer opUserId) {
        for (WarehouseInventoryTransfersDTO inventoryTransfersDTO : warehouseInventoryTransfersDTO) {
            modTransfersWarehouseInventory(inventoryTransfersDTO, opUserId);
        }
    }
    //
    // @Override
    // public void sendShopWarehouseInventoryRecord(List<ShopWarehouseInventoryRecordDTO> list) {
    // warehouseInventoryManageBL.sendShopWarehouseInventoryRecord(list);
    // }

    /**
     * 经销商确认出入库
     *
     * @param agencyStockOperateDTO
     */
    @Override
    public void agencyStockOperate(AgencyStockOperateDTO agencyStockOperateDTO) {
        LOGGER.info(String.format("经销商确认出入库：%s", JSON.toJSONString(agencyStockOperateDTO)));
        AssertUtils.notNull(agencyStockOperateDTO.getWarehouseId(), "经销商出入库_仓库不能空");
        AssertUtils.notNull(agencyStockOperateDTO.getYiJingXiaoEventType(), "经销商出入库_类型不能空");
        AssertUtils.notNull(agencyStockOperateDTO.getOrderType(), "经销商出入库_订单类型不能空");
        AssertUtils.notNull(agencyStockOperateDTO.getCityId(), "经销商出入库_城市Id不能空");
        for (AgencyStockProductDTO agencyStockProductDTO : agencyStockOperateDTO.getItems()) {
            AssertUtils.notNull(agencyStockProductDTO.getChannel(), "经销商出入库_渠道不能空");
            AssertUtils.notNull(agencyStockProductDTO.getSource(), "经销商出入库_产品来源不能空");
            AssertUtils.notNull(agencyStockProductDTO.getChangeCount(), "经销商出入库_变化数量不能空");
            AssertUtils.notNull(agencyStockProductDTO.getProductSpecificationId(), "经销商出入库_产品规格Id不能空");
            AssertUtils.notNull(agencyStockProductDTO.getOwnerId(), "经销商出入库_货主Id不能空");
        }
        warehouseInventoryManageBL.agencyStockOperate(agencyStockOperateDTO);
    }

    /**
     * 微酒(确认出入库)
     *
     * @param wjStockOperateDTO
     */
    @Override
    public void wjStockOperate(WJStockOperateDTO wjStockOperateDTO) {
        LOGGER.info(String.format("微酒确认出入库：%s", JSON.toJSONString(wjStockOperateDTO)));
        AssertUtils.notNull(wjStockOperateDTO.getWarehouseId(), "微酒出入库_仓库不能空");
        AssertUtils.notNull(wjStockOperateDTO.getEventType(), "微酒出入库_类型不能空");
        AssertUtils.notNull(wjStockOperateDTO.getOrderType(), "微酒出入库_订单类型不能空");
        AssertUtils.notNull(wjStockOperateDTO.getCityId(), "微酒出入库_城市Id不能空");
        for (WJStockProductDTO productDTO : wjStockOperateDTO.getItems()) {
            AssertUtils.notNull(productDTO.getProductSkuId(), "微酒出入库_SkuId不能空");
            AssertUtils.notNull(productDTO.getChannel(), "微酒出入库_渠道不能空");
            AssertUtils.notNull(productDTO.getSource(), "微酒出入库_产品来源不能空");
            AssertUtils.notNull(productDTO.getChangeCount(), "微酒出入库_变化数量不能空");
            AssertUtils.notNull(productDTO.getProductSpecificationId(), "微酒出入库_产品规格Id不能空");
            AssertUtils.notNull(productDTO.getOwnerId(), "微酒出入库_货主Id不能空");
            AssertUtils.notNull(productDTO.getSecOwnerId(), "微酒出入库_二级货主Id不能空");
        }
        warehouseInventoryManageBL.wjStockOperate(wjStockOperateDTO);
    }

    /**
     * 根据仓库id获取 城市id
     *
     * @param warehouseId
     * @return
     */
    @Override
    public Integer getCityIdByWarehouseId(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return warehouseInventoryManageBL.getCityIdByWarehouseId(warehouseId);
    }

    @Override
    public List<WarehouseInventoryChangeDTO>
        validateAndProcessProductStore(ProcessProductStoreDTO processProductStoreDTO) {
        LOGGER.info("库存操作参数 : {}", JSON.toJSONString(processProductStoreDTO));
        List<WarehouseInventoryChangeBO> warehouseChangeList =
            WarehouseChangListBOConverter.warehouseInventoryChangeDTOS2WarehouseChangListBO(
                processProductStoreDTO.getWarehouseInventoryChangeDTOS());
        AssertUtils.notEmpty(warehouseChangeList, "仓库变更数据不能为空");

        warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList,
            processProductStoreDTO.getCheckWarehouseInventory(), processProductStoreDTO.getUpdateDeliveryCount(),
            processProductStoreDTO.getUpdateProductStore(), processProductStoreDTO.getUpdateProductBatchStore(),
            processProductStoreDTO.getSkipNotExitsSku());
        return WarehouseChangListBOConverter.warehouseChangListBO2WarehouseInventoryChangeDTOS(warehouseChangeList);
    }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存 todo
     *
     * @return
     */
    @Override
    public List<ProductInventoryDTO> getLongProductInventoryPOMapAndCreateNoExits(ProductInventoryQueryDTO queryDTO) {
        // LOGGER.info("getLongProductInventoryPOMapAndCreateNoExits 查询库存参数：{}", JSON.toJSONString(queryDTO));
        AssertUtils.notNull(queryDTO, "查询库存参数不能为空");
        AssertUtils.isTrue(queryDTO.getSpecSkuMap() != null && !queryDTO.getSpecSkuMap().isEmpty(), "查询库存规格ID不能为空");
        AssertUtils.notNull(queryDTO.getCityId(), "查询库存城市ID不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "查询库存仓库ID不能为空");
        Map<Long, List<ProductInventoryPO>> inventoryPOMap =
            warehouseInventoryManageBL.findExactProductInventoryBySpecAndOwner(queryDTO.getSpecSkuMap(),
                queryDTO.getCityId(), queryDTO.getWarehouseId(), queryDTO.getOwnerId(), queryDTO.getChannel(),
                queryDTO.getSecOwnerId(), queryDTO.getCreateInventory());
        if (inventoryPOMap == null) {
            return Collections.emptyList();
        }
        // LOGGER.info("getLongProductInventoryPOMapAndCreateNoExits 查询库存结果：{}", JSON.toJSONString(inventoryPOMap));
        return inventoryPOMap.values().stream().filter(e -> CollectionUtils.isNotEmpty(e)).flatMap(e -> e.stream())
            .filter(Objects::nonNull).map(e -> ProductInventoryConverter.productInventoryPO2ProductInventoryDTO(e))
            .collect(Collectors.toList());
    }

    /**
     * 库存校验
     */
    @Override
    public Map<Long, BigDecimal> checkProductInventory(List<ProductInventoryCheckDTO> productInventoryCheckDTOS) {
        return warehouseInventoryManageBL.checkProductInventory(productInventoryCheckDTOS);
    }

}

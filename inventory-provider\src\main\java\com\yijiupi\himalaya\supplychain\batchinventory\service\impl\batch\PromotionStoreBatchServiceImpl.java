package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.PromotionStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryMoveDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.*;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IPromotionStoreBatchService;

/**
 * 促销批次库存
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Service(timeout = 60000)
public class PromotionStoreBatchServiceImpl implements IPromotionStoreBatchService {

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    /**
     * 获取存在促销批次库存产品数据
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<PromotionStoreBatchResultDTO> listPromotionStoreBatchProduct(PromotionStoreBatchQueryDTO queryDTO) {
        return promotionStoreBatchBL.listPromotionStoreBatchProduct(queryDTO);
    }

    /**
     * 获取产品是否混合批次库存标识
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<PromotionStoreBatchResultDTO> listProductMixedBatchFlag(PromotionStoreBatchQueryDTO queryDTO) {
        return promotionStoreBatchBL.listProductMixedBatchFlag(queryDTO);
    }

    /**
     * 批量新增促销批次库存
     *
     * @param addDTOS
     * @return
     */
    @Override
    public void insertBatch(List<ProductPromotionStoreBatchDTO> addDTOS) {
        promotionStoreBatchBL.insertBatch(addDTOS);
    }

    /**
     * 批量删除促销批次库存
     *
     * @param ids
     * @return
     */
    @Override
    public void batchDeleteByIds(List<Long> ids) {
        promotionStoreBatchBL.batchDeleteByIds(ids);
    }

    /**
     * 条件查询促销批次库存
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductPromotionStoreBatchDTO> queryByCondition(PromotionStoreBatchQueryDTO queryDTO) {
        return promotionStoreBatchBL.queryByCondition(queryDTO);
    }

    /**
     * 根据批次编号检查删除促销批次库存
     *
     * @param deleteDTO
     * @return
     */
    @Override
    public void deleteByBatchAttributeInfoNo(ProductPromotionStoreBatchDTO deleteDTO) {
        promotionStoreBatchBL.deleteByBatchAttributeInfoNo(deleteDTO);
    }

    /**
     * 入库促销货位检查
     *
     * @param checkDTOS
     * @return
     */
    @Override
    public void checkInStockLocation(List<PromotionLocationCheckDTO> checkDTOS) {
        promotionStoreBatchBL.checkInStockLocation(checkDTOS);
    }

    /**
     * 根据批次库存批量新增促销批次库存记录,并判断是否新增关联货位代办
     *
     * @param addDTO
     * @return
     */
    @Override
    public void batchInsertByStoreBatchIds(ProductPromotionStoreBatchDTO addDTO) {
        promotionStoreBatchBL.batchInsertByStoreBatchIds(addDTO);
    }

    /**
     * 自动转残次品
     *
     * @param moveDTO
     * @return
     */
    @Override
    public void storeBatchTransferToDefective(BatchInventoryMoveDTO moveDTO) {
        promotionStoreBatchBL.storeBatchTransferToDefective(moveDTO);
    }

    /**
     * 交易促销活动同步
     *
     * @param message
     * @return
     */
    @Override
    public void synTrdPromotion(TrdPromotionSyncMessage message) {
        promotionStoreBatchBL.synTrdPromotion(message);
    }

    /**
     * 获取存在促销产品批次库存信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<PromotionStoreBatchResultDTO> listPromotionStoreBatchNoGroup(PromotionStoreBatchQueryDTO queryDTO) {
        return promotionStoreBatchBL.listPromotionStoreBatchNoGroup(queryDTO);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreChangeDetailDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreChangeDetailQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.batch.BatchProductStoreChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.BatchInventoryQueryRecordDTO;

/**
 * <AUTHOR>
 */
public interface ProductStoreChangeRecordPOMapper {

    /**
     * 根据商品skuId、经销商、仓库编号查询库存出入库历史记录明细
     *
     * @param queryDTO
     * @return
     */
    PageResult<ProductStoreChangeDetailDTO> findProductStoreRecordList(ProductStoreChangeDetailQueryDTO queryDTO);

    List<String> findStoreChangeOrderNos(@Param("orderNos") List<String> orderNos,
        @Param("excludeUser") String excludeUser);

    List<BatchProductStoreChangeRecordPO> findBatchInventoryByOrderNos(@Param("orderNos") List<String> orderNos);

    List<BatchProductStoreChangeRecordPO> findBatchInventory(@Param("dto") BatchInventoryQueryRecordDTO queryRecordDTO);

    /**
     * 根据订单号查订单数量
     *
     * @param orderNos
     * @return
     */
    Long queryCountBatchInventoryByOrderNo(@Param("orderNos") List<String> orderNos);

    List<BatchProductStoreChangeRecordPO>
        findBatchInventoryProductionDate(@Param("dto") BatchInventoryQueryRecordDTO queryRecordDTO);
}
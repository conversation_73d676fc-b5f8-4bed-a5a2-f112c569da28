<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.WarehouseChargeConfigPOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="UnloadingCharge" property="unloadingcharge" jdbcType="DECIMAL"/>
        <result column="SortingCharge" property="sortingcharge" jdbcType="DECIMAL"/>
        <result column="CustodianCharge" property="custodiancharge" jdbcType="DECIMAL"/>
        <result column="LoadingCharge" property="loadingcharge" jdbcType="DECIMAL"/>
        <result column="TransportCharge" property="transportcharge" jdbcType="DECIMAL"/>
        <result column="LandingCharge" property="landingcharge" jdbcType="DECIMAL"/>
        <result column="Status" property="status" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createuser" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createtime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateUser" property="lastupdateuser" jdbcType="BIGINT"/>
        <result column="LastUpdateTime" property="lastupdatetime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, Warehouse_Id, UnloadingCharge, SortingCharge, CustodianCharge, LoadingCharge,
        TransportCharge, LandingCharge, Status, CreateUser, CreateTime, LastUpdateUser, LastUpdateTime
    </sql>


    <insert id="insertWarehouseChargeConfig"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO">
        insert into
        warehousechargeconfig (
        Id,
        Warehouse_Id,
        UnloadingCharge,
        SortingCharge,
        CustodianCharge,
        LoadingCharge,
        TransportCharge,
        LandingCharge,
        Status,
        CreateUser,
        CreateTime,
        LastUpdateUser,
        LastUpdateTime
        )
        values (
        #{po.id,jdbcType=BIGINT},
        #{po.warehouseId,jdbcType=INTEGER},
        #{po.unloadingcharge,jdbcType=DECIMAL},
        #{po.sortingcharge,jdbcType=DECIMAL},
        #{po.custodiancharge,jdbcType=DECIMAL},
        #{po.loadingcharge,jdbcType=DECIMAL},
        #{po.transportcharge,jdbcType=DECIMAL},
        #{po.landingcharge,jdbcType=DECIMAL},
        #{po.status,jdbcType=TINYINT},
        #{po.createuser,jdbcType=BIGINT},
        now(),
        #{po.createuser,jdbcType=BIGINT},
        now()
        )
    </insert>


    <select id="selectWarehouseChargeConfigById"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChargeConfigDTO"
            parameterType="java.lang.Integer">
        select
        whc.Id as id,
        whc.Warehouse_Id as warehouseId,
        wh.Name as warehouseName,
        wh.City_Id as cityId,
        wh.City as city,
        wh.DetailAddress as detailAddress,
        whc.UnloadingCharge as unloadingcharge,
        whc.SortingCharge as sortingcharge,
        whc.CustodianCharge as custodiancharge,
        whc.LoadingCharge as loadingcharge ,
        whc.TransportCharge as transportcharge,
        whc.LandingCharge as landingcharge ,
        whc.Status as status,
        whc.CreateUser as createuser ,
        whc.CreateTime as createtime,
        whc.LastUpdateUser as lastupdateuser,
        whc.LastUpdateTime as lastupdatetime
        from warehousechargeconfig whc inner join warehouse wh where whc.Warehouse_Id = wh.id
        and whc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>


    <update id="updateWarehouseChargeConfig"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO">
        update warehousechargeconfig
        <set>

            <if test="po.unloadingcharge != null">
                UnloadingCharge = #{po.unloadingcharge,jdbcType=DECIMAL},
            </if>
            <if test="po.sortingcharge != null">
                SortingCharge = #{po.sortingcharge,jdbcType=DECIMAL},
            </if>
            <if test="po.custodiancharge != null">
                CustodianCharge = #{po.custodiancharge,jdbcType=DECIMAL},
            </if>
            <if test="po.loadingcharge != null">
                LoadingCharge = #{po.loadingcharge,jdbcType=DECIMAL},
            </if>
            <if test="po.transportcharge != null">
                TransportCharge = #{po.transportcharge,jdbcType=DECIMAL},
            </if>
            <if test="po.landingcharge != null">
                LandingCharge = #{po.landingcharge,jdbcType=DECIMAL},
            </if>
            <if test="po.status != null">
                Status = #{po.status,jdbcType=TINYINT},
            </if>

            <if test="po.lastupdateuser != null">
                LastUpdateUser = #{po.lastupdateuser,jdbcType=BIGINT},
            </if>
            LastUpdateTime = now()

        </set>
        where Warehouse_Id = #{po.warehouseId,jdbcType=BIGINT}
    </update>


    <update id="updateChargeConfigStatus"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO">
        update warehousechargeconfig
        set
        Status = #{po.status,jdbcType=TINYINT},
        LastUpdateUser = #{po.lastupdateuser,jdbcType=BIGINT},
        LastUpdateTime = now()
        where Warehouse_Id = #{po.warehouseId,jdbcType=INTEGER}
    </update>


    <select id="selectWarehouseChargeList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChargeConfigDTO">

        select
        whc.Id as id,
        whc.Warehouse_Id as warehouseId,
        wh.Name as warehouseName,
        wh.City_Id as cityId,
        wh.City as city,
        wh.DetailAddress as detailAddress,
        whc.UnloadingCharge as unloadingcharge,
        whc.SortingCharge as sortingcharge,
        whc.CustodianCharge as custodiancharge,
        whc.LoadingCharge as loadingcharge ,
        whc.TransportCharge as transportcharge,
        whc.LandingCharge as landingcharge ,
        whc.Status as status,
        whc.CreateUser as createuser ,
        whc.CreateTime as createtime,
        whc.LastUpdateUser as lastupdateuser,
        whc.LastUpdateTime as lastupdatetime,
        whc.WarehouseCharge as warehouseCharge ,
        whc.PlatFormCharge as platFormCharge ,
        whc.PlatFromFee as platFormFee
        from warehousechargeconfig whc inner join warehouse wh where whc.Warehouse_Id = wh.id
        <if test="list != null and list.size >0">
            and whc.Warehouse_Id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>


    </select>


    <select id="selectCountByWarehouseId" resultType="java.lang.Integer">
        select
        count(1) as count
        from warehousechargeconfig
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
    </select>


    <select id="findWarehouseChargeList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWareHouseDTO">
        select
        wh.City_Id as cityId,
        wh.City as city,
        wh.Id as warehouseId,
        wh.Name as warehouseName,
        wh.shopId as shopId,
        wh.DetailAddress as detailAddress
        from warehouse wh
        where wh.State =1
        and wh.warehouseType=0
        <if test="city != null and city !=''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>
        <if test="cityId != null">
            and wh.City_Id = #{cityId,jdbcType=INTEGER}
        </if>

    </select>


    <select id="findOwnerWarehouseList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChooseReturnDTO">

        select
        wh.Id as id,
        wh.Name as name,
        wh.Province as province,
        wh.City as city,
        wh.County as county,
        wh.Street as street,
        wh.DetailAddress as detailAddress,
        2 as warehouseType
        from warehouse wh
        where wh.ShopId = #{shopId}
        and wh.State =1
        and wh.warehouseType=3
        <if test="city != null and city !=''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>
        group by wh.id
    </select>

    <select id="findDepositWarehouseList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChooseReturnDTO">
        select
        wh.Id as id,
        wh.Name as name,
        wh.Province as province,
        wh.City as city,
        wh.County as county,
        wh.Street as street,
        wh.DetailAddress as detailAddress,
        wh.City_Id as facilitatorId,
        3 as warehouseType
        from warehouse wh
        where wh.State =1
        and wh.warehouseType=0
        <if test="city != null and city !=''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>
        group by wh.id
    </select>


    <select id="findDealerWarehouseList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChooseReturnDTO">
        select
        wh.Id as id,
        wh.Name as name,
        wh.Province as province,
        wh.City as city,
        wh.County as county,
        wh.Street as street,
        wh.DetailAddress as detailAddress,
        wh.City_Id as facilitatorId,
        4 as warehouseType
        from warehouse wh inner join productstore ps on wh.Id = ps.Warehouse_Id
        where wh.State =1
        and wh.warehouseType=0
        and ps.TotalCount_MinUnit>0
        <if test="city != null and city !=''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>
        <if test="shopId !=null">
            and ps.Owner_Id=#{shopId,jdbcType=BIGINT}
        </if>
        group by wh.id
    </select>

    <select id="findWarehouseList" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChooseReturnDTO">
        ( select
        wh.Id as id,
        wh.Name as name,
        wh.Province as province,
        wh.City as city,
        wh.County as county,
        wh.Street as street,
        wh.DetailAddress as detailAddress,
        wh.City_Id as facilitatorId,
        2 as warehouseType
        from warehouse wh
        where wh.ShopId = #{shopId,jdbcType=BIGINT}
        and wh.State =1
        and wh.warehouseType=3
        <if test="city != null and city !=''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>
        group by wh.id)

        union all

        ( select
        wh.Id as id,
        wh.Name as name,
        wh.Province as province,
        wh.City as city,
        wh.County as county,
        wh.Street as street,
        wh.DetailAddress as detailAddress,
        wh.City_Id as facilitatorId,
        4 as warehouseType
        from warehouse wh inner join productstore ps on wh.Id = ps.Warehouse_Id
        where wh.State =1
        and wh.warehouseType=0
        and ps.TotalCount_MinUnit>0
        <if test="city != null and city !=''">
            and wh.City = #{city,jdbcType=VARCHAR}
        </if>

        <if test="shopId !=null">
            and ps.Owner_Id=#{shopId}
        </if>
        group by wh.id
        )
    </select>

    <select id="findWarehouseChargeDetailList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChargeConfigDTO">
        select
        whc.Id as id,
        whc.Warehouse_Id as warehouseId,
        wh.Name as warehouseName,
        wh.City_Id as cityId,
        wh.City as city,
        wh.DetailAddress as detailAddress,
        whc.UnloadingCharge as unloadingcharge,
        whc.SortingCharge as sortingcharge,
        whc.CustodianCharge as custodiancharge,
        whc.LoadingCharge as loadingcharge ,
        whc.TransportCharge as transportcharge,
        whc.LandingCharge as landingcharge ,
        whc.Status as status,
        whc.CreateUser as createuser ,
        whc.CreateTime as createtime,
        whc.LastUpdateUser as lastupdateuser,
        whc.LastUpdateTime as lastupdatetime,
        whc.WarehouseCharge as warehouseCharge ,
        whc.PlatFormCharge as platFormCharge ,
        whc.PlatFromFee as platFormFee
        from warehousechargeconfig whc inner join warehouse wh on wh.Id = whc.Warehouse_Id
        where wh.State = 1
        and wh.warehouseType=0
        <if test="cityId != null">
            and wh.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and whc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>

    </select>


    <select id="selectShopIdList" resultType="java.lang.Integer">
        select
        wh.ShopId as shopId
        from warehouse wh
        where wh.Id = #{warehouseId,jdbcType=INTEGER}
        and wh.warehouseType=3
    </select>


    <select id="selectWarehouseIdList" resultType="java.lang.Integer">
        select
        wh.Id as warehouseId
        from warehouse wh
        where wh.State=1
        and wh.warehouseType=0
    </select>


    <select id="getCityIdByWarehouseId" resultType="java.lang.Integer">
        select
        wh.City_Id as cityId
        from warehouse wh
        where wh.Id = #{warehouseId,jdbcType=INTEGER}
    </select>


    <select id="findWarehouseServiceStoreList"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseServiceStoreDTO">

        select
        dcc.Dealer_Id as dealerId ,
        dcc.MobileNo as mobileNo,
        wh.City_Id as cityId,
        wh.City as city,
        wh.Id as warehouseId,
        wh.Name as warehouseName
        from dealerchargeconfig dcc inner join warehouse wh on wh.ShopId = dcc.Facilitator_Id
        where wh.ShopId = #{facilitatorId}
        and wh.warehouseType=4
        <if test="mobileNo != null">
            and dcc.MobileNo like concat('%',#{mobileNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="cityId != null">
            and wh.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            and wh.Id = #{warehouseId,jdbcType=INTEGER}
        </if>

    </select>


    <select id="findByWarehouseId" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseReturnDTO">
        select
        wh.Id as id,
        wh.Name as name,
        wh.City_Id as cityId,
        wh.City as city,
        wh.warehouseType as warehouseType,
        wh.ShopId as shopId
        from warehouse wh
        where wh.Id = #{warehouseId,jdbcType=INTEGER}
        <if test="dealerId !=null">
            and wh.ShopId=#{dealerId}
        </if>
    </select>


    <select id="findByWarehouseList" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseReturnDTO">
        select
        wh.Id as id,
        wh.Name as name,
        wh.City_Id as cityId,
        wh.City as city,
        wh.warehouseType as warehouseType,
        wh.ShopId as shopId
        from warehouse wh
        <where>
            <if test="list != null and list.size >0">
                and wh.Id in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
</mapper>
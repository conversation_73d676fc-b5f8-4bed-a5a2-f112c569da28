package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.productSku;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品skuPO
 *
 * <AUTHOR> 2017/11/17
 */
public class ProductSkuPO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 产品skuid
     */
    private Long productSkuId;
    /**
     * 产品规格ID
     */
    private Long productSpecificationId;
    /**
     * 销售模式
     */
    private Integer productSaleMode;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;

    /**
     * 货主类型 0：酒批 1：合作商 2：入驻商
     */
    private Integer ownerType;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 产品保质期
     */
    private Integer monthOfShelfLife;

    /**
     * 保质期单位 1=年 2=月 3=日
     */
    private Integer shelfLifeUnit;

    /**
     * 保质期是否为长期
     */
    private Boolean shelfLifeLongTime;

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 主键id
     *
     * @return id 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 产品名称
     *
     * @return name 产品名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * 设置 产品名称
     *
     * @param name 产品名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 产品skuid
     *
     * @return productSkuId 产品skuid
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuid
     *
     * @param productSkuId 产品skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品规格ID
     *
     * @return productSpecificationId 产品规格ID
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品规格ID
     *
     * @param productSpecificationId 产品规格ID
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 销售模式
     *
     * @return productSaleMode 销售模式
     */
    public Integer getProductSaleMode() {
        return this.productSaleMode;
    }

    /**
     * 设置 销售模式
     *
     * @param productSaleMode 销售模式
     */
    public void setProductSaleMode(Integer productSaleMode) {
        this.productSaleMode = productSaleMode;
    }

    /**
     * 获取 公司id
     *
     * @return companyId 公司id
     */
    public Long getCompanyId() {
        return this.companyId;
    }

    /**
     * 设置 公司id
     *
     * @param companyId 公司id
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取 包装规格名称
     *
     * @return specificationName 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     *
     * @param specificationName 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     *
     * @return packageName 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     *
     * @param packageName 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     *
     * @return unitName 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     *
     * @param unitName 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     *
     * @return packageQuantity 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     *
     * @param packageQuantity 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @return source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款抵押）
     *
     * @param source 产品来源，0:酒批，1:微酒（贷款抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public Integer getMonthOfShelfLife() {
        return monthOfShelfLife;
    }

    public void setMonthOfShelfLife(Integer monthOfShelfLife) {
        this.monthOfShelfLife = monthOfShelfLife;
    }

    public Integer getShelfLifeUnit() {
        return shelfLifeUnit;
    }

    public void setShelfLifeUnit(Integer shelfLifeUnit) {
        this.shelfLifeUnit = shelfLifeUnit;
    }

    public Boolean getShelfLifeLongTime() {
        return shelfLifeLongTime;
    }

    public void setShelfLifeLongTime(Boolean shelfLifeLongTime) {
        this.shelfLifeLongTime = shelfLifeLongTime;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.Collections;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryListQueryService;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.junit.runner.GeneralRunner;

/**
 * <AUTHOR>
 * @since 2023-08-09 15:59
 **/
@RunWith(GeneralRunner.class)
public class InventoryListQueryServiceImplTest {

    @Reference
    private IInventoryListQueryService inventoryListQueryService;

    @Test
    public void findStoreReportPageInfoByAuth() {
        StockReportSO so = new StockReportSO();
        so.setWarehouseIds(Collections.singletonList(9981));
        so.setInventoryPinProperty("A");
        PagerCondition pager = new PagerCondition(1, 100);
        PageList<InventoryReportDTO> page = inventoryListQueryService.findStoreReportPageInfoByAuth(so, pager);
        Assert.assertNotNull(page);
        System.out.println(JSON.toJSONString(page, SerializerFeature.PrettyFormat));
    }

    @Test
    public void findStoreReportPageByAuth() {
        StockReportSO so = new StockReportSO();
        so.setWarehouseIds(Collections.singletonList(9981));
        so.setInventoryPinProperty("A");
        PagerCondition pager = new PagerCondition(1, 100);
        PageList<InventoryReportDTO> page = inventoryListQueryService.findStoreReportPageByAuth(so, pager);
        Assert.assertNotNull(page);
        System.out.println(JSON.toJSONString(page, SerializerFeature.PrettyFormat));
    }
}
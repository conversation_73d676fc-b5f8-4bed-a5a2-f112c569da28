package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate.InStockHandleProductDateBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.dto.WareHoseInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InStockHandleProductDateTest {

    @Autowired
    private InStockHandleProductDateBL inStockHandleProductDateBL;
    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;

    @Test
    public void processOrderProductDateTest() {
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setCurrentPage(1);
        wareHoseInventoryQueryDTO.setPageSize(20);
        wareHoseInventoryQueryDTO.setWarehouseId(9981);
        PageList<WarehouseStoreDTO> result = warehouseInventoryQueryBL.getProductInventorysByPager(wareHoseInventoryQueryDTO);
        PageList<WarehouseStoreDTO> result1 = warehouseInventoryQueryBL.getProductInventoryByHandPager(wareHoseInventoryQueryDTO);

        Assertions.assertThat(result1).isNotNull();
    }


}

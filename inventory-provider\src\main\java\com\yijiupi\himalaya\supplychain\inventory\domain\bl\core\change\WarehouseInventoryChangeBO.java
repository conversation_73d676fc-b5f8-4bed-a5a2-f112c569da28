package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change;

import com.yijiupi.himalaya.supplychain.inventory.constant.InventoryChangeTypes;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.SellInventoryChangeMessage;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.util.UUIDUtil;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓库库存变更BO. Created by Lifeng on 2017/7/18.
 */
public class WarehouseInventoryChangeBO extends BaseInventoryChangeBO {

    private Long productSkuId;

    private Integer warehouseId;

    private BigDecimal count;

    /**
     * 关联仓库Id
     */
    private Integer relateWarehouseId;

    /**
     * 是否更新交易平台库存.
     */
    private Boolean hasUpdateOPInventory = true;

    /**
     * 是否校验自身库存
     */
    private Boolean isValidateSelf = false;

    /**
     * 批属性JSON
     */
    private String attributeList;
    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Integer deliveryMode;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * '货位名称'
     */
    private String locationName;

    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 批次库存表ID
     */
    private String productStoreBatchId;

    /**
     * 是否在上架任务完成后处理销售库存 0 - 否 1 - 是 默认 ： 0 （否）
     */
    private Byte isProcessSalesStockAfterPutAway;

    /**
     * 是否需要关联货位.
     */
    private Boolean associateProductLocation = false;

    /**
     * 订单项ID（处理组合订单库存变更明细替换为子单）
     */
    private Long orderItemId;

    /**
     * 原订单ID
     */
    private String oldOrderId;

    /**
     * 原订单编号
     */
    private String oldOrderNo;

    /**
     * 结果数量(供应商库存计算用)
     */
    private BigDecimal resultCount;

    /**
     * 出入库类型
     */
    private Byte outInType;

    /**
     * 是否分配计算
     */
    private Boolean allocationCalculation = true;

    private String batchNo;

    /**
     * 明细id
     */
    private Long orderItemDetailId;

    /**
     * 子业务类型
     */
    private Byte businessType;

    /**
     * 货区/货区类型
     */
    private Byte locationSubcategory;

    /**
     * oms订单项id
     */
    private Long omsOrderItemId;

    /**
     * 是否内配退
     */
    private Boolean allotReturn;

    /**
     * 调拨类型
     */
    private Byte allotType;

    /**
     * 订单能力类型
     */
    private Byte capabilityType;

    /**
     * 中台skuId
     */
    private Long unifySkuId;

    /***
     * 判断是否发送订单明细到oms
     */
    private Boolean sendDetailToOMS;

    /**
     * 原始明细id
     */
    private Long originOrderItemDetailId;

    /**
     * 是否临期
     */
    private Boolean isAdvent;

    public Long getUnifySkuId() {
        return unifySkuId;
    }

    public void setUnifySkuId(Long unifySkuId) {
        this.unifySkuId = unifySkuId;
    }

    public Boolean getAllotReturn() {
        return allotReturn;
    }

    public void setAllotReturn(Boolean allotReturn) {
        this.allotReturn = allotReturn;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getOldOrderId() {
        return oldOrderId;
    }

    public void setOldOrderId(String oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public ProductInventoryChangeRecordPO createProductInventoryChangeRecordPO(String productStoreId,
        BigDecimal sourceTotalCount) {
        ProductInventoryChangeRecordPO recordPO = createChangeRecord();
        recordPO.setId(UUIDUtil.getUUID());
        recordPO.setCountMaxUnit(BigDecimal.ZERO);
        recordPO.setCountMinUnit(count);
        recordPO.setCreateTime(new Date());
        recordPO.setProductStoreId(productStoreId);
        recordPO.setSourceTotalCount(sourceTotalCount);// 库存变更前的数量.
        recordPO.setTotalCount(count);
        recordPO.setStoreType(InventoryChangeTypes.WAREHOUSE);
        recordPO.setWarehouseId(warehouseId);
        return recordPO;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getWarehouseIdAndChannelAndOwnerId() {
        // 货主为null的表示久批产品，需要考虑多供应商情况
        if (getOwnId() == null) {
            return getWarehouseId() + "_" + getChannel() + "_" + getOwnId();
        } else {
            return getWarehouseId() + "_" + getChannel() + "_" + getOwnId() + "_" + getSecOwnerId();
        }
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public Boolean getValidateSelf() {
        return isValidateSelf;
    }

    public void setValidateSelf(Boolean validateSelf) {
        isValidateSelf = validateSelf;
    }

    public boolean getHasUpdateOPInventory() {
        return hasUpdateOPInventory;
    }

    public void setHasUpdateOPInventory(boolean hasUpdateOPInventory) {
        this.hasUpdateOPInventory = hasUpdateOPInventory;
    }

    public String getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(String attributeList) {
        this.attributeList = attributeList;
    }

    public Integer getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Integer deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getProductStoreBatchId() {
        return productStoreBatchId;
    }

    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    public Byte getIsProcessSalesStockAfterPutAway() {
        return isProcessSalesStockAfterPutAway;
    }

    public void setIsProcessSalesStockAfterPutAway(Byte isProcessSalesStockAfterPutAway) {
        this.isProcessSalesStockAfterPutAway = isProcessSalesStockAfterPutAway;
    }

    public Boolean getAssociateProductLocation() {
        return associateProductLocation;
    }

    public void setAssociateProductLocation(Boolean associateProductLocation) {
        this.associateProductLocation = associateProductLocation;
    }

    public Integer getRelateWarehouseId() {
        return relateWarehouseId;
    }

    public void setRelateWarehouseId(Integer relateWarehouseId) {
        this.relateWarehouseId = relateWarehouseId;
    }

    /**
     * 构建sku唯一标示
     */
    public String getSkuSign() {
        // String skuSign = productSkuId == null ? "" : getProductSkuId().toString();
        // if (getOwnId() != null) {
        // skuSign += getOwnId();
        // }
        return String.format("%s-%s-%s-%s", getWarehouseId(), getProductSpecificationId(), getOwnId(), getSecOwnerId());
    }

    /**
     * 构建mq消息体.
     */
    public SellInventoryChangeMessage createSellInventoryChangeMessage() {
        SellInventoryChangeMessage message = new SellInventoryChangeMessage();
        message.setCityId(getCityId());
        message.setWarehouseId(warehouseId);
        message.setProductSpecificationId(getProductSpecificationId());
        message.setOwnerType(getOwnType());
        message.setJiupiEventType(getJiupiEventType());
        message.setTotalCount(count);
        message.setCreateTime(new Date());

        // 非必填消息
        message.setOwnerId(getOwnId());
        message.setOrderNo(getOrderNo());
        message.setOrderId(getOrderId());
        message.setOrderType(getOrderType());
        message.setCreateUserId(getCreateUserId());
        message.setCreateUserName(getCreateUserName());
        message.setDescription(getDescription());
        message.setErpEventType(getErpEventType());
        message.setSecOwnerId(getSecOwnerId());

        message.setAllotReturn(getAllotReturn());
        message.setAllotType(getAllotType());
        message.setRelateWarehouseId(relateWarehouseId);

        return message;
    }

    public BigDecimal getResultCount() {
        return resultCount;
    }

    public void setResultCount(BigDecimal resultCount) {
        this.resultCount = resultCount;
    }

    public Byte getOutInType() {
        return outInType;
    }

    public void setOutInType(Byte outInType) {
        this.outInType = outInType;
    }

    public Boolean getAllocationCalculation() {
        return allocationCalculation;
    }

    public void setAllocationCalculation(Boolean allocationCalculation) {
        this.allocationCalculation = allocationCalculation;
    }

    public Long getOrderItemDetailId() {
        return orderItemDetailId;
    }

    public void setOrderItemDetailId(Long orderItemDetailId) {
        this.orderItemDetailId = orderItemDetailId;
    }

    public Byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Byte getLocationSubcategory() {
        return locationSubcategory;
    }

    public void setLocationSubcategory(Byte locationSubcategory) {
        this.locationSubcategory = locationSubcategory;
    }

    public Long getOmsOrderItemId() {
        return omsOrderItemId;
    }

    public void setOmsOrderItemId(Long omsOrderItemId) {
        this.omsOrderItemId = omsOrderItemId;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Byte getCapabilityType() {
        return capabilityType;
    }

    public void setCapabilityType(Byte capabilityType) {
        this.capabilityType = capabilityType;
    }

    public Boolean getSendDetailToOMS() {
        return sendDetailToOMS;
    }

    public void setSendDetailToOMS(Boolean sendDetailToOMS) {
        this.sendDetailToOMS = sendDetailToOMS;
    }

    public Long getOriginOrderItemDetailId() {
        return originOrderItemDetailId;
    }

    public void setOriginOrderItemDetailId(Long originOrderItemDetailId) {
        this.originOrderItemDetailId = originOrderItemDetailId;
    }

    public Boolean getIsAdvent() {
        return isAdvent;
    }

    public void setIsAdvent(Boolean isAdvent) {
        this.isAdvent = isAdvent;
    }
}

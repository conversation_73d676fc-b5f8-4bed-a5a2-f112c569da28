package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory;

import java.util.Objects;

import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.batchinventory.constant.ProcessLocationInventoryTypeConstants;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProcessLocationInventoryBO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Service
public class ProcessNormalLocationInventoryBL extends ProcessLocationInventoryBaseBL {
    @Override
    protected boolean doSupport(ProcessLocationInventoryBO bo) {
        if (ProcessLocationInventoryTypeConstants.isDTN(bo.getProcessType())) {
            return Boolean.FALSE;
        }
        if (Objects.equals(ERPType.库存盘点单.getType(), bo.getProductInventoryChangeRecordPO().getOrderType())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

}

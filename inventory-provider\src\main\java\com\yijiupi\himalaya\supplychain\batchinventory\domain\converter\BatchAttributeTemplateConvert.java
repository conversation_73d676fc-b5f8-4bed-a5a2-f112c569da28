package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplatePO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateDTO;

/**
 * 批属性模板转换类
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeTemplateConvert {

    public static BatchAttributeTemplatePO BatchAttributeTemplateDTO2PO(BatchAttributeTemplateDTO dto) {
        if (dto == null) {
            return null;
        }
        BatchAttributeTemplatePO batchAttributeTemplatePO = new BatchAttributeTemplatePO();
        batchAttributeTemplatePO.setTemplateName(dto.getTemplateName());
        batchAttributeTemplatePO.setRemark(dto.getRemark());
        batchAttributeTemplatePO.setEnable(dto.getEnable());
        batchAttributeTemplatePO.setCreateUser(dto.getCreateUser());
        return batchAttributeTemplatePO;
    }
}

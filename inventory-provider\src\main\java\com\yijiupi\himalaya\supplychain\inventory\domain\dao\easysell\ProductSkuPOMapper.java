package com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductInfoStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductInfoStoreQueryDTO;

/**
 * <AUTHOR>
 */
public interface ProductSkuPOMapper {

    /**
     * 根据经销商、规格id查询经销商在不同仓库库存信息接口
     * 
     * @param productInfoStoreQueryDTO
     * @return
     *
     */
    PageResult<ProductInfoStoreDTO> findProductInfoStoreList(ProductInfoStoreQueryDTO productInfoStoreQueryDTO);

    /**
     * 根据skuId集合，经销商id,仓库查询商品信息参数
     * 
     * @param productDetailsQueryDTO
     * @return
     *
     */
    PageResult<ProductDetailsDTO> findProductDetailsList(ProductDetailsQueryDTO productDetailsQueryDTO);
}
package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.attribute;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeInfoBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueSaveDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueSaveReturnDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchAttributeInfoService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
@Service(timeout = 60000)
public class BatchAttributeInfoServiceImpl implements IBatchAttributeInfoService {

    @Autowired
    private BatchAttributeInfoBL batchAttributeInfoBL;

    /**
     * 新增批属性info
     *
     * @param batchAttributeInfoDTOS
     */
    // @Override
    // public void addBatchAttributeInfo(List<BatchAttributeInfoDTO> batchAttributeInfoDTOS) {
    // batchAttributeInfoBL.addBatchAttributeInfo(batchAttributeInfoDTOS);
    // }

    /**
     * 根据批属性编号查找详情
     *
     * @param batchAttributeInfoNo
     * @return
     */
    @Override
    public List<BatchAttributeInfoDTO> findAttributeInfoByNo(String batchAttributeInfoNo) {
        return batchAttributeInfoBL.findAttributeInfoByNo(batchAttributeInfoNo);
    }

    /**
     * 同步批次号
     *
     * @param ownerType
     */
    @Override
    public void processBatchNo(Integer ownerType) {
        batchAttributeInfoBL.processBatchNo(ownerType);
    }

    /**
     * 根据仓库产品信息获取入库批次编号
     */
    @Override
    public List<BatchNOQueryResultDTO> genOrderBatchInfoNO(BatchNOQueryDTO queryDTO) {
        return batchAttributeInfoBL.genOrderBatchInfoNO(queryDTO);
    }

    /**
     * 保存批属性并且生成批次编号
     */
    @Override
    public List<BatchAttributeValueSaveReturnDTO> saveBatchAttributeDicInfo(BatchAttributeValueSaveDTO saveDTO) {
        return batchAttributeInfoBL.saveBatchAttributeDicInfo(saveDTO);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStorePOReturnMapper">

    <select id="findProductStoreList" resultType="com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreReturnDTO">
        select
        psku.ProductSku_Id as productSkuId,
        psku.Name as name,
        psku.specificationName as specificationName,
        psku.packageName as packageName,
        psku.packageQuantity as packageQuantity,
        ps.TotalCount_MinUnit as totalCountMinUnit
        from productstore ps
        inner join productsku psku on ps.ProductSpecification_Id = psku.ProductSpecification_Id

        <if test="warehouseId != null">
            and ps.Warehouse_Id =#{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="shopId != null">
            and ps.Owner_Id =#{shopId,jdbcType=BIGINT}
        </if>


    </select>


</mapper>
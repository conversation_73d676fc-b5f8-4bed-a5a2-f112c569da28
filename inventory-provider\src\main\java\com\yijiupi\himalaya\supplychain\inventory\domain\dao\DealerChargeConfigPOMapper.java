/*
 * @ClassName DealerChargeConfigPOMapper
 * 
 * @Description
 * 
 * @version 1.0
 * 
 * @Date 2018-11-28 19:14:09
 */
package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;

public interface DealerChargeConfigPOMapper {

    /**
     * 新增经销商配置
     * 
     * @Title insert
     * @param po
     * @return int
     */
    int insertDealerChargeConfig(@Param("po") DealerChargeConfigPO po);

    /**
     * 经销商是否收费明细查询
     * 
     * @param
     * @return
     */
    DealerChargeConfigDTO selectDealerChargeConfigById(DealerCountQuery dealerCountQuery);

    /**
     * 启用停用经销商收费
     * 
     * @param po
     * @return
     */
    int updateDealerConfigStatus(@Param("po") DealerChargeConfigPO po);

    /**
     * 经销商是否收费修改
     * 
     * @param po
     * @return
     */
    int updateDealerChargeConfig(@Param("po") DealerChargeConfigPO po);

    /**
     * 根据服务id查询经销商费用配置
     * 
     * @param dealerChargeConfigQuery
     * @return
     */

    PageResult<DealerChargeConfigDTO> selectDealerChargeList(DealerChargeConfigQuery dealerChargeConfigQuery);

    /**
     * 根据服务id集合查询经销商费用配置
     * 
     * @param dealerChargeConfigQuery
     * @return
     */

    PageResult<DealerChargeConfigDTO> selectDealerList(DealerChargeConfigQuery dealerChargeConfigQuery);

    /**
     * 根据经销商id查询个数
     * 
     * @param
     * @return
     */
    int selectCountByDealerId(DealerCountQuery dealerCountQuery);

    /**
     * 根据经销商id城市id过滤仓库
     * 
     * @param agencyWarehouseQuery
     * @return
     */
    PageResult<DealerWarehouseDTO> selectDealerWarehouseList(DealerWarehouseQuery agencyWarehouseQuery);

    /**
     * 根据服务商id,仓库id查询经销商费用配置
     * 
     * @param cityWarehouseQuery
     * @return
     */
    PageResult<DealerChargeConfigDTO> selectDealerInfoList(CityWarehouseQuery cityWarehouseQuery);

}
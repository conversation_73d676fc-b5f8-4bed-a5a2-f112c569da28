package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.Pager;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.ordercenter.sdk.serviceability.ServiceAbilityClient;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.dto.WarehouseInventoryReportQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.erp.ERPStoreVO;
import com.yijiupi.himalaya.supplychain.enums.CheckDiffEnum;
import com.yijiupi.himalaya.supplychain.enums.CheckStateEnum;
import com.yijiupi.himalaya.supplychain.enums.StoreTypeEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.PutawayTaskItemWaitCountDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IPutawayTaskService;
import com.yijiupi.himalaya.supplychain.instockorder.dto.qc.QualityControlBillQueryDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.qc.IQualityControlBillService;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.check.OrderCenterInventoryCheckBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventorySyncEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.InventoryConvertor;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ProductStoreConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.ERPCityMergeDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterUnConfirmOrderInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.check.CheckStoreInventoryByCityInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.UuidGenerator;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.productsync.dto.enums.ProductTypeEnums;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoSpecificationDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoSpecificationSerivce;
import com.yijiupi.himalaya.supplychain.service.IWarehouseInventoryReportQueryService;
import com.yijiupi.himalaya.supplychain.storecheck.domain.dto.ForceStoreCheckAddDTO;
import com.yijiupi.himalaya.supplychain.storecheck.service.StoreCheckOrderService;
import com.yijiupi.himalaya.supplychain.storecheck.utils.BigDecimalUtils;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.DisposedProductInventorDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductRelationGroupService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;
import com.yijiupi.himalaya.supplychain.wmsdubbop.adapter.IWarehouseInventoryManageService;
import com.yijiupi.himalaya.supplychain.wmsdubbop.config.ServerPath;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryCheckByWarehouseBL.MIN_DIFF_NUM;

/**
 * 库存对账 【销售库存】 = 仓库库存 - 已下单未发货库存 - 处理品库存 - 待上架数量 - 安全库存（店仓）- 未质检产品数量 【仓库库存】 = ERP库存 - 已发货未完成库存
 * <p>
 * 新版跟中台对账：仓库库存+已发货未完成-已入库未完成=ERP库存
 *
 * <AUTHOR>
 * @date 2019/1/8 15:20
 */
@Service
public class WarehouseInventoryCheckBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseInventoryCheckBL.class);
    
    // 存储ERP二级货主ID到久批货主ID的映射关系
    private Map<String, List<String>> erpToWmsOwnerMap = new HashMap<>(16);
    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;
    @Autowired
    private DeliveryStoreRecordBL deliveryStoreRecordBL;
    @Autowired
    private OrderCenterInventoryCheckBL orderCenterInventoryCheckBL;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IWarehouseInventoryManageService iWarehouseInventoryManageServiceDubbop;
    @Reference
    private StoreCheckOrderService storeCheckOrderService;
    @Autowired
    private InventorySyncRecordBL inventorySyncRecordBL;
    @Reference
    private IProductRelationGroupService iProductRelationGroupService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IProductInfoSpecificationSerivce productInfoSpecificationSerivce;
    @Reference
    private IPutawayTaskService iPutawayTaskService;
    // @ReferGateway(path = ServerPath.EASY_CHAIN)
    // private ShopProductB2bProductRangeQueryService shopProductB2bProductRangeQueryService;
    @Reference
    private IWarehouseQueryService warehouseQueryService;
    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;
    @Autowired
    private SaleInventoryByOmsQueryBL saleInventoryByOmsQueryBL;
    @Reference
    private IContentConfigurationService iContentConfigurationService;
    @Reference
    private IQualityControlBillService iQualityControlBillService;
    @Autowired
    private InventorySyncEventFireBL inventorySyncEventFireBL;
    @Autowired
    private ServiceAbilityClient serviceAbilityClient;

    @Reference(timeout = 30000)
    private OwnerService ownerService;
    @Reference
    private IWarehouseInventoryReportQueryService warehouseInventoryReportQueryService;

    @Value("${ordercenter.sdk.timeout}")
    private Integer timeout;

    // 销售库存是否使用redis
    private static final String SALE_INVENTORY_USE_REDIS = "waitDeliveryCountUseRedis";

    // 获取在途库存数量批量查询数目
    private static final String ALLOT_DELIVERYING_COUNT = "AllotDeliveryingCount";

    /**
     * 判断是否是店仓仓库
     */
    public boolean isShopWarehouse(Integer warehouseId) {
        Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        if (warehouse != null && Objects.equals(warehouse.getWarehouseType(), (int) WarehouseTypeEnum.店仓合一.getType())) {
            return true;
        }
        return false;
    }

    /**
     * 读取内容配置获取当前城市查销售库存是否使用redis
     *
     * @return
     */
    private boolean isUseRedis(Integer cityId) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        // 得到内容配置
        String contentValue = iContentConfigurationService.getContentValue(SALE_INVENTORY_USE_REDIS, null, "");
        if (!StringUtils.isEmpty(contentValue)) {
            List<String> contentList = JSON.parseArray(contentValue, String.class);
            if (contentList.contains(cityId.toString())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 读取内容配置获取在途库存数量批量查询数目
     *
     * @return
     */
    public Integer getAllotDeliveryingCount() {
        // 得到内容配置
        String contentValue = iContentConfigurationService.getContentValue(ALLOT_DELIVERYING_COUNT, null, "30");
        if (!StringUtils.isEmpty(contentValue)) {
            return Integer.valueOf(contentValue);
        }
        return null;
    }

    /**
     * 获取销售库存
     */
    public List<WarehouseStoreDTO> getSaleInventoryList(ProductStoreQueryDTO productStoreQueryDTO) {
        return saleInventoryByOmsQueryBL.getSaleInventoryList(productStoreQueryDTO);
        // // 店仓仓库 || 当前城市满足使用redis内容配置
        // if (isShopWarehouse(productStoreQueryDTO.getWarehouseId()) || isUseRedis(productStoreQueryDTO.getCityId())) {
        // // LOGGER.info("查询销售库存使用redis：{}-{}", productStoreQueryDTO.getCityId(),
        // // productStoreQueryDTO.getWarehouseId());
        // return saleInventoryByOmsQueryBL.getSaleInventoryList(productStoreQueryDTO);
        // } else {
        // // LOGGER.info("查询销售库存直接查数据库：{}", productStoreQueryDTO.getCityId());
        // // 1、查仓库库存
        // List<WarehouseStoreDTO> warehouseStoreDTOList = getWarehouseStoreDTOS(productStoreQueryDTO);
        // // 2、计算销售库存
        // processSaleInventory(productStoreQueryDTO, warehouseStoreDTOList);
        // return warehouseStoreDTOList;
        // }
    }

    /**
     * 获取仓库库存
     */
    public List<WarehouseStoreDTO> getInventoryList(ProductStoreQueryDTO productStoreQueryDTO) {
        // 1、查仓库库存
        List<WarehouseStoreDTO> warehouseStoreDTOList = getWarehouseStoreDTOS(productStoreQueryDTO);
        // 2、将仓库库存中的大宗库存合并到酒批库存中
        if (warehouseStoreDTOList != null && !CollectionUtils.isEmpty(warehouseStoreDTOList)) {
            mergeWarehouseStoreOfLarge(warehouseStoreDTOList);
        }
        return warehouseStoreDTOList;
    }

    public PageList<WarehouseStoreDTO> getWarehouseInventoryList(WarehouseProductStoreQueryDTO productStoreQueryDTO) {
        PageList<WarehouseStoreDTO> pagerResult = new PageList<>();
        List<WarehouseStoreDTO> warehouseStoreDTOList = new ArrayList<>();
        // 如果按产品sku查询库存，50个一组分别查询
        if (!CollectionUtils.isEmpty(productStoreQueryDTO.getProductSkuIds())) {
            ProductStoreQueryDTO queryDTO = new ProductStoreQueryDTO();
            BeanUtils.copyProperties(productStoreQueryDTO, queryDTO);
            List<List<Long>> splitProductskuIdList =
                    InventoryConvertor.splitListNew(productStoreQueryDTO.getProductSkuIds(), 50);
            for (List<Long> skuIdList : splitProductskuIdList) {
                // 按产品sku分组去查询
                productStoreQueryDTO.setProductSkuIds(skuIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getProductInventorys(queryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    warehouseStoreDTOList.addAll(splitStoreDTOList);
                }
            }
            pagerResult.setDataList(warehouseStoreDTOList);
            pagerResult.setPager(
                    new Pager(productStoreQueryDTO.getPageNum(), Integer.MAX_VALUE, warehouseStoreDTOList.size()));
            // 如果按产品规格和ownerId查询库存，50个一组分别查询
        } else if (!CollectionUtils.isEmpty(productStoreQueryDTO.getSpecAndOwnerIds())) {
            ProductStoreQueryDTO queryDTO = new ProductStoreQueryDTO();
            BeanUtils.copyProperties(productStoreQueryDTO, queryDTO);
            List<List<ProductSpecAndOwnerIdDTO>> specAndOwnerIdList =
                    InventoryConvertor.splitListNew(productStoreQueryDTO.getSpecAndOwnerIds(), 50);
            for (List<ProductSpecAndOwnerIdDTO> specIdList : specAndOwnerIdList) {
                // 按规格和ownerId分组去查询
                productStoreQueryDTO.setSpecAndOwnerIds(specIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getProductInventorys(queryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    warehouseStoreDTOList.addAll(splitStoreDTOList);
                }
            }
            pagerResult.setDataList(warehouseStoreDTOList);
            pagerResult.setPager(
                    new Pager(productStoreQueryDTO.getPageNum(), Integer.MAX_VALUE, warehouseStoreDTOList.size()));
            // 按城市id或仓库id查询库存
        } else if (!CollectionUtils.isEmpty(productStoreQueryDTO.getSpecAndOwnerIdAndSecOwnerIds())) {
            ProductStoreQueryDTO queryDTO = new ProductStoreQueryDTO();
            BeanUtils.copyProperties(productStoreQueryDTO, queryDTO);
            List<List<ProductSpecAndOwnerIdDTO>> specAndOwnerAndSecOwnerIdList =
                    InventoryConvertor.splitListNew(productStoreQueryDTO.getSpecAndOwnerIdAndSecOwnerIds(), 50);
            for (List<ProductSpecAndOwnerIdDTO> specIdList : specAndOwnerAndSecOwnerIdList) {
                // 按规格和ownerId和secOwnerId分组去查询
                queryDTO.setSpecAndOwnerIdAndSecOwnerIds(specIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getProductInventorys(queryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    warehouseStoreDTOList.addAll(splitStoreDTOList);
                }
            }
            pagerResult.setDataList(warehouseStoreDTOList);
            pagerResult.setPager(
                    new Pager(productStoreQueryDTO.getPageNum(), Integer.MAX_VALUE, warehouseStoreDTOList.size()));
            // 按城市id或仓库id查询库存
        } else {
            WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
            wareHoseInventoryQueryDTO.setOwnId(productStoreQueryDTO.getOwnerId());
            wareHoseInventoryQueryDTO.setWarehouseId(productStoreQueryDTO.getWarehouseId());
            wareHoseInventoryQueryDTO.setProductSkuIds(productStoreQueryDTO.getProductSkuIds());
            wareHoseInventoryQueryDTO.setSpecAndOwnerIds(productStoreQueryDTO.getSpecAndOwnerIds());
            wareHoseInventoryQueryDTO.setProductSpecIds(productStoreQueryDTO.getProductSpecIds());
            wareHoseInventoryQueryDTO.setPageSize(productStoreQueryDTO.getPageSize());
            wareHoseInventoryQueryDTO.setCurrentPage(productStoreQueryDTO.getPageNum());
            // TODO
            pagerResult = warehouseInventoryQueryBL.getProductInventorysByPager(wareHoseInventoryQueryDTO);
        }

        // 2、将仓库库存中的大宗库存合并到酒批库存中
        if (pagerResult != null && !CollectionUtils.isEmpty(pagerResult.getDataList())) {
            mergeWarehouseStoreOfLarge(pagerResult.getDataList());
        }

        return pagerResult;
    }

    /**
     * 计算销售库存
     */
    @Deprecated
    private void processSaleInventory(ProductStoreQueryDTO productStoreQueryDTO,
                                      List<WarehouseStoreDTO> warehouseStoreDTOList) {
        // 1、将仓库库存中的大宗库存合并到酒批库存中
        mergeWarehouseStoreOfLarge(warehouseStoreDTOList);
        // 将仓库库存中同规格同货主的供应商库存合并
        warehouseStoreDTOList = mergeWarehouseStoreOfSecOwnerId(warehouseStoreDTOList);
        // 2、获取已下单未发货的数量(已下单未发货==销售库存已经扣过了)
        Map<String, BigDecimal> unDeliveryCountMap = getUnDeliveryCountMap(productStoreQueryDTO.getCityId(),
                productStoreQueryDTO.getWarehouseId(), warehouseStoreDTOList);
        // 3、获取处理品库存
        List<Long> productSkuIdList =
                warehouseStoreDTOList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> disposeCountMap = getDisposeProductCountMap(productStoreQueryDTO.getCityId(),
                productStoreQueryDTO.getWarehouseId(), productSkuIdList);
        // 4、获取待上架数量
        Map<String, BigDecimal> waitCountMap = getWaitPutawayCountMap(productStoreQueryDTO.getCityId(),
                productStoreQueryDTO.getWarehouseId(), productSkuIdList);
        // 5、获取店仓安全库存数量
        Map<String, BigDecimal> safetyStoreCountMap = getEasyChainSafetyStoreCountMap(warehouseStoreDTOList);
        // 6、查询未质检产品数量
        Map<String, BigDecimal> qualityControlCountMap = getQualityControlCountMap(warehouseStoreDTOList);
        // 7、计算销售库存 = 仓库库存-已下单未发货库存-处理品库存-待上架数量-安全库存-未质检产品数量
        getSellInventorys(warehouseStoreDTOList, unDeliveryCountMap, disposeCountMap, waitCountMap, safetyStoreCountMap,
                qualityControlCountMap, productStoreQueryDTO.getPrintLog());
        // 8、处理没有入过库的，返回销售库存数量传null
        saleInventoryByOmsQueryBL.fillNotWarehouseStoreDTOList(warehouseStoreDTOList,
                productStoreQueryDTO.getSpecAndOwnerIds(), productStoreQueryDTO.getCityId(),
                productStoreQueryDTO.getWarehouseId());
    }

    /**
     * 将仓库库存中同规格同货主的供应商库存合并
     */
    private List<WarehouseStoreDTO> mergeWarehouseStoreOfSecOwnerId(List<WarehouseStoreDTO> warehouseStoreDTOList) {
        List<WarehouseStoreDTO> mergeWarehouseStoreList = new ArrayList<>();
        // 按规格id+货主id+仓库id分组
        Map<String, List<WarehouseStoreDTO>> warehouseStoreMap = warehouseStoreDTOList.stream().collect(Collectors
                .groupingBy(p -> String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId())));
        warehouseStoreMap.forEach((str, list) -> {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            WarehouseStoreDTO warehouseStoreDTO = list.get(0);
            BigDecimal count =
                    list.stream().map(p -> p.getWarehouseTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            warehouseStoreDTO.setWarehouseTotalCount(count);
            warehouseStoreDTO.setSecOwnerId(null);
            mergeWarehouseStoreList.add(warehouseStoreDTO);
        });
        return mergeWarehouseStoreList;
    }

    /**
     * 校正销售库存（指定产品）
     */
    public void checkSellInventory(List<Long> productSkuIds, Integer warehouseId, Integer cityId, Integer opUserId) {
        AssertUtils.notEmpty(productSkuIds, "产品SkuId不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(cityId, "城市id不能为空");
        LOGGER.info("校正销售库存参数：{}", JSON.toJSONString(productSkuIds));
        // 获取销售库存
        ProductStoreQueryDTO productStoreQueryDTO = new ProductStoreQueryDTO();
        productStoreQueryDTO.setCityId(cityId);
        productStoreQueryDTO.setWarehouseId(warehouseId);
        productStoreQueryDTO.setProductSkuIds(productSkuIds);
        productStoreQueryDTO.setPrintLog(true);
        List<WarehouseStoreDTO> warehouseStoreDTOList = getSaleInventoryList(productStoreQueryDTO);
        if (!CollectionUtils.isEmpty(warehouseStoreDTOList)) {
            // 更新销售库存
            updateSellInventory(warehouseStoreDTOList, opUserId);
        }
    }

    /**
     * 根据cityId, 获取仓库库存
     */
    public List<WarehouseStoreDTO> getInventoryListByCityId(Integer cityId) {
        List<WarehouseStoreDTO> warehouseStoreDTOList = getProductInventorysByCityId(cityId);
        if (warehouseStoreDTOList != null && !CollectionUtils.isEmpty(warehouseStoreDTOList)) {
            // 2、将仓库库存中的大宗库存合并到酒批库存中
            mergeWarehouseStoreOfLarge(warehouseStoreDTOList);
        }
        return warehouseStoreDTOList;
    }

    /**
     * 需要排除自动同步销售库存的城市
     */
    private List<Integer> lstExSaleInventoryCityId = Arrays.asList(106, 720);
    /**
     * 需要排除自动同步销售库存的仓库
     */
    private List<Integer> lstExSaleInventoryWarehouseId = new ArrayList<>();

    /**
     * 更新销售库存
     *
     * @param opUserId
     * @param warehouseStoreDTOList
     */
    private void updateSellInventory(List<WarehouseStoreDTO> warehouseStoreDTOList, Integer opUserId) {
        List<List<WarehouseStoreDTO>> lists = InventoryConvertor.splitList(warehouseStoreDTOList, 50);
        for (List<WarehouseStoreDTO> list : lists) {
            // 调交易平台接口,修复销售库存.
            iWarehouseInventoryManageServiceDubbop
                    .setSellInventory(InventoryConvertor.warehouseStoreDTOS2WarehouseInventoryModDTOS(list, opUserId));
        }
    }

    /**
     * 获取需要更新的销售库存
     *
     * @param warehouseStoreDTOList
     * @param unDeliveryCountMap
     * @param disposeCountMap
     */
    private void getSellInventorys(List<WarehouseStoreDTO> warehouseStoreDTOList,
                                   Map<String, BigDecimal> unDeliveryCountMap, Map<String, BigDecimal> disposeCountMap,
                                   Map<String, BigDecimal> waitCountMap, Map<String, BigDecimal> safetyStoreCountMap,
                                   Map<String, BigDecimal> qualityControlCountMap, Boolean isPrintLog) {
        warehouseStoreDTOList.forEach(p -> {
            String productSkuIdCityIDWarehouseId =
                    String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId());
            BigDecimal orderUnDeliveryCount =
                    (unDeliveryCountMap == null || unDeliveryCountMap.get(productSkuIdCityIDWarehouseId) == null)
                            ? BigDecimal.ZERO : unDeliveryCountMap.get(productSkuIdCityIDWarehouseId);
            BigDecimal disposeCount =
                    (disposeCountMap == null || disposeCountMap.get(productSkuIdCityIDWarehouseId) == null)
                            ? BigDecimal.ZERO : disposeCountMap.get(productSkuIdCityIDWarehouseId);
            BigDecimal waitCount = (waitCountMap == null || waitCountMap.get(productSkuIdCityIDWarehouseId) == null)
                    ? BigDecimal.ZERO : waitCountMap.get(productSkuIdCityIDWarehouseId);
            BigDecimal safeCount =
                    (safetyStoreCountMap == null || safetyStoreCountMap.get(productSkuIdCityIDWarehouseId) == null)
                            ? BigDecimal.ZERO : safetyStoreCountMap.get(productSkuIdCityIDWarehouseId);
            BigDecimal qualityCount =
                    (qualityControlCountMap == null || qualityControlCountMap.get(productSkuIdCityIDWarehouseId) == null)
                            ? BigDecimal.ZERO : qualityControlCountMap.get(productSkuIdCityIDWarehouseId);
            // 销售库存数量
            p.setSaleStoreTotalCount(p.getWarehouseTotalCount().subtract(orderUnDeliveryCount).subtract(disposeCount)
                    .subtract(waitCount).subtract(safeCount).subtract(qualityCount));
            if (isPrintLog) {
                LOGGER.info(String.format(
                        "WarehouseId：%s, Sku：%s, Store:%s, unDeliveryCount:%s, disposeCount:%s, waitCount:%s, safeCount:%s, qualityCount:%s, saleStore:%s",
                        p.getWarehouseId(), p.getProductSkuId(), p.getWarehouseTotalCount(), orderUnDeliveryCount,
                        disposeCount, waitCount, safeCount, qualityCount, p.getSaleStoreTotalCount()));
            }
        });
    }

    /**
     * 获取待上架数量
     *
     * @return
     */
    private Map<String, BigDecimal> getWaitPutawayCountMap(Integer cityId, Integer warehouseId,
                                                           List<Long> productSkuIds) {
        Map<String, BigDecimal> waitCountMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(productSkuIds)) {
            List<List<Long>> splitProductskuIdList = InventoryConvertor.splitListNew(productSkuIds, 200);
            for (List<Long> skuIdList : splitProductskuIdList) {
                List<PutawayTaskItemWaitCountDTO> waitCountDTOS =
                        iPutawayTaskService.listWaitPutawayCount(cityId, warehouseId, skuIdList);
                if (!CollectionUtils.isEmpty(waitCountDTOS)) {
                    waitCountDTOS.forEach(p -> {
                        String key = String.format("%s-%s-%s", p.getProductSpecificationId(), p.getOwnerId(),
                                p.getWarehouseId());
                        BigDecimal count = p.getCount();
                        if (waitCountMap.containsKey(key)) {
                            count = count.add(waitCountMap.get(key));
                        }
                        waitCountMap.put(key, count);
                    });
                }
            }
        }
        return waitCountMap;
    }

    /**
     * 获取残次品货位库存
     *
     * @return
     */
    private List<WarehouseStoreDTO> getBatchInventoryByDisposeLocation(Integer cityId, Integer warehouseId,
                                                                       List<Long> productSkuIds) {
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setCityId(cityId);
        wareHoseInventoryQueryDTO.setWarehouseId(warehouseId);
        wareHoseInventoryQueryDTO.setProductSkuIds(productSkuIds);
        List<WarehouseStoreDTO> warehouseStoreDTOList =
                warehouseInventoryQueryBL.getDisposedProductInventories(wareHoseInventoryQueryDTO);
        return warehouseStoreDTOList;
    }

    /**
     * 获取处理品库存
     *
     * @return
     */
    private Map<String, BigDecimal> getDisposeProductCountMap(Integer cityId, Integer warehouseId,
                                                              List<Long> productSkuIds) {
        List<WarehouseStoreDTO> disposeCountListAll = new ArrayList<>();
        List<List<Long>> splitProductskuIdList = InventoryConvertor.splitListNew(productSkuIds, 500);
        for (List<Long> skuIdList : splitProductskuIdList) {
            List<WarehouseStoreDTO> batchInventoryDTOS =
                    getBatchInventoryByDisposeLocation(cityId, warehouseId, skuIdList);
            if (!CollectionUtils.isEmpty(batchInventoryDTOS)) {
                disposeCountListAll.addAll(batchInventoryDTOS);
            }
        }
        // LOGGER.info("获取处理品库存: {}", JSON.toJSONString(disposeCountListAll));
        Map<String, BigDecimal> disposeCountMap = getStringBigDecimalMap(disposeCountListAll);
        return disposeCountMap;
    }

    private Map<String, BigDecimal> getStringBigDecimalMap(List<WarehouseStoreDTO> disposeCountList) {
        Map<String, BigDecimal> disposeCountMap = new HashMap<>(16);
        if (disposeCountList != null && !CollectionUtils.isEmpty(disposeCountList)) {
            disposeCountList.forEach(n -> {
                String key = String.format("%s-%s-%s", n.getProductSpecId(), n.getOwnerId(), n.getWarehouseId());
                BigDecimal count = n.getWarehouseTotalCount();
                if (disposeCountMap.containsKey(key)) {
                    count = count.add(disposeCountMap.get(key));
                }
                disposeCountMap.put(key, count);
            });
        }
        return disposeCountMap;
    }

    /**
     * 获取处理品及陈列品库存
     *
     * @return
     */
    public BigDecimal getDisposeProductCountBySkuId(Integer cityId, Integer warehouseId, Long productSkuId) {
        BigDecimal result = BigDecimal.ZERO;
        List<Long> realQuerySkuList = Lists.newArrayList(productSkuId);
        realQuerySkuList.addAll(getProductRefSkuIdList(cityId, warehouseId, productSkuId));
        List<WarehouseStoreDTO> disposeCountListAll =
                getBatchInventoryByDisposeLocation(cityId, warehouseId, realQuerySkuList);
        // LOGGER.info("获取处理品及陈列品SKU信息：{}, 库存结果：{}", JSON.toJSONString(realQuerySkuList),
        // JSON.toJSONString(disposeCountListAll));
        if (!CollectionUtils.isEmpty(disposeCountListAll)) {
            for (WarehouseStoreDTO dto : disposeCountListAll) {
                result = result.add(ObjectUtils.defaultIfNull(dto.getWarehouseTotalCount(), BigDecimal.ZERO));
            }
        }
        return result;
    }

    // 获取产品-关联产品SKU信息
    private List<Long> getProductRefSkuIdList(Integer cityId, Integer warehouseId, Long productSkuId) {
        List<Long> refSkuIdList = Lists.newArrayList();
        Map<Long, List<ProductSkuDTO>> refProductMap = iProductRelationGroupService
                .findSameGroupProductBySkuIds(warehouseId, Collections.singletonList(productSkuId));
        if (refProductMap != null && !refProductMap.isEmpty()
                && !CollectionUtils.isEmpty(refProductMap.get(productSkuId))) {
            List<Long> skuIdList =
                    refProductMap.get(productSkuId).stream().filter(e -> e != null && e.getProductSkuId() != null)
                            .map(e -> e.getProductSkuId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(skuIdList)) {
                refSkuIdList.addAll(skuIdList);
            }
        }
        return refSkuIdList;
    }

    public Map<Long, BigDecimal> getBatchDisposeProductCountBySkuId(Integer cityId, Integer warehouseId,
                                                                    List<Long> productSkuIdList, Map<Long, List<ProductSkuDTO>> relationMap) {
        if (CollectionUtils.isEmpty(productSkuIdList)) {
            return Maps.newHashMap();
        }
        productSkuIdList = productSkuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 查询传递产品信息
        List<ProductSkuDTO> allSkuDTOList = iProductSkuQueryService.findBySku(productSkuIdList);
        Map<Long, ProductSkuDTO> allSkuMap = allSkuDTOList.stream().filter(e -> e != null)
                .collect(Collectors.toMap(e -> e.getProductSkuId(), Function.identity()));
        // 记录所有 skuId
        List<Long> allSkuIdList = Lists.newArrayList(productSkuIdList);
        // 产品关联关系
        Map<Long, List<ProductSkuDTO>> refProductMap = new HashMap<>(16);
        if (relationMap == null || relationMap.isEmpty()) {
            // 查询关联产品
            Map<Long, List<ProductSkuDTO>> refConfigMap =
                    iProductRelationGroupService.findSameGroupProductBySkuIds(warehouseId, productSkuIdList);
            // 添加关联关系
            refProductMap.putAll(refConfigMap);
        } else {
            // 外部传递进来则用外部关联关系
            refProductMap.putAll(relationMap);
        }
        if (refProductMap != null && !refProductMap.isEmpty()) {
            List<Long> refAllSkuIds = refProductMap.values().stream().filter(e -> !CollectionUtils.isEmpty(e))
                    .flatMap(e -> e.stream()).filter(e -> e != null && e.getProductSkuId() != null)
                    .map(e -> e.getProductSkuId()).collect(Collectors.toList());
            allSkuIdList.addAll(refAllSkuIds);
        }
        // 去重
        allSkuIdList = allSkuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 因调用其他系统拆分成多个调用
        List<List<Long>> partition = Lists.partition(allSkuIdList, 500);
        List<WarehouseStoreDTO> disposeCountListAll = new ArrayList<>();
        for (List<Long> skuIds : partition) {
            List<WarehouseStoreDTO> disposeProductCount =
                    getBatchInventoryByDisposeLocation(cityId, warehouseId, skuIds);
            if (!CollectionUtils.isEmpty(disposeProductCount)) {
                disposeCountListAll.addAll(disposeProductCount);
            }
        }
        Map<Long, List<WarehouseStoreDTO>> cityMergeMap =
                disposeCountListAll.stream().filter(e -> e != null && e.getProductSkuId() != null)
                        .collect(Collectors.groupingBy(WarehouseStoreDTO::getProductSkuId));
        // 结果
        Map<Long, BigDecimal> result = new HashMap<>(16);
        for (Long skuId : productSkuIdList) {
            // 记录所有结果数据
            List<WarehouseStoreDTO> curCityMergeList = new ArrayList<>();
            // 当前产品 sku 信息
            ProductSkuDTO skuDTO = allSkuMap.get(skuId);
            // 关联产品 sku 信息
            List<ProductSkuDTO> skuDTOList = refProductMap.get(skuId);
            if (!CollectionUtils.isEmpty(skuDTOList)) {
                for (ProductSkuDTO refSkuDTO : skuDTOList) {
                    List<WarehouseStoreDTO> cityMergeDTOS = cityMergeMap.get(refSkuDTO.getProductSkuId());
                    if (CollectionUtils.isEmpty(cityMergeDTOS)) {
                        continue;
                    }
                    curCityMergeList.addAll(cityMergeDTOS);
                }
            }
            List<WarehouseStoreDTO> mainMergeList = cityMergeMap.get(skuId);
            if (!CollectionUtils.isEmpty(mainMergeList)) {
                curCityMergeList.addAll(mainMergeList);
            }

            BigDecimal totalCount = curCityMergeList.stream().filter(e -> e != null)
                    .map(e -> ObjectUtils.defaultIfNull(e.getWarehouseTotalCount(), BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 记录结果
            result.put(skuId, totalCount);
        }
        return result;
    }

    public List<DisposedProductInventorDTO> findDisposedProductInventorBySkuId(Integer cityId, Integer warehouseId,
                                                                               List<Long> productSkuIdList) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(productSkuIdList, "产品skuId信息不能为空");
        productSkuIdList = productSkuIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 查询传递产品信息
        List<ProductSkuDTO> allSkuDTOList = iProductSkuQueryService.findBySku(productSkuIdList);
        Map<Long, ProductSkuDTO> allSkuMap = allSkuDTOList.stream().filter(e -> e != null)
                .collect(Collectors.toMap(e -> e.getProductSkuId(), Function.identity()));
        // 记录所有 skuId
        List<Long> allSkuIdList = Lists.newArrayList(productSkuIdList);
        // 产品关联关系
        Map<Long, List<ProductSkuDTO>> refProductMap =
                iProductRelationGroupService.findSameGroupProductBySkuIds(warehouseId, productSkuIdList);
        if (refProductMap == null) {
            refProductMap = new HashMap<>(16);
        }
        List<Long> refAllSkuIds = refProductMap.values().stream().filter(e -> !CollectionUtils.isEmpty(e))
                .flatMap(e -> e.stream()).filter(e -> e != null && e.getProductSkuId() != null)
                .map(e -> e.getProductSkuId()).collect(Collectors.toList());
        allSkuIdList.addAll(refAllSkuIds);
        // 去重
        allSkuIdList = allSkuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 因调用其他系统拆分成多个调用
        List<List<Long>> partition = Lists.partition(allSkuIdList, 500);
        List<DisposedProductInventorDTO> disposeCountListAll = new ArrayList<>();
        for (List<Long> skuIds : partition) {
            WareHoseInventoryQueryDTO inventoryQueryDTO = new WareHoseInventoryQueryDTO();
            inventoryQueryDTO.setCityId(cityId);
            inventoryQueryDTO.setWarehouseId(warehouseId);
            inventoryQueryDTO.setProductSkuIds(skuIds);
            List<DisposedProductInventorDTO> disposedProductInventorDetails =
                    warehouseInventoryQueryBL.findDisposedProductInventorDetails(inventoryQueryDTO);
            if (!CollectionUtils.isEmpty(disposedProductInventorDetails)) {
                disposeCountListAll.addAll(disposedProductInventorDetails);
            }
        }
        // 结果
        List<DisposedProductInventorDTO> result = new ArrayList<>(productSkuIdList.size());
        for (Long skuId : productSkuIdList) {
            List<DisposedProductInventorDTO> subDisposedInventors = new ArrayList<>();
            // 查找自己残次品信息
            List<DisposedProductInventorDTO> disposedInventors = disposeCountListAll.stream()
                    .filter(e -> e != null && Objects.equals(e.getProductSkuId(), skuId)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(disposedInventors)) {
                subDisposedInventors.addAll(disposedInventors);
            }
            // 查找关联产品残次品信息
            List<ProductSkuDTO> refSkuList = refProductMap.get(skuId);
            if (!CollectionUtils.isEmpty(refSkuList)) {
                List<Long> refSkuIds = refSkuList.stream().filter(Objects::nonNull).map(e -> e.getProductSkuId())
                        .distinct().collect(Collectors.toList());
                List<DisposedProductInventorDTO> refDisposedInventors = disposeCountListAll.stream()
                        .filter(e -> e != null && refSkuIds.contains(e.getProductSkuId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(refDisposedInventors)) {
                    subDisposedInventors.addAll(refDisposedInventors);
                }
            }
            if (!CollectionUtils.isEmpty(subDisposedInventors)) {
                subDisposedInventors.stream().filter(Objects::nonNull).forEach(e -> e.setProductSkuId(skuId));
                result.addAll(subDisposedInventors);
            }
        }
        return result;
    }

    /**
     * 获取已下单未发货的数量
     *
     * @return
     */
    @Deprecated
    private Map<String, BigDecimal> getUnDeliveryCountMap(Integer cityId, Integer warehouseId,
                                                          List<WarehouseStoreDTO> warehouseStoreDTOList) {
        Map<String, BigDecimal> unDeliveryCountMap = new HashMap<>(16);
        List<List<WarehouseStoreDTO>> lists = InventoryConvertor.splitList(warehouseStoreDTOList, 30);
        for (List<WarehouseStoreDTO> list : lists) {
            List<ERPCityMergeDTO> infoList = list.stream().map(p -> {
                ERPCityMergeDTO cityMergeDTO = new ERPCityMergeDTO();
                cityMergeDTO.setProductSpecificationId(p.getProductSpecId());
                cityMergeDTO.setProductOwnerId(p.getOwnerId());
                return cityMergeDTO;
            }).collect(Collectors.toList());
            // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 未处理
            // LOGGER.info("已下单未发货数量入参:{}, cityId:{}, warehouseId:{}", JSON.toJSONString(infoList),
            // cityId, warehouseId);
            // List<CityMergeDTO> count = iOrderQueryService.getCityMergeCountBySkus(infoList, cityId, warehouseId);
            // // LOGGER.info("已下单未发货数量count: {}", JSON.toJSONString(count));
            // if (!CollectionUtils.isEmpty(count)) {
            // count.forEach(n -> {
            // unDeliveryCountMap.put(String.format("%s-%s-%s", n.getProductSpecificationId(),
            // n.getProductOwnerId(), n.getWarehouseId()), n.getCount());
            // });
            // count.clear();
            // }
        }
        lists.clear();
        return unDeliveryCountMap;
    }

    /**
     * 获取在途库存数量
     *
     * @return
     */
    private Map<String, BigDecimal> getAllotDeliveryingCountMap(List<WarehouseInventoryDTO> warehouseStoreDTOList,
                                                                Map<Integer, Boolean> openCenterMap) {
        Map<String, BigDecimal> allotDeliveryingCountMap = new HashMap<>(16);

        return allotDeliveryingCountMap;
    }

    /**
     * 将仓库库存中的大宗库存合并到酒批库存中
     *
     * @param warehouseStoreDTOList
     */
    private void mergeWarehouseStoreOfLarge(List<WarehouseStoreDTO> warehouseStoreDTOList) {
        for (Iterator<WarehouseStoreDTO> it = warehouseStoreDTOList.iterator(); it.hasNext(); ) {
            WarehouseStoreDTO p = it.next();
            if (Objects.equals(p.getChannel(), 1)) {
                String productSkuIdWarehouseId =
                        String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId());
                Optional<WarehouseStoreDTO> warehouseStoreDTO = warehouseStoreDTOList.stream()
                        .filter(q -> Objects.equals(q.getChannel(), 0)
                                && String.format("%s-%s-%s", q.getProductSpecId(), q.getOwnerId(), q.getWarehouseId())
                                .equals(productSkuIdWarehouseId))
                        .findAny();
                warehouseStoreDTO.ifPresent(warehouseInventoryDTO -> warehouseInventoryDTO.setWarehouseTotalCount(
                        p.getWarehouseTotalCount().add(warehouseInventoryDTO.getWarehouseTotalCount())));
                // 移除大宗库存
                it.remove();
            }
        }
    }

    /**
     * 根据条件查询仓库库存
     *
     * @return
     */
    private List<WarehouseStoreDTO> getWarehouseStoreDTOS(ProductStoreQueryDTO productStoreQueryDTO) {
        List<WarehouseStoreDTO> warehouseStoreDTOList = new ArrayList<>();
        // 如果按产品sku查询库存，50个一组分别查询
        if (!CollectionUtils.isEmpty(productStoreQueryDTO.getProductSkuIds())) {
            List<List<Long>> splitProductskuIdList =
                    InventoryConvertor.splitListNew(productStoreQueryDTO.getProductSkuIds(), 50);
            for (List<Long> skuIdList : splitProductskuIdList) {
                // 按产品sku分组去查询
                productStoreQueryDTO.setProductSkuIds(skuIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getProductInventorys(productStoreQueryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    warehouseStoreDTOList.addAll(splitStoreDTOList);
                }
            }
            // 如果按产品规格和ownerId查询库存，50个一组分别查询
        } else if (!CollectionUtils.isEmpty(productStoreQueryDTO.getSpecAndOwnerIds())) {
            List<List<ProductSpecAndOwnerIdDTO>> specAndOwnerIdList =
                    InventoryConvertor.splitListNew(productStoreQueryDTO.getSpecAndOwnerIds(), 50);
            for (List<ProductSpecAndOwnerIdDTO> specIdList : specAndOwnerIdList) {
                // 按规格和ownerId分组去查询
                productStoreQueryDTO.setSpecAndOwnerIds(specIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getProductInventorys(productStoreQueryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    warehouseStoreDTOList.addAll(splitStoreDTOList);
                }
            }
            // 按城市id或仓库id查询库存
        } else {
            warehouseStoreDTOList = getProductInventorysByPager(productStoreQueryDTO);
        }
        return warehouseStoreDTOList;
    }

    /**
     * 查询仓库库存
     *
     * @return
     */
    private List<WarehouseStoreDTO> getProductInventorys(ProductStoreQueryDTO productStoreQueryDTO) {
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setOwnId(productStoreQueryDTO.getOwnerId());
        wareHoseInventoryQueryDTO.setWarehouseId(productStoreQueryDTO.getWarehouseId());
        wareHoseInventoryQueryDTO.setProductSkuIds(productStoreQueryDTO.getProductSkuIds());
        wareHoseInventoryQueryDTO.setSpecAndOwnerIds(productStoreQueryDTO.getSpecAndOwnerIds());
        wareHoseInventoryQueryDTO.setProductSpecIds(productStoreQueryDTO.getProductSpecIds());
        wareHoseInventoryQueryDTO
                .setSpecAndOwnerIdAndSecOwnerIds(productStoreQueryDTO.getSpecAndOwnerIdAndSecOwnerIds());
        List<WarehouseStoreDTO> warehouseStoreDTOList =
                warehouseInventoryQueryBL.getProductInventorys(wareHoseInventoryQueryDTO);
        return warehouseStoreDTOList;
    }

    /**
     * 查询仓库库存
     *
     * @return
     */
    private List<WarehouseStoreDTO> getProductInventorysByPager(ProductStoreQueryDTO productStoreQueryDTO) {
        LOGGER.info("根据条件查询仓库库存，仓库id：{}", JSON.toJSONString(productStoreQueryDTO));
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setOwnId(productStoreQueryDTO.getOwnerId());
        wareHoseInventoryQueryDTO.setWarehouseId(productStoreQueryDTO.getWarehouseId());
        wareHoseInventoryQueryDTO.setProductSkuIds(productStoreQueryDTO.getProductSkuIds());
        wareHoseInventoryQueryDTO.setSpecAndOwnerIds(productStoreQueryDTO.getSpecAndOwnerIds());
        wareHoseInventoryQueryDTO.setProductSpecIds(productStoreQueryDTO.getProductSpecIds());

        wareHoseInventoryQueryDTO.setPageSize(1000);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("根据条件查询仓库库存");
        PageList<WarehouseStoreDTO> result =
                warehouseInventoryQueryBL.getProductInventoryByHandPager(wareHoseInventoryQueryDTO);
        stopWatch.stop();
        if (stopWatch.getTotalTimeSeconds() >= 30) {
            LOGGER.error("查询时间过长:{}", stopWatch.prettyPrint());
        }
        return result.getDataList();
        // 遍历剩余的页数
        // Integer totalPage = 1;
        // int pageSize = 1000;
        // for (int i = 1; i <= totalPage; i++) {
        // if (i == 1) {
        // PageHelper.startPage(i, pageSize);
        // } else {
        // PageHelper.startPage(i, pageSize, false);
        // }
        // wareHoseInventoryQueryDTO.setCurrentPage(i);
        // PageList<WarehouseStoreDTO> pagerResult =
        // warehouseInventoryQueryBL.getProductInventorysByPager(wareHoseInventoryQueryDTO);
        // if (pagerResult != null && pagerResult.getDataList() != null && pagerResult.getDataList().size() > 0) {
        // if (i == 1) {
        // totalPage = pagerResult.getPager().getTotalPage();
        // }
        // warehouseStoreDTOList.addAll(pagerResult.getDataList());
        // pagerResult.getDataList().clear();
        // }
        // }
        // return warehouseStoreDTOList;
    }

    /**
     * 根据城市id查询仓库库存
     *
     * @return
     */
    public List<WarehouseStoreDTO> getProductInventorysByCityId(Integer cityId) {
        LOGGER.info("根据城市id查询仓库库存，仓库id：{}", cityId);
        List<WarehouseStoreDTO> lstResult = new ArrayList<>();
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setCityId(cityId);
        wareHoseInventoryQueryDTO.setPageSize(1000);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("根据城市id查询仓库库存");
        PageList<WarehouseStoreDTO> pageList =
                warehouseInventoryQueryBL.getProductInventoryByHandPager(wareHoseInventoryQueryDTO);
        stopWatch.stop();
        if (stopWatch.getTotalTimeSeconds() >= 30) {
            LOGGER.error("查询时间过长:{}", stopWatch.prettyPrint());
        }
        return pageList.getDataList();
        // // 遍历剩余的页数
        // Integer totalPage = 1;
        // int pageSize = 1000;
        // for (int i = 1; i <= totalPage; i++) {
        // if (i == 1) {
        // PageHelper.startPage(i, pageSize);
        // } else {
        // PageHelper.startPage(i, pageSize, false);
        // }
        // wareHoseInventoryQueryDTO.setCurrentPage(i);
        // PageList<WarehouseStoreDTO> pagerResult =
        // warehouseInventoryQueryBL.getProductInventorysByPager(wareHoseInventoryQueryDTO);
        // if (pagerResult != null && pagerResult.getDataList() != null && pagerResult.getDataList().size() > 0) {
        // if (i == 1) {
        // totalPage = pagerResult.getPager().getTotalPage();
        // }
        // lstResult.addAll(pagerResult.getDataList());
        // pagerResult.getDataList().clear();
        // }
        // }
        //
        // return lstResult;
    }

    /**
     * 需要排除根据ERP自动同步库存的城市
     */
    private List<Integer> lstExStoreInventoryCityId = Arrays.asList(897, 899, 411, 9000001);
    /**
     * 需要排除根据ERP自动同步库存的仓库
     */
    private List<Integer> lstExStoreInventoryWarehouseId = Arrays.asList(8981, 8984, 8987);

    /**
     * 根据城市id，库存对账（只保存库存对比记录，不矫正实际库存） （仓库库存 = ERP库存 - 已发货未完成库存）
     */
    public void checkStoreInventoryByCityId(Integer cityId, Integer opUserId, boolean isSnap) {
        checkStoreInventoryByCityId(new CheckStoreInventoryByCityInfoDTO(cityId, opUserId, isSnap,
                CheckStoreInventoryByCityInfoDTO.OLD_VERSION));
    }

    public void checkStoreInventoryByCityId(CheckStoreInventoryByCityInfoDTO dto) {
        if (lstExStoreInventoryCityId.contains(dto.getCityId())) {
            LOGGER.info(String.format("城市{}不需要同步ERP库存！", dto.getCityId()));
            return;
        }
        LOGGER.info("开始库存对账cityId:{}", dto.getCityId());
        try {
            Long dtStart = System.currentTimeMillis();
            // 1、查询仓库库存
            List<WarehouseInventoryDTO> warehouseInventoryDTOList = getWarehouseInventoryDTOList(dto);
            Long dtFirst = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账cityId: %s, -第一步查库存，耗时：%sms，共%s条", dto.getCityId(), (dtFirst - dtStart),
                    warehouseInventoryDTOList.size()));
            processSyncRecordByList(dto, warehouseInventoryDTOList, dtFirst);
            warehouseInventoryDTOList.clear();
        } catch (Exception e) {
            LOGGER.info("库存对账失败，cityId:{}，{}", dto.getCityId(), e.getMessage());
        }
        LOGGER.info("结束库存对账cityId:{}", dto.getCityId());
    }

    /**
     * 根据城市id，库存对账（只保存库存对比记录，不矫正实际库存） （仓库库存 = ERP库存 - 已发货未完成库存）
     */
    public void checkStoreInventoryByOwner(Integer opUserId) {
        // LOGGER.info("开始记录有货主的库存快照");
        // Long dtStart = System.currentTimeMillis();
        // // 1、查询仓库库存
        // List<WarehouseInventoryDTO> warehouseInventoryDTOList = getWarehouseInventoryDTOListByOwner();
        // Long dtFirst = System.currentTimeMillis();
        // LOGGER.info(String.format("根据ERP库存对账-第一步查库存，耗时：%sms，共%s条", (dtFirst - dtStart),
        // warehouseInventoryDTOList.size()));
        // processSyncRecordByList(null, opUserId, true, warehouseInventoryDTOList, dtFirst);
        // warehouseInventoryDTOList.clear();
        // LOGGER.info("结束记录有货主的库存快照");
    }

    private void processSyncRecordByList(CheckStoreInventoryByCityInfoDTO dto,
                                         List<WarehouseInventoryDTO> warehouseInventoryDTOList, Long dtFirst) {
        Integer cityId = dto.getCityId();
        Integer opUserId = dto.getOpUserId();
        boolean isSnap = dto.getIsIsSnap();
        if (warehouseInventoryDTOList != null && !CollectionUtils.isEmpty(warehouseInventoryDTOList)) {
            // 移除总部虚拟仓库，易酒批统采区域直供仓，统采（虚拟仓）三个虚拟仓库
            warehouseInventoryDTOList.removeIf(p -> lstExStoreInventoryWarehouseId.contains(p.getWarehouseId()));
            // 2、将仓库库存中的大宗库存合并到酒批库存中
            mergeWarehouseInventoryOfLarge(warehouseInventoryDTOList);
            Long dtSecond = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第二步查合并大宗库存cityId: %s，耗时：%sms", cityId, (dtSecond - dtFirst)));
            // 3-1、获取已发货未完成的数量
            List<Integer> warehouseIdList =
                    warehouseInventoryDTOList.stream().map(p -> p.getWarehouseId()).distinct().collect(Collectors.toList());

            Map<Integer, Boolean> openCenterMap =
                    orderCenterInventoryCheckBL.openOrderCenterMap(warehouseIdList, dto.getVersion());
            Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount =
                    orderCenterInventoryCheckBL.getOpenCenterCount(warehouseIdList, openCenterMap);

            Map<String, BigDecimal> deliveryCountMap =
                    isSnap ? new HashMap<>(16) : getDeliveryCountMap(cityId, warehouseIdList, openCenterCount);
            Long dtThird_1 = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第三-1步查已发货未完成数量cityId: %s，耗时：%sms，共%s条", cityId, (dtThird_1 - dtSecond),
                    deliveryCountMap.size()));
            // 3-2、获取已收货的数量
            Map<String, BigDecimal> getDeliveryedReturnCountMap =
                    isSnap ? new HashMap<>(16) : getDeliveryedReturnCountMap(cityId, openCenterCount);
            Long dtThird_2 = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第三-2步查已入库未收款退货单数量cityId: %s，耗时：%sms，共%s条", cityId,
                    (dtThird_2 - dtThird_1), getDeliveryedReturnCountMap.size()));
            // 3-3、获取在途库存数量
            Map<String, BigDecimal> allotDeliveryingCountMap =
                    isSnap ? new HashMap<>(16) : getAllotDeliveryingCountMap(warehouseInventoryDTOList, openCenterMap);
            Long dtThird_3 = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第三-3步获取在途库存数量cityId: %s，耗时：%sms，共%s条", cityId, (dtThird_3 - dtThird_2),
                    allotDeliveryingCountMap.size()));
            // 4、查询ERP库存
            Map<String, ERPStoreVO> erpStoreMap = isSnap ? new HashMap<>(16) : getERPStoreVOMap(cityId);
            Long dtFourth = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第四步查ERP库存cityId: %s，耗时：%sms，共%s条", cityId, (dtFourth - dtThird_3),
                    erpStoreMap.size()));
            // 5、获取仓库库存对比记录
            List<InventorySyncRecordDTO> inventorySyncRecordDTOS =
                    getModWarehouseInventoryDTOS(warehouseInventoryDTOList, deliveryCountMap, getDeliveryedReturnCountMap,
                            erpStoreMap, allotDeliveryingCountMap, opUserId, isSnap, openCenterMap);

            Long dtFive = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第五步获取库存对比记录cityId: %s，数量：%s，原始库存数量：%s,耗时：%sms", cityId,
                    inventorySyncRecordDTOS.size(), warehouseInventoryDTOList.size(), (dtFive - dtFourth)));

            warehouseInventoryDTOList.clear();
            deliveryCountMap.clear();
            getDeliveryedReturnCountMap.clear();
            erpStoreMap.clear();

            // // 有差异的数量增加预警
            long count =
                    inventorySyncRecordDTOS.stream().filter(p -> p.getDiff().equals(CheckDiffEnum.有差异.getType())).count();
            if (count > 0) {
                LOGGER.error(String.format("根据ERP库存对账, cityId: %s, 差异数量：%s", cityId, count));
            }

            // 6、新增库存对账记录
            insertInventorySyncRecord(inventorySyncRecordDTOS);
            Long dtSix = System.currentTimeMillis();
            LOGGER.info(String.format("根据ERP库存对账-第六步新增库存对账记录cityId: %s，数量：%s,耗时：%sms", cityId,
                    inventorySyncRecordDTOS.size(), (dtSix - dtFive)));

            inventorySyncRecordDTOS.clear();
        }
    }

    /**
     * 生成强制盘点单
     *
     * @param lstResult
     */
    private void addForceStoreCheck(List<WarehouseInventoryDTO> lstResult) {
        if (!CollectionUtils.isEmpty(lstResult)) {
            Map<Integer, List<WarehouseInventoryDTO>> storeCheckMap = lstResult.stream()
                    .filter(d -> d.getWarehouseId() != null).collect(Collectors.groupingBy(p -> p.getWarehouseId()));
            LOGGER.info(String.format("生成强制盘点单的产品：%s", JSON.toJSONString(storeCheckMap)));
            if (null != storeCheckMap) {
                storeCheckMap.forEach((warehouseId, list) -> {
                    List<Long> skuIds =
                            list.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
                    Integer tempCityId = list.stream().findFirst().get().getCityId();
                    ForceStoreCheckAddDTO forceStoreCheckAddDTO = new ForceStoreCheckAddDTO();
                    forceStoreCheckAddDTO.setWarehouseId(warehouseId);
                    forceStoreCheckAddDTO.setCityId(tempCityId);
                    forceStoreCheckAddDTO.setSkuIds(skuIds);
                    forceStoreCheckAddDTO.setRemark("ERP库存同步");
                    storeCheckOrderService.addForceStoreCheckOrder(forceStoreCheckAddDTO);
                });
            }
        }
    }

    /**
     * 新增校正库存记录
     */
    private void insertInventorySyncRecord(List<InventorySyncRecordDTO> inventorySyncRecordDTOS) {
        if (CollectionUtils.isEmpty(inventorySyncRecordDTOS)) {
            return;
        }
        List<Long> lstSecOwnerIds = inventorySyncRecordDTOS.stream().filter(p -> p.getSecOwnerId() != null)
                .map(p -> p.getSecOwnerId()).distinct().collect(Collectors.toList());
        List<OwnerDTO> ownerDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lstSecOwnerIds)) {
            ownerDTOS = ownerService.listOwnerByIds(lstSecOwnerIds);
        }
        List<List<InventorySyncRecordDTO>> lists =
                InventoryConvertor.splitInventorySyncRecordList(inventorySyncRecordDTOS, 200);
        for (List<InventorySyncRecordDTO> list : lists) {
            try {
                inventorySyncRecordBL.saveInventorySyncRecordList(list);
                inventorySyncEventFireBL.inventorySyncEvent(list, ownerDTOS);
                // list.clear();
            } catch (Exception e) {
                LOGGER.error("新增校正库存记录异常：", e);
                LOGGER.info("新增校正库存记录异常List: {}", JSON.toJSONString(list));
            }
        }
        lists.clear();
        inventorySyncRecordDTOS.clear();
    }
    //
    // private void updateDeliveryStoreRecord(List<WarehouseInventoryDTO> lstResult) {
    // List<List<WarehouseInventoryDTO>> lists = InventoryConvertor.splitInventoryList(lstResult, 500);
    // for (List<WarehouseInventoryDTO> list : lists) {
    // try {
    // // 批量更新已发货总数量
    // updateDeliveryStoreRecordByDTOS(list);
    //// lists.clear();
    // } catch (Exception oe) {
    // LOGGER.info(String.format("更新已发货数量出错:%s，错误信息:%s", JSON.toJSONString(list), oe.getMessage()));
    // }
    // }
    // lists.clear();
    // }
    //
    // /**
    // * 更新仓库库存
    // */
    // private void updateWarehouseInventory(Integer opUserId, List<WarehouseInventoryDTO> lstResult) {
    // List<List<WarehouseInventoryDTO>> lists = InventoryConvertor.splitInventoryList(lstResult, 500);
    // for (List<WarehouseInventoryDTO> list : lists) {
    // try {
    // List<WarehouseInventoryModDTO> storeInventory =
    // InventoryConvertor.warehouseInventoryDTOS2WarehouseInventoryModDTOS(list);
    // LOGGER.info(String.format("更新仓库库存：%s", JSON.toJSONString(storeInventory)));
    // // 更新仓库库存
    // warehouseInventoryManageBL.modWarehouseInventory(storeInventory, opUserId, InventoryChangeTypeEnum.ERP库存同步);
    /// / // 批量更新已发货总数量
    /// / updateDeliveryStoreRecordByDTOS(list);
    // } catch (Exception oe) {
    // LOGGER.info(String.format("更新库存出错:%s，错误信息:%s", JSON.toJSONString(list), oe.getMessage()));
    // }
    // }
    // lists.clear();
    // }

    private static List<Long> lstRongXiaoOwner = Arrays.asList(20000L, 30000L);

    /**
     * 获取有差异的仓库库存 （全量数据存档）
     *
     * @return
     */
    private List<InventorySyncRecordDTO> getModWarehouseInventoryDTOS(
            List<WarehouseInventoryDTO> warehouseInventoryDTOList, Map<String, BigDecimal> deliveryCountMap,
            Map<String, BigDecimal> getDeliveryedReturnCountMap, Map<String, ERPStoreVO> erpStoreMap,
            Map<String, BigDecimal> allotDeliveryingCountMap, Integer opUserId, boolean isSnap,
            Map<Integer, Boolean> openCenterMap) {
        Map<String, InventorySyncRecordDTO> lstResult = new HashMap<>(16);
        Date now = new Date();
        // 创建久批库存的键映射，用于稍后检查ERP中有但久批中没有的情况
        Map<String, WarehouseInventoryDTO> warehouseInventoryMap = new HashMap<>(16);

        warehouseInventoryDTOList.forEach(p -> {
            // 已存在相同库存ID的跳过，避免重复处理
            if (lstResult.containsKey(p.getId())) {
                return;
            }
            String remark = getRemark(openCenterMap, p.getWarehouseId());
            
            // 为久批库存对象设置ERP二级货主ID
            if (p.getSecOwnerId() != null) {
                String wmsOwnerId = p.getSecOwnerId().toString();
                p.setErpSecOwnerId(erpToWmsOwnerMap.entrySet().stream()
                        .filter(entry -> entry.getValue().contains(wmsOwnerId))
                        .map(Map.Entry::getKey)
                        .findFirst()
                        .orElse(null));

                // 07-24 线上问题，久批存在1,2两条二级货主的库存，erp存在货主=2的库存，久批存在货主1的ERP货主ID=2，最终库存对账产生2条记录，都匹配到了ERP库存
                // 这种情况不应该处理兼容逻辑，没匹配到二级货主的，直接跳过
                if (StringUtils.isEmpty(p.getErpSecOwnerId())) {
                    LOGGER.info("为久批库存对象设置ERP二级货主ID失败，wmsOwnerId:{}", wmsOwnerId);
                    return;
                }
            }
            
            String identityKeyByErpSecOwnerId = String.format("%s-%s-%s-%s", p.getProductSpecId(), p.getOwnerId(),
                    p.getWarehouseId(), p.getErpSecOwnerId());
            String identityKeyByScmSecOwnerId = String.format("%s-%s-%s-%s", p.getProductSpecId(), p.getOwnerId(),
                    p.getWarehouseId(), p.getSecOwnerId());
            // 将久批库存加入映射
            warehouseInventoryMap.put(identityKeyByErpSecOwnerId, p);

            if (!erpStoreMap.containsKey(identityKeyByErpSecOwnerId) && !isSnap) {
                // 融销的一级货主不为null，需要跟ERP比较差异
                if ((!Objects.equals(p.getOwnerId(), null) && !lstRongXiaoOwner.contains(p.getOwnerId()))
                        || BigDecimal.ZERO.compareTo(p.getWarehouseTotalCount()) == 0) {
                    return;
                }
                remark += "-ERP不存在";
            }
            ERPStoreVO erpStoreVO = erpStoreMap.get(identityKeyByErpSecOwnerId);
            BigDecimal erpRealCount = erpStoreVO == null ? BigDecimal.ZERO : erpStoreVO.getErpRealCount();
            BigDecimal orderUnDeliveryCount = deliveryCountMap.get(identityKeyByScmSecOwnerId) == null ? BigDecimal.ZERO
                    : deliveryCountMap.get(identityKeyByScmSecOwnerId);
            BigDecimal orderUnReturnCount = getDeliveryedReturnCountMap.get(identityKeyByScmSecOwnerId) == null
                    ? BigDecimal.ZERO : getDeliveryedReturnCountMap.get(identityKeyByScmSecOwnerId);
            BigDecimal allotDeliveryingCount = allotDeliveryingCountMap.get(identityKeyByScmSecOwnerId) == null
                    ? BigDecimal.ZERO : allotDeliveryingCountMap.get(identityKeyByScmSecOwnerId);
            BigDecimal realStoreCount =
                    erpRealCount.subtract(orderUnDeliveryCount).add(orderUnReturnCount).subtract(allotDeliveryingCount);
            LOGGER.info(String.format(
                    "WarehouseId: %s,Sku：%s,Store:%s,TMSDeliveryedCount:%s,TMSUnfinishedReturnCount:%s,allotDeliveryingCount:%s,erpCount:%s,realCount:%s,ERPDetail:%s",
                    p.getWarehouseId(), p.getProductSkuId(), p.getWarehouseTotalCount(), orderUnDeliveryCount,
                    orderUnReturnCount, allotDeliveryingCount, erpRealCount, realStoreCount,
                    JSON.toJSONString(erpStoreVO)));

            // TMS已发货数量
            p.setDeliveryedCount(orderUnDeliveryCount);
            // TMS已退货未完成
            p.setUnReturnCount(orderUnReturnCount);
            if (!isSnap) {
                // 仓库库存(差异值)
                p.setDiffTotalCount(realStoreCount.subtract(p.getWarehouseTotalCount()));
                // 假如差异绝对值小于0.01，设置为0
                if (p.getDiffTotalCount().abs().compareTo(new BigDecimal(MIN_DIFF_NUM)) < 0) {
                    p.setDiffTotalCount(BigDecimal.ZERO);
                }

            } else {
                p.setDiffTotalCount(BigDecimal.ZERO);
            }

            // 校正记录
            InventorySyncRecordDTO inventorySyncRecordDTO = getInventorySyncRecordDTO(p, erpRealCount,
                    orderUnDeliveryCount, orderUnReturnCount, opUserId, now, allotDeliveryingCount, remark);
            if (isSnap) {
                if (inventorySyncRecordDTO.getRemark() == null) {
                    inventorySyncRecordDTO.setRemark("库存快照");
                } else {
                    inventorySyncRecordDTO.setRemark(inventorySyncRecordDTO.getRemark() + "-库存快照");
                }
            }
            lstResult.put(p.getId(), inventorySyncRecordDTO);
        });

        // 处理ERP库存中存在但久批库存中不存在的情况
        if (!isSnap) {
            Map<String, InventorySyncRecordDTO> lstNoResult = new HashMap<>(16);
            
            for (Map.Entry<String, ERPStoreVO> entry : erpStoreMap.entrySet()) {
                String key = entry.getKey();
                ERPStoreVO erpStoreVO = entry.getValue();
                
                // 直接检查久批库存中是否存在该ERP记录
                // 不需要再检查不同的映射，因为现在久批库存的key也是用ERP的二级货主ID构建的
                boolean existsInWarehouse = warehouseInventoryMap.containsKey(key);
                
                // 如果确实不存在于久批库存中，且ERP库存不为零，则创建差异记录
                if (!existsInWarehouse &&
                        erpStoreVO.getErpRealCount() != null &&
                        erpStoreVO.getErpRealCount().compareTo(BigDecimal.ZERO) > 0) {

                    // 直接从ERPStoreVO中获取值
                    WarehouseInventoryDTO virtualDTO = new WarehouseInventoryDTO();
                    virtualDTO.setId(String.valueOf(UUIDGenerator.getUUID("WarehouseInventoryDTO")));

                    // 从ERPStoreVO中获取值，如果为null则设为默认值
                    virtualDTO.setProductSpecId(erpStoreVO.getProductSpecificationId());
                    virtualDTO.setOwnerId(erpStoreVO.getOwnerId());
                    virtualDTO.setWarehouseId(erpStoreVO.getWarehouseId());
                    // 设置ERP原始的二级货主ID
                    virtualDTO.setErpSecOwnerId(erpStoreVO.getSecOwnerId());
                    
                    // 优先使用映射的久批货主ID，如果有的话
                    if (!StringUtils.isEmpty(erpStoreVO.getSecOwnerId()) && 
                            erpToWmsOwnerMap.containsKey(erpStoreVO.getSecOwnerId()) && 
                            !CollectionUtils.isEmpty(erpToWmsOwnerMap.get(erpStoreVO.getSecOwnerId()))) {
                        // 选择第一个映射的久批货主ID
                        String firstWmsOwnerId = erpToWmsOwnerMap.get(erpStoreVO.getSecOwnerId()).get(0);
                        virtualDTO.setSecOwnerId(StringUtils.isEmpty(firstWmsOwnerId) ? null : Long.parseLong(firstWmsOwnerId));
                    } else {
                        virtualDTO.setSecOwnerId(null);
                    }

                    virtualDTO.setWarehouseTotalCount(BigDecimal.ZERO); // 久批库存为0
                    virtualDTO.setPackageQuantity(erpStoreVO.getPackageQuantity());
                    virtualDTO.setProductSkuId(erpStoreVO.getProductSkuId());

                    // 使用横线替代未知的值
                    virtualDTO.setProductName(erpStoreVO.getProductName());
                    virtualDTO.setSpecificationName("-");

                    // 计算差异值 = ERP实际库存 - 0
                    BigDecimal orderUnDeliveryCount = deliveryCountMap.get(key) == null ?
                            BigDecimal.ZERO : deliveryCountMap.get(key);
                    BigDecimal orderUnReturnCount = getDeliveryedReturnCountMap.get(key) == null ?
                            BigDecimal.ZERO : getDeliveryedReturnCountMap.get(key);
                    BigDecimal allotDeliveryingCount = allotDeliveryingCountMap.get(key) == null ?
                            BigDecimal.ZERO : allotDeliveryingCountMap.get(key);
                    BigDecimal realStoreCount = erpStoreVO.getErpRealCount()
                            .subtract(orderUnDeliveryCount)
                            .add(orderUnReturnCount)
                            .subtract(allotDeliveryingCount);
                    LOGGER.info(String.format("久批不存在 ERPDetail:%s", JSON.toJSONString(erpStoreVO)));

                    // 差异=ERP库存-0
                    virtualDTO.setDiffTotalCount(realStoreCount);
                    // 假如差异绝对值小于0.01，设置为0
                    if (virtualDTO.getDiffTotalCount().abs().compareTo(new BigDecimal(MIN_DIFF_NUM)) < 0) {
                        virtualDTO.setDiffTotalCount(BigDecimal.ZERO);
                    }

                    // 创建校正记录
                    String remark = "按城市库存对账-久批不存在";
                    InventorySyncRecordDTO inventorySyncRecordDTO = getInventorySyncRecordDTO(
                            virtualDTO, erpStoreVO.getErpRealCount(), orderUnDeliveryCount, orderUnReturnCount,
                            opUserId, now, allotDeliveryingCount, remark);

                    // 添加到结果中
                    lstNoResult.put(virtualDTO.getId(), inventorySyncRecordDTO);
                }
            }

            //补充缺失数据
            if (!CollectionUtils.isEmpty(lstNoResult)) {

                // 补充城市ID信息
                List<Integer> lstWarehouseIds = lstNoResult.values().stream()
                        .filter(p -> p.getWarehouseId() != null)
                        .map(InventorySyncRecordDTO::getWarehouseId)
                        .distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(lstWarehouseIds)) {
                    List<Warehouse> warehouses = warehouseQueryService.listWarehouseByIds(lstWarehouseIds);
                    warehouses.forEach(warehouse -> {
                        lstNoResult.values().forEach(p -> {
                            if (Objects.equals(p.getWarehouseId(), warehouse.getId())) {
                                p.setOrgId(warehouse.getCityId());
                            }
                        });
                    });
                }

                // 补充SKU信息
                List<Long> lstSpecId = lstNoResult.values().stream()
                        .map(InventorySyncRecordDTO::getProductSpecificationId)
                        .distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(lstSpecId)) {
                    List<ProductInfoSpecificationDTO> lstProductInfo = productInfoSpecificationSerivce.findByProductInfoIds(lstSpecId);
                    lstProductInfo.forEach(productInfoSpecificationDTO -> {
                        lstNoResult.values().forEach(p -> {
                            if (Objects.equals(p.getProductSpecificationId(), productInfoSpecificationDTO.getId())) {
                                p.setProductName(productInfoSpecificationDTO.getProductName());
                                p.setPackageQuantity(productInfoSpecificationDTO.getPackageQuantity());
                                p.setSpecName(productInfoSpecificationDTO.getName());
                            }
                        });
                    });
                }
            }
            lstResult.putAll(lstNoResult);
        }
        return new ArrayList<>(lstResult.values());
    }

    private String getRemark(Map<Integer, Boolean> openCenterMap, Integer warehouseId) {
        if (openCenterMap.get(warehouseId)) {
            return "中台库存对账";
        }

        return "ERP库存对账";
    }

    /**
     * 获取校正记录
     *
     * @return
     */
    private InventorySyncRecordDTO getInventorySyncRecordDTO(WarehouseInventoryDTO p, BigDecimal erpRealCount,
                                                             BigDecimal orderUnDeliveryCount, BigDecimal orderUnReturnCount, Integer opUserId, Date now,
                                                             BigDecimal allotDeliveryingCount, String remark) {
        InventorySyncRecordDTO inventorySyncRecordDTO = new InventorySyncRecordDTO();
        inventorySyncRecordDTO.setId(UuidGenerator.generatorLongId());
        inventorySyncRecordDTO.setOrgId(p.getCityId());
        inventorySyncRecordDTO.setWmsDeliveryedCount(BigDecimal.ZERO);
        inventorySyncRecordDTO.setProductStoreId(p.getId());
        inventorySyncRecordDTO.setWarehouseId(p.getWarehouseId());
        inventorySyncRecordDTO.setTmsDeliveryedCount(orderUnDeliveryCount);
        inventorySyncRecordDTO.setErpRealCount(erpRealCount);
        inventorySyncRecordDTO.setStoreCountMinUnit(p.getWarehouseTotalCount());
        inventorySyncRecordDTO.setDiffTotalCount(p.getDiffTotalCount());
        BigDecimal[] diffCountInfo = BigDecimalUtils
                .divideAndRemainderByDecimalScale6AndRoundUpWithZero(p.getDiffTotalCount(), p.getPackageQuantity());
        inventorySyncRecordDTO.setDiffMaxCount(diffCountInfo[0]);
        inventorySyncRecordDTO.setDiffMinCount(diffCountInfo[1]);
        inventorySyncRecordDTO.setProductSkuId(p.getProductSkuId());
        inventorySyncRecordDTO.setProductSpecificationId(p.getProductSpecId());
        inventorySyncRecordDTO.setProductName(p.getProductName());
        inventorySyncRecordDTO.setSpecName(p.getSpecificationName());
        inventorySyncRecordDTO.setPackageQuantity(p.getPackageQuantity());
        inventorySyncRecordDTO.setOwnerId(p.getOwnerId());
        inventorySyncRecordDTO.setSecOwnerId(p.getSecOwnerId());
        inventorySyncRecordDTO.setStoreType(StoreTypeEnum.仓库库存.getType());
        inventorySyncRecordDTO.setCreateTime(now);
        inventorySyncRecordDTO.setCreateUserId(opUserId);
        inventorySyncRecordDTO.setRemark(remark);
        if (orderUnReturnCount.compareTo(BigDecimal.ZERO) != 0) {
            inventorySyncRecordDTO.setRemark(inventorySyncRecordDTO.getRemark() + "-已退货入库未结账：" + orderUnReturnCount);
        }
        if (allotDeliveryingCount != null && allotDeliveryingCount.compareTo(BigDecimal.ZERO) != 0) {
            inventorySyncRecordDTO.setRemark(inventorySyncRecordDTO.getRemark() + "-在途库存：" + allotDeliveryingCount);
        }
        if (inventorySyncRecordDTO.getDiffTotalCount().compareTo(BigDecimal.ZERO) != 0) {
            inventorySyncRecordDTO.setDiff(CheckDiffEnum.有差异.getType());
            inventorySyncRecordDTO.setState(CheckStateEnum.未处理.getType());
        } else {
            inventorySyncRecordDTO.setDiff(CheckDiffEnum.无差异.getType());
            inventorySyncRecordDTO.setState(CheckStateEnum.不需要处理.getType());
        }
        return inventorySyncRecordDTO;
    }

    /**
     * 查询ERP库存
     *
     * @param cityId
     * @return
     */
    public Map<String, ERPStoreVO> getERPStoreVOMap(Integer cityId) {
        List<ERPStoreVO> lstErpStore = InventoryConvertor.getErpStoreCountByCity(cityId);
        // LOGGER.info("获取ERP库存: {}", JSON.toJSONString(lstErpStore));
        Map<String, ERPStoreVO> erpStoreMap = new HashMap<>(16);
        // 存储ERP二级货主ID到久批货主ID的映射关系，用于后续检查
        Map<String, List<String>> erpToWmsOwnerMap = new HashMap<>(16);
        
        if (!CollectionUtils.isEmpty(lstErpStore)) {
            // 移除总部虚拟仓库，易酒批统采区域直供仓，统采（虚拟仓）三个虚拟仓库
            lstErpStore.removeIf(p -> lstExStoreInventoryWarehouseId.contains(p.getWarehouseId()));
            if (!CollectionUtils.isEmpty(lstErpStore)) {
                List<String> secOwnerIdList = lstErpStore.stream().filter(p -> !StringUtils.isEmpty(p.getSecOwnerId()))
                        .map(ERPStoreVO::getSecOwnerId).collect(Collectors.toList());
                List<OwnerDTO> lstSecOwner = new ArrayList<>(16);
                if (!CollectionUtils.isEmpty(secOwnerIdList)) {
                    lstSecOwner = ownerService.findOwnerByErpId(secOwnerIdList, null);
                }
                
                // 构建ERP二级货主ID到久批货主ID的映射关系
                for (OwnerDTO ownerDTO : lstSecOwner) {
                    if (ownerDTO != null && !StringUtils.isEmpty(ownerDTO.getRefPartnerId())) {
                        String erpSecOwnerId = ownerDTO.getRefPartnerId();
                        if (!erpToWmsOwnerMap.containsKey(erpSecOwnerId)) {
                            erpToWmsOwnerMap.put(erpSecOwnerId, new ArrayList<>());
                        }
                        erpToWmsOwnerMap.get(erpSecOwnerId).add(ownerDTO.getId().toString());
                    }
                }
                
                for (ERPStoreVO p : lstErpStore) {
                    // 直接使用ERP原始的二级货主ID作为key的一部分
                    String key = String.format("%s-%s-%s-%s", p.getProductSpecificationId(), p.getOwnerId(),
                            p.getWarehouseId(), p.getSecOwnerId());
                    
                    if (erpStoreMap.containsKey(key)) {
                        ERPStoreVO vo = erpStoreMap.get(key);
                        if (null != vo) {
                            p.setErpRealCount(p.getErpRealCount().add(vo.getErpRealCount()));
                        }
                    }
                    erpStoreMap.put(key, p);
                }
                lstErpStore.clear();
            }
        }
        // 将映射关系保存在一个静态变量或类属性中，供getModWarehouseInventoryDTOS方法使用
        this.erpToWmsOwnerMap = erpToWmsOwnerMap;
        return erpStoreMap;
    }

    /**
     * 获取已发货未完成的数量
     *
     * @param cityId
     * @return
     */
    private Map<String, BigDecimal> getDeliveryedReturnCountMap(Integer cityId,
                                                                Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount) {
        Map<String, BigDecimal> deliveryedReturnCountMap = new HashMap<>(16);

        if (CollectionUtils.isEmpty(openCenterCount)) {
            return deliveryedReturnCountMap;
        }

        List<OrderCenterUnConfirmOrderInventoryResultDTO> list = new ArrayList<>();
        for (Map.Entry<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> entry : openCenterCount.entrySet()) {
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                list.addAll(entry.getValue());
            }
        }

        // list = list.stream().filter(m ->
        // OrderCenterInventoryOrderTypeConstants.TYPE_IN.equals(m.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return deliveryedReturnCountMap;
        }
        // 中台调整了接口，已经把出入库统一聚合起来，有正负数
        list.forEach(n -> {
            deliveryedReturnCountMap.put(String.format("%s-%s-%s-%s", n.getProductSpecificationId(), n.getOwnerId(),
                    n.getWarehouseId(), n.getSecOwnerId()), BigDecimal.ZERO);
        });

        return deliveryedReturnCountMap;
    }

    /**
     * 获取已发货未完成的数量
     *
     * @param cityId
     * @return
     */
    private Map<String, BigDecimal> getDeliveryCountMap(Integer cityId, List<Integer> warehouseIdList,
                                                        Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> openCenterCount) {
        Map<String, BigDecimal> deliveryCountMap = new HashMap<>(16);

        if (CollectionUtils.isEmpty(openCenterCount)) {
            return deliveryCountMap;
        }

        List<OrderCenterUnConfirmOrderInventoryResultDTO> list = warehouseIdList.stream().map(openCenterCount::get)
                .filter(m -> !CollectionUtils.isEmpty(m)).flatMap(m -> m.stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return deliveryCountMap;
        }

        // list = list.stream().filter(m -> OrderCenterInventoryOrderTypeConstants.TYPE_OUT.equals(m.getType()))
        // .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return deliveryCountMap;
        }
        list.forEach(n -> {
            deliveryCountMap.put(String.format("%s-%s-%s-%s", n.getProductSpecificationId(), n.getOwnerId(),
                    n.getWarehouseId(), n.getSecOwnerId()), n.getCount());
        });

        return deliveryCountMap;
    }

    /**
     * 将仓库库存中的大宗库存合并到酒批库存中
     *
     * @param warehouseInventoryDTOList
     */
    private void mergeWarehouseInventoryOfLarge(List<WarehouseInventoryDTO> warehouseInventoryDTOList) {
        for (Iterator<WarehouseInventoryDTO> it = warehouseInventoryDTOList.iterator(); it.hasNext(); ) {
            WarehouseInventoryDTO p = it.next();
            if (Objects.equals(p.getChannel(), 1)) {
                String productSkuIdWarehouseId =
                        String.format("%s-%s-%s", p.getProductSpecId(), p.getOwnerId(), p.getWarehouseId());
                Optional<WarehouseInventoryDTO> inventoryDTO = warehouseInventoryDTOList.stream()
                        .filter(q -> Objects.equals(q.getChannel(), 0)
                                && String.format("%s-%s-%s", q.getProductSpecId(), q.getOwnerId(), q.getWarehouseId())
                                .equals(productSkuIdWarehouseId))
                        .findAny();
                inventoryDTO.ifPresent(warehouseInventoryDTO -> warehouseInventoryDTO.setWarehouseTotalCount(
                        p.getWarehouseTotalCount().add(warehouseInventoryDTO.getWarehouseTotalCount())));
                // 移除大宗库存
                it.remove();
            }
        }
    }

    /**
     * 根据城市idc查询仓库库存
     *
     * @return
     */
    private List<WarehouseInventoryDTO> getWarehouseInventoryDTOList(CheckStoreInventoryByCityInfoDTO dto) {
        Integer cityId = dto.getCityId();
        List<WarehouseInventoryDTO> lstResult = new ArrayList<>();
        WarehouseInventoryReportQueryDTO queryDTO = new WarehouseInventoryReportQueryDTO();
        queryDTO.setCityId(cityId);
        queryDTO.setExpOrgMode(2);
        queryDTO.setWarehouseTypes(new Integer[]{0, 1, 5, 6, 8, 10, 12});
        // 遍历剩余的页数
        Integer totalPage = 1;
        int pageSize = 5000;
        for (int i = 1; i <= totalPage; i++) {
            // if (i == 1) {
            // PageHelper.startPage(i, pageSize);
            // } else {
            // PageHelper.startPage(i, pageSize, false);
            // }
            queryDTO.setPageNum(i);
            queryDTO.setPageSize(pageSize);
            queryDTO.setProductTypeList(Arrays.asList((int) ProductTypeEnums.包装材料.getType(),
                    (int) ProductTypeEnums.半成品.getType(), (int) ProductTypeEnums.成品.getType()));

            // PageList<WarehouseInventoryDTO> pagerResult = warehouseInventoryQueryBL.listWarehouseInventory(queryDTO,
            // new PagerCondition(i, pageSize));
            PageList<WarehouseInventoryDTO> pagerResult = ProductStoreConverter.warehouseInventoryListToReportPageList(
                    warehouseInventoryReportQueryService.listWarehouseInventoryByCityId(queryDTO));

            if (pagerResult != null && pagerResult.getDataList() != null && pagerResult.getDataList().size() > 0) {
                if (i == 1) {
                    totalPage = pagerResult.getPager().getTotalPage();
                }
                List<String> lstSkuInfo = pagerResult.getDataList().stream()
                        .map(p -> p.getProductSkuId() + "-" + p.getProductSpecId()).collect(Collectors.toList());
                LOGGER.info(String.format("第%s页，Sku:%s", i, JSON.toJSONString(lstSkuInfo)));
                lstResult.addAll(pagerResult.getDataList());
                pagerResult.getDataList().clear();
            }
        }
        return lstResult;
    }

    /**
     * 查询有货主产品的仓库库存
     */
    private List<WarehouseInventoryDTO> getWarehouseInventoryDTOListByOwner() {
        List<WarehouseInventoryDTO> lstResult = new ArrayList<>();
        WarehouseInventoryReportQueryDTO queryDTO = new WarehouseInventoryReportQueryDTO();
        // 遍历剩余的页数
        Integer totalPage = 1;
        int pageSize = 5000;
        for (int i = 1; i <= totalPage; i++) {
            // if (i == 1) {
            // PageHelper.startPage(i, pageSize);
            // } else {
            // PageHelper.startPage(i, pageSize, false);
            // }
            queryDTO.setPageNum(i);
            queryDTO.setPageSize(pageSize);
            PageList<WarehouseInventoryDTO> pagerResult = ProductStoreConverter.warehouseInventoryListToReportPageList(
                    warehouseInventoryReportQueryService.listWarehouseInventoryWithOwner(queryDTO));
            if (pagerResult != null && pagerResult.getDataList() != null && pagerResult.getDataList().size() > 0) {
                if (i == 1) {
                    totalPage = pagerResult.getPager().getTotalPage();
                }
                lstResult.addAll(pagerResult.getDataList());
                pagerResult.getDataList().clear();
            }
        }
        return lstResult;
    }

    // /**
    // * 批量更新已发货总数量
    // * @param cityMergeDTOList
    // */
    // private void updateDeliveryStoreRecordByDTOS(List<WarehouseInventoryDTO> cityMergeDTOList) {
    // if (!CollectionUtils.isEmpty(cityMergeDTOList)) {
    // try {
    // List<DeliveryStoreRecordDTO> lstTmp = cityMergeDTOList.stream().map(p -> {
    // DeliveryStoreRecordDTO dto = new DeliveryStoreRecordDTO();
    // dto.setId(p.getId());
    // dto.setDeliveryedCount(p.getDeliveryedCount());
    // return dto;
    // }).collect(Collectors.toList());
    // deliveryStoreRecordBL.updateDeliveryStoreRecordBatch(lstTmp);
    //// lstTmp.clear();
    // } catch (Exception oe) {
    // LOGGER.error("更新已发货未完成数量出错！" + oe.getMessage(), oe);
    // }
    // }
    // }

    /**
     * 获取易款连锁店仓-安全库存数量
     *
     * @return
     */
    private Map<String, BigDecimal> getEasyChainSafetyStoreCountMap(List<WarehouseStoreDTO> warehouseStoreDTOList) {
        // Map<String, BigDecimal> safetyStoreCountMap = new HashMap<>(16);
        // try {
        // // 按仓库分组
        // Map<Integer, List<WarehouseStoreDTO>> warehouseStoreMap =
        // warehouseStoreDTOList.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId()));
        // if (warehouseStoreMap != null) {
        // warehouseStoreMap.forEach((warehouseId, storeList) -> {
        // Warehouse warehouse = warehouseQueryService.findWarehouseById(warehouseId);
        // // 不是店仓仓库，则跳过查安全库存
        // if (warehouse == null
        // || !Objects.equals(warehouse.getWarehouseType(), (int)WarehouseTypeEnum.店仓合一.getType())) {
        // return;
        // }
        // // 50个一组
        // List<List<WarehouseStoreDTO>> lists = InventoryConvertor.splitList(storeList, 50);
        // for (List<WarehouseStoreDTO> list : lists) {
        // Set<Long> specIdSet = list.stream().filter(p -> p.getProductSpecId() != null)
        // .map(p -> p.getProductSpecId()).collect(Collectors.toSet());
        // Map<Long, ShopB2bProductRetailSafetyInventoryDTO> resultSafetyMap =
        // shopProductB2bProductRangeQueryService
        // .getShopProductB2bProductRangeMap(warehouseId.longValue(), specIdSet);
        // LOGGER.info("连锁安全库存数量: {}", JSON.toJSONString(resultSafetyMap));
        // if (resultSafetyMap != null && resultSafetyMap.size() > 0) {
        // resultSafetyMap.forEach((specId, safetyDto) -> {
        // if (safetyDto != null) {
        // safetyStoreCountMap.put(String.format("%s-%s-%s",
        // safetyDto.getYjpProductInfoSpecId(), null, safetyDto.getWarehouseId()),
        // safetyDto.getRetailSafetyInventory());
        // }
        // });
        // }
        // resultSafetyMap.clear();
        // }
        // });
        // }
        // } catch (Exception e) {
        // LOGGER.error("获取易款连锁店仓-安全库存数量异常", e);
        // }
        // return safetyStoreCountMap;
        return Collections.emptyMap();
    }

    /**
     * 查询未质检产品数量
     *
     * @return
     */
    private Map<String, BigDecimal> getQualityControlCountMap(List<WarehouseStoreDTO> warehouseStoreDTOList) {
        Map<String, BigDecimal> qualityControlCountMap = new HashMap<>(16);
        try {
            if (!CollectionUtils.isEmpty(warehouseStoreDTOList)) {
                // 按仓库分组
                Map<Integer, List<WarehouseStoreDTO>> warehouseStoreMap =
                        warehouseStoreDTOList.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId()));
                if (warehouseStoreMap != null) {
                    warehouseStoreMap.forEach((warehouseId, storeList) -> {
                        // 100个一组
                        List<List<WarehouseStoreDTO>> lists = InventoryConvertor.splitList(storeList, 100);
                        for (List<WarehouseStoreDTO> list : lists) {
                            List<Long> skuIdList = list.stream().filter(p -> p.getProductSkuId() != null)
                                    .map(p -> p.getProductSkuId()).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(skuIdList)) {
                                break;
                            }
                            // 查询未质检产品数量
                            QualityControlBillQueryDTO queryDTO = new QualityControlBillQueryDTO();
                            queryDTO.setOrgId(list.get(0).getCityId());
                            queryDTO.setWarehouseId(warehouseId);
                            queryDTO.setSkuIdList(skuIdList);
                            Map<Long, BigDecimal> qualityControlMap =
                                    iQualityControlBillService.findQcCountBySkuIds(queryDTO);
                            if (qualityControlMap != null && qualityControlMap.size() > 0) {
                                LOGGER.info("未质检产品数量: {}", JSON.toJSONString(qualityControlMap));
                                qualityControlMap.forEach((skuId, count) -> {
                                    Optional<WarehouseStoreDTO> optional = list.stream()
                                            .filter(p -> Objects.equals(p.getProductSkuId(), skuId)).findFirst();
                                    if (optional.isPresent()) {
                                        qualityControlCountMap
                                                .put(
                                                        String.format("%s-%s-%s", optional.get().getProductSpecId(),
                                                                optional.get().getOwnerId(), optional.get().getWarehouseId()),
                                                        count);
                                    }
                                });
                            }
                            qualityControlMap.clear();
                        }
                    });
                }
            }
        } catch (Exception e) {
            LOGGER.error("查询未质检产品数量异常", e);
        }
        return qualityControlCountMap;
    }

    // /**
    // * 根据库存id获取批次库存最老生产日期
    // *
    // * @return
    // */
    // private void getWarehouseStoreProductDate(List<WarehouseStoreDTO> warehouseStoreDTOList) {
    // if (CollectionUtils.isEmpty(warehouseStoreDTOList)) {
    // return;
    // }
    //
    // List<String> productStoreIds = warehouseStoreDTOList.stream().filter(p -> !StringUtils.isEmpty(p.getId()))
    // .map(WarehouseStoreDTO::getId).distinct().collect(Collectors.toList());
    // if (CollectionUtils.isEmpty(productStoreIds)) {
    // return;
    // }
    //
    // Map<String, Date> dateMap = iBatchInventoryQueryService.findProductionDateByProductStoreIds(productStoreIds);
    // if (dateMap == null || dateMap.size() <= 0) {
    // return;
    // }
    //
    // warehouseStoreDTOList.forEach(p -> {
    // if (dateMap.containsKey(p.getId())) {
    // p.setManufactureTime(dateMap.get(p.getId()));
    // }
    // });
    // }
}

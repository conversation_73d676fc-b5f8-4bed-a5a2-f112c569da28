package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeDicPO;

@Mapper
public interface BatchAttributeDicMapper {
    /**
     * 新增
     *
     * @param batchAttributeDicPO
     * @return
     */
    int insert(@Param("batchAttributeDicPO") BatchAttributeDicPO batchAttributeDicPO);

    /**
     * 批量新增
     *
     * @param batchAttributeDicPOs
     * @return
     */
    int insertList(@Param("batchAttributeDicPOs") List<BatchAttributeDicPO> batchAttributeDicPOs);

    /**
     * 修改状态,启动,停用.
     *
     * @param batchAttributeDicDTO
     */
    void updateIsEnable(@Param("batchAttributeDicDTO") BatchAttributeDicDTO batchAttributeDicDTO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteById(@Param("id") Long id);

    /**
     * 列表
     *
     * @param batchAttributeDicQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchAttributeDicDTO> findBatchAttributeDicList(
        @Param("batchAttributeDicQueryDTO") BatchAttributeDicQueryDTO batchAttributeDicQueryDTO,
        @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 通过主键id查找字典信息
     *
     * @param id
     * @return
     */
    BatchAttributeDicPO findById(@Param("id") Long id);

    /**
     * 通过主键id查找字典信息
     *
     * @param idS
     * @return
     */
    List<BatchAttributeDicPO> findByIdS(@Param("list") List<Long> idS);

    /**
     * 修改
     *
     * @param batchAttributeDicDTO
     */
    void update(@Param("dto") BatchAttributeDicDTO batchAttributeDicDTO);

}

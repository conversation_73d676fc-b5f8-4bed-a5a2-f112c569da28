package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.io.Serializable;

/**
 * <AUTHOR> 2017/11/20
 */
public class DeliveryOrderItemPO implements Serializable {
    /**
     * deliveryOrderItem表主键
     */
    private Long id;
    /**
     * order_id
     */
    private Long orderId;
    /**
     * 配送单id
     */
    private Long deliveryOrderId;
    /**
     * 产品skuid
     */
    private Long productSkuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 销售数量小单位
     */
    private Integer minUnitTotalCount;

    /**
     * 获取 deliveryOrderItem表主键
     *
     * @return id deliveryOrderItem表主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 deliveryOrderItem表主键
     *
     * @param id deliveryOrderItem表主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 order_id
     *
     * @return orderId order_id
     */
    public Long getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 order_id
     *
     * @param orderId order_id
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 配送单id
     *
     * @return deliveryOrderId 配送单id
     */
    public Long getDeliveryOrderId() {
        return this.deliveryOrderId;
    }

    /**
     * 设置 配送单id
     *
     * @param deliveryOrderId 配送单id
     */
    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    /**
     * 获取 产品skuid
     *
     * @return productSkuId 产品skuid
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品skuid
     *
     * @param productSkuId 产品skuid
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 产品名称
     *
     * @return productName 产品名称
     */
    public String getProductName() {
        return this.productName;
    }

    /**
     * 设置 产品名称
     *
     * @param productName 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 获取 销售数量小单位
     *
     * @return minUnitTotalCount 销售数量小单位
     */
    public Integer getMinUnitTotalCount() {
        return this.minUnitTotalCount;
    }

    /**
     * 设置 销售数量小单位
     *
     * @param minUnitTotalCount 销售数量小单位
     */
    public void setMinUnitTotalCount(Integer minUnitTotalCount) {
        this.minUnitTotalCount = minUnitTotalCount;
    }
}

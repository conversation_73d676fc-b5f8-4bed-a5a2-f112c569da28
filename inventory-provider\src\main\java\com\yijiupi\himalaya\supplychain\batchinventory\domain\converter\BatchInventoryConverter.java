package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/13
 */
@Component
public class BatchInventoryConverter {

    public List<BatchInventoryDTO> convertDTOList(List<BatchInventoryPO> batchInventoryPOS) {
        List<BatchInventoryDTO> batchInventoryDTOS = new ArrayList<>();
        for (BatchInventoryPO batchInventoryPO : batchInventoryPOS) {
        }
        return batchInventoryDTOS;
    }

}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.constant.StockAgeInventoryQueryType;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeInfoBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.ordercenter.BatchInventoryOrderCenterBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.ordercenter.SaleInventoryQueryConvertor;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchInventoryConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.StockAgeInventoryConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.WarehouseProductStoreConvertor;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.manager.OmsInventoryManager;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchLocationInfoQueryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.*;
import com.yijiupi.himalaya.supplychain.batchinventory.util.StringFormatUtil;
import com.yijiupi.himalaya.supplychain.dto.*;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.utils.DateUtil;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckService;
import com.yijiupi.himalaya.supplychain.service.IOutStockStrategyService;
import com.yijiupi.himalaya.supplychain.service.IWarehouseInventoryReportQueryService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeProductQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductRelationGroupService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStockAgeStrategyService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants.SCM_VERSION_3;

/**
 * 批次库存相关
 *
 * <AUTHOR> 2018/3/28
 */
@Service
public class BatchInventoryQueryBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventoryQueryBL.class);

    @Autowired
    private BatchInventoryProductSkuMapper batchInventoryProductSkuMapper;
    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private BatchAttributeInfoBL batchAttributeInfoBL;
    @Autowired
    private BatchInventoryQueryNewBL batchInventoryQueryNewBL;
    @Autowired
    private BatchInventoryOrderCenterBL batchInventoryOrderCenterBL;
    @Reference
    private IOutStockStrategyService iOutStockStrategyService;
    @Reference
    private LocationAreaService locationAreaService;
    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Reference
    private IProductRelationGroupService iProductRelationGroupService;

    @Reference
    private IStockAgeStrategyService stockAgeStrategyService;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    @Reference
    private OwnerService ownerService;

    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;
    @Resource
    private OmsInventoryManager omsInventoryManager;
    @Reference
    private IWarehouseInventoryReportQueryService iWarehouseInventoryReportQueryService;

    @Reference
    private IWarehouseInventoryCheckService iWarehouseInventoryCheckService;

    /**
     * 根据货区查可用货位(可以不传locationId)
     *
     * @param batchLocationInfoQueryDTOS
     * @return
     */
    public Map<String, List<BatchLocationInfoDTO>> findBatchLocationDTOBySkuAndCityId(
            List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Boolean isIngoreProductionDate) {
        Map<String, List<BatchLocationInfoDTO>> map = new HashMap<>(16);
        for (BatchLocationInfoQueryDTO batchLocationInfoQueryDTO : batchLocationInfoQueryDTOS) {
            BatchLocationInfoQueryDTO queryDTO = new BatchLocationInfoQueryDTO();
            BeanUtils.copyProperties(batchLocationInfoQueryDTO, queryDTO);
            if (isIngoreProductionDate) {
                queryDTO.setProductionDate(null);
                queryDTO.setBatchTime(null);
            }
            Long productSkuId = batchLocationInfoQueryDTO.getProductSkuId();
            Long locationId = batchLocationInfoQueryDTO.getLocationId();
            List<BatchLocationInfoDTO> batchHuoWeiDTOBySku = batchInventoryProductSkuMapper.findBatchAllDTOBySkuAndCityId(queryDTO);
            String key = productSkuId + "|" + locationId + "|" + batchLocationInfoQueryDTO.getSource() + "|"
                    + batchLocationInfoQueryDTO.getProductionDateStr() + "|" + batchLocationInfoQueryDTO.getChannel();
            // if (map.containsKey(key)) {
            // key = key;
            // }
            if (isIngoreProductionDate) {
                processNegativeStore(batchHuoWeiDTOBySku);
                map.put(key, batchHuoWeiDTOBySku);
            } else {
                List<BatchLocationInfoDTO> lstBatchInventory = batchHuoWeiDTOBySku.stream()
                        .filter(p -> (p.getProductSkuId() + "|" + p.getLocationId() + "|"
                                + batchLocationInfoQueryDTO.getSource() + "|" + p.getProductionDateStr() + "|"
                                + batchLocationInfoQueryDTO.getChannel()).equals(key))
                        .collect(Collectors.toList());
                if (lstBatchInventory.size() < batchHuoWeiDTOBySku.size()) {
                    LOGGER.info(String.format("生产日期不一致货位库存排除，Key:%s,FilterResult:%s,OldData:%s", key,
                            JSON.toJSONString(lstBatchInventory), JSON.toJSONString(batchHuoWeiDTOBySku)));
                }
                processNegativeStore(lstBatchInventory);
                // LOGGER.info(String.format("移库查询货位库存，Key:%s,FilterResult:%s,OldData:%s", key,
                // JSON.toJSONString(lstBatchInventory), JSON.toJSONString(batchHuoWeiDTOBySku)));
                map.put(key, lstBatchInventory);
            }
        }
        return map;
    }

    /**
     * 如果既存在货位库存大于0，又存在小于0的，排除小于0的，只处理大于0的
     *
     * @param lstBatchInventory
     */
    private void processNegativeStore(List<BatchLocationInfoDTO> lstBatchInventory) {
        if (lstBatchInventory.stream().anyMatch(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) < 0)
                || lstBatchInventory.stream().anyMatch(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) > 0)) {
            lstBatchInventory.removeIf(p -> p.getTotalCount().compareTo(BigDecimal.ZERO) == 0);
        }
        lstBatchInventory = lstBatchInventory.stream()
                .sorted(Comparator.comparing(BatchLocationInfoDTO::getTotalCount).reversed()).collect(Collectors.toList());
    }

    public Map<Long, List<BatchLocationInfoDTO>>
    findBatchLocationDTOBySku(List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS) {
        Map<Long, List<BatchLocationInfoDTO>> map = new HashMap<>(16);
        for (BatchLocationInfoQueryDTO batchLocationInfoQueryDTO : batchLocationInfoQueryDTOS) {
            Long productSkuId = batchLocationInfoQueryDTO.getProductSkuId();
            List<BatchLocationInfoDTO> batchHuoWeiDTOBySku = findBatchLocationDTOBySingleSku(batchLocationInfoQueryDTO);
            map.put(productSkuId, batchHuoWeiDTOBySku);
        }
        return map;
    }

    public List<BatchLocationInfoDTO>
    findBatchLocationDTOBySingleSku(BatchLocationInfoQueryDTO batchLocationInfoQueryDTO) {
        List<BatchLocationInfoDTO> batchHuoWeiDTOBySku =
                batchInventoryProductSkuMapper.findBatchAllDTOBySku(batchLocationInfoQueryDTO);
        return batchHuoWeiDTOBySku;
    }

    /**
     * 根据拣货产品的sku及货区Id查询货位相关信息(可以不传locationId)（只查询库存信息存在的货位）
     *
     * @param batchLocationInfoQueryDTOS
     * @param repositoryPriorityRules    库位优先规则
     * @param repositoryLimit            库位限制
     * @return
     */
    public Map<Long, List<BatchLocationInfoDTO>> findBatchHuoWeiDTOBySku(
            List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Byte repositoryPriorityRules,
            Byte repositoryLimit) {
        Map<Long, List<BatchLocationInfoDTO>> map = new HashMap<>(16);
        for (BatchLocationInfoQueryDTO batchLocationInfoQueryDTO : batchLocationInfoQueryDTOS) {
            Long productSkuId = batchLocationInfoQueryDTO.getProductSkuId();
            List<BatchLocationInfoDTO> batchHuoWeiDTOBySku;
            LOGGER.info("根据拣货产品的sku及货区Id查询货位相关信息-参数：{}", JSON.toJSONString(batchLocationInfoQueryDTO));
            if (batchLocationInfoQueryDTO.getLocationId() != null) {
                batchHuoWeiDTOBySku = batchInventoryProductSkuMapper.findBatchHuoQuDTOBySkuAndLocation(batchLocationInfoQueryDTO);
            } else {
                batchHuoWeiDTOBySku = batchInventoryProductSkuMapper.findBatchHuoWeiDTOBySku(batchLocationInfoQueryDTO);
            }
            LOGGER.info("根据拣货产品的sku及货区Id查询货位相关信息-结果：{}", JSON.toJSONString(batchHuoWeiDTOBySku));
            List<BatchLocationInfoDTO> batchLocationInfoDTOList = processLocationByLimit(batchLocationInfoQueryDTO,
                    batchHuoWeiDTOBySku, repositoryPriorityRules, repositoryLimit);
            if (map.containsKey(productSkuId)) {
                map.get(productSkuId).addAll(batchLocationInfoDTOList);
            } else {
                map.put(productSkuId, batchLocationInfoDTOList);
                // LOGGER.info("添加map{}", JSON.toJSONString(map));
            }
        }
        // LOGGER.info("map{}", JSON.toJSONString(map));
        return map;
    }

    /**
     * 根据货区查可用货位（所有货位，库存信息可能不存在）(areaId必传)
     *
     * @param batchLocationInfoQueryDTOS
     * @param repositoryPriorityRules    库位优先规则
     * @param repositoryLimit            库位限制
     * @return
     */
    public Map<Long, List<BatchLocationInfoDTO>> findBatchHuoWeiDTOBySkuAndArea(
            List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Byte repositoryPriorityRules,
            Byte repositoryLimit) {
        Map<Long, List<BatchLocationInfoDTO>> map = new HashMap<>(16);
        for (BatchLocationInfoQueryDTO batchLocationInfoQueryDTO : batchLocationInfoQueryDTOS) {
            Long productSkuId = batchLocationInfoQueryDTO.getProductSkuId();
            List<BatchLocationInfoDTO> batchHuoWeiDTOBySku =
                    batchInventoryProductSkuMapper.findBatchHuoWeiDTOBySkuAndArea(batchLocationInfoQueryDTO);
            List<BatchLocationInfoDTO> batchLocationInfoDTOList = processLocationByLimit(batchLocationInfoQueryDTO,
                    batchHuoWeiDTOBySku, repositoryPriorityRules, repositoryLimit);
            if (map.containsKey(productSkuId)) {
                map.get(productSkuId).addAll(batchLocationInfoDTOList);
            } else {
                map.put(productSkuId, batchLocationInfoDTOList);
                // LOGGER.info("添加map{}", JSON.toJSONString(map));
            }
        }
        // LOGGER.info("map{}", JSON.toJSONString(map));
        return map;
    }

    private String getProductStoreIdBySku(BatchLocationInfoQueryDTO batchLocationInfoQueryDTO) {
        List<String> productStoreId =
                batchInventoryProductStoreBatchMapper.findProductStoreId(batchLocationInfoQueryDTO.getWarehouseId(),
                        batchLocationInfoQueryDTO.getProductSkuId(), batchLocationInfoQueryDTO.getChannel(),
                        batchLocationInfoQueryDTO.getSource(), batchLocationInfoQueryDTO.getSecOwnerId());
        return productStoreId.size() > 0 ? productStoreId.get(0) : null;
    }

    // 库位优先规则 0 无 1 货位编号 2线路顺序
    // 库位限制 0 不限制 1不同商品相同批号 2相同商品不同批号 3相同商品相同批号
    private List<BatchLocationInfoDTO> processLocationByLimit(BatchLocationInfoQueryDTO batchLocationInfoQueryDTO,
                                                              List<BatchLocationInfoDTO> lstLocationInfo, Byte repositoryPriorityRules, Byte repositoryLimit) {
        if (repositoryLimit != null && repositoryLimit.intValue() > 0) {
            String productStoreId = null;
            if (lstLocationInfo.stream().anyMatch(p -> StringUtil.isNotEmpty(p.getProductStoreId()))) {
                // sku + 二级货主找库存ID, 找不到再随机找
                Optional<BatchLocationInfoDTO> secBatchOpt = lstLocationInfo.stream()
                        .filter(p -> StringUtil.isNotEmpty(p.getProductStoreId())
                                && Objects.equals(batchLocationInfoQueryDTO.getProductSkuId(), p.getProductSkuId())
                                && Objects.equals(batchLocationInfoQueryDTO.getCurSecOwnerId(), p.getSecOwnerId()))
                        .findAny();
                if (secBatchOpt.isPresent()) {
                    productStoreId = secBatchOpt.get().getProductStoreId();
                }
                // 如果按二级货主没有找到则随机货主
                if (StringUtils.isBlank(productStoreId)) {
                    Optional<BatchLocationInfoDTO> tmpBatchLocation =
                            lstLocationInfo.stream().filter(p -> StringUtil.isNotEmpty(p.getProductStoreId())).findFirst();
                    if (tmpBatchLocation.isPresent()) {
                        productStoreId = tmpBatchLocation.get().getProductStoreId();
                    }
                }
            } else {
                productStoreId = getProductStoreIdBySku(batchLocationInfoQueryDTO);
            }
            // 新导入的商品第一次采购入库时 之判断是否有相同商品
            if (StringUtils.isEmpty(productStoreId)) {
                checkIsHasOtherProductStoreByLocationAndStoreId(lstLocationInfo, productStoreId);
                return lstLocationInfo;
            }
            // AssertUtils.notNull(productStoreId, "找不到产品在仓库的库存，请联系管理员！");
            String batchNo = batchAttributeInfoBL.getBatchAttributeInfoNo(productStoreId, null,
                    batchLocationInfoQueryDTO.getProductionDate(), batchLocationInfoQueryDTO.getBatchTime());
            LOGGER.info("库位优先规则 - 产品[{}] 库存ID ：{}, 生产日期：{}, 批次时间：{}, 生成批次编号：{}",
                    batchLocationInfoQueryDTO.getProductSkuId(), productStoreId,
                    batchLocationInfoQueryDTO.getProductionDateStr(),
                    DateFormatUtils.format(batchLocationInfoQueryDTO.getBatchTime(), "yyyy-MM-dd HH:mm:ss"), batchNo);
            // 库位限制过滤
            if (repositoryLimit.intValue() == 1) {
                // 货位产品不限，只能存批号相同的
                // 1 不同商品相同批号
                // 空货位
                // 只需要查没有这个商品的货位
                // 有这个商品，且批号相同的货位

                // 判断所有有库存的货位

                // 不满足条件的：
                // 如果货位上有多个批次编号的，不满足条件

                // 满足条件的：
                // 1 、只有一个批次，且和当前批次相同
                // 2、货位上没有这个产品的或者货位为空的
                checkIsHasOtherBatchNoByLocationAndBatchNo(lstLocationInfo, batchNo);
            } else if (repositoryLimit.intValue() == 2) {
                // 货位只能存相同产品，批号不限
                // 2 相同商品不同批号
                // 只要货位上有这个产品
                // 空货位

                // 判断所有有库存的货位

                // 不满足条件的：
                // 空货位上有其他产品

                // 满足条件的：
                // 空货位
                // 货位上有这个产品的

                checkIsHasOtherProductStoreByLocationAndStoreId(lstLocationInfo, productStoreId);
            } else if (repositoryLimit.intValue() == 3) {
                // 3 相同商品相同批号

                // 判断所有有库存的货位

                // 不满足条件的：
                // 货位上有其他产品
                // 货位上该产品有其他批号

                // 满足条件的：
                // 空货位
                // 货位上只有这一个产品，且批号相同

                // 校验没有库存的货位上是否有其他产品，如果有其他产品，要移除掉
                checkIsHasOtherProductStoreByLocationAndStoreId(lstLocationInfo, productStoreId);

                // 非空货位上，只能有当前批次
                checkIsHasOtherBatchNoByLocationAndBatchNo(lstLocationInfo, batchNo);
            }

        }
        // if (repositoryPriorityRules != null && repositoryPriorityRules.intValue() > 0) {
        // // 货位排序
        // if (repositoryPriorityRules.intValue() == 1) {
        // lstLocationInfo.forEach(p -> {
        // if (p.getLocationName() == null) {
        // p.setLocationName("");
        // }
        // });
        // LOGGER.info("货位排序前：{}", JSON.toJSONString(lstLocationInfo));
        // lstLocationInfo =
        // lstLocationInfo.stream().sorted(Comparator.comparing(BatchLocationInfoDTO::getLocationName)).collect(Collectors.toList());
        // LOGGER.info("货位排序后：{}", JSON.toJSONString(lstLocationInfo));
        // } else if (repositoryPriorityRules.intValue() == 2) {
        // LOGGER.info("线路排序前111：{}", JSON.toJSONString(lstLocationInfo));
        // lstLocationInfo.forEach(p -> {
        // if (p.getSequence() == null) {
        // p.setSequence(Integer.MAX_VALUE);
        // }
        // });
        // LOGGER.info("线路排序前：{}", JSON.toJSONString(lstLocationInfo));
        // lstLocationInfo =
        // lstLocationInfo.stream().sorted(Comparator.comparing(BatchLocationInfoDTO::getSequence)).collect(Collectors.toList());
        // LOGGER.info("线路排序后：{}", JSON.toJSONString(lstLocationInfo));
        // }
        // }
        return lstLocationInfo;

    }

    /**
     * 检查有库存的货位上是否存在其他批次的相同产品
     *
     * @param lstLocationInfo
     * @param batchNo
     */
    private void checkIsHasOtherBatchNoByLocationAndBatchNo(List<BatchLocationInfoDTO> lstLocationInfo,
                                                            String batchNo) {
        // LOGGER.info("lstLocationInfo:{}", JSON.toJSONString(lstLocationInfo));
        Map<Long, List<BatchLocationInfoDTO>> mapHasStoreByLocationId =
                lstLocationInfo.stream().filter(p -> StringUtil.isNotEmpty(p.getProductStoreId()))
                        .collect(Collectors.groupingBy(BatchLocationInfoDTO::getLocationId));
        // LOGGER.info("checkIsHasOtherBatchNoByLocationAndBatchNo:{}", JSON.toJSONString(mapHasStoreByLocationId));
        for (Map.Entry<Long, List<BatchLocationInfoDTO>> entry : mapHasStoreByLocationId.entrySet()) {
            // 校验货位上的产品批次号是否相同，如果不同要过滤掉
            if (entry.getValue().stream().anyMatch(p -> StringUtils.isEmpty(p.getBatchAttributeInfoNo())
                    || !p.getBatchAttributeInfoNo().equals(batchNo))) {
                lstLocationInfo.removeIf(p -> p.getLocationId().equals(entry.getKey()));
                LOGGER.info(String.format("检查是否混批，参数：%s,混批的货位：%s  batchNo：%s ", JSON.toJSONString(entry.getValue()),
                        entry.getKey(), batchNo));
            }
        }
    }

    /**
     * 检查没有库存的货位上，是否有其他产品
     *
     * @param lstLocationInfo
     * @param productStoreId
     */
    private void checkIsHasOtherProductStoreByLocationAndStoreId(List<BatchLocationInfoDTO> lstLocationInfo,
                                                                 String productStoreId) {
        Map<Long, List<BatchLocationInfoDTO>> mapNoStoreByLocationId = lstLocationInfo.stream()
                // .filter(p -> StringUtil.isEmpty(p.getProductStoreId()))

                .collect(Collectors.groupingBy(BatchLocationInfoDTO::getLocationId));
        if (mapNoStoreByLocationId.size() > 0) {
            // 校验空货位上是否有其他产品，如果有其他产品，要移除掉
            List<String> lstExitsOtherStoreLocationIds = batchInventoryProductStoreBatchMapper
                    .checkExistOtherProductInLocation(mapNoStoreByLocationId.keySet(), productStoreId);
            // LOGGER.info(String.format("检查是否混放，参数：%s,已经混放的货位：%s", JSON.toJSONString(mapNoStoreByLocationId),
            // lstExitsOtherStoreLocationIds));
            if (CollectionUtils.isNotEmpty(lstExitsOtherStoreLocationIds)) {
                for (String locationId : lstExitsOtherStoreLocationIds) {
                    lstLocationInfo.removeIf(p -> locationId.equals(String.valueOf(p.getLocationId())));
                }
            }
        }
    }
    //
    // /**
    // * 根据货区查可用货位(只传locationId)
    // *
    // * @return
    // */
    // public List<BatchLocationInfoDTO> findBatchLocationDTOByLocationId(Long locationId, Integer channel) {
    // List<BatchLocationInfoDTO> batchHuoWeiDTOBySku = productSkuMapper.findBatchLocationDTOByLocationId(locationId,
    // channel);
    // return batchHuoWeiDTOBySku;
    // }

    /**
     * 根据货位Id和SkuId，检查产品能否放到当前货位上
     *
     * @return
     */
    public Boolean checkIsCanPutIntoLocation(CheckBatchInventoryDTO checkBatchInventoryDTO, String msg) {
        boolean result = true;

        if (checkBatchInventoryDTO.getChannel() == null) {
            checkBatchInventoryDTO.setChannel(ProductChannelType.JIUPI);
        }

        // 根据locationId查询出货位顺序.
        LocationReturnDTO location =
                locationAreaService.findLocationById(checkBatchInventoryDTO.getLocationId().toString());
        AssertUtils.notNull(location, "货位不存在！" + checkBatchInventoryDTO.getLocationId());
        // 如果货位设置可以混放混批，或者没有设置这两个属性，则可以随便放
        boolean isNotSetBatch = (location.getIsChaosBatch() == null || location.getIsChaosBatch())
                && (location.getIsChaosPut() == null || location.getIsChaosPut());
        if (isNotSetBatch) {
            return result;
        }
        // 查找当前货位上所有的货位库存
        List<BatchLocationInfoDTO> batchHuoWeiDTOBySku =
                batchInventoryProductSkuMapper.findBatchLocationDTOByLocationId(checkBatchInventoryDTO.getLocationId(), null);
        // 如果货位上没有存放任何产品，可以随便放
        // 如果货位上已存放产品，需要校验是否混放混批
        if (CollectionUtils.isNotEmpty(batchHuoWeiDTOBySku)) {
            // 如果不能混放
            if (!location.getIsChaosPut()) {
                // 如果当前货位上已经存放其他SKU，则不符合混放
                result = !batchHuoWeiDTOBySku.stream()
                        .anyMatch(p -> !(p.getProductSkuId().equals(checkBatchInventoryDTO.getProductSkuId())));
                if (!result) {
                    throw new BusinessValidateException("当前货位不允许放其他产品！");
                }
            }
            // 如果不能混批
            if (!location.getIsChaosBatch()) {
                String batchNo =
                        batchAttributeInfoBL.getBatchAttributeInfoNoBySku(checkBatchInventoryDTO.getWarehouseId(),
                                checkBatchInventoryDTO.getProductSkuId(), checkBatchInventoryDTO.getChannel(),
                                checkBatchInventoryDTO.getProductionDate(), checkBatchInventoryDTO.getBatchTime());
                // 如果存在批次编号为空，或者批次编号不等于当前批次编号的，则不符合混批
                result = batchNo != null && !batchHuoWeiDTOBySku.stream()
                        .anyMatch(p -> Objects.equals(p.getProductSkuId(), checkBatchInventoryDTO.getProductSkuId())
                                && p.getBatchAttributeInfoNo() != null && !p.getBatchAttributeInfoNo().equals(batchNo));
                if (!result) {
                    throw new BusinessValidateException("当前货位只能存放相同批次的产品！");
                }
            }
            batchHuoWeiDTOBySku.clear();
        }
        return result;
    }

    /**
     * 查看批次库存信息
     *
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findBatchInventoryList(BatchInventoryQueryDTO dto) {
        LOGGER.info("查看批次库存信息入参: {}", JSON.toJSONString(dto));
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryList(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // 查询仓库名称
        Warehouse warehouse = warehouseQueryService.findWarehouseById(dto.getWarehouseId());
        AssertUtils.notNull(warehouse, "仓库找不到 : " + dto.getWarehouseId());

        // 将小数量转换成大单位数量/小单位数量,设置仓库名称.
        batchInventoryDTOS.forEach(n -> {
            n.setWarehouseName(warehouse.getName());
            BigDecimal packageQuantity = n.getPackageQuantity();
            BigDecimal[] storeTotalCountReminder = n.getStoreTotalCount().divideAndRemainder(packageQuantity);
            n.setStoreCountMax(storeTotalCountReminder[0]);
            n.setStoreCountMin(storeTotalCountReminder[1]);
            if (n.getOwnerType() != null) {
                int ownerType = n.getOwnerType();
                n.setStoreOwnerTypeName(OwnerTypeConst.getEnmuName(ownerType));
            } else {
                n.setStoreOwnerTypeName(OwnerTypeConst.getEnmuName(0));
            }
            // 货区/货位类型名称
            if (Objects.equals(n.getLocationCategory(), CategoryEnum.CARGO_LOCATION.getValue())) {
                n.setLocationSubcategoryName(LocationEnum.getEnumStr(n.getLocationSubcategory()));
            } else if (Objects.equals(n.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue())) {
                n.setLocationSubcategoryName(LocationAreaEnum.getEnumStr(n.getLocationSubcategory()));
            }
        });
        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查看批次库存信息
     *
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findBatchInventoryListBySpecification(BatchInventoryQueryDTO dto) {
        // LOGGER.info("查看批次库存信息参数: {}", JSON.toJSONString(dto));
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryListBySpecification(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // 查询仓库名称
        Warehouse warehouse = warehouseQueryService.findWarehouseById(dto.getWarehouseId());
        AssertUtils.notNull(warehouse, "仓库找不到 : " + dto.getWarehouseId());

        // 将小数量转换成大单位数量/小单位数量,设置仓库名称.
        batchInventoryDTOS.forEach(n -> {
            n.setWarehouseName(warehouse.getName());
            BigDecimal packageQuantity = n.getPackageQuantity();
            BigDecimal[] storeTotalCountReminder = n.getStoreTotalCount().divideAndRemainder(packageQuantity);
            n.setStoreCountMax(storeTotalCountReminder[0]);
            n.setStoreCountMin(storeTotalCountReminder[1]);
            if (n.getOwnerType() != null) {
                int ownerType = n.getOwnerType();
                n.setStoreOwnerTypeName(OwnerTypeConst.getEnmuName(ownerType));
            } else {
                n.setStoreOwnerTypeName(OwnerTypeConst.getEnmuName(0));
            }
        });

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查看批次库存信息(根据货位或产品名称获取批次库存)
     *
     * @param dto
     * @return
     */
    @Deprecated
    public PageList<BatchInventoryDTO> findBatchInventoryListNew(BatchInventoryQueryDTO dto) {
        // LOGGER.info("BatchInventoryQueryDTO:{}", JSON.toJSONString(dto));
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryListNew(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // LOGGER.info("batchInventoryDTOS:{}", JSON.toJSONString(batchInventoryDTOS));

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查看批次库存信息(根据货位或产品名称获取批次库存，支持skuIds)
     *
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findBatchInventoryListBatchNew(BatchInventoryQueryDTO dto) {
        // LOGGER.info("BatchInventoryQueryDTO:{}", JSON.toJSONString(dto));
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryListBatchNew(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // LOGGER.info("batchInventoryDTOS:{}", JSON.toJSONString(batchInventoryDTOS));

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 根据skuid获取库存货位
     *
     * @return
     */
    public List<BatchInventoryDTO> findInventoryLocationBySkuId(BatchInventoryQueryDTO dto) {
        List<BatchInventoryPO> poList = batchInventoryProductStoreBatchMapper.findInventoryLocationBySkuId(dto);
        List<BatchInventoryDTO> dtoList = BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(poList);
        // LOGGER.info("根据skuid获取库存货位:{}", JSON.toJSONString(dtoList));
        return dtoList;
    }

    /**
     * 根据sku查询自身与关联产品的货位库存。 返回结果以传入sku为主，其对应关联产品的货位库存以：货位 + 生产日期 方式合并至 sku 对应货位中
     *
     * @param dto
     * @return
     */
    public List<BatchInventoryDTO> findProductAndRefProductLocationStoreBySkuId(BatchInventoryQueryDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notEmpty(dto.getProductSkuIdList(), "SkuId集合不能为空");
        // 原始SKU
        List<Long> originalSkuIdList =
                dto.getProductSkuIdList().stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 所有产品 sku
        List<Long> allSkuIdList = new ArrayList<>();
        allSkuIdList.addAll(originalSkuIdList);
        // 查询产品关联关系
        Map<ProductSkuDTO, List<ProductSkuDTO>> relationSkuMap =
                iProductRelationGroupService.findGroupTotalProductBySkuIds(dto.getWarehouseId(), originalSkuIdList);
        // 将key过滤只保留sku
        Map<Long, List<ProductSkuDTO>> refSkuMap = new HashMap<>(16);
        if (relationSkuMap != null && !relationSkuMap.isEmpty()) {
            for (Map.Entry<ProductSkuDTO, List<ProductSkuDTO>> entry : relationSkuMap.entrySet()) {
                if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                // 简化关联产品集合
                refSkuMap.put(entry.getKey().getProductSkuId(), entry.getValue());
                // 关联产品 SkuId
                List<Long> valueSkuIdList =
                        entry.getValue().stream().filter(e -> e != null && e.getProductSkuId() != null)
                                .map(e -> e.getProductSkuId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(valueSkuIdList)) {
                    allSkuIdList.addAll(valueSkuIdList);
                }
            }
            List<Long> keySkuIds = relationSkuMap.keySet().stream().filter(e -> e != null).map(e -> e.getProductSkuId())
                    .collect(Collectors.toList());
            allSkuIdList.addAll(keySkuIds);
        } else {
            // 为空则给空 map 方便后面运算
            relationSkuMap = new HashMap<>(16);
        }
        // 获取到所有产品及其关联产品sku后去重
        allSkuIdList = allSkuIdList.stream().filter(e -> e != null).distinct().collect(Collectors.toList());
        // 重新设置查询的SKU
        dto.setProductSkuIdList(allSkuIdList);
        LOGGER.info("findProductAndRefProductLocationStoreBySkuId 查询批次库存参数：{}", JSON.toJSONString(dto));
        List<BatchInventoryPO> poList = batchInventoryProductStoreBatchMapper.findInventoryLocationBySkuId(dto);
        List<BatchInventoryDTO> dtoList = BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(poList);
        if (CollectionUtils.isEmpty(dtoList)) {
            return dtoList;
        }
        // 有关联关系的主产品SKU信息
        Map<Long, ProductSkuDTO> productSkuMap = relationSkuMap.keySet().stream().filter(e -> e != null)
                .collect(Collectors.toMap(e -> e.getProductSkuId(), Function.identity()));
        // 按 sku 进行分组
        Map<Long, List<BatchInventoryDTO>> productBatchStoreMap =
                dtoList.stream().filter(e -> e != null).collect(Collectors.groupingBy(e -> e.getProductSkuId()));
        // 结果集
        List<BatchInventoryDTO> result = new ArrayList<>();
        for (int i = 0; i < originalSkuIdList.size(); i++) {
            Long skuId = originalSkuIdList.get(i);
            if (skuId == null) {
                continue;
            }
            List<BatchInventoryDTO> batchInventoryDTOS =
                    mergeRefProductBatchStore(productBatchStoreMap, refSkuMap, productSkuMap, skuId);
            LOGGER.info("findProductAndRefProductLocationStoreBySkuId - 产品[{}] 批次数据：{}", skuId,
                    JSON.toJSONString(batchInventoryDTOS));
            if (CollectionUtils.isNotEmpty(batchInventoryDTOS)) {
                result.addAll(batchInventoryDTOS);
            }
        }
        return result;
    }

    /**
     * 合并产品-关联产品的货位库存
     *
     * @param productBatchStoreMap
     * @param refSkuMap
     * @param productSkuMap
     * @param originalSkuId
     * @return
     */
    private List<BatchInventoryDTO> mergeRefProductBatchStore(Map<Long, List<BatchInventoryDTO>> productBatchStoreMap,
                                                              Map<Long, List<ProductSkuDTO>> refSkuMap, Map<Long, ProductSkuDTO> productSkuMap, Long originalSkuId) {
        if (productBatchStoreMap == null || productBatchStoreMap.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        // 有关联产品的主产品SKU信息
        ProductSkuDTO origSkuDTO = productSkuMap.get(originalSkuId);
        // 产品批次库存集合
        List<BatchInventoryDTO> productBatchStoreDTOS = productBatchStoreMap.get(originalSkuId);
        LOGGER.info("mergeRefProductBatchStore 产品批次库存信息：{}", JSON.toJSONString(productBatchStoreDTOS));
        // 关联产品信息
        List<ProductSkuDTO> refSkuDTOS = refSkuMap.get(originalSkuId);
        if (CollectionUtils.isEmpty(refSkuDTOS)) {
            // 关联产品为空直接返回
            return productBatchStoreDTOS;
        }
        List<Long> refSkuIdList =
                refSkuDTOS.stream().filter(e -> e != null).map(e -> e.getProductSkuId()).collect(Collectors.toList());
        // 关联产品批次库存集合
        List<BatchInventoryDTO> refProductBatchStoreDTOS = new ArrayList<>();
        // 获取关联产品批次库存
        refSkuIdList.forEach(e -> {
            List<BatchInventoryDTO> refBatchStoreDTOS = productBatchStoreMap.get(e);
            if (CollectionUtils.isNotEmpty(refBatchStoreDTOS)) {
                refProductBatchStoreDTOS.addAll(refBatchStoreDTOS);
            }
        });
        if (CollectionUtils.isEmpty(refProductBatchStoreDTOS)) {
            // 关联产品的批次库存为空则直接返回
            return productBatchStoreDTOS;
        }
        // 1、主产品批次库存不存在，关联产品有批次库存
        if (CollectionUtils.isEmpty(productBatchStoreDTOS)) {
            // 替换SKU
            replaceBatchProduct(refProductBatchStoreDTOS, origSkuDTO);
            LOGGER.info("mergeRefProductBatchStore 主产品不存在，关联产品替换后数据：{}", JSON.toJSONString(refProductBatchStoreDTOS));
            // 返回替换后的集合
            return refProductBatchStoreDTOS;
        } else {
            // 2、 主-关联都有
            // 关联产品 ：货位 + 生产日期 分组
            Map<String, List<BatchInventoryDTO>> refProductStoreMap =
                    refProductBatchStoreDTOS.stream().filter(e -> e != null).collect(Collectors
                            .groupingBy(e -> StringFormatUtil.format("%s-%s", e.getLocationId(), e.getProductionDate())));
            for (Map.Entry<String, List<BatchInventoryDTO>> entry : refProductStoreMap.entrySet()) {
                if (entry == null || CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                List<BatchInventoryDTO> value = entry.getValue();
                LOGGER.info("mergeRefProductBatchStore 关联产品叠加库存至主产品， key : {}, value : {}", entry.getKey(),
                        JSON.toJSONString(value));
                Optional<BatchInventoryDTO> existOpt =
                        productBatchStoreDTOS.stream()
                                .filter(e -> e != null && StringFormatUtil
                                        .format("%s-%s", e.getLocationId(), e.getProductionDate()).equals(entry.getKey()))
                                .findAny();
                if (existOpt.isPresent()) {
                    BatchInventoryDTO inventoryDTO = existOpt.get();
                    LOGGER.info("mergeRefProductBatchStore 存在叠加主产品 : {}", JSON.toJSONString(inventoryDTO));
                    BigDecimal storeTotalCount =
                            ObjectUtils.defaultIfNull(inventoryDTO.getStoreTotalCount(), BigDecimal.ZERO);
                    BigDecimal packageQuantity =
                            ObjectUtils.defaultIfNull(inventoryDTO.getPackageQuantity(), BigDecimal.ONE);
                    // 存在, 直接将批次库存数添加
                    BigDecimal refTotalCount = value.stream().filter(e -> e != null && e.getStoreTotalCount() != null)
                            .map(e -> e.getStoreTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    storeTotalCount = storeTotalCount.add(refTotalCount);
                    // 重新计算大小件
                    BigDecimal[] newCountArr = storeTotalCount.divideAndRemainder(packageQuantity);
                    inventoryDTO.setStoreCountMax(newCountArr[0]);
                    inventoryDTO.setStoreCountMin(newCountArr[1]);
                    inventoryDTO.setStoreTotalCount(storeTotalCount);
                } else {
                    // 不存在, 直接新增到集合
                    replaceBatchProduct(value, origSkuDTO);
                    LOGGER.info("mergeRefProductBatchStore 叠加时不存在符合的主产品替换后数据: {}", JSON.toJSONString(value));
                    productBatchStoreDTOS.addAll(value);
                }
            }
            return productBatchStoreDTOS;
        }
    }

    /**
     * 根据 sku 信息替换批次信息中的产品信息
     *
     * @param batchStoreDTOS
     * @param origSkuDTO
     */
    private void replaceBatchProduct(List<BatchInventoryDTO> batchStoreDTOS, ProductSkuDTO origSkuDTO) {
        for (int i = 0; i < batchStoreDTOS.size(); i++) {
            BatchInventoryDTO refBatchStore = batchStoreDTOS.get(i);
            if (refBatchStore == null) {
                continue;
            }
            refBatchStore.setProductSkuId(origSkuDTO.getProductSkuId());
            refBatchStore.setProductSkuName(origSkuDTO.getName());
            refBatchStore.setProductSpecificationId(origSkuDTO.getProductSpecificationId());
            refBatchStore
                    .setPackageQuantity(ObjectUtils.defaultIfNull(origSkuDTO.getPackageQuantity(), BigDecimal.ONE));
            refBatchStore.setPackageName(origSkuDTO.getPackageName());
            refBatchStore.setUnitName(origSkuDTO.getUnitName());
            // 计算大小件
            BigDecimal storeTotalCount = ObjectUtils.defaultIfNull(refBatchStore.getStoreTotalCount(), BigDecimal.ZERO);
            BigDecimal[] countArr = storeTotalCount.divideAndRemainder(refBatchStore.getPackageQuantity());
            refBatchStore.setStoreCountMin(countArr[1]);
            refBatchStore.setStoreCountMax(countArr[0]);
        }
    }

    /**
     * 查询周转区库存
     *
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findCBHInventoryList(BatchInventoryQueryDTO dto) {
        dto.setSubCategory(LocationAreaEnum.周转区.getType());
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryList(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // 将小数量转换成大单位数量/小单位数量,设置仓库名称.
        batchInventoryDTOS.forEach(n -> {
            BigDecimal packageQuantity = n.getPackageQuantity();
            BigDecimal[] storeTotalCountReminder = n.getStoreTotalCount().divideAndRemainder(packageQuantity);
            n.setStoreCountMax(storeTotalCountReminder[0]);
            n.setStoreCountMin(storeTotalCountReminder[1]);
        });

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查询计算好的下架策略
     *
     * @param orderItemDTOList
     * @param billType         单据类型
     */
    public List<OrderItemDTO> findOutStockStrategyRule(List<OrderItemDTO> orderItemDTOList, String billType) {
        // 获取策略
        List<OutStockStrategyDTO> outStockStrategyDTOS = getOutStockStrategyDTOList(orderItemDTOList, billType);
        // 查不到策略不做处理.
        if (CollectionUtils.isEmpty(outStockStrategyDTOS)) {
            return orderItemDTOList;
        }
        // 查询批次库存,货位信息
        Map<Long, List<BatchLocationInfoDTO>> locationInfoBySkuIdMap = getLocationInfoBySkuIdS(orderItemDTOList, null);

        // 最后返回 k(itemId)->v(List<OrderItemDTO>)
        List<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            processPickCountByStrategyRule(outStockStrategyDTOS, locationInfoBySkuIdMap, returnOrderItemDTOS,
                    orderItemDTO);
        }
        // 找出剩余没有分配完的项，将数量全部分配到已分配货位的最后一个货位上，PDA做缺货处理
        return getNoStoreOrderItemDTOS(orderItemDTOList, returnOrderItemDTOS);
    }

    private List<BatchLocationInfoDTO> processPickCountByStrategyRule(List<OutStockStrategyDTO> outStockStrategyDTOS,
                                                                      Map<Long, List<BatchLocationInfoDTO>> locationInfoBySkuIdMap, List<OrderItemDTO> returnOrderItemDTOS,
                                                                      OrderItemDTO orderItemDTO) {
        List<BatchLocationInfoDTO> locationInfoDTOS = new ArrayList<>();
        for (OutStockStrategyDTO outStockStrategyDTO : outStockStrategyDTOS) {
            for (OutStockStrategyRuleDTO outStockStrategyRuleDTO : outStockStrategyDTO.getStrategyRuleList()) {
                Long productSkuId = orderItemDTO.getProductSkuId();
                BigDecimal specQuantity = orderItemDTO.getSpecQuantity();
                // 批次库存
                List<BatchLocationInfoDTO> batchLocationInfoDTOS = locationInfoBySkuIdMap.get(productSkuId);
                if (CollectionUtils.isEmpty(batchLocationInfoDTOS)) {
                    LOGGER.info("该产品找不到批次库存记录 productskuId :{},仓库id:{},渠道:{},产品来源:{}", productSkuId,
                            orderItemDTO.getWarehouseId(), orderItemDTO.getChannel(), orderItemDTO.getSource());
                    break;
                }
                List<BatchLocationInfoDTO> lstTmpBatchLocations = batchLocationInfoDTOS.stream()
                        .filter(p -> p.getChannel() == null || orderItemDTO.getChannel() == null
                                || p.getChannel().equals(orderItemDTO.getChannel()))
                        .collect(Collectors.toList());

                // LOGGER.info("下架策略:{},批次库存:{},相同渠道库存:{}", JSON.toJSONString(outStockStrategyRuleDTO),
                // JSON.toJSONString(batchLocationInfoDTOS), JSON.toJSONString(lstTmpBatchLocations));

                if (CollectionUtils.isEmpty(lstTmpBatchLocations)) {
                    LOGGER.info("该产品找不到相同渠道的批次库存记录 productskuId :{},仓库id:{},渠道:{},产品来源:{}", productSkuId,
                            orderItemDTO.getWarehouseId(), orderItemDTO.getChannel(), orderItemDTO.getSource());
                    break;
                }

                // 过滤掉不满足条件的货区信息
                lstTmpBatchLocations = filterLocationInfo(outStockStrategyRuleDTO, lstTmpBatchLocations);

                LOGGER.info("满足条件的货区信息:{}", JSON.toJSONString(lstTmpBatchLocations));
                if (CollectionUtils.isNotEmpty(lstTmpBatchLocations)) {
                    // LOGGER.info("过滤后的货区信息:{}", JSON.toJSONString(lstTmpBatchLocations));
                    // 按货位分配规则 1.由高到低 2 清空库位（由低到高） 3路线/库位顺序 处理 4 生产日期
                    lstTmpBatchLocations = processLocationAssignRule(outStockStrategyRuleDTO, lstTmpBatchLocations);
                    // 按照二级货主排序（优先处理跟自身相同的二级货主，其次处理其他）
                    lstTmpBatchLocations = batchInventoryManageBL
                            .batchLocationInfoSortedBySecOwnerId(lstTmpBatchLocations, orderItemDTO.getSecOwnerId());
                    // 处理分配规则 1按大单位数量分配 2按小单位数量分配
                    List<OrderItemDTO> orderItemDTOS =
                            processAssignRule(orderItemDTO, outStockStrategyRuleDTO, lstTmpBatchLocations);
                    // LOGGER.info("分配好的订单项:{}", JSON.toJSONString(orderItemDTOS));
                    // 如果没有结果,原单返回.
                    orderItemDTOS.forEach(n -> {
                        BigDecimal[] pickUpCountRemainder = n.getPickUpCount().divideAndRemainder(specQuantity);
                        n.setPackageCount(pickUpCountRemainder[0]);
                        n.setUnitCount(pickUpCountRemainder[1]);
                    });
                    returnOrderItemDTOS.addAll(orderItemDTOS);
                }
                locationInfoDTOS.addAll(lstTmpBatchLocations);
            }
        }
        return locationInfoDTOS;
    }

    /**
     * 查询计算好的延迟配送下架策略
     *
     * @param orderItemDTOList
     * @param billType         单据类型
     */
    public List<OrderItemDTO> findDelayOutStockStrategyRule(List<OrderItemDTO> orderItemDTOList, String billType) {

        // 查询批次库存,货位信息
        Map<Long, List<BatchLocationInfoDTO>> locationInfoBySkuIdMap =
                getLocationInfoBySkuIdS(orderItemDTOList, LocationAreaEnum.退货区.getType());

        // 最后返回 k(itemId)->v(List<OrderItemDTO>)
        List<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            Long productSkuId = orderItemDTO.getProductSkuId();
            BigDecimal specQuantity = orderItemDTO.getSpecQuantity();
            // 批次库存
            List<BatchLocationInfoDTO> batchLocationInfoDTOS = locationInfoBySkuIdMap.get(productSkuId);
            if (CollectionUtils.isEmpty(batchLocationInfoDTOS)) {
                LOGGER.info("该产品找不到批次库存记录 productskuId :{},仓库id:{},渠道:{},产品来源:{}", productSkuId,
                        orderItemDTO.getWarehouseId(), orderItemDTO.getChannel(), orderItemDTO.getSource());
                break;
            }
            List<BatchLocationInfoDTO> lstTmpBatchLocations =
                    batchLocationInfoDTOS.stream().filter(p -> p.getChannel() == null || orderItemDTO.getChannel() == null
                            || p.getChannel().equals(orderItemDTO.getChannel())).collect(Collectors.toList());

            LOGGER.info("延迟配送下架策略,批次库存:{},相同渠道库存:{}", JSON.toJSONString(batchLocationInfoDTOS),
                    JSON.toJSONString(lstTmpBatchLocations));

            if (CollectionUtils.isEmpty(lstTmpBatchLocations)) {
                LOGGER.info("该产品找不到相同渠道的批次库存记录 productskuId :{},仓库id:{},渠道:{},产品来源:{}", productSkuId,
                        orderItemDTO.getWarehouseId(), orderItemDTO.getChannel(), orderItemDTO.getSource());
                break;
            }

            // 按二级货主去匹配货位
            if (orderItemDTO.getSecOwnerId() != null) {
                lstTmpBatchLocations = lstTmpBatchLocations.stream()
                        .filter(p -> Objects.equals(p.getOwnerId(), orderItemDTO.getOwnerId())
                                && Objects.equals(p.getSecOwnerId(), orderItemDTO.getSecOwnerId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(lstTmpBatchLocations)) {
                    LOGGER.info("[延迟配送下架策略]该产品找不到相同二级货主的批次库存记录 productskuId :{},仓库id:{},货主id:{},二级货主id:{}",
                            productSkuId, orderItemDTO.getWarehouseId(), orderItemDTO.getOwnerId(),
                            orderItemDTO.getSecOwnerId());
                    break;
                }
            }

            // 处理分配规则 1按大单位数量分配 2按小单位数量分配
            List<OrderItemDTO> orderItemDTOS = processUnitCount(orderItemDTO, lstTmpBatchLocations);
            // 如果没有结果,原单返回.
            orderItemDTOS.forEach(n -> {
                BigDecimal[] pickUpCountRemainder = n.getPickUpCount().divideAndRemainder(specQuantity);
                n.setPackageCount(pickUpCountRemainder[0]);
                n.setUnitCount(pickUpCountRemainder[1]);
            });
            returnOrderItemDTOS.addAll(orderItemDTOS);
        }
        return getNoStoreOrderItemDTOS(orderItemDTOList, returnOrderItemDTOS);

    }

    private List<OrderItemDTO> getNoStoreOrderItemDTOS(List<OrderItemDTO> orderItemDTOList,
                                                       List<OrderItemDTO> returnOrderItemDTOS) {
        LOGGER.info("已分配的项: {}", JSON.toJSONString(returnOrderItemDTOS));
        // 找出剩余没有分配完的项，将数量全部分配到已分配货位的最后一个货位上，PDA做缺货处理
        List<OrderItemDTO> lstLeftItems = orderItemDTOList.stream()
                .filter(p -> p.getUnitTotalCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        List<OrderItemDTO> lstHasItems = returnOrderItemDTOS.stream()
                .filter(p -> lstLeftItems.stream().anyMatch(q -> Objects.equals(q.getIdentityKey(), p.getIdentityKey())))
                .collect(Collectors.toList());
        List<Long> lstProcessIds = new ArrayList<>();
        lstHasItems.stream().forEach(p -> {
            if (lstProcessIds.contains(p.getId())) {
                return;
            }
            lstProcessIds.add(p.getId());
            // 找到最后一个分配的货位
            List<OrderItemDTO> lstHasItemDTOS = lstHasItems.stream()
                    .filter(q -> Objects.equals(q.getIdentityKey(), p.getIdentityKey())).collect(Collectors.toList());
            OrderItemDTO lastDTO = lstHasItemDTOS.get(lstHasItemDTOS.size() - 1);

            // 将没有分配完的项，全部分到最后一个货位上
            List<OrderItemDTO> lstNoItemDTOS = lstLeftItems.stream()
                    .filter(q -> Objects.equals(q.getIdentityKey(), p.getIdentityKey())).collect(Collectors.toList());
            BigDecimal specQuantity = lastDTO.getSpecQuantity();
            lstNoItemDTOS.forEach(q -> {
                lastDTO.setPickUpCount(lastDTO.getPickUpCount().add(q.getUnitTotalCount()));
                q.setUnitTotalCount(BigDecimal.ZERO);
            });
            BigDecimal[] pickUpCountRemainder = lastDTO.getPickUpCount().divideAndRemainder(specQuantity);
            lastDTO.setPackageCount(pickUpCountRemainder[0]);
            lastDTO.setUnitCount(pickUpCountRemainder[1]);
            LOGGER.info(String.format("存在没有分配完的货位:%s，分配到最后一个找到的货位：%s", JSON.toJSONString(lstNoItemDTOS),
                    JSON.toJSONString(lastDTO)));
        });

        // 没有分配好的单子,原单返回.
        List<String> collect = returnOrderItemDTOS.stream().map(n -> n.getIdentityKey()).collect(Collectors.toList());
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            if (!collect.contains(orderItemDTO.getIdentityKey())) {
                returnOrderItemDTOS.add(orderItemDTO);
            }
        }
        List<OrderItemDTO> lstResult = returnOrderItemDTOS.stream()
                .filter(n -> n.getLocationId() == null
                        || n.getLocationId() != null && n.getPickUpCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        LOGGER.info(String.format("生拣货任务查找货位结果:%s", JSON.toJSONString(lstResult)));
        return lstResult;
    }

    // 处理分配规则 1按大单位数量分配 2按小单位数量分配
    private ArrayList<OrderItemDTO> processAssignRule(OrderItemDTO orderItemDTO,
                                                      OutStockStrategyRuleDTO outStockStrategyRuleDTO, List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        ArrayList<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();
        if (outStockStrategyRuleDTO == null || outStockStrategyRuleDTO.getAssignRule() == null) {
            return returnOrderItemDTOS;
        }
        switch (outStockStrategyRuleDTO.getAssignRule()) {
            // 按大单位数量分配
            case 1:
                returnOrderItemDTOS = processPackageCount(orderItemDTO, batchLocationInfoDTOS);
                break;
            // 按小单位数量分配
            case 2:
                returnOrderItemDTOS = processUnitCount(orderItemDTO, batchLocationInfoDTOS);
                break;
            default:
                break;
        }
        return returnOrderItemDTOS;
    }

    // 分配规则 2按小单位数量分配
    private ArrayList<OrderItemDTO> processUnitCount(OrderItemDTO orderItemDTO,
                                                     List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        BigDecimal pickUpCount = orderItemDTO.getUnitTotalCount();
        Long id = orderItemDTO.getId();
        ArrayList<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();

        // 按促销匹配策略对批次库存进行排序
        List<BatchLocationInfoDTO> sortedBatchLocations = sortBatchLocationsByAdventMatch(orderItemDTO, batchLocationInfoDTOS);

        LOGGER.info("orderItem:{},oriInventory:{},sortedInventory:{}", JSON.toJSONString(orderItemDTO), JSON.toJSONString(batchLocationInfoDTOS), JSON.toJSONString(sortedBatchLocations));

        // 1.如果扣减数量=0,结束;
        // 2.如果扣减数量>该批次库存,扣完该批次.下一批继续扣.
        // 3.如果扣减数量<该历次库存.扣完结束.
        for (BatchLocationInfoDTO batchLocationInfoDTO : sortedBatchLocations) {
            BigDecimal totalCount = batchLocationInfoDTO.getTotalCount();
            if (pickUpCount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            if (totalCount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            BigDecimal tmpPickCount = BigDecimal.ZERO;
            if (pickUpCount.compareTo(totalCount) >= 0) {
                tmpPickCount = totalCount;
            } else {
                tmpPickCount = pickUpCount;
            }
            pickUpCount = pickUpCount.subtract(tmpPickCount);
            totalCount = totalCount.subtract(tmpPickCount);
            batchLocationInfoDTO.setTotalCount(totalCount);
            // ***这里讲pickup数量扣减下来,以免下次循环的时候又重置了.
            orderItemDTO.setUnitTotalCount(pickUpCount);

            OrderItemDTO returnOrderItemDTO = new OrderItemDTO();
            returnOrderItemDTO.setId(id);
            returnOrderItemDTO.setLocationId(batchLocationInfoDTO.getLocationId());
            returnOrderItemDTO.setLocationName(batchLocationInfoDTO.getLocationName());
            returnOrderItemDTO.setSubCategory(batchLocationInfoDTO.getSubcategory());
            returnOrderItemDTO.setAreaId(batchLocationInfoDTO.getAreaId());
            returnOrderItemDTO.setAreaName(batchLocationInfoDTO.getAreaName());
            returnOrderItemDTO.setPickUpCount(tmpPickCount);
            returnOrderItemDTO.setSpecQuantity(orderItemDTO.getSpecQuantity());
            returnOrderItemDTO.setBatchTime(batchLocationInfoDTO.getBatchTime());
            returnOrderItemDTO.setProductionDate(batchLocationInfoDTO.getProductionDate());
            returnOrderItemDTO.setItemDetailId(orderItemDTO.getItemDetailId());
            returnOrderItemDTO.setSecOwnerId(batchLocationInfoDTO.getSecOwnerId());
            returnOrderItemDTOS.add(returnOrderItemDTO);
        }
        return returnOrderItemDTOS;
    }

    // 分配规则 1按大单位数量分配
    private ArrayList<OrderItemDTO> processPackageCount(OrderItemDTO orderItemDTO,
                                                        List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        BigDecimal pickUpCount = orderItemDTO.getUnitTotalCount();
        BigDecimal specQuantity = orderItemDTO.getSpecQuantity();
        Long id = orderItemDTO.getId();
        ArrayList<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();

        // 按促销匹配策略对批次库存进行排序
        List<BatchLocationInfoDTO> sortedBatchLocations = sortBatchLocationsByAdventMatch(orderItemDTO, batchLocationInfoDTOS);

        // 1.如果 扣减数量<规格系数,或者该批次库存数量<规格系数,说明不够扣.跳到下个批次
        // 2.如果 扣减数量小于该批次数量,尽可能的扣 扣减数量
        // 3.如果扣减数量大于该批次数量,尽可能的扣该批次数量
        for (BatchLocationInfoDTO batchLocationInfoDTO : sortedBatchLocations) {
            BigDecimal totalCount = batchLocationInfoDTO.getTotalCount();
            if (totalCount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (pickUpCount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            // 如果拣货数量或者当前货位库存总数量，不足1件，舍弃，改为小单位拣货
            if (pickUpCount.min(totalCount).compareTo(specQuantity) < 0) {
                break;
            }
            BigDecimal tmpPickCount = BigDecimal.ZERO;
            if (pickUpCount.compareTo(totalCount) < 0) {
                tmpPickCount = pickUpCount;
            } else {
                tmpPickCount = totalCount;
            }
            pickUpCount = pickUpCount.subtract(tmpPickCount);
            totalCount = totalCount.subtract(tmpPickCount);
            batchLocationInfoDTO.setTotalCount(totalCount);
            // ***这里讲pickup数量扣减下来,以免下次循环的时候又重置了.
            orderItemDTO.setUnitTotalCount(pickUpCount);

            OrderItemDTO returnOrderItemDTO = new OrderItemDTO();
            returnOrderItemDTO.setId(id);
            returnOrderItemDTO.setLocationId(batchLocationInfoDTO.getLocationId());
            returnOrderItemDTO.setLocationName(batchLocationInfoDTO.getLocationName());
            returnOrderItemDTO.setSubCategory(batchLocationInfoDTO.getSubcategory());
            returnOrderItemDTO.setAreaId(batchLocationInfoDTO.getAreaId());
            returnOrderItemDTO.setAreaName(batchLocationInfoDTO.getAreaName());
            returnOrderItemDTO.setPickUpCount(tmpPickCount);
            returnOrderItemDTO.setSpecQuantity(orderItemDTO.getSpecQuantity());
            returnOrderItemDTO.setBatchTime(batchLocationInfoDTO.getBatchTime());
            returnOrderItemDTO.setProductionDate(batchLocationInfoDTO.getProductionDate());
            returnOrderItemDTO.setItemDetailId(orderItemDTO.getItemDetailId());
            returnOrderItemDTO.setSecOwnerId(batchLocationInfoDTO.getSecOwnerId());
            returnOrderItemDTOS.add(returnOrderItemDTO);
        }
        return returnOrderItemDTOS;
    }

    // 按货位分配规则 1.由高到低 2 清空库位（由低到高） 3路线/库位顺序 处理 4 生产日期
    private List<BatchLocationInfoDTO> processLocationAssignRule(OutStockStrategyRuleDTO outStockStrategyRuleDTO,
                                                                 List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        if (outStockStrategyRuleDTO == null || outStockStrategyRuleDTO.getLocationAssignRule() == null) {
            return batchLocationInfoDTOS;
        }
        switch (outStockStrategyRuleDTO.getLocationAssignRule()) {
            case 1:
                // 数量由高到低
                batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                        .sorted((o1, o2) -> o2.getTotalCount().compareTo(o1.getTotalCount())).collect(Collectors.toList());
                break;
            case 2:
                // 数量由低到高
                batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                        .sorted(Comparator.comparing(BatchLocationInfoDTO::getTotalCount)).collect(Collectors.toList());
                break;
            case 3:
                // 路线/库位顺序
                // 根据locationId查询出货位顺序.
                List<LocationReturnDTO> locationListById = locationAreaService.findLocationListById(
                        batchLocationInfoDTOS.stream().map(n -> n.getLocationId().toString()).collect(Collectors.toList()));
                Map<Long, Integer> returnLocationSequenceMap = new HashMap<>(16);
                for (LocationReturnDTO locationReturnDTO : locationListById) {
                    returnLocationSequenceMap.put(locationReturnDTO.getId(), locationReturnDTO.getSequence());
                }
                batchLocationInfoDTOS.forEach(n -> {
                    Integer sequence = returnLocationSequenceMap.get(n.getLocationId());
                    n.setSequence(sequence);
                });
                // //没有线路顺序的按名称
                // if (batchLocationInfoDTOS.stream().allMatch(p -> p.getSequence() == null)) {
                // batchLocationInfoDTOS =
                // batchLocationInfoDTOS.stream().sorted(Comparator.comparing(BatchLocationInfoDTO::getLocationName)).collect(Collectors.toList());
                // } else {
                // //如果既有线路顺序的，又有没有线路顺序的，把没有线路顺序的序号改成最大值，然后按线路顺序排序
                // //如果全部都有线路顺序，按线路顺序升序
                // if (batchLocationInfoDTOS.stream().anyMatch(p -> p.getSequence() != null)) {
                // batchLocationInfoDTOS.forEach(n -> {
                // if (n.getSequence() == null) {
                // n.setSequence(Integer.MAX_VALUE);
                // }
                // });
                // }
                // batchLocationInfoDTOS =
                // batchLocationInfoDTOS.stream().sorted(Comparator.comparing(BatchLocationInfoDTO::getSequence)).collect(Collectors.toList());
                // }

                List<BatchLocationInfoDTO> returnBatchLocationInfoDTOS = new ArrayList<>();
                // 有线路顺序(按线路顺序),
                List<BatchLocationInfoDTO> sequenceIsNULL =
                        batchLocationInfoDTOS.stream().filter(n -> n.getSequence() != null).collect(Collectors.toList());
                sequenceIsNULL = sequenceIsNULL.stream().sorted(Comparator.comparing(BatchLocationInfoDTO::getSequence))
                        .collect(Collectors.toList());
                // 没有线路顺序的排后面(按名称)
                List<BatchLocationInfoDTO> sequenceIsNotNULL =
                        batchLocationInfoDTOS.stream().filter(n -> n.getSequence() == null).collect(Collectors.toList());
                // 如果线路名称为空，强制设置为空，避免报错
                sequenceIsNotNULL.forEach(p -> {
                    if (StringUtil.isEmpty(p.getLocationName())) {
                        p.setLocationName("");
                    }
                });
                sequenceIsNotNULL = sequenceIsNotNULL.stream()
                        .sorted(Comparator.comparing(BatchLocationInfoDTO::getLocationName)).collect(Collectors.toList());
                // 有线路顺序的放前面,没有的放后面
                if (sequenceIsNULL.size() > 0) {
                    returnBatchLocationInfoDTOS.addAll(sequenceIsNULL);
                }
                if (sequenceIsNotNULL.size() > 0) {
                    returnBatchLocationInfoDTOS.addAll(sequenceIsNotNULL);
                }
                batchLocationInfoDTOS = returnBatchLocationInfoDTOS;
                break;
            case 4:
                // 生产日期+批次日期升序
                batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                        .sorted(Comparator.nullsFirst(Comparator
                                .comparing(BatchLocationInfoDTO::getProductionDate, Comparator.nullsFirst(Date::compareTo))
                                .thenComparing(BatchLocationInfoDTO::getBatchTime, Comparator.nullsFirst(Date::compareTo))))
                        .collect(Collectors.toList());
                break;
            default:
                break;
        }
        return batchLocationInfoDTOS;
    }

    // 过滤掉不满足条件的货区信息
    private List<BatchLocationInfoDTO> filterLocationInfo(OutStockStrategyRuleDTO outStockStrategyRuleDTO,
                                                          List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        Byte locationAssignLimitType = outStockStrategyRuleDTO.getLocationAssignLimitType();
        Byte locationAssignType = outStockStrategyRuleDTO.getLocationAssignType();
        // 过滤掉周转区
        batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                .filter(n -> n.getSubcategory() != null && n.getSubcategory().intValue() != LocationAreaEnum.周转区.getType())
                .collect(Collectors.toList());
        if (locationAssignLimitType != null) {
            batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                    .filter(n -> n.getSubcategory() != null && !n.getSubcategory().equals(locationAssignLimitType))
                    .collect(Collectors.toList());
        }
        if (locationAssignType != null) {
            batchLocationInfoDTOS = batchLocationInfoDTOS.stream()
                    .filter(n -> n.getSubcategory() != null && n.getSubcategory().equals(locationAssignType))
                    .collect(Collectors.toList());
        }
        // 优先货位，其次根据货位类别
        // todo npe
        // batchLocationInfoDTOS.sort(Comparator.comparing(BatchLocationInfoDTO::getLocationCategory).thenComparing(BatchLocationInfoDTO::getSubcategory));
        return batchLocationInfoDTOS;
    }

    /**
     * 按促销匹配策略对批次库存进行排序
     * 非促销订单：优先非促销库存，其次促销库存
     * 促销订单：优先促销库存，其次非促销库存
     *
     * @param orderItemDTO 订单项
     * @param batchLocationInfoDTOS 批次库存列表
     * @return 排序后的批次库存列表
     */
    private List<BatchLocationInfoDTO> sortBatchLocationsByAdventMatch(OrderItemDTO orderItemDTO,
                                                                       List<BatchLocationInfoDTO> batchLocationInfoDTOS) {
        if (batchLocationInfoDTOS == null || batchLocationInfoDTOS.isEmpty()) {
            return batchLocationInfoDTOS;
        }

        // 判断订单项是否为促销订单（IsAdvent == 1 表示促销）
        boolean isAdventOrder = orderItemDTO.getIsAdvent() != null && orderItemDTO.getIsAdvent().equals((byte) 1);

        // 分离促销库存和非促销库存
        List<BatchLocationInfoDTO> adventStock = new ArrayList<>();
        List<BatchLocationInfoDTO> normalStock = new ArrayList<>();

        for (BatchLocationInfoDTO batchLocation : batchLocationInfoDTOS) {
            // adventId > 0 表示促销库存
            if (batchLocation.getAdventId() != null && batchLocation.getAdventId() > 0) {
                adventStock.add(batchLocation);
            } else {
                normalStock.add(batchLocation);
            }
        }

        // 根据订单类型决定优先级顺序
        List<BatchLocationInfoDTO> sortedList = new ArrayList<>();
        if (isAdventOrder) {
            // 促销订单：优先促销库存，其次正常库存
            sortedList.addAll(adventStock);
            sortedList.addAll(normalStock);
        } else {
            // 非促销订单：优先非促销库存，其次促销库存
            sortedList.addAll(normalStock);
            sortedList.addAll(adventStock);
        }

        return sortedList;
    }

    // 查询批次库存,货位信息
    private Map<Long, List<BatchLocationInfoDTO>> getLocationInfoBySkuIdS(List<OrderItemDTO> items,
                                                                          Integer subcategory) {
        try {
            return batchInventoryQueryNewBL.getLocationInfoBySkuIdS(items, subcategory);
        } catch (Throwable t) {
            LOGGER.warn("分页查询出错: ", t);
        }
        return getLocationInfoBySkuIdsOld(items, subcategory);
    }

    private Map<Long, List<BatchLocationInfoDTO>> getLocationInfoBySkuIdsOld(List<OrderItemDTO> orderItemDTOList,
                                                                             Integer subcategory) {
        Map<Long, List<BatchLocationInfoDTO>> map = new HashMap<>(16);
        List<BatchLocationInfoQueryPO> batchLocationInfoQueryPOS = new ArrayList<>();
        orderItemDTOList.forEach(itemDTO -> {
            BatchLocationInfoQueryPO batchLocationInfoQueryPO = new BatchLocationInfoQueryPO();
            batchLocationInfoQueryPO.setProductSkuId(itemDTO.getProductSkuId());
            batchLocationInfoQueryPO.setWarehouseId(itemDTO.getWarehouseId());
            batchLocationInfoQueryPO.setChannel(itemDTO.getChannel());
            batchLocationInfoQueryPO.setSource(itemDTO.getSource());
            if (!batchLocationInfoQueryPOS.stream()
                    .anyMatch(p -> p.getGroupKey().equals(batchLocationInfoQueryPO.getGroupKey()))) {
                batchLocationInfoQueryPOS.add(batchLocationInfoQueryPO);
            }
        });

        if (CollectionUtils.isEmpty(batchLocationInfoQueryPOS)) {
            // LOGGER.info(String.format("getLocationInfoBySkuIdS参数为空！%s", JSON.toJSONString(orderItemDTOList)));
            return map;
        }
        // //传入发货城市id,为了解决招商订单与长株潭合并skuId不一致问题.
        // //前面做了断言,orderItemDTOList一定不为null
        // Integer deliverCityId = warehouseCityBL.getCityIdByWarehouseId(orderItemDTOList.get(0).getWarehouseId());
        Integer warehouseId = orderItemDTOList.get(0).getWarehouseId();
        // 查询批次库存,货位信息
        List<BatchLocationInfoDTO> batchDTOBySkuList;
        if (subcategory == null) {
            batchDTOBySkuList = batchInventoryProductSkuMapper.findBatchDTOBySkuList(batchLocationInfoQueryPOS, warehouseId);
        } else if (subcategory == LocationAreaEnum.存储区.getType().intValue()) {
            batchDTOBySkuList = batchInventoryProductSkuMapper.findBatchBySkuListAndCategory(batchLocationInfoQueryPOS, Arrays
                            .asList(LocationAreaEnum.存储区.getType(), LocationAreaEnum.拣货区.getType(), LocationAreaEnum.零拣区.getType()),
                    warehouseId);
        } else {
            batchDTOBySkuList =
                    batchInventoryProductSkuMapper.findBatchDTOBySkuListAndCategory(batchLocationInfoQueryPOS, subcategory, warehouseId);
        }
        // LOGGER.info("获取货位结果：" + JSON.toJSONString(batchDTOBySkuList));
        batchDTOBySkuList.forEach(p -> {
            if (p.getAreaId() == null && p.getLocationId() != null && p.getLocationCategory() != null
                    && p.getLocationCategory().intValue() == 1) {
                p.setAreaId(p.getLocationId());
                p.setAreaName(p.getLocationName());
            }
        });
        batchDTOBySkuList = getProductStoreBatchOrderByProductionDateAndBatchDate(batchDTOBySkuList);
        // k(productSkuId)->v
        map = batchDTOBySkuList.stream().collect(Collectors.groupingBy(BatchLocationInfoDTO::getProductSkuId));
        return map;
    }

    /**
     * 批次库存排序规则-（生产日期+批次时间） 1、生产日期（为空的排最前） 2、批次时间（为空的排最前）
     */
    public static List<BatchLocationInfoDTO>
    getProductStoreBatchOrderByProductionDateAndBatchDate(List<BatchLocationInfoDTO> productStoreBatchPOS) {
        List<BatchLocationInfoDTO> lstAllProductStores = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)) {
            List<Date> lstAllProductionDates = new ArrayList<>();
            lstAllProductionDates.add(null);
            List<Date> lstTmpProductionDates = productStoreBatchPOS.stream().filter(p -> p.getProductionDate() != null)
                    .map(p -> p.getProductionDate()).distinct().sorted().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lstTmpProductionDates)) {
                lstAllProductionDates.addAll(lstTmpProductionDates);
            }

            for (Date dtProducttionDate : lstAllProductionDates) {
                List<BatchLocationInfoDTO> lstTmp =
                        getProductStoreBatchPOsOrderByBatchTime(productStoreBatchPOS, dtProducttionDate);
                if (CollectionUtils.isNotEmpty(lstTmp)) {
                    lstAllProductStores.addAll(lstTmp);
                }
            }
            if (lstTmpProductionDates.size() > 1) {
                LOGGER.info(String.format("存在多个生产日期！生产日期：%s，批次库存排序结果：%s", JSON.toJSONString(lstAllProductionDates),
                        JSON.toJSONString(lstAllProductStores)));
            }
        }
        return lstAllProductStores;
    }

    /**
     * 同一生产日期的批次库存排序规则 批次时间（为空的排最前）
     */
    public static List<BatchLocationInfoDTO> getProductStoreBatchPOsOrderByBatchTime(
            List<BatchLocationInfoDTO> productStoreBatchPOS, Date dtProducttionDate) {
        List<BatchLocationInfoDTO> lstTmp = productStoreBatchPOS.stream()
                .filter(p -> Objects.equals(dtProducttionDate, p.getProductionDate())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstTmp)) {
            // 批次为空的数据
            List<BatchLocationInfoDTO> lstNoBatchDate =
                    lstTmp.stream().filter(p -> p.getBatchTime() == null).collect(Collectors.toList());
            // 优先扣批次日期为空或者批次临期的
            List<BatchLocationInfoDTO> lstHasBatchDate = lstTmp.stream().filter(p -> p.getBatchTime() != null)
                    .sorted(Comparator.nullsFirst(Comparator.comparing(BatchLocationInfoDTO::getBatchTime)))
                    .collect(Collectors.toList());
            lstTmp = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lstNoBatchDate)) {
                lstTmp.addAll(lstNoBatchDate);
            }
            if (CollectionUtils.isNotEmpty(lstHasBatchDate)) {
                lstTmp.addAll(lstHasBatchDate);
            }
        }
        return lstTmp;
    }

    /**
     * 获取策略
     *
     * @param orderItemDTOList
     * @param billType
     * @return
     */
    private List<OutStockStrategyDTO> getOutStockStrategyDTOList(List<OrderItemDTO> orderItemDTOList, String billType) {
        OrderItemDTO orderItemDTO = orderItemDTOList.get(0);
        Integer warehouseId = orderItemDTO.getWarehouseId();

        OutStockStrategyDTO outStockStrategyDTO = new OutStockStrategyDTO();
        outStockStrategyDTO.setBillType(billType);
        outStockStrategyDTO.setWarehouseId(warehouseId);
        // 根据仓库id和单据类型查询下架策略
        List<OutStockStrategyDTO> outStockStrategyDTOS =
                iOutStockStrategyService.findOutStockStrategyByOutStockStrategy(outStockStrategyDTO);
        return outStockStrategyDTOS;
    }

    private static List<BatchLocationInfoQueryPO>
    orderItemDTOS2BatchLocationInfoQueryPOS(List<OrderItemDTO> orderItemDTOS) {
        List<BatchLocationInfoQueryPO> batchLocationInfoQueryPOS = new ArrayList<>();
        for (OrderItemDTO itemDTO : orderItemDTOS) {
            BatchLocationInfoQueryPO batchLocationInfoQueryPO = new BatchLocationInfoQueryPO();
            batchLocationInfoQueryPO.setProductSkuId(itemDTO.getProductSkuId());
            batchLocationInfoQueryPO.setWarehouseId(itemDTO.getWarehouseId());
            batchLocationInfoQueryPO.setChannel(itemDTO.getChannel());
            batchLocationInfoQueryPO.setSource(itemDTO.getSource());
            if (!batchLocationInfoQueryPOS.stream()
                    .anyMatch(p -> p.getGroupKey().equals(batchLocationInfoQueryPO.getGroupKey()))) {
                batchLocationInfoQueryPOS.add(batchLocationInfoQueryPO);
            }
        }
        return batchLocationInfoQueryPOS;
    }

    /**
     * 推荐货位查询
     *
     * @param orderItemDTOList
     * @return
     */
    public Map<Long, List<BatchLocationInfoDTO>> findLocationByStrategyRule(List<OrderItemDTO> orderItemDTOList,
                                                                            String billType) {
        Map<Long, List<BatchLocationInfoDTO>> locationInfoMap = new HashMap<>(16);
        // 获取策略
        List<OutStockStrategyDTO> outStockStrategyDTOS = getOutStockStrategyDTOList(orderItemDTOList, billType);
        // 查不到策略不做处理.
        if (CollectionUtils.isEmpty(outStockStrategyDTOS)) {
            return locationInfoMap;
        }
        // 查询批次库存,货位信息
        Map<Long, List<BatchLocationInfoDTO>> locationInfoBySkuIdMap = getLocationInfoBySkuIdS(orderItemDTOList, null);

        LOGGER.info("查询批次库存,货位信息:", JSON.toJSONString(locationInfoBySkuIdMap));

        List<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            List<BatchLocationInfoDTO> locationInfoDTOS = processPickCountByStrategyRule(outStockStrategyDTOS,
                    locationInfoBySkuIdMap, returnOrderItemDTOS, orderItemDTO);
            locationInfoMap.put(orderItemDTO.getProductSkuId(), locationInfoDTOS);
        }
        return locationInfoMap;
    }

    /**
     * 根据skuid获取库存货位（不关联location）
     *
     * @return
     */
    public List<BatchInventoryDTO> findBatchStoreBySkuId(BatchInventoryQueryDTO dto) {
        List<BatchInventoryPO> poList = batchInventoryProductStoreBatchMapper.findBatchStoreBySkuId(dto);
        List<BatchInventoryDTO> dtoList = BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(poList);
        // LOGGER.info("根据skuid获取库存货位:{}", JSON.toJSONString(dtoList));
        return dtoList;
    }

    /**
     * 检查产品货位库存是否足够
     */
    public void checkProductInventory(List<CheckBatchInventoryDTO> checkBatchInventoryDTOS) {
        Integer warehouseId = checkBatchInventoryDTOS.get(0).getWarehouseId();
        List<Long> skuIds =
                checkBatchInventoryDTOS.stream().map(CheckBatchInventoryDTO::getProductSkuId).collect(Collectors.toList());
        BatchInventoryQueryDTO batchInventoryQueryDTO = new BatchInventoryQueryDTO();
        batchInventoryQueryDTO.setProductSkuIdList(skuIds);
        batchInventoryQueryDTO.setWarehouseId(warehouseId);
        List<BatchInventoryDTO> batchStoreBySkuId = findBatchStoreBySkuId(batchInventoryQueryDTO);
        LOGGER.info("查询出的货位库存信息,参数:{},结果:{}", JSON.toJSONString(batchInventoryQueryDTO),
                JSON.toJSONString(batchStoreBySkuId));

        Map<Long, List<BatchInventoryDTO>> batchStoreBySkuIdMap =
                batchStoreBySkuId.stream().collect(Collectors.groupingBy(BatchInventoryDTO::getProductSkuId));
        checkBatchInventoryDTOS.forEach(item -> {
            Long productSkuId = item.getProductSkuId();
            Date productionDate = item.getProductionDate();
            Long fromLocationId = item.getLocationId();
            List<BatchInventoryDTO> batchInventoryDTOS = batchStoreBySkuIdMap.get(productSkuId);
            BigDecimal unitTotalCount = batchInventoryDTOS.stream().filter(batchInventory -> {
                if (productionDate == null) {
                    return fromLocationId.equals(batchInventory.getLocationId())
                            && batchInventory.getProductionDate() == null;
                } else {
                    return fromLocationId.equals(batchInventory.getLocationId())
                            && productionDate.equals(batchInventory.getProductionDate());
                }
            }).map(BatchInventoryDTO::getStoreTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (unitTotalCount.compareTo(item.getUnitTotalCount()) < 0) {
                throw new BusinessValidateException("skuId:" + productSkuId + ",货位id:" + fromLocationId + ",生产日期:"
                        + DateUtil.format(productionDate, DateUtil.YYYYMMDD_HHMMSS) + "的产品货位库存不足");
            }
        });
    }

    /**
     * 库龄产品批次库存查询
     */
    public PageList<BatchInventoryDTO>
    pageListStockAgeProductInventory(StockAgeInventoryQueryDTO stockAgeInventoryQueryDTO) {
        PageList<BatchInventoryDTO> pageList = new PageList<>();
        StockAgeProductQueryDTO stockAgeProductQueryDTO =
                StockAgeInventoryConvert.stockAgeInventoryQueryDTO2StockAgeProductQueryDTO(stockAgeInventoryQueryDTO);
        PageList<StockAgeStrategyDTO> stockAgeStrategyList =
                stockAgeStrategyService.pageListStockAgeProductStrategy(stockAgeProductQueryDTO);

        List<StockAgeProductInventorySO> stockAgeProductInventorySOS =
                StockAgeInventoryConvert.stockAgeStrategyList2stockAgeProductQueryDTOS(stockAgeStrategyList.getDataList(),
                        stockAgeInventoryQueryDTO);
        LOGGER.info("库龄产品批次库存查询参数:{}", JSON.toJSONString(stockAgeProductInventorySOS));
        if (CollectionUtils.isNotEmpty(stockAgeProductInventorySOS)) {
            // 查询符合库龄策略的产品所有的批次库存
            List<Long> skuIds = stockAgeProductInventorySOS.stream().flatMap(so -> so.getSkuIds().stream())
                    .collect(Collectors.toList());
            List<BatchInventoryDTO> stockAgeProductInventory = batchInventoryProductStoreBatchMapper
                    .findStockAgeProductInventoryBySku(skuIds, stockAgeInventoryQueryDTO.getWarehouseId());
            if (CollectionUtils.isNotEmpty(stockAgeProductInventory)) {
                List<BatchInventoryDTO> signBatchInventoryDTOS = new ArrayList<>();
                List<BatchInventoryDTO> result = new ArrayList<>();
                Map<String, List<BatchInventoryDTO>> stockAgeProductInventoryMap = stockAgeProductInventory.stream()
                        .collect(Collectors.groupingBy(BatchInventoryDTO::getBatchAttributeInfoNo));

                // 同批次编号的去重合一
                stockAgeProductInventoryMap.forEach((batchAttributeInfoNo, batchInventoryList) -> {
                    BatchInventoryDTO batchInventoryDTO = batchInventoryList.get(0);
                    BigDecimal storeTotalCount = batchInventoryList.stream().map(BatchInventoryDTO::getStoreTotalCount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    batchInventoryDTO.setStoreTotalCount(storeTotalCount);

                    if (StringUtils.isEmpty(batchInventoryDTO.getOwnerName())) {
                        batchInventoryDTO.setOwnerName(ownerService.getDefaultOwnerName());
                    }

                    signBatchInventoryDTOS.add(batchInventoryDTO);
                });

                // 计算库龄，超期天数
                Long stockAgeRange =
                        stockAgeInventoryQueryDTO.getStockAge() == null ? 0 : stockAgeInventoryQueryDTO.getStockAge();
                Long overdueRange =
                        stockAgeInventoryQueryDTO.getOverdue() == null ? 0 : stockAgeInventoryQueryDTO.getOverdue();
                Long overdueSurplusRange = stockAgeInventoryQueryDTO.getOverdueSurplus() == null ? 0
                        : stockAgeInventoryQueryDTO.getOverdueSurplus();
                Byte stockAgeInventoryQueryType = stockAgeInventoryQueryDTO.getStockAgeInventoryQueryType();
                stockAgeProductInventorySOS.forEach(so -> {
                    Date currentDate = new Date();
                    List<Long> filterSkuIds = so.getSkuIds();
                    Long maxStockAge = so.getMaxStockAge();
                    Long minStockAge = so.getMinStockAge();

                    signBatchInventoryDTOS.stream()
                            .filter(batchInventory -> filterSkuIds.contains(batchInventory.getProductSkuId())
                                    && batchInventory.getBatchTime() != null)
                            .forEach(batchInventory -> {
                                Long stockAge = com.yijiupi.himalaya.supplychain.batchinventory.util.DateUtil
                                        .betweenDays(batchInventory.getBatchTime(), currentDate) + 1;
                                batchInventory.setStockAge(stockAge);

                                // 按查询条件过滤
                                boolean isProcessStoreAge = stockAge >= stockAgeRange
                                        && (overdueSurplusRange == 0 || maxStockAge - stockAge >= overdueSurplusRange)
                                        && (overdueRange == 0 || stockAge - maxStockAge >= overdueRange);
                                if (isProcessStoreAge) {

                                    if (StockAgeInventoryQueryType.全部.getType().equals(stockAgeInventoryQueryType)) {
                                        batchInventory.setOverdue(maxStockAge - stockAge);
                                        result.add(batchInventory);
                                    } else if (StockAgeInventoryQueryType.临期.getType().equals(stockAgeInventoryQueryType)
                                            && stockAge >= minStockAge && stockAge < maxStockAge) {
                                        batchInventory.setOverdue(maxStockAge - stockAge);
                                        result.add(batchInventory);
                                    } else if (StockAgeInventoryQueryType.超期.getType().equals(stockAgeInventoryQueryType)
                                            && stockAge >= maxStockAge) {
                                        batchInventory.setOverdue(stockAge - maxStockAge);
                                        result.add(batchInventory);
                                    }
                                }
                            });
                });
                pageList.setDataList(result);
                pageList.setPager(stockAgeStrategyList.getPager());
            }
        }
        return pageList;
    }

    /**
     * 查找生产日期
     *
     * @param productionDateQueryDTOS
     * @return
     */
    public List<ProductionDateDTO> findProductionDate(List<ProductionDateQueryDTO> productionDateQueryDTOS) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("生产日期查询参数:{}", JSON.toJSONString(productionDateQueryDTOS));
        }
        List<ProductionDateDTO> productionDateList = new ArrayList<>();
        Map<String, BigDecimal> saleInventoryMap = new HashMap<>(16);

        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 换接口 未处理
        Lists.partition(productionDateQueryDTOS, 100).forEach(dtosList -> {
            List<ProductionDateDTO> productionDateDTOS = batchInventoryProductStoreBatchMapper.findProductionDateList(dtosList);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.info("生产日期查询 productionDateDTOS结果:{}", JSON.toJSONString(productionDateDTOS));
            }

            if (CollectionUtils.isNotEmpty(productionDateDTOS)) {
                productionDateList.addAll(productionDateDTOS);

                Map<Integer, List<ProductionDateDTO>> queryMap =
                        productionDateDTOS.stream().collect(Collectors.groupingBy(ProductionDateDTO::getWarehouseId));

                List<OrderCenterSaleInventoryQueryDTO> queryDTO = SaleInventoryQueryConvertor.convertSaleInventoryQueryRootDTO(queryMap);
                List<OrderCenterSaleInventoryQueryResultDTO> omsInventoryInfos = batchInventoryOrderCenterBL.findSaleInventoryList(queryDTO);

                if (CollectionUtils.isNotEmpty(omsInventoryInfos)) {
                    omsInventoryInfos.forEach(o -> {
                        String saleInventoryKey = String.format("%s-%s", o.getWarehouseId(), o.getInternalKey());
                        if (saleInventoryMap.containsKey(saleInventoryKey)) {
                            saleInventoryMap.put(saleInventoryKey,
                                    o.getSaleInventoryCount().add(saleInventoryMap.get(saleInventoryKey)));
                        } else {
                            saleInventoryMap.put(saleInventoryKey, o.getSaleInventoryCount());
                        }
                    });
                }
            }
        });

        // 获取生产日期时需比对销售库存
        List<ProductionDateDTO> result = getProductionDateReult(productionDateQueryDTOS, productionDateList, saleInventoryMap);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.info("生产日期查询结果:{}", JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询库龄产品相关批次信息
     *
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findStoreAgeRefBatchInventoryList(BatchInventoryQueryDTO dto) {
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findStoreAgeRefBatchInventoryList(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查询所有负库存仓库
     *
     * @return
     */
    public List<BatchInventoryNegativeDTO> listWarehouseByInventoryNegative() {
        List<BatchInventoryNegativeDTO> nagativeInventoryList =
                batchInventoryProductStoreBatchMapper.listWarehouseByInventoryNegative();
        if (CollectionUtils.isEmpty(nagativeInventoryList)) {
            return null;
        }

        // 找出开启货位库存的仓库
        WarehouseConfigQueryDTO configQueryDTO = new WarehouseConfigQueryDTO();
        configQueryDTO.setPageSize(Integer.MAX_VALUE);
        configQueryDTO.setCurrentPage(1);
        PageList<WarehouseConfigDTO> configDTOList = warehouseConfigService.pageList(configQueryDTO);
        if (configDTOList == null || CollectionUtils.isEmpty(configDTOList.getDataList())) {
            return null;
        }

        List<BatchInventoryNegativeDTO> resultList = new ArrayList<>();
        nagativeInventoryList.forEach(nagativeInventoryDTO -> {
            if (configDTOList.getDataList().stream()
                    .anyMatch(p -> Objects.equals(p.getWarehouse_Id(), nagativeInventoryDTO.getWarehouseId())
                            && p.getIsOpenLocationStock())) {
                resultList.add(nagativeInventoryDTO);
            }
        });

        LOGGER.info("查询所有开启货位库存的负库存仓库:{}", JSON.toJSONString(resultList));
        return resultList;
    }

    /**
     * 根据仓库及产品规格信息查询批次生产日期
     */
    public List<BatchProductionDateDTO>
    findProductionDateFromStoreBatch(BatchProductionDateQueryDTO productionDateQueryDTO) {
        LOGGER.info("查询产品批次生产日期参数:{}", JSON.toJSONString(productionDateQueryDTO));
        AssertUtils.notNull(productionDateQueryDTO, "查询产品批次生产日期参数不能为空！");
        AssertUtils.notNull(productionDateQueryDTO.getWarehouseId(), "查询产品批次生产日期仓库ID不能为空！");
        AssertUtils.notEmpty(productionDateQueryDTO.getSpecList(), "查询产品批次生产日期规格信息不能为空！");
        List<BatchProductionDateDTO> productionDateDTOList =
                batchInventoryProductStoreBatchMapper.findProductionDateFromStoreBatch(productionDateQueryDTO);
        return productionDateDTOList;
    }

    /**
     * 根据货位、批次库存信息查询产品SKU基础信息
     */
    public PageList<BatchInventoryDTO> findProductBaseInfoFromBatchInventory(BatchInventoryQueryDTO dto) {
        LOGGER.info("根据货位、批次信息查询产品SKU基础信息参数 :{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto, "查询参数不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库ID不能为空");
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findProductBaseInfoFromBatchInventory(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());
        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    public List<ProductionDatePriceDTO> findProductionDatePriceBySkuIds(Integer warehouseId, List<Long> skuIds) {
        return batchInventoryProductStoreBatchMapper.findProductionDatePriceBySkuIds(warehouseId, skuIds);
    }

    /**
     * 查找货位库存
     */
    public PageList<ProductStoreBatchDTO> listProductStoreBatch(BatchInventoryQueryDTO dto) {
        PageResult<ProductStoreBatchPO> pageResult =
                batchInventoryProductStoreBatchMapper.listProductStoreBatch(dto, dto.getPageNum(), dto.getPageSize());

        PageList<ProductStoreBatchPO> productStoreBatchPOPageList = pageResult.toPageList();
        List<ProductStoreBatchDTO> productStoreBatchDTOS =
                BatchInventoryConvert.productStoreBatchPOToProductStoreBatchDTO(productStoreBatchPOPageList.getDataList());

        PageList<ProductStoreBatchDTO> ProductStoreBatchDTOList = new PageList<>();
        ProductStoreBatchDTOList.setDataList(productStoreBatchDTOS);
        ProductStoreBatchDTOList.setPager(productStoreBatchPOPageList.getPager());
        return ProductStoreBatchDTOList;
    }

    /**
     * 查找有货位库存的skuId列表
     */
    public List<Long> listSkuIdByBatchInventory(Integer warehouseId) {
        ReportBatchLocationInfoQueryDTO queryDTO = new ReportBatchLocationInfoQueryDTO();
        queryDTO.setWarehouseId(warehouseId);

        // List<BatchLocationInfoDTO> storeList = productStoreBatchMapper.listProductStoreBatchBySku(queryDTO);
        List<BatchLocationInfoDTO> storeList =
                BatchInventoryConvert.reportToBatchLocationInfoDTO(this.listAllStoreBatchBySkuReport(queryDTO));
        return storeList.stream().map(BatchLocationInfoDTO::getProductSkuId).distinct().collect(Collectors.toList());
    }

    /**
     * 根据sku查货位库存
     */
    public Map<Long, List<BatchLocationInfoDTO>> listProductStoreBatchBySku(List<BatchLocationInfoQueryDTO> queryDTOS) {
        Map<String, List<BatchLocationInfoQueryDTO>> skuIdGroup =
                queryDTOS.stream().collect(Collectors.groupingBy(elem -> String.format("%s_%s_%s_%s", elem.getWarehouseId(),
                        elem.getLocationId(), elem.getSource(), elem.getChannel())));

        boolean isExistProductionDate = queryDTOS.stream().findFirst().get().getExistProductionDate();
        List<BatchLocationInfoDTO> queryResultList = new ArrayList<>();
        skuIdGroup.forEach((key, groupList) -> {
            ReportBatchLocationInfoQueryDTO newQueryDto = new ReportBatchLocationInfoQueryDTO();
            List<Long> skuIdListElem =
                    groupList.stream().map(BatchLocationInfoQueryDTO::getProductSkuId).collect(Collectors.toList());
            newQueryDto.setProductSkuIdList(skuIdListElem);

            String[] keySplit = key.split("_");
            newQueryDto.setWarehouseId(Integer.valueOf(keySplit[0]));
            newQueryDto.setLocationId(Objects.equals(keySplit[1], "null") ? null : Long.valueOf(keySplit[1]));
            newQueryDto.setSource(Integer.valueOf(keySplit[2]));
            newQueryDto.setChannel(Integer.valueOf(keySplit[3]));
            newQueryDto.setExistProductionDate(isExistProductionDate);

            // queryResultList.addAll(productStoreBatchMapper.listProductStoreBatchBySku(newQueryDto));
            queryResultList.addAll(
                    BatchInventoryConvert.reportToBatchLocationInfoDTO(this.listAllStoreBatchBySkuReport(newQueryDto)));
        });

        return queryResultList.stream().collect(Collectors.groupingBy(BatchLocationInfoDTO::getProductSkuId));
    }

    public List<Long> findSkuIdByBatchInventory(BatchLocationInfoQueryDTO queryDTO) {
        // List<BatchLocationInfoDTO> storeList = productStoreBatchMapper.listProductStoreBatchBySku(queryDTO);
        ReportBatchLocationInfoQueryDTO reportQueryDTO = new ReportBatchLocationInfoQueryDTO();
        BeanUtils.copyProperties(queryDTO, reportQueryDTO);
        List<BatchLocationInfoDTO> storeList =
                BatchInventoryConvert.reportToBatchLocationInfoDTO(this.listAllStoreBatchBySkuReport(reportQueryDTO));
        return storeList.stream().map(BatchLocationInfoDTO::getProductSkuId).distinct().collect(Collectors.toList());
    }

    /**
     * 查询有生产日期的货位库存
     */
    public PageList<BatchLocationInfoDTO> findProductDateInventory(BatchLocationInfoQueryDTO queryDTO) {
        // PageResult<BatchLocationInfoDTO> storeList =
        // productStoreBatchMapper.listProductStoreBatchBySku(queryDTO,queryDTO.getPageNum(),queryDTO.getPageSize());
        //
        // PageList<BatchLocationInfoDTO> ProductStoreBatchDTOList = new PageList<>();
        // ProductStoreBatchDTOList.setDataList(storeList.getResult());
        // ProductStoreBatchDTOList.setPager(storeList.getPager());
        // return ProductStoreBatchDTOList;

        ReportBatchLocationInfoQueryDTO reportQueryDTO = new ReportBatchLocationInfoQueryDTO();
        BeanUtils.copyProperties(queryDTO, reportQueryDTO);
        PageList<ReportBatchLocationInfoDTO> reportPageList =
                iWarehouseInventoryReportQueryService.pageListProductStoreBatchBySku(reportQueryDTO);
        // LOGGER.info("sqlReport优化part，结果：{}",JSON.toJSONString(reportPageList));
        PageList<BatchLocationInfoDTO> storeResult = new PageList<>();
        storeResult.setDataList(BatchInventoryConvert.reportToBatchLocationInfoDTO(reportPageList.getDataList()));
        storeResult.setPager(reportPageList.getPager());
        return storeResult;
    }

    /**
     * 查询有批次库存、有生产日期的批次库存（所有）
     */
    public List<ReportBatchLocationInfoDTO>
    listAllStoreBatchBySkuReport(ReportBatchLocationInfoQueryDTO reportQueryDTO) {
        int pageSize = 5000;

        List<ReportBatchLocationInfoDTO> resultList = new ArrayList<>();
        Integer pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {

            reportQueryDTO.setPageNum(pageNum);
            reportQueryDTO.setPageSize(pageSize);
            PageList<ReportBatchLocationInfoDTO> pageList =
                    iWarehouseInventoryReportQueryService.pageListProductStoreBatchBySku(reportQueryDTO);
            if (CollectionUtils.isNotEmpty(pageList.getDataList())) {
                if (pageNum == 1) {
                    pageCount = pageList.getPager().getTotalPage();
                }
                resultList.addAll(pageList.getDataList());
                pageList.getDataList().clear();
            }
        }
        // LOGGER.info("sqlReport优化all，结果：{}",JSON.toJSONString(resultList));
        return resultList;
    }

    /**
     * 查询批次库存信息
     *
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findBatchInventoryInfo(BatchInventoryQueryDTO dto) {
        LOGGER.info("查询批次库存信息 findBatchInventoryInfo,入参: {}", JSON.toJSONString(dto));
        // 最小、最大库龄转换
        Date nowDate = new Date();
        dto.setStartStockAgeTime(
                dto.getStartStockAge() != null ? com.yijiupi.himalaya.supplychain.batchinventory.util.DateUtil.add(nowDate,
                        Calendar.DAY_OF_MONTH, -dto.getStartStockAge().intValue()) : null);
        dto.setEndStockAgeTime(
                dto.getEndStockAge() != null ? com.yijiupi.himalaya.supplychain.batchinventory.util.DateUtil.add(nowDate,
                        Calendar.DAY_OF_MONTH, -dto.getEndStockAge().intValue()) : null);
        PageResult<BatchInventoryPO> pageResult =
                batchInventoryProductStoreBatchMapper.findBatchInventoryInfo(dto, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        LOGGER.info("batchInventoryPOPageList,入参: {}", JSON.toJSONString(batchInventoryPOPageList));
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // 查询仓库名称
        Warehouse warehouse = warehouseQueryService.findWarehouseById(dto.getWarehouseId());
        AssertUtils.notNull(warehouse, "仓库找不到 : " + dto.getWarehouseId());

        // 将小数量转换成大单位数量/小单位数量,设置仓库名称.
        batchInventoryDTOS.forEach(n -> {
            n.setWarehouseName(warehouse.getName());
            BigDecimal packageQuantity = n.getPackageQuantity();
            BigDecimal[] storeTotalCountReminder = n.getStoreTotalCount().divideAndRemainder(packageQuantity);
            n.setStoreCountMax(storeTotalCountReminder[0]);
            n.setStoreCountMin(storeTotalCountReminder[1]);
            if (n.getOwnerType() != null) {
                int ownerType = n.getOwnerType();
                n.setStoreOwnerTypeName(OwnerTypeConst.getEnmuName(ownerType));
            } else {
                n.setStoreOwnerTypeName(OwnerTypeConst.getEnmuName(0));
            }
            // 货区/货位类型名称
            if (Objects.equals(n.getLocationCategory(), CategoryEnum.CARGO_LOCATION.getValue())) {
                n.setLocationSubcategoryName(LocationEnum.getEnumStr(n.getLocationSubcategory()));
            } else if (Objects.equals(n.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue())) {
                n.setLocationSubcategoryName(LocationAreaEnum.getEnumStr(n.getLocationSubcategory()));
            }
        });

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查询符合条件的存储位
     *
     * @param dto
     * @return
     */
    public List<OrderItemDTO> findPackageOutStockLocation(BatchInventoryPackageLocationDTO dto) {
        AssertUtils.notEmpty(dto.getOrderItemDTOList(), "入参集合不能为空");
        for (OrderItemDTO orderItemDTO : dto.getOrderItemDTOList()) {
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(orderItemDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(orderItemDTO.getChannel(), "产品渠道不能为空");
            AssertUtils.notNull(orderItemDTO.getSource(), "产品来源不能为空");
            AssertUtils.notNull(orderItemDTO.getUnitTotalCount(), "下架数量不能为空");
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "规格系数不能为空");

        }

        List<OrderItemDTO> orderItemDTOList = dto.getOrderItemDTOList();

        // 查询批次库存,货位信息
        Map<Long, List<BatchLocationInfoDTO>> locationInfoBySkuIdMap =
                getLocationInfoBySkuIdS(orderItemDTOList, LocationAreaEnum.存储区.getType());

        // 最后返回 k(itemId)->v(List<OrderItemDTO>)
        List<OrderItemDTO> returnOrderItemDTOS = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            Long productSkuId = orderItemDTO.getProductSkuId();
            BigDecimal specQuantity = orderItemDTO.getSpecQuantity();
            // 批次库存
            List<BatchLocationInfoDTO> batchLocationInfoDTOS = locationInfoBySkuIdMap.get(productSkuId);
            if (CollectionUtils.isEmpty(batchLocationInfoDTOS)) {
                LOGGER.info("该产品找不到批次库存记录 productskuId :{},仓库id:{},渠道:{},产品来源:{}", productSkuId,
                        orderItemDTO.getWarehouseId(), orderItemDTO.getChannel(), orderItemDTO.getSource());
                continue;
            }
            List<BatchLocationInfoDTO> lstTmpBatchLocations = batchLocationInfoDTOS.stream()
                    .filter(p -> p.getChannel() == null || orderItemDTO.getChannel() == null
                            || p.getChannel().equals(orderItemDTO.getChannel()))
                    .filter(m -> Objects.nonNull(m.getSubcategory())
                            && LocationEnum.分拣位.getType() == m.getSubcategory().intValue())
                    .collect(Collectors.toList());

            LOGGER.info("查找存储位信息,批次库存:{},相同渠道库存:{}", JSON.toJSONString(batchLocationInfoDTOS),
                    JSON.toJSONString(lstTmpBatchLocations));

            if (CollectionUtils.isEmpty(lstTmpBatchLocations)) {
                LOGGER.info("该产品找不到相同渠道的批次库存记录 productskuId :{},仓库id:{},渠道:{},产品来源:{}", productSkuId,
                        orderItemDTO.getWarehouseId(), orderItemDTO.getChannel(), orderItemDTO.getSource());
                continue;
            }

            // 按二级货主去匹配货位
            if (orderItemDTO.getSecOwnerId() != null) {
                lstTmpBatchLocations = lstTmpBatchLocations.stream()
                        .filter(p -> Objects.equals(p.getOwnerId(), orderItemDTO.getOwnerId())
                                && Objects.equals(p.getSecOwnerId(), orderItemDTO.getSecOwnerId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(lstTmpBatchLocations)) {
                    LOGGER.info("[存储位]该产品找不到相同二级货主的批次库存记录 productskuId :{},仓库id:{},货主id:{},二级货主id:{}", productSkuId,
                            orderItemDTO.getWarehouseId(), orderItemDTO.getOwnerId(), orderItemDTO.getSecOwnerId());
                    break;
                }
            }

            BatchLocationInfoDTO batchLocationInfoDTO = filterLocation(lstTmpBatchLocations);

            orderItemDTO.setAreaId(batchLocationInfoDTO.getAreaId());
            orderItemDTO.setAreaName(batchLocationInfoDTO.getAreaName());
            orderItemDTO.setLocationId(batchLocationInfoDTO.getLocationId());
            orderItemDTO.setLocationName(batchLocationInfoDTO.getLocationName());
            orderItemDTO.setSubCategory(batchLocationInfoDTO.getSubcategory());
        }
        return orderItemDTOList;
    }

    private BatchLocationInfoDTO filterLocation(List<BatchLocationInfoDTO> lstTmpBatchLocations) {
        List<BatchLocationInfoDTO> nonNullSequenceLocationList =
                lstTmpBatchLocations.stream().filter(m -> Objects.nonNull(m.getSequence())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonNullSequenceLocationList)) {
            return lstTmpBatchLocations.get(0);
        }
        nonNullSequenceLocationList.sort(Comparator.comparingInt(BatchLocationInfoDTO::getSequence));

        return nonNullSequenceLocationList.get(0);
    }

    /**
     * 根据仓库版本查看货位库存详情
     *
     * @param dto
     * @return
     */
    public List<BatchInventoryDTO> findBatchInventoryInfoList(BatchInventoryQueryDTO dto) {
        LOGGER.info("货位库存查询入参>>{}", JSON.toJSONString(dto));
        WarehouseConfigDTO configByWareHouseId = warehouseConfigService.getConfigByWareHouseId(dto.getWarehouseId());
        // 是否为4.0版本 不是则为2.5
        Boolean isVersionPro =
                configByWareHouseId.getIsOpenLocationStock() && SCM_VERSION_3.equals(configByWareHouseId.getScmVersion());
        // 不分页查询
        dto.setPageSize(1000);
        if (isVersionPro || configByWareHouseId.getIsOpenLocationStock()) {
            List<BatchInventoryPO> batchInventoryPOS = batchInventoryProductStoreBatchMapper
                    .findBatchInventoryListNew(dto, dto.getPageNum(), dto.getPageSize()).toPageList().getDataList();
            List<BatchInventoryDTO> batchInventoryDTOS =
                    BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOS);
            return batchInventoryDTOS;
        }
        // 2.5关联货位查询库存
        List<BatchInventoryPO> dataList = batchInventoryProductStoreBatchMapper
                .findProductLocationBatchInventoryList(dto, dto.getPageNum(), dto.getPageSize()).toPageList().getDataList();
        List<BatchInventoryDTO> batchInventoryDTOS =
                BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(dataList);
        return batchInventoryDTOS;
    }

    /**
     * 根据storeIds查询批次库存最老生产日期
     *
     * @param productStoreIds
     * @return Map<String, Date>
     */
    public Map<String, Date> findProductionDateByProductStoreIds(List<String> productStoreIds) {
        LOGGER.info("根据storeIds查询批次库存最老生产日期,入参>>{}", JSON.toJSONString(productStoreIds));
        if (CollectionUtils.isEmpty(productStoreIds)) {
            return Maps.newHashMap();
        }

        List<ProductStoreBatchPO> productStoreBatchPOList = new ArrayList<>();
        Lists.partition(productStoreIds, 500).forEach(productStoreIdList -> {
            List<ProductStoreBatchPO> productStoreBatchPOS =
                    batchInventoryProductStoreBatchMapper.findProductionDateByProductStoreIds(productStoreIdList,
                            Arrays.asList(LocationEnum.残次品位.getType(), LocationAreaEnum.残次品区.getType()));
            if (!CollectionUtils.isEmpty(productStoreBatchPOS)) {
                productStoreBatchPOList.addAll(productStoreBatchPOS);
            }
        });

        if (CollectionUtils.isEmpty(productStoreBatchPOList)) {
            return Maps.newHashMap();
        }

        Map<String, Date> dateMap =
                productStoreBatchPOList.stream().filter(p -> StringUtils.isNotEmpty(p.getProductStoreId()))
                        .collect(Collectors.toMap(p -> p.getProductStoreId(), p -> p.getProductionDate(), (v1, v2) -> v1));
        LOGGER.info("根据storeIds查询批次库存最老生产日期,结果>>{}", JSON.toJSONString(productStoreIds));
        return dateMap;
    }

    /**
     * 查询一条为0的批次库存
     *
     * @param queryDTO
     * @return
     */
    public BatchInventoryDTO queryZeroBatchInventory(ZeroBatchInventoryQueryDTO queryDTO) {
        BatchInventoryQueryDTO dto = new BatchInventoryQueryDTO();
        dto.setLocationIds(Collections.singletonList(queryDTO.getLocationId()));
        dto.setSkuIds(Collections.singletonList(queryDTO.getSkuId()));
        dto.setWarehouseId(queryDTO.getWarehouseId());
        BatchInventoryPO batchInventoryPO = batchInventoryProductStoreBatchMapper.queryZeroBatchInventory(dto);

        return BatchInventoryConvert.batchInventoryPO2BatchInventoryDTO(batchInventoryPO);
    }

    /**
     * 根据计算的销售库存获取生产日期
     *
     * @param productionDateQueryDTOS
     * @return
     */
    public List<ProductionDateDTO>
    findProductionDateByCalculation(List<ProductionDateQueryDTO> productionDateQueryDTOS) {
        LOGGER.info("根据计算的销售库存获取生产日期 参数:{}", JSON.toJSONString(productionDateQueryDTOS));
        List<ProductionDateDTO> productionDateList = new ArrayList<>();
        Map<String, BigDecimal> saleInventoryMap = new HashMap<>(16);
        // 查询存在生产日期的批次库存，并根据仓库数据计算销售库存
        processProductionDate(productionDateQueryDTOS, productionDateList, saleInventoryMap);
        // 获取生产日期时需比对销售库存
        List<ProductionDateDTO> result =
                getProductionDateReult(productionDateQueryDTOS, productionDateList, saleInventoryMap);
        LOGGER.info("根据计算的销售库存获取生产日期 结果:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 查询存在生产日期的批次库存，并根据仓库数据计算销售库存
     *
     * @param productionDateQueryDTOS
     * @return
     */
    private void processProductionDate(List<ProductionDateQueryDTO> productionDateQueryDTOS,
                                       List<ProductionDateDTO> productionDateList, Map<String, BigDecimal> saleInventoryMap) {
        Map<Integer, List<ProductionDateQueryDTO>> warehouseQueryMap =
                productionDateQueryDTOS.stream().collect(Collectors.groupingBy(ProductionDateQueryDTO::getWarehouseId));

        // 按仓库id分组
        warehouseQueryMap.forEach((wareohuseId, dtos) -> {
            // 一次查100条数据
            Lists.partition(dtos, 100).forEach(dtosList -> {
                List<ProductionDateDTO> productionDateDTOS = batchInventoryProductStoreBatchMapper.findProductionDateList(dtosList);
                LOGGER.info("根据计算的销售库存获取生产日期 productionDateDTOS结果:{}", JSON.toJSONString(productionDateDTOS));
                if (CollectionUtils.isEmpty(productionDateDTOS)) {
                    return;
                }
                productionDateList.addAll(productionDateDTOS);

                // 通过仓库id+规格id+货主id+二级货主id 获取销售库存
                WarehouseProductStoreQueryDTO saleQueryDTO =
                        WarehouseProductStoreConvertor.convertWarehouseProductStoreQueryDTO(productionDateDTOS);
                List<WarehouseStoreDTO> warehouseStoreDTOList =
                        iWarehouseInventoryCheckService.calculationSaleInventory(saleQueryDTO);
                LOGGER.info("根据计算的销售库存获取生产日期 warehouseStoreDTOList结果:{}", JSON.toJSONString(warehouseStoreDTOList));
                if (CollectionUtils.isEmpty(warehouseStoreDTOList)) {
                    return;
                }

                warehouseStoreDTOList.forEach(w -> {
                    String saleInventoryKey = String.format("%s-%s-%s-%s", w.getWarehouseId(), w.getProductSpecId(),
                            w.getOwnerId(), w.getSecOwnerId());
                    if (saleInventoryMap.containsKey(saleInventoryKey)) {
                        saleInventoryMap.put(saleInventoryKey,
                                w.getSaleStoreTotalCount().add(saleInventoryMap.get(saleInventoryKey)));
                    } else {
                        saleInventoryMap.put(saleInventoryKey, w.getSaleStoreTotalCount());
                    }
                });
            });
        });
    }

    private List<ProductionDateDTO> getProductionDateReult(List<ProductionDateQueryDTO> productionDateQueryDTOS,
                                                           List<ProductionDateDTO> productionDateList, Map<String, BigDecimal> saleInventoryMap) {
        // 时间为0001-01-01 00:00:00的需要当作null处理
        if (CollectionUtils.isNotEmpty(productionDateList)) {
            productionDateList.forEach(productionDate -> {
                // java.util.Date类中的getYear()，返回值需要加上1900才是真正的年份
                if (productionDate.getProductionDate() != null && productionDate.getProductionDate().getYear() <= 100) {
                    productionDate.setProductionDate(null);
                }
            });
        }

        List<ProductionDateDTO> result = new ArrayList<>();
        productionDateQueryDTOS.forEach(product -> {
            Map<String,
                    List<ProductionDateDTO>> productionDateMap = productionDateList.stream()
                    .filter(productionDate -> Objects.equals(productionDate.getProductSpecificationId(),
                            product.getProductSpecificationId())
                            && Objects.equals(productionDate.getOwnerId(), product.getOwnerId())
                            && Objects.equals(productionDate.getWarehouseId(), product.getWarehouseId()))
                    .collect(Collectors.groupingBy(ProductionDateDTO::getInventorySign));

            for (Map.Entry<String, List<ProductionDateDTO>> entry : productionDateMap.entrySet()) {

                BigDecimal saleCount = saleInventoryMap.get(entry.getKey()) == null ? BigDecimal.ZERO
                        : saleInventoryMap.get(entry.getKey());

//                LOGGER.info(String.format("productionDateMap:%s,saleCount:%s,key:%s,saleMap:%s",
//                    JSON.toJSONString(productionDateMap), saleCount, entry.getKey(),
//                    JSON.toJSONString(saleInventoryMap)));

                if (saleCount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // 2022-04-26 根据批次库存及销售库存，计算生产日期,排除残次品数量
                List<ProductionDateDTO> productionDateDTOS = entry.getValue().stream()
                        .filter(batch -> !Objects.equals(batch.getLocationSubcategory(), LocationAreaEnum.残次品区.getType())
                                && !Objects.equals(batch.getLocationSubcategory(), LocationEnum.残次品位.getType()))
                        .collect(Collectors.toList());

                // 生产日期倒序
                productionDateDTOS.sort((o1, o2) -> {
                    if (o1.getProductionDate() == null && o2.getProductionDate() != null) {
                        return -1;
                    } else if (o1.getProductionDate() != null && o2.getProductionDate() == null) {
                        return 1;
                    } else if (o1.getProductionDate() != null && o2.getProductionDate() != null) {
                        return o2.getProductionDate().compareTo(o1.getProductionDate());
                    } else {
                        return 1;
                    }
                });

                // LOGGER.info("生产日期查询 productionDateDTOS排序结果:{}", JSON.toJSONString(productionDateDTOS));
                ProductionDateDTO productionDateDTO = new ProductionDateDTO();
                productionDateDTO.setProductSpecificationId(product.getProductSpecificationId());
                productionDateDTO.setWarehouseId(product.getWarehouseId());
                productionDateDTO.setSaleCount(saleCount);

                // 倒序生产日期与销售库存比较，取最后一条
                for (ProductionDateDTO productionDate : productionDateDTOS) {
                    BigDecimal batchStoreCount = productionDate.getBatchStoreCount();
                    productionDateDTO.setOwnerId(productionDate.getOwnerId());
                    productionDateDTO.setSecOwnerId(productionDate.getSecOwnerId());
                    productionDateDTO.setProductionDate(productionDate.getProductionDate());
                    // FIXME 下面代码没set，暂时没用
                    if (batchStoreCount.compareTo(saleCount) >= 0) {
                        break;
                    } else {
                        saleCount = saleCount.subtract(batchStoreCount);
                    }
                }

                result.add(productionDateDTO);
            }
        });

        return result;
    }

    /**
     * 批次库存查询，返回促销id
     */
    public List<BatchInventoryDTO> listStoreBatchWithPromotion(BatchInventoryQueryDTO dto) {
        AssertUtils.notNull(dto, "查询不能为空");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库id不能为空");
        return batchInventoryProductStoreBatchMapper.listStoreBatchWithPromotion(dto);
    }

    /**
     * 查找有货位库存的skuId列表
     */
    public List<Long> listSkuIdByAllBatchInventory(Integer warehouseId) {
        ReportBatchLocationInfoQueryDTO queryDTO = new ReportBatchLocationInfoQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setExistProductionDate(false);
        List<BatchLocationInfoDTO> storeList =
                BatchInventoryConvert.reportToBatchLocationInfoDTO(this.listAllStoreBatchBySkuReport(queryDTO));
        return storeList.stream().map(BatchLocationInfoDTO::getProductSkuId).distinct().collect(Collectors.toList());
    }
}

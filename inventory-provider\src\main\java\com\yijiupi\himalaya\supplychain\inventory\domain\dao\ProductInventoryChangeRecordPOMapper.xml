<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryChangeRecordPOMapper">
    <resultMap type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO"
               id="BaseResultMap">
        <id column="Id" property="id"></id>
        <result column="ProductStore_Id" property="productStoreId"/>
        <result column="City_Id" property="cityId"/>
        <result column="OrderType" property="orderType"/>
        <result column="Order_Id" property="orderId"/>
        <result column="OrderNo" property="orderNo"/>
        <result column="JiupiEventType" property="jiupiEventType"/>
        <result column="ERPEventType" property="erpEventType"/>
        <result column="Count_MaxUnit" property="countMaxUnit"/>
        <result column="Count_MinUnit" property="countMinUnit"/>
        <result column="SourceTotalCount" property="sourceTotalCount"/>
        <result column="TotalCount" property="totalCount"/>
        <result column="Description" property="description"/>
        <result column="CreateTime" property="createTime"/>
        <result column="CreateUser" property="createUser"/>
        <result column="StoreType" property="storeType"/>
    </resultMap>

    <sql id="Base_Column_list">
        Id,ProductStore_Id,City_Id,OrderType,Order_Id,OrderNo,JiupiEventType,ERPEventType,Count_MaxUnit,Count_MinUnit,SourceTotalCount,TotalCount,Description,CreateTime,CreateUser,StoreType
    </sql>

    <insert id="insertProductInventoryChangeRecordBatch" parameterType="java.util.List">
        insert into productstorechangerecord
        (id,ProductStore_Id,City_Id,OrderType,Order_Id,OrderNo,JiupiEventType,ERPEventType,Count_MaxUnit,Count_MinUnit,SourceTotalCount,
        TotalCount,Description,CreateTime,CreateUser,StoreType,source)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.productStoreId},#{item.cityId},#{item.orderType},#{item.orderId},#{item.orderNo},#{item.jiupiEventType},#{item.erpEventType},#{item.countMaxUnit},#{item.countMinUnit},#{item.sourceTotalCount},
            #{item.totalCount},#{item.description},#{item.createTimeStr,jdbcType=TIMESTAMP},#{item.createUser},#{item.storeType},#{item.systemSource})
        </foreach>
    </insert>

    <select id="findProductInventoryChangeRecordPOList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_list"/>
        from productstorechangerecord
        where ProductStore_Id = #{dto.storeId,jdbcType=VARCHAR}
        <if test="dto.recordType!=null">
            AND OrderType=#{dto.recordType,jdbcType=INTEGER}
        </if>
        <if test="dto.timeS!=null">
            AND CreateTime    <![CDATA[ >= ]]> concat(#{dto.timeS,jdbcType=VARCHAR},' 00:00:00')
        </if>
        <if test="dto.timeE!=null">
            AND CreateTime      <![CDATA[ <= ]]>concat(#{dto.timeE,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="dto.orderNO!=null">
            AND OrderNo like concat('%',#{dto.orderNO,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.storeType != null">
            AND StoreType = #{dto.storeType}
        </if>
        ORDER BY CreateTime DESC,SourceTotalCount ASC
    </select>

    <select id="findProductInventoryChangeRecordPOListBySkuId" resultMap="BaseResultMap">
        select
        psc.Id,psc.ProductStore_Id,psc.City_Id,psc.OrderType,psc.Order_Id,psc.OrderNo,psc.JiupiEventType,psc.ERPEventType,psc.Count_MaxUnit,psc.Count_MinUnit,psc.SourceTotalCount,psc.TotalCount,psc.Description,psc.CreateTime,psc.CreateUser,psc.StoreType
        from productstorechangerecord psc
        inner join productstore ps on psc.ProductStore_Id = ps.Id
        inner join productsku sku ON ps.City_Id = sku.City_Id
        AND ps.ProductSpecification_Id = sku.ProductSpecification_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null and ps.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        where sku.ProductSku_Id=#{dto.productSkuId,jdbcType=BIGINT}
        AND ps.Warehouse_Id=#{dto.warehouseId,jdbcType=INTEGER}
        ORDER BY CreateTime DESC,SourceTotalCount ASC
    </select>

    <select id="listProductStoreChangeRecordByOrder"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO">
        select
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        psku.ProductSku_Id as productSkuId,
        psku.Name as productName,
        psku.specificationName as specName,
        psku.packageQuantity as specQuantity,
        psku.packageName as packageName,
        psku.unitName as unitName,
        IFNULL(own.Id, psku.Company_Id) AS ownerId,
        IFNULL(own.OwnerName, psku.OwnerName) AS ownerName,
        ps.SecOwner_Id AS secOwnerId,
        secOwn.OwnerName AS secOwnerName,
        ps.ProductSpecification_Id as specId,
        ps.Id as storeId,
        ps.OwnerType as ownerType,
        psr.Id as storeRecordId,
        psr.OrderType as orderType,
        psr.JiupiEventType as jiupiEventType,
        psr.ERPEventType as erpEventType,
        psr.Order_Id as orderId,
        psr.OrderNo as orderNo,
        psr.Count_MaxUnit as countMaxUnit,
        psr.Count_MinUnit as countMinUnit,
        psr.TotalCount as totalCount,
        psr.SourceTotalCount as sourceTotalCount,
        psr.Description as des,
        psr.CreateTime as createTime,
        psr.CreateUser as createUser
        from productstore ps
        inner join productstorechangerecord psr on ps.id = psr.ProductStore_Id
        left join productsku psku on psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join owner own on ps.Owner_Id is not null and ps.Owner_Id = own.Id
        left join owner secOwn on ps.SecOwner_Id is not null and ps.SecOwner_Id = secOwn.Id
        <where>
            <if test="cityId != null">
                ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="orderId !=null and orderId !=''">
                and psr.Order_Id = #{orderId,jdbcType=VARCHAR}
            </if>
            <if test="orderNo !=null and orderNo !=''">
                and psr.OrderNo = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderNoList != null and orderNoList.size() != 0">
                and psr.OrderNo in
                <foreach collection="orderNoList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productName !=null and productName !=''">
                and psku.Name like concat ('%', #{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="jiupiEventTypeList != null and jiupiEventTypeList.size() != 0">
                and psr.JiupiEventType in
                <foreach collection="jiupiEventTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by psku.ProductSku_Id,psr.CreateTime, psr.Id
    </select>

    <select id="listProductStoreChangeRecordGroupByOrder"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO">
        select pc.OrderNo as orderNo,ps.ProductSpecification_Id as specId,ps.Owner_Id AS ownerId,ps.SecOwner_Id AS
        secOwnerId,sum(pc.TotalCount) as totalCount,min(pc.createtime) as createTime
        from productstore ps
        inner join productstorechangerecord pc on pc.ProductStore_Id = ps.Id
        <where>
            ps.warehouse_id = #{warehouseId,jdbcType=INTEGER}
            and pc.OrderNo in
            <foreach collection="orderNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
        GROUP BY pc.OrderNo,ps.ProductSpecification_Id,ps.Owner_Id,ps.SecOwner_Id
    </select>

    <update id="updateProductInventoryChangeRecord"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO">
        update productstorechangerecord
        set
        OrderType = #{orderType,jdbcType=TINYINT},
        OrderNo = #{orderNo,jdbcType=VARCHAR},
        JiupiEventType = #{jiupiEventType,jdbcType=INTEGER},
        ERPEventType = #{erpEventType,jdbcType=INTEGER},
        CreateUser = #{createUser,jdbcType=VARCHAR},
        Description = #{description,jdbcType=VARCHAR}
        <if test="createTime !=null">
            ,CreateTime = #{createTime,jdbcType=TIMESTAMP}
        </if>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="findInventoryStorage"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryStorageDTO">
        SELECT
        psbr.id AS batchChangeRecordId,
        psr.OrderNo AS orderNo,
        psr.OrderType AS orderType,
        psr.JiupiEventType AS jiuPiEventType,
        psbr.batch_id AS batchId,
        - psbr.totalcount_minunit AS unitTotalCount,
        psbr.createtime AS orderOutStockTime,
        IFNULL(psb.batchtime,psb.createtime) AS batchTime,
        ps.ProductSpecification_Id AS productSpecificationId,
        ps.Owner_Id AS ownerId,
        ps.SecOwner_Id AS secOwnerId,
        psku.ProductSku_Id AS skuId
        FROM
        productstorechangerecord psr
        INNER JOIN productstorebatchchangerecord psbr ON psbr.changerecord_id = psr.id
        INNER JOIN productstorebatch psb ON psb.id = psbr.batch_id
        INNER JOIN productstore ps ON ps.Id = psr.productstore_id
        LEFT JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ( ( psku.Company_Id IS NULL AND ps.Owner_Id IS NULL ) OR ( psku.Company_Id = ps.Owner_Id ) )
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        WHERE
        psbr.totalcount_minunit != 0
        AND psr.OrderNo in
        <foreach collection="orderNos" item="orderNo" separator="," open="(" close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findInventoryChangeBySpec" resultMap="BaseResultMap">
        select
        psc.Id,psc.ProductStore_Id,psc.OrderType,psc.Order_Id,psc.OrderNo,psc.JiupiEventType,psc.ERPEventType,psc.Count_MaxUnit,psc.Count_MinUnit,psc.SourceTotalCount,psc.TotalCount,psc.Description,psc.CreateTime,psc.CreateUser,psc.StoreType
        from productstorechangerecord psc
        inner join productstore ps on psc.ProductStore_Id = ps.Id
        where ps.ProductSpecification_Id=#{dto.productSpecId,jdbcType=BIGINT}
        AND ps.Warehouse_Id=#{dto.warehouseId,jdbcType=INTEGER}
        <if test="dto.ownerId == null">
            AND ps.Owner_Id is null
        </if>
        <if test="dto.ownerId != null">
            AND ps.Owner_Id = #{dto.ownerId,jdbcType=BIGINT}
        </if>
        <if test="dto.timeS!=null">
            AND psc.CreateTime    <![CDATA[ >= ]]> concat(#{dto.timeS,jdbcType=VARCHAR},' 00:00:00')
        </if>
        <if test="dto.timeE!=null">
            AND psc.CreateTime      <![CDATA[ <= ]]>concat(#{dto.timeE,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="dto.orderNO!=null">
            AND psc.OrderNo like concat('%',#{dto.orderNO,jdbcType=VARCHAR},'%')
        </if>
        ORDER BY CreateTime DESC,SourceTotalCount ASC
    </select>


    <select id="findProductStoreBatchChangeRecordList"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.InventoryProductStoreBatchChangeRecordDTO">
        SELECT
        ps.Id as storeId,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        psku.ProductSku_Id as productSkuId,
        psku.Name as productName,
        psku.specificationName as specName,
        psku.packageQuantity as specQuantity,
        psku.packageName as packageName,
        psku.unitName as unitName,
        IFNULL(own.Id, psku.Company_Id) AS ownerId,
        IFNULL(own.OwnerName, psku.OwnerName) AS ownerName,
        ps.SecOwner_Id AS secOwnerId,
        secOwn.OwnerName AS secOwnerName,
        ps.ProductSpecification_Id as specId,
        ps.OwnerType as ownerType,
        psr.Id as storeRecordId,
        psr.OrderType as orderType,
        psr.JiupiEventType as jiupiEventType,
        psr.ERPEventType as erpEventType,
        psr.Order_Id as orderId,
        psr.OrderNo as orderNo,
        psbr.batch_id AS productStoreBatchId,
        psbr.totalcount_minunit AS batchChangeUnitTotalCount,
        psb.BatchAttributeInfoNo,
        psb.location_id locationId,
        psb.location_name locationName,
        psr.Count_MaxUnit as countMaxUnit,
        psr.Count_MinUnit as countMinUnit,
        psr.TotalCount as totalCount,
        psr.SourceTotalCount as sourceTotalCount,
        psb.productiondate,psb.batchTime,
        ps.channel,psb.createUser
        FROM
        productstorechangerecord psr
        INNER JOIN productstorebatchchangerecord psbr ON psbr.changerecord_id = psr.id
        INNER JOIN productstorebatch psb ON psb.id = psbr.batch_id
        INNER JOIN productstore ps ON ps.Id = psr.productstore_id
        LEFT JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ( ( psku.Company_Id IS NULL AND ps.Owner_Id IS NULL ) OR ( psku.Company_Id = ps.Owner_Id ) )
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        left join owner own on ps.Owner_Id is not null and ps.Owner_Id = own.Id
        left join owner secOwn on ps.SecOwner_Id is not null and ps.SecOwner_Id = secOwn.Id
        <where>
            <if test="cityId != null">
                ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="orderNoList != null and orderNoList.size() != 0">
                and psr.OrderNo in
                <foreach collection="orderNoList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>


</mapper>
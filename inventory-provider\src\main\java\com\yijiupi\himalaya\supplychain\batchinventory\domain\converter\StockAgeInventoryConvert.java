package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.StockAgeInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.StockAgeProductInventorySO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeProductConfigQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeProductQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyConfigDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.stockAgeStrategy.StockAgeStrategyDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class StockAgeInventoryConvert {

    public static StockAgeProductQueryDTO
        stockAgeInventoryQueryDTO2StockAgeProductQueryDTO(StockAgeInventoryQueryDTO stockAgeInventoryQueryDTO) {
        StockAgeProductQueryDTO stockAgeProductQueryDTO = new StockAgeProductQueryDTO();
        stockAgeProductQueryDTO.setProductName(stockAgeInventoryQueryDTO.getProductName());
        stockAgeProductQueryDTO.setOwnerId(stockAgeInventoryQueryDTO.getOwnerId());
        stockAgeProductQueryDTO.setWarehouseId(stockAgeInventoryQueryDTO.getWarehouseId());
        if (stockAgeInventoryQueryDTO.getStorageType() != null) {
            StockAgeProductConfigQueryDTO productConfigQueryDTO = new StockAgeProductConfigQueryDTO();
            productConfigQueryDTO.setStorageType(stockAgeInventoryQueryDTO.getStorageType());
            stockAgeProductQueryDTO.setProductConfigQueryDTO(productConfigQueryDTO);
        }
        stockAgeProductQueryDTO.setPageSize(stockAgeInventoryQueryDTO.getPageSize());
        stockAgeProductQueryDTO.setPageNum(stockAgeInventoryQueryDTO.getPageNum());
        return stockAgeProductQueryDTO;
    }

    public static List<StockAgeProductInventorySO> stockAgeStrategyList2stockAgeProductQueryDTOS(
        List<StockAgeStrategyDTO> stockAgeStrategyDTOS, StockAgeInventoryQueryDTO stockAgeInventoryQueryDTO) {
        if (CollectionUtils.isEmpty(stockAgeStrategyDTOS)) {
            return null;
        }
        Long stockAge = stockAgeInventoryQueryDTO.getStockAge();
        Long overdue = stockAgeInventoryQueryDTO.getOverdue();
        Long overdueSurplus = stockAgeInventoryQueryDTO.getOverdueSurplus();
        List<StockAgeProductInventorySO> stockAgeProductInventorySOS = new ArrayList<>();
        stockAgeStrategyDTOS.forEach(stockAgeStrategy -> {
            StockAgeProductInventorySO so = new StockAgeProductInventorySO();
            long maxStockAge = stockAgeStrategy.getMaxStockAge().longValue();
            long minStockAge = stockAgeStrategy.getMinStockAge().longValue();
            so.setMaxStockAge(maxStockAge);
            so.setMinStockAge(minStockAge);
            if (stockAge != null) {
                so.setStockAgeRange(stockAge);
            }

            if (overdue != null) {
                so.setOverdueRange(maxStockAge + overdue);
            } else if (overdueSurplus != null) {
                so.setOverdueRange(maxStockAge - overdueSurplus);
            }
            List<Long> skuIds = stockAgeStrategy.getStockAgeStrategyConfigDTOS().stream()
                .map(StockAgeStrategyConfigDTO::getSkuId).collect(Collectors.toList());
            so.setSkuIds(skuIds);

            stockAgeProductInventorySOS.add(so);
        });
        return stockAgeProductInventorySOS;
    }
}

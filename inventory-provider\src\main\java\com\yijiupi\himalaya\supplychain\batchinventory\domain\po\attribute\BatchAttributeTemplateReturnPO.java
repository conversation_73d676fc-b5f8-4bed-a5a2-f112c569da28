package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplateRelationReturnPO;

import java.util.List;

/**
 * 批属性模板管理po
 *
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeTemplateReturnPO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板所含属性(所含字典的主键id集合)
     */
    private List<BatchAttributeTemplateRelationReturnPO> relationList;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private Byte state;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 模板名称
     */
    public List<BatchAttributeTemplateRelationReturnPO> getRelationList() {
        return this.relationList;
    }

    /**
     * 设置 模板名称
     */
    public void setRelationList(List<BatchAttributeTemplateRelationReturnPO> relationList) {
        this.relationList = relationList;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 状态
     */
    public Byte getState() {
        return this.state;
    }

    /**
     * 设置 状态
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public String getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 模板名称
     */
    public String getTemplateName() {
        return this.templateName;
    }

    /**
     * 设置 模板名称
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.putawayTypeEnum;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.inventory.domain.aspect.InventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.*;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreItemDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 库存订单业务BL.
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ErpInventoryOrderBizBL {

    private static final Logger LOG = LoggerFactory.getLogger(ErpInventoryOrderBizBL.class);

    @Autowired
    private InventoryErpBL inventoryErpBL;

    @Autowired
    private InventorySendFaildMQ inventorySendFaildMQ;

    /**
     * 处理入库单库存
     */
    public void processInStockOrderInventory(ProcessInStockOrderInventoryDTO processDTO) {
        List<StockOrderStoreDTO> orderStoreDTOList = processInventoryByErp(processDTO);
        if (CollectionUtils.isEmpty(orderStoreDTOList)) {
            LOG.info(String.format("初始化入库单库存操作项异常：%s", JSON.toJSON(processDTO)));
        }
        processInventoryByErp(orderStoreDTOList, processDTO.getAllowThrowException());
    }

    /**
     * 处理出库单库存
     */
    public void processOutStockOrderInventory(ProcessOutStockOrderInventoryDTO processDTO) {
        List<StockOrderStoreDTO> orderStoreDTOList = processOutInventoryByErp(processDTO);
        if (CollectionUtils.isEmpty(orderStoreDTOList)) {
            LOG.info(String.format("初始化出库单库存操作项异常：%s", JSON.toJSON(processDTO)));
        }
        processInventoryByErp(orderStoreDTOList, processDTO.getAllowThrowException());
    }

    /**
     * 出入库客户端直接加库存
     */
    public void processInventoryByErp(List<StockOrderStoreDTO> stockOrderStoreDTOS) {
        try {
            LOG.info("ERP->直接加减库存开始：{}", JSON.toJSONString(stockOrderStoreDTOS));
            if (CollectionUtils.isEmpty(stockOrderStoreDTOS)) {
                LOG.warn("SKU库存为空: 忽略: {}", JSON.toJSONString(stockOrderStoreDTOS));
                return;
            }
            for (StockOrderStoreDTO stockOrderStoreDTO : stockOrderStoreDTOS) {
                boolean isCheckStoreCount = stockOrderStoreDTO.getNeedCheckStoreCount() == null
                    ? Objects.equals(stockOrderStoreDTO.getErpType(), ERPType.第三方出库.getType())
                    : stockOrderStoreDTO.getNeedCheckStoreCount();
                // boolean isCheckStoreCount = Objects.equals(stockOrderStoreDTO.getErpType(), ERPType.第三方出库.getType());
                inventoryErpBL.applyErpOrder(stockOrderStoreDTO, isCheckStoreCount, false,
                    !stockOrderStoreDTO.getNotChangeStock(), true, stockOrderStoreDTO.getNeedToChangeSaleStore());
            }
            LOG.info("ERP->直接加减库存成功");

        } catch (Exception e) {
            LOG.info("出入库客户端直接加库存失败！", e);
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(stockOrderStoreDTOS), "processInventoryByErp", e);
        }
    }

    public void processInventoryByErp(List<StockOrderStoreDTO> stockOrderStoreDTOS, Boolean isThrowException) {
        try {
            LOG.info("ERP->直接加减库存开始：{}", JSON.toJSONString(stockOrderStoreDTOS));
            if (CollectionUtils.isEmpty(stockOrderStoreDTOS)) {
                LOG.warn("SKU库存为空: 忽略: {}", JSON.toJSONString(stockOrderStoreDTOS));
                return;
            }
            for (StockOrderStoreDTO stockOrderStoreDTO : stockOrderStoreDTOS) {
                boolean isCheckStoreCount = stockOrderStoreDTO.getNeedCheckStoreCount() == null
                    ? Objects.equals(stockOrderStoreDTO.getErpType(), ERPType.第三方出库.getType())
                    : stockOrderStoreDTO.getNeedCheckStoreCount();
                // boolean isCheckStoreCount = Objects.equals(stockOrderStoreDTO.getErpType(), ERPType.第三方出库.getType());
                // 是否处理销售库存
                Boolean needToChangeSaleStore =
                    ObjectUtils.defaultIfNull(stockOrderStoreDTO.getNeedToChangeSaleStore(), true);
                inventoryErpBL.applyErpOrder(stockOrderStoreDTO, isCheckStoreCount, false,
                    !stockOrderStoreDTO.getNotChangeStock(), true, needToChangeSaleStore);
            }
            LOG.info("ERP->直接加减库存成功");

        } catch (Exception e) {
            LOG.info("出入库客户端直接加库存失败！", e);
            if (isThrowException != null && isThrowException) {
                throw e;
            }
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(stockOrderStoreDTOS), "processInventoryByErp", e);
        }
    }

    public List<StockOrderStoreDTO> processInventoryByErp(ProcessInStockOrderInventoryDTO processDTO) {
        // 处理描述
        if (StringUtils.isBlank(processDTO.getDescription())) {
            Optional<InStockOrderDTO> orderTypeOpt = processDTO.getInStockOrderDTOList().stream()
                .filter(e -> e != null && e.getOrderType() != null).findAny();
            if (orderTypeOpt.isPresent()) {
                Byte orderType = orderTypeOpt.get().getOrderType();
                InStockOrderTypeEnum typeEnum = InStockOrderTypeEnum.getEnum(orderType);
                ERPEventType erpEventType = ERPEventType.getEnum(processDTO.getErpEventType());
                if (typeEnum == null && erpEventType == null) {
                    processDTO.setDescription("直接入库");
                } else {
                    String typeName = Objects.equals(InStockOrderTypeEnum.采购入库.getType(), orderType) ? "入库"
                        : (typeEnum != null ? typeEnum.name() : "");
                    processDTO.setDescription(
                        String.format("%s-%s", typeName, erpEventType != null ? erpEventType.name() : ""));
                }
            } else {
                processDTO.setDescription("直接入库");
            }
        }
        List<StockOrderStoreDTO> stockOrderStoreDTOList = new ArrayList<>();
        for (InStockOrderDTO orderDTO : processDTO.getInStockOrderDTOList()) {
            StockOrderStoreDTO stockOrderStoreDTO = new StockOrderStoreDTO();
            List<StockOrderStoreItemDTO> stockOrderStoreItemDTOS = new ArrayList<>();
            Byte shelfType = orderDTO.getShelfType();
            if (shelfType != null) {
                if (shelfType == putawayTypeEnum.采购上架.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.采购入库单.getType());
                } else if (shelfType == putawayTypeEnum.调拨上架.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.物料调拨单.getType());
                } else if (shelfType == putawayTypeEnum.退货上架.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.退货入库单.getType());
                } else if (shelfType == putawayTypeEnum.第三方上架.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.第三方入库.getType());
                } else if (shelfType == putawayTypeEnum.处理品转出.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.处理品转出.getType());
                } else if (shelfType == putawayTypeEnum.同城调拨转入.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.同城调拨转入.getType());
                } else if (shelfType == putawayTypeEnum.陈列品转出.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.陈列品转出.getType());
                } else if (shelfType == putawayTypeEnum.其它入库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.其他入库单.getType());
                } else if (shelfType == putawayTypeEnum.返库上架.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.返库入库单.getType());
                }
            }
            if (Objects.equals(orderDTO.getOrderType(), InStockOrderTypeEnum.大客户入库.getType())) {
                stockOrderStoreDTO.setErpType(orderDTO.getOrderType().intValue());
            }
            stockOrderStoreDTO.setDescription(processDTO.getDescription());
            stockOrderStoreDTO.setErpEventType(processDTO.getErpEventType());
            stockOrderStoreDTO.setCityId(String.valueOf(orderDTO.getOrgId()));
            // 如果是销售退货则用销售退货原始单据NO【relatedNoteNO】
            if (Objects.equals(orderDTO.getOrderType(), InStockOrderTypeEnum.退货入库.getType())
                && StringUtils.isNotBlank(orderDTO.getRelatedNoteNO())) {
                stockOrderStoreDTO.setErpOrderId(orderDTO.getRelatedNoteNO());
                stockOrderStoreDTO.setDescription(
                    String.format("[%s]-%s", orderDTO.getRefOrderNo(), stockOrderStoreDTO.getDescription()));
            } else {
                // 优先取 newNoteNo , 为空则获取 refOrderNO, 供应链通用入库单据 refOrderNO 直接替代 newNoteNo
                stockOrderStoreDTO.setErpOrderId(StringUtils.isNotBlank(orderDTO.getNewNoteNo())
                    ? orderDTO.getNewNoteNo() : orderDTO.getRefOrderNo());
            }
            stockOrderStoreDTO.setStockOrderId(String.valueOf(orderDTO.getId()));
            stockOrderStoreDTO.setWarehouseId(orderDTO.getWarehouseId());
            stockOrderStoreDTO.setOutInType(outInType.in);
            if (orderDTO.getNotUpdateProductStore() != null) {
                stockOrderStoreDTO.setNotChangeStock(orderDTO.getNotUpdateProductStore());
            }
            // 是否需要计算库存：入库单默认不需要计算库存
            stockOrderStoreDTO.setAllocationCalculation(orderDTO.getNeedInventoryCount() == null ? false
                : (Objects.equals(YesOrNoEnum.NO.getValue(), orderDTO.getNeedInventoryCount()) ? false : true));
            // 是否处理销售库存
            stockOrderStoreDTO
                .setNeedToChangeSaleStore(ObjectUtils.defaultIfNull(processDTO.getNeedToChangeSaleStore(), true));
            // 记录操作人
            stockOrderStoreDTO.setUserName(orderDTO.getUserName());
            List<InStockOrderItemDTO> inStockOrderItemDTOS = orderDTO.getInStockOrderItemDTOList();
            for (InStockOrderItemDTO inStockOrderItemPO : inStockOrderItemDTOS) {
                if (CollectionUtils.isEmpty(inStockOrderItemPO.getItemDetailDTOList())) {
                    // 没有Detail单据
                    StockOrderStoreItemDTO stockOrderStoreItemDTO = new StockOrderStoreItemDTO();
                    stockOrderStoreItemDTO.setStockOrderItemId(inStockOrderItemPO.getId());
                    stockOrderStoreItemDTO.setProductSkuId(String.valueOf(inStockOrderItemPO.getSkuId()));
                    stockOrderStoreItemDTO.setTotalStoreCountMinUnit(inStockOrderItemPO.getUnitTotalCount());
                    stockOrderStoreItemDTO.setChannel(inStockOrderItemPO.getChannel() == null ? ProductChannelType.JIUPI
                        : Integer.valueOf(inStockOrderItemPO.getChannel()));
                    stockOrderStoreItemDTO.setSource(inStockOrderItemPO.getSource() == null ? ProductSourceType.易酒批
                        : Integer.valueOf(inStockOrderItemPO.getSource()));
                    stockOrderStoreItemDTO.setLocationId(inStockOrderItemPO.getLocationId());
                    stockOrderStoreItemDTO.setLocationName(inStockOrderItemPO.getLocationName());
                    stockOrderStoreItemDTO.setBatchTime(inStockOrderItemPO.getBatchTime());
                    stockOrderStoreItemDTO.setProductionDate(inStockOrderItemPO.getProductionDate());
                    stockOrderStoreItemDTO.setExpireTime(inStockOrderItemPO.getExpireTime());
                    // SCM-6845
                    stockOrderStoreItemDTO.setOwnerId(inStockOrderItemPO.getOwnerId());
                    stockOrderStoreItemDTO.setSecOwnerId(inStockOrderItemPO.getSecOwnerId());
                    stockOrderStoreItemDTO.setProductSpecificationId(inStockOrderItemPO.getProductSpecificationId());
                    // SCM2-670 是否在上架任务完成后处理销售库存
                    stockOrderStoreItemDTO
                        .setIsProcessSalesStockAfterPutAway(inStockOrderItemPO.getIsProcessSalesStockAfterPutAway());
                    stockOrderStoreItemDTOS.add(stockOrderStoreItemDTO);
                } else {
                    // 有Detail单据
                    inStockOrderItemPO.getItemDetailDTOList().stream().filter(Objects::nonNull).forEach(detail -> {
                        StockOrderStoreItemDTO stockOrderStoreItemDTO = new StockOrderStoreItemDTO();
                        stockOrderStoreItemDTO.setStockOrderItemId(inStockOrderItemPO.getId());
                        stockOrderStoreItemDTO.setProductSkuId(String.valueOf(inStockOrderItemPO.getSkuId()));
                        stockOrderStoreItemDTO.setChannel(inStockOrderItemPO.getChannel() == null
                            ? ProductChannelType.JIUPI : Integer.valueOf(inStockOrderItemPO.getChannel()));
                        stockOrderStoreItemDTO.setSource(inStockOrderItemPO.getSource() == null ? ProductSourceType.易酒批
                            : Integer.valueOf(inStockOrderItemPO.getSource()));
                        stockOrderStoreItemDTO.setTotalStoreCountMinUnit(detail.getUnitTotalCount());
                        stockOrderStoreItemDTO.setLocationId(detail.getLocationId());
                        stockOrderStoreItemDTO.setLocationName(detail.getLocationName());
                        stockOrderStoreItemDTO.setBatchTime(detail.getBatchTime());
                        stockOrderStoreItemDTO.setProductionDate(detail.getProductionDate());
                        stockOrderStoreItemDTO.setExpireTime(detail.getExpireTime());
                        // SCM-6845
                        stockOrderStoreItemDTO.setOwnerId(detail.getOwnerId());
                        stockOrderStoreItemDTO.setSecOwnerId(detail.getSecOwnerId());
                        stockOrderStoreItemDTO.setProductSpecificationId(detail.getProductSpecificationId());
                        // SCM2-670 是否在上架任务完成后处理销售库存
                        stockOrderStoreItemDTO.setIsProcessSalesStockAfterPutAway(
                            inStockOrderItemPO.getIsProcessSalesStockAfterPutAway());
                        // SCM2-9937 批次编号
                        stockOrderStoreItemDTO.setBatchNo(detail.getBatchAttributeInfoNo());
                        stockOrderStoreItemDTOS.add(stockOrderStoreItemDTO);
                    });
                }
            }

            stockOrderStoreDTO.setProductSkuList(stockOrderStoreItemDTOS);
            stockOrderStoreDTOList.add(stockOrderStoreDTO);
        }
        LOG.info("ERP入库单库存处理项：" + JSON.toJSONString(stockOrderStoreDTOList));
        return stockOrderStoreDTOList;
    }

    public List<StockOrderStoreDTO> processOutInventoryByErp(ProcessOutStockOrderInventoryDTO processDTO) {
        // 处理出库时描述
        if (StringUtils.isBlank(processDTO.getDescription())) {
            Optional<OutStockOrderDTO> orderTypeOpt = processDTO.getOutStockOrderDTOList().stream()
                .filter(e -> e != null && e.getOrderType() != null).findAny();
            if (orderTypeOpt.isPresent()) {
                OutStockOrderTypeEnum typeEnum = OutStockOrderTypeEnum.getEnum(orderTypeOpt.get().getOrderType());
                ERPEventType erpEventType = ERPEventType.getEnum(processDTO.getErpEventType());
                if (typeEnum == null && erpEventType == null) {
                    processDTO.setDescription("直接出库");
                } else {
                    processDTO.setDescription(String.format("%s-%s", typeEnum != null ? typeEnum.name() : "",
                        erpEventType != null ? erpEventType.name() : ""));
                }
            } else {
                processDTO.setDescription("直接出库");
            }
        }
        List<StockOrderStoreDTO> stockOrderStoreDTOList =
            Lists.transform(processDTO.getOutStockOrderDTOList(), (input) -> {
                StockOrderStoreDTO stockOrderStoreDTO = new StockOrderStoreDTO();
                List<StockOrderStoreItemDTO> stockOrderStoreItemDTOS = Lists.newArrayList();
                Byte shelfType = input.getOrderType();
                if (shelfType == OutStockOrderTypeEnum.销售出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.销售出库单.getType());
                } else if (shelfType == OutStockOrderTypeEnum.调拨出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.物料调拨单.getType());
                } else if (shelfType == OutStockOrderTypeEnum.破损出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.破损出库单.getType());
                } else if (shelfType == OutStockOrderTypeEnum.其他出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.其他出库单.getType());
                } else if (shelfType == OutStockOrderTypeEnum.采购退货.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.采购退货单.getType());
                } else if (shelfType == OutStockOrderTypeEnum.盘亏出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.库存盘点单.getType());
                } else if (shelfType == OutStockOrderTypeEnum.第三方出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.第三方出库.getType());
                } else if (shelfType == OutStockOrderTypeEnum.同城调拨出库.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.同城调拨转出.getType());
                } else if (shelfType == OutStockOrderTypeEnum.处理品转入.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.处理品转入.getType());
                } else if (shelfType == OutStockOrderTypeEnum.陈列品转入.getType()) {
                    stockOrderStoreDTO.setErpType(ERPType.陈列品转入.getType());
                }
                stockOrderStoreDTO.setErpEventType(processDTO.getErpEventType());
                stockOrderStoreDTO.setCityId(String.valueOf(input.getOrgId()));
                stockOrderStoreDTO.setErpOrderId(input.getNewNoteNo());
                stockOrderStoreDTO.setNeedCheckStoreCount(input.getNeedCheckStoreCount());
                if (stockOrderStoreDTO.getErpOrderId() == null) {
                    // 此处 erpOrderId 实则为出库单单号, 能直接用 refOrderNo 取代 newNoteNo
                    stockOrderStoreDTO.setErpOrderId(input.getRefOrderNo());
                }
                stockOrderStoreDTO.setDescription(processDTO.getDescription());
                stockOrderStoreDTO.setStockOrderId(String.valueOf(input.getId()));
                stockOrderStoreDTO.setWarehouseId(input.getWarehouseId());
                stockOrderStoreDTO.setOutInType(outInType.out);
                if (input.getNotUpdateProductStore() != null) {
                    stockOrderStoreDTO.setNotChangeStock(input.getNotUpdateProductStore());
                }
                stockOrderStoreDTO.setAllocationCalculation(input.getAllocationCalculation());
                List<OutStockOrderItemDTO> inStockOrderItemDTOS = input.getOutStockOrderItemDTOS();
                for (OutStockOrderItemDTO outStockOrderItemDTO : inStockOrderItemDTOS) {
                    StockOrderStoreItemDTO stockOrderStoreItemDTO = new StockOrderStoreItemDTO();
                    stockOrderStoreItemDTO.setStockOrderItemId(StringUtils.isNumeric(outStockOrderItemDTO.getId())
                        ? Long.valueOf(outStockOrderItemDTO.getId()) : null);
                    stockOrderStoreItemDTO.setProductSkuId(String.valueOf(outStockOrderItemDTO.getSkuId()));
                    stockOrderStoreItemDTO.setTotalStoreCountMinUnit(outStockOrderItemDTO.getUnitTotalCount().negate());
                    stockOrderStoreItemDTO.setChannel(outStockOrderItemDTO.getChannel() == null
                        ? ProductChannelType.JIUPI : outStockOrderItemDTO.getChannel());
                    stockOrderStoreItemDTO.setSource(outStockOrderItemDTO.getSource());
                    stockOrderStoreItemDTO.setLocationId(outStockOrderItemDTO.getLocationid());
                    stockOrderStoreItemDTO.setLocationName(outStockOrderItemDTO.getLocationname());
                    stockOrderStoreItemDTO.setBatchTime(outStockOrderItemDTO.getBatchTime());
                    stockOrderStoreItemDTO.setProductionDate(outStockOrderItemDTO.getProductionDate());
                    // SCM-6845
                    stockOrderStoreItemDTO.setOwnerId(outStockOrderItemDTO.getOwnerId());
                    stockOrderStoreItemDTO.setSecOwnerId(outStockOrderItemDTO.getSecOwnerId());
                    stockOrderStoreItemDTO.setProductSpecificationId(outStockOrderItemDTO.getProductSpecificationId());
                    stockOrderStoreItemDTOS.add(stockOrderStoreItemDTO);
                }

                stockOrderStoreDTO.setProductSkuList(stockOrderStoreItemDTOS);
                return stockOrderStoreDTO;
            });
        return stockOrderStoreDTOList;
    }
}

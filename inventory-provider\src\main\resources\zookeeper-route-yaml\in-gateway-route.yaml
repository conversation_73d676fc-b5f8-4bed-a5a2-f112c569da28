# $schema: https://yjp-dev.yjpcdn.com/schemas/gateway-router.json
- hosts:
    - "*"
    - "scapi.*"
  signVerify: false
  timeout: 60000
  priority: 1
  extras:
    wrapper: "ROResult:wms"
  pathRouters:
    - paths:
        - "/wms/inventory/{service}/{method}"
        - "/wmsSaas/inventory/{service}/{method}"
      proxyUrl: "dubbo://supplychain-microservice-inventory/com.yijiupi.himalaya.supplychain.inventory.service.<service>/<method>"
      priority: 10
    - paths:
        - "/wms/batchInventory/{service}/{method}"
        - "/wmsSaas/batchInventory/{service}/{method}"
      proxyUrl: "dubbo://supplychain-microservice-inventory/com.yijiupi.himalaya.supplychain.batchinventory.service.<service>/<method>"
      priority: 10
    - paths:
        - "/oldwms/storeBatch/transferToDefective"
        - "/oldpda/storeBatch/transferToDefective"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-inventory#/storeBatch/transferToDefective"
    - paths:
        - "/oldwms/storeBatch/calCcpPrice"
        - "/oldpda/storeBatch/calCcpPrice"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-inventory#/storeBatch/calCcpPrice"
    - paths:
        - "/IBatchInventoryQueryService/findProductionDate"
      proxyUrl: "dubbo://supplychain-microservice-inventory/com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService/findProductionDate"
      priority: 10

    - paths:
        - "/oldwms/store/report/findStorePage"  # 库存查询
      stripPath: true
      proxyUrl: "http://#supplychain-microservice-inventory#/store/report/findStorePage"

    - paths:
        - "/oldwms/store/report/findStorePageInfo"  # 库存查询新
      stripPath: true
      proxyUrl: "http://#supplychain-microservice-inventory#/store/report/findStorePageInfo"

    - paths:
        - "/oldwms/batchInventory/findBatchInventoryInfo"  # 批次库存新
      stripPath: true
      proxyUrl: "http://#supplychain-microservice-inventory#/batchInventory/findBatchInventoryInfo"

    - paths:
        - "/oldwms/productSku/applyProductStorageAge"  # 申请商品库龄
      stripPath: true
      proxyUrl: "http://#supplychain-microservice-inventory#/productSku/applyProductStorageAge"

    - paths:
        - "/oldwms/storeBatch/listProductMixedBatchFlag"
        - "/oldpda/storeBatch/listProductMixedBatchFlag"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-inventory#/storeBatch/listProductMixedBatchFlag"

    - paths:
        - "/oldwms/defective/queryDefectiveLimit"
      stripPath: true
      preserveHost: false
      proxyUrl: "http://#supplychain-microservice-inventory#/defective/queryDefectiveLimit"




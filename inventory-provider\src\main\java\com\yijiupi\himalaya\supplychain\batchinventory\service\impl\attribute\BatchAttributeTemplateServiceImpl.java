package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.attribute;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeTemplateBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchAttributeTemplateService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 批属性模板管理
 *
 * <AUTHOR> 2018/4/9
 */
@Service
public class BatchAttributeTemplateServiceImpl implements IBatchAttributeTemplateService {

    @Autowired
    private BatchAttributeTemplateBL batchAttributeTemplateBL;

    /**
     * 新增
     *
     * @param batchAttributeTemplateDTO
     */
    @Override
    public void addBatchAttributeTemplate(BatchAttributeTemplateDTO batchAttributeTemplateDTO) {
        batchAttributeTemplateBL.addBatchAttributeTemplate(batchAttributeTemplateDTO);
    }

    /**
     * 编辑
     * 
     * @param batchAttributeTemplateDTO
     */
    @Override
    public void updateBatchAttributeTemplate(BatchAttributeTemplateDTO batchAttributeTemplateDTO) {
        AssertUtils.notNull(batchAttributeTemplateDTO.getId(), "主键不能为空");
        AssertUtils.notNull(batchAttributeTemplateDTO.getEnable(), "状态不能为空");
        AssertUtils.notNull(batchAttributeTemplateDTO.getTemplateName(), "模板名称不能为空");
        batchAttributeTemplateBL.updateBatchAttributeTemplate(batchAttributeTemplateDTO);
    }

    /**
     * 批属性模板管理列表
     *
     * @param batchAttributeTemplateQueryDTO
     * @return
     */
    @Override
    public PageList<BatchAttributeTemplateReturnDTO>
        findBatchAttributeTemplateList(BatchAttributeTemplateQueryDTO batchAttributeTemplateQueryDTO) {
        return batchAttributeTemplateBL.findBatchAttributeTemplateList(batchAttributeTemplateQueryDTO);
    }

    /**
     * 停用,启用
     *
     * @param batchAttributeTemplateDTO
     */
    @Override
    public void updateBatchAttributeTemplateState(BatchAttributeTemplateDTO batchAttributeTemplateDTO) {
        AssertUtils.notNull(batchAttributeTemplateDTO.getId(), "主键不能为空");
        AssertUtils.notNull(batchAttributeTemplateDTO.getEnable(), "状态不能为空");
        batchAttributeTemplateBL.updateBatchAttributeTemplateState(batchAttributeTemplateDTO);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public void deleteById(Long id) {
        AssertUtils.notNull(id, "主键不能为空");
        batchAttributeTemplateBL.deleteById(id);
    }

    /**
     * 根据产品信息查询批属性应填属性
     *
     * @param batchProductInfoDTO
     * @return
     */
    @Override
    public List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeTemplateRelation(BatchProductInfoDTO batchProductInfoDTO) {
        AssertUtils.notNull(batchProductInfoDTO.getWarehouseId(), "仓库不能为空");
        return batchAttributeTemplateBL.findBatchAttributeTemplateRelation(batchProductInfoDTO);
    }

    /**
     * 查询类目和仓库绑定的批属性字典
     *
     * @param batchAttributeEnableQueryDTO
     * @return
     */
    @Override
    public List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeEnable(BatchAttributeEnableQueryDTO batchAttributeEnableQueryDTO) {
        return batchAttributeTemplateBL.findBatchAttributeEnable(batchAttributeEnableQueryDTO);
    }

    /**
     * 根据配置类型和属性值Id查询批属性字典
     */
    @Override
    public List<BatchAttributeTemplateRelationReturnDTO>
        findAttributeTemplateByRuleType(BatchAttributeTemplateRuleTypeQueryDTO ruleTypeQueryDTO) {
        return batchAttributeTemplateBL.findAttributeTemplateByRuleType(ruleTypeQueryDTO);
    }
}

/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.WarehouseStoreReportDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;

/**
 * Created by 杨康 on 2016/10/14.
 */
public class ProductStoreConverter {

    private ProductStoreConverter() {
        super();
    }
    //
    // /**
    // * po转为仓库库存模型DTO
    // *
    // * @return
    // */
    // public static ProductWarehouseStoreDTO convertToProductWarehouseStoreDTO(ProductStorePO productStorePO) {
    // ProductWarehouseStoreDTO warehouseStore = new ProductWarehouseStoreDTO();
    // warehouseStore.setWarehouseId(productStorePO.getWarehouseId());
    // warehouseStore.setCityId(productStorePO.getCityId());
    // warehouseStore.setWarehouseTotalCount(productStorePO.getTotalCountMinUnit());
    // warehouseStore.setId(productStorePO.getId());
    // warehouseStore.setProductSpecId(productStorePO.getProductSpecificationId());
    // warehouseStore.setOwnerType(productStorePO.getOwnerType());
    // warehouseStore.setOwnerId(productStorePO.getOwnerId());
    // warehouseStore.setDeliveryedCount(productStorePO.getDeliveryedCount());
    // return warehouseStore;
    // }

    public static WarehouseStoreDTO productStorePO2WarehouseStoreDTO(ProductInventoryPO productInventoryPO) {
        if (productInventoryPO == null) {
            return null;
        }
        WarehouseStoreDTO warehouseStoreDTO = new WarehouseStoreDTO();

        warehouseStoreDTO.setWarehouseTotalCount(productInventoryPO.getTotalCountMinUnit() != null
            ? productInventoryPO.getTotalCountMinUnit() : BigDecimal.ZERO);
        warehouseStoreDTO.setCityId(productInventoryPO.getCityId());
        warehouseStoreDTO.setProductSkuId(productInventoryPO.getProductSkuId());
        warehouseStoreDTO.setWarehouseId(productInventoryPO.getWarehouseId());
        warehouseStoreDTO.setProductSpecId(productInventoryPO.getProductSpecificationId());
        warehouseStoreDTO.setOwnerId(productInventoryPO.getOwnerId());
        warehouseStoreDTO.setChannel(productInventoryPO.getChannel());
        warehouseStoreDTO.setPackageQuantity(productInventoryPO.getPackageQuantity());
        warehouseStoreDTO.setPackageName(productInventoryPO.getPackageName());
        warehouseStoreDTO.setUnitName(productInventoryPO.getUnitName());
        warehouseStoreDTO.setSpecificationName(productInventoryPO.getSpecificationName());
        warehouseStoreDTO.setSource(productInventoryPO.getSource());
        warehouseStoreDTO.setOwnerName(productInventoryPO.getOwnerName());
        warehouseStoreDTO.setProductName(productInventoryPO.getProductName());
        warehouseStoreDTO.setSaleModel(productInventoryPO.getSaleModel());
        warehouseStoreDTO.setProductInfoId(productInventoryPO.getProductInfoId());
        warehouseStoreDTO.setStatisticsClass(productInventoryPO.getStatisticsClass());
        warehouseStoreDTO.setStatisticsClassName(productInventoryPO.getStatisticsClassName());
        warehouseStoreDTO.setLastUpdateTime(productInventoryPO.getLastUpdateTime());
        warehouseStoreDTO.setSecOwnerId(productInventoryPO.getSecOwnerId());
        warehouseStoreDTO.setSecOwnerName(productInventoryPO.getSecOwnerName());
        warehouseStoreDTO.setId(productInventoryPO.getId());
        return warehouseStoreDTO;
    }

    public static List<WarehouseStoreDTO>
        productStorePOS2WarehouseStoreDTOS(List<ProductInventoryPO> productInventoryPOS) {
        ArrayList<WarehouseStoreDTO> list = new ArrayList<>();
        for (ProductInventoryPO productInventoryPO : productInventoryPOS) {
            list.add(productStorePO2WarehouseStoreDTO(productInventoryPO));
        }
        productInventoryPOS.clear();
        return list;
    }

    public static PageList<WarehouseStoreDTO>
        productStorePOS2WarehouseStoreDTOS(PageResult<ProductInventoryPO> productInventoryPOS) {
        ArrayList<WarehouseStoreDTO> list = new ArrayList<>();
        for (ProductInventoryPO productInventoryPO : productInventoryPOS) {
            list.add(productStorePO2WarehouseStoreDTO(productInventoryPO));
        }
        PageList<WarehouseStoreDTO> pageList = new PageList<>();
        pageList.setPager(productInventoryPOS.getPager());
        pageList.setDataList(list);
        return pageList;
    }

    public static List<WarehouseInventoryDTO>
        warehouseInventoryListToReportList(List<WarehouseStoreReportDTO> reportDTOS) {
        List<WarehouseInventoryDTO> inventoryDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(reportDTOS)) {
            return inventoryDTOS;
        }
        reportDTOS.forEach(reportDTO -> {
            inventoryDTOS.add(warehouseInventoryToReport(reportDTO));
        });
        return inventoryDTOS;
    }

    public static WarehouseInventoryDTO warehouseInventoryToReport(WarehouseStoreReportDTO reportDTO) {
        if (reportDTO == null) {
            return null;
        }
        WarehouseInventoryDTO inventoryDTO = new WarehouseInventoryDTO();
        BeanUtils.copyProperties(reportDTO, inventoryDTO);
        return inventoryDTO;
    }

    public static PageList<WarehouseInventoryDTO>
        warehouseInventoryListToReportPageList(PageList<WarehouseStoreReportDTO> reportDTOS) {
        PageList<WarehouseInventoryDTO> inventoryDTOS = new PageList<>();
        inventoryDTOS.setPager(reportDTOS.getPager());
        if (CollectionUtils.isEmpty(reportDTOS.getDataList())) {
            return inventoryDTOS;
        }
        inventoryDTOS.setDataList(warehouseInventoryListToReportList(reportDTOS.getDataList()));
        return inventoryDTOS;
    }
}

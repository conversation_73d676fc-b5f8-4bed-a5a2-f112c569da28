package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.promotion.ProductPromotionStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.ProductPromotionStoreBatchDTO;

/**
 * 促销批次库存转换类
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
public class PromotionStoreBatchConvert {

    public static ProductPromotionStoreBatchDTO convertDTO(ProductPromotionStoreBatchPO po) {
        if (po == null) {
            return null;
        }
        ProductPromotionStoreBatchDTO dto = new ProductPromotionStoreBatchDTO();
        BeanUtils.copyProperties(po, dto);

        return dto;
    }

    public static List<ProductPromotionStoreBatchDTO> convertDTOList(List<ProductPromotionStoreBatchPO> pos) {
        List<ProductPromotionStoreBatchDTO> dtos = new ArrayList<>();
        for (ProductPromotionStoreBatchPO po : pos) {
            dtos.add(convertDTO(po));
        }
        return dtos;
    }

}

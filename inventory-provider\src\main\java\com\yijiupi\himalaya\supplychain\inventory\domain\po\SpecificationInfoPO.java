package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.math.BigDecimal;

/**
 * 产品信息
 * 
 * <AUTHOR> 2018/1/24
 */
public class SpecificationInfoPO {
    /**
     * 大单位名称
     */
    private String packageName;
    /**
     * 小单位名称
     */
    private String unitName;
    /**
     * 转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 获取 大单位名称
     *
     * @return packageName 大单位名称
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 大单位名称
     *
     * @param packageName 大单位名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 小单位名称
     *
     * @return unitName 小单位名称
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 小单位名称
     *
     * @param unitName 小单位名称
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 转换系数
     *
     * @return packageQuantity 转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 转换系数
     *
     * @param packageQuantity 转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }
}

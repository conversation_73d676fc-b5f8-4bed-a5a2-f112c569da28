package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change;

import java.math.BigDecimal;

import org.springframework.stereotype.Service;

/**
 * 库存变更工厂
 *
 * <AUTHOR>
 */
@Service
public class InventoryChangeFactory {

    public WarehouseInventoryChangeBO createWarehouseInventoryChangeBO(Long productSkuId, Integer warehouseId,
        BigDecimal count, Integer channel, Long ownerId) {
        WarehouseInventoryChangeBO warehouseInventoryChangeBO = new WarehouseInventoryChangeBO();
        warehouseInventoryChangeBO.setProductSkuId(productSkuId);
        warehouseInventoryChangeBO.setWarehouseId(warehouseId);
        warehouseInventoryChangeBO.setCount(count);
        warehouseInventoryChangeBO.setChannel(channel);
        warehouseInventoryChangeBO.setOwnId(ownerId);
        return warehouseInventoryChangeBO;
    }

    public WarehouseInventoryChangeBO createWarehouseInventoryChangeBO(Long productSkuId, Integer warehouseId,
        BigDecimal count, Integer channel, Long ownerId, Long secOwnerId) {
        WarehouseInventoryChangeBO warehouseInventoryChangeBO = new WarehouseInventoryChangeBO();
        warehouseInventoryChangeBO.setProductSkuId(productSkuId);
        warehouseInventoryChangeBO.setWarehouseId(warehouseId);
        warehouseInventoryChangeBO.setCount(count);
        warehouseInventoryChangeBO.setChannel(channel);
        warehouseInventoryChangeBO.setOwnId(ownerId);
        warehouseInventoryChangeBO.setSecOwnerId(secOwnerId);
        return warehouseInventoryChangeBO;
    }

    public SellInventoryChangeBO createSellInventoryChangeBO(Long productSkuId, Integer warehouseId, BigDecimal count) {
        SellInventoryChangeBO sellInventoryChangeBO = new SellInventoryChangeBO();
        sellInventoryChangeBO.setProductSkuId(productSkuId);
        sellInventoryChangeBO.setWarehouseId(warehouseId);
        sellInventoryChangeBO.setCount(count);
        return sellInventoryChangeBO;
    }

}

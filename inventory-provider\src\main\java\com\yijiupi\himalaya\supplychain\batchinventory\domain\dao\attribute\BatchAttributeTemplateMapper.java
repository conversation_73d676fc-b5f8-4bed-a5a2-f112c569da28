package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplatePO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateReturnDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatchAttributeTemplateMapper {
    /**
     * 新增
     *
     * @param batchAttributeTemplatePO
     * @return
     */
    int insert(@Param("batchAttributeTemplatePO") BatchAttributeTemplatePO batchAttributeTemplatePO);

    int insertList(@Param("batchAttributeTemplatePOs") List<BatchAttributeTemplatePO> batchAttributeTemplatePOs);

    int update(@Param("batchAttributeTemplatePO") BatchAttributeTemplatePO batchAttributeTemplatePO);

    /**
     * 列表
     *
     * @param dto
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<BatchAttributeTemplateReturnDTO> findBatchAttributeTemplateList(
        @Param("dto") BatchAttributeTemplateQueryDTO dto, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 启用,停用
     *
     * @param batchAttributeTemplateDTO
     */
    void updateIsEnable(@Param("batchAttributeTemplatePO") BatchAttributeTemplateDTO batchAttributeTemplateDTO);

    int deleteById(@Param("id") Long id);

}

package com.yijiupi.himalaya.supplychain.inventory.domain.model;

import java.io.Serializable;
import java.util.List;

/**
 * ERP获取处理品数量
 *
 * <AUTHOR>
 * @date 2019/1/11 16:47
 */
public class ErpDisposedCountParam implements Serializable {

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * SkuId集合
     */
    private List<Long> listSkuId;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<Long> getListSkuId() {
        return listSkuId;
    }

    public void setListSkuId(List<Long> listSkuId) {
        this.listSkuId = listSkuId;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.ordercenter.BatchInventoryOrderCenterBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryResultDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.*;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批次库存相关查询
 *
 * <AUTHOR> 2018/3/28
 */
@Service(timeout = 60000)
public class BatchInventoryQueryServiceImpl implements IBatchInventoryQueryService {
    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventoryQueryServiceImpl.class);
    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;
    @Autowired
    private ProductStoreBatchBL productStoreBatchBL;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;
    @Autowired
    private CategoryLocationStockBL categoryLocationStockBL;
    @Autowired
    private QueryBatchInventoryBL queryBatchInventoryBL;
    @Autowired
    private BatchInventoryOrderCenterBL batchInventoryOrderCenterBL;

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public PageList<BatchInventoryDTO> findBatchInventoryList(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO.getWarehouseId(), "仓库id不能为空");
        return batchInventoryQueryBL.findBatchInventoryList(batchInventoryQueryDTO);
    }

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public PageList<BatchInventoryDTO>
        findBatchInventoryListBySpecification(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO.getWarehouseId(), "仓库id不能为空");
        return batchInventoryQueryBL.findBatchInventoryListBySpecification(batchInventoryQueryDTO);
    }

    /**
     * 查看批次库存信息（根据货位或产品名称获取批次库存）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public PageList<BatchInventoryDTO> findBatchInventoryListNew(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        return batchInventoryQueryBL.findBatchInventoryListNew(batchInventoryQueryDTO);
        // return queryBatchInventoryBL.findBatchInventoryListNew(batchInventoryQueryDTO);
    }

    /**
     * 查看批次库存信息（根据货位或产品名称获取批次库存，支持skuIds）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public PageList<BatchInventoryDTO> findBatchInventoryListBatchNew(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        return batchInventoryQueryBL.findBatchInventoryListBatchNew(batchInventoryQueryDTO);
    }

    /**
     * 根据skuid获取库存货位
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public List<BatchInventoryDTO> findInventoryLocationBySkuId(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        return batchInventoryQueryBL.findInventoryLocationBySkuId(batchInventoryQueryDTO);
    }

    /**
     * 根据sku查询自身与关联产品的货位库存。 返回结果以传入sku为主，其对应关联产品的货位库存以：货位 + 生产日期 方式合并至 sku 对应货位中
     *
     * @param dto
     * @return
     */
    @Override
    public List<BatchInventoryDTO> findProductAndRefProductLocationStoreBySkuId(BatchInventoryQueryDTO dto) {
        return batchInventoryQueryBL.findProductAndRefProductLocationStoreBySkuId(dto);
    }

    /**
     * 查看周转区库存
     */
    @Override
    public PageList<BatchInventoryDTO> findCBHInventoryList(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO.getWarehouseId(), "仓库id不能为空");
        return batchInventoryQueryBL.findCBHInventoryList(batchInventoryQueryDTO);
    }

    /**
     * 根据货区查可用货位(可以不传locationId)
     *
     * @param batchLocationInfoQueryDTOS
     * @return
     */
    @Override
    public Map<Long, List<BatchLocationInfoDTO>>
        findBatchLocationDTOBySku(List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS) {
        validate(batchLocationInfoQueryDTOS, false);
        return batchInventoryQueryBL.findBatchLocationDTOBySku(batchLocationInfoQueryDTOS);
    }

    /**
     * 查询单个产品货位库存
     *
     * @param batchLocationInfoQueryDTO
     * @return
     */
    @Override
    public List<BatchLocationInfoDTO>
        findBatchLocationDTOBySingleSku(BatchLocationInfoQueryDTO batchLocationInfoQueryDTO) {
        AssertUtils.notNull(batchLocationInfoQueryDTO.getWarehouseId(), "批次库存查询仓库id不能为空");
        AssertUtils.notNull(batchLocationInfoQueryDTO.getProductSkuId(), "批次库存查询产品SKUID不能为空");
        return batchInventoryQueryBL.findBatchLocationDTOBySingleSku(batchLocationInfoQueryDTO);
    }

    private void validate(List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, boolean isAreaRequired) {
        for (BatchLocationInfoQueryDTO batchLocationInfoQueryDTO : batchLocationInfoQueryDTOS) {
            AssertUtils.notNull(batchLocationInfoQueryDTO.getProductSkuId(), "批次库存查询产品SKUID不能为空");
            AssertUtils.notNull(batchLocationInfoQueryDTO.getChannel(), "批次库存查询产品渠道类型不能为空");
            AssertUtils.notNull(batchLocationInfoQueryDTO.getSource(), "批次库存查询产品来源类型不能为空");
            AssertUtils.notNull(batchLocationInfoQueryDTO.getWarehouseId(), "批次库存查询仓库id不能为空");
            if (isAreaRequired) {
                AssertUtils.notNull(batchLocationInfoQueryDTO.getAreaId(), "货区id不能为空");
            }
        }
    }

    /**
     * 根据拣货产品的sku及货区Id查询货位相关信息(可以不传locationId)
     *
     * @param batchLocationInfoQueryDTOS
     * @param repositoryPriorityRules 库位优先规则
     * @param repositoryLimit 库位限制
     * @return
     */
    @Override
    public Map<Long, List<BatchLocationInfoDTO>> findBatchHuoWeiDTOBySku(
        List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Byte repositoryPriorityRules,
        Byte repositoryLimit) {
        validate(batchLocationInfoQueryDTOS, false);
        return batchInventoryQueryBL.findBatchHuoWeiDTOBySku(batchLocationInfoQueryDTOS, repositoryPriorityRules,
            repositoryLimit);
    }

    /**
     * 根据货区查可用货位（所有货位，库存信息可能不存在）(areaId必传)
     *
     * @param batchLocationInfoQueryDTOS
     * @param repositoryPriorityRules 库位优先规则
     * @param repositoryLimit 库位限制
     * @return
     */
    @Override
    public Map<Long, List<BatchLocationInfoDTO>> findBatchHuoWeiDTOBySkuAndArea(
        List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS, Byte repositoryPriorityRules,
        Byte repositoryLimit) {
        validate(batchLocationInfoQueryDTOS, true);
        return batchInventoryQueryBL.findBatchHuoWeiDTOBySkuAndArea(batchLocationInfoQueryDTOS, repositoryPriorityRules,
            repositoryLimit);
    }

    // /**
    // * 根据货区查可用货位(只穿locationId)
    // *
    // * @param locationId
    // * @return
    // */
    // @Override
    // public List<BatchLocationInfoDTO> findBatchLocationDTOByLocationId(Long locationId) {
    // AssertUtils.notNull(locationId, "货位id不能为空");
    // return batchInventoryQueryBL.findBatchLocationDTOByLocationId(locationId, 0);
    // }

    /**
     * 查询计算好的下架策略
     *
     * @param orderItemDTOList
     * @return
     */
    @Override
    public List<OrderItemDTO> findOutStockStrategyRule(List<OrderItemDTO> orderItemDTOList, String billType) {
        LOGGER.info("下架策略计算入参 {}", JSON.toJSONString(orderItemDTOList));
        AssertUtils.notEmpty(orderItemDTOList, "入参集合不能为空");
        AssertUtils.notNull(billType, "单据类型不能为空");
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            AssertUtils.notNull(orderItemDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(orderItemDTO.getChannel(), "产品渠道不能为空");
            AssertUtils.notNull(orderItemDTO.getSource(), "产品来源不能为空");
            AssertUtils.notNull(orderItemDTO.getUnitTotalCount(), "下架数量不能为空");
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "规格系数不能为空");
        }
        Integer warehouseId = orderItemDTOList.get(0).getWarehouseId();
        List<OrderItemDTO> orderItemDTOS = batchInventoryQueryBL.findOutStockStrategyRule(orderItemDTOList, billType);
        orderItemDTOS = categoryLocationStockBL.findStrategyRuleWithReleteLocation(orderItemDTOS, warehouseId);
        return orderItemDTOS;
    }

    /**
     * 查询计算好的延迟配送下架策略
     *
     * @param orderItemDTOList
     * @param billType
     * @return
     */
    @Override
    public List<OrderItemDTO> findDelayOutStockStrategyRule(List<OrderItemDTO> orderItemDTOList, String billType) {
        AssertUtils.notEmpty(orderItemDTOList, "入参集合不能为空");
        AssertUtils.notNull(billType, "单据类型不能为空");
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(orderItemDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(orderItemDTO.getChannel(), "产品渠道不能为空");
            AssertUtils.notNull(orderItemDTO.getSource(), "产品来源不能为空");
            AssertUtils.notNull(orderItemDTO.getUnitTotalCount(), "下架数量不能为空");
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "规格系数不能为空");

        }
        return batchInventoryQueryBL.findDelayOutStockStrategyRule(orderItemDTOList, billType);
    }

    /**
     * 查询符合条件的存储位
     *
     * @param dto
     * @return
     */
    @Override
    public List<OrderItemDTO> findPackageOutStockLocation(BatchInventoryPackageLocationDTO dto) {
        AssertUtils.notEmpty(dto.getOrderItemDTOList(), "入参集合不能为空");
        for (OrderItemDTO orderItemDTO : dto.getOrderItemDTOList()) {
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(orderItemDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(orderItemDTO.getChannel(), "产品渠道不能为空");
            AssertUtils.notNull(orderItemDTO.getSource(), "产品来源不能为空");
            AssertUtils.notNull(orderItemDTO.getUnitTotalCount(), "下架数量不能为空");
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "规格系数不能为空");

        }

        return batchInventoryQueryBL.findPackageOutStockLocation(dto);
    }

    @Override
    public List<LocationCapacityDTO> getLocationCapacityDTOs(List<Long> lstLocationIds) {
        return productStoreBatchBL.getLocationCapacityDTOs(lstLocationIds);
    }

    /**
     * 获取批次库存变更记录
     *
     * @return
     */
    @Override
    public PageList<ProductStoreBatchChangeRecordDTO>
        listProductStoreBatchChangeRecord(ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO) {
        return productStoreBatchChangeRecordBL.listProductStoreBatchChangeRecord(productStoreBatchChangeRecordQueryDTO);
    }

    /**
     * 获取批次库存变更流水
     *
     * @return
     */
    @Override
    public PageList<ProductStoreBatchChangeFlowDTO>
        listProductStoreBatchChangeFlow(ProductStoreBatchChangFlowQueryDTO productStoreBatchChangeRecordQueryDTO) {
        return productStoreBatchChangeRecordBL.listProductStoreBatchChangeFlow(productStoreBatchChangeRecordQueryDTO);
    }

    /**
     * 检查产品能否存放到某个货位上
     *
     * @param checkBatchInventoryDTO
     * @param msg
     * @return
     */
    @Override
    public Boolean checkIsCanPutIntoLocation(CheckBatchInventoryDTO checkBatchInventoryDTO, String msg) {
        AssertUtils.notNull(checkBatchInventoryDTO, "参数不能为空！");
        AssertUtils.notNull(checkBatchInventoryDTO.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notNull(checkBatchInventoryDTO.getLocationId(), "货位Id不能为空！");
        AssertUtils.notNull(checkBatchInventoryDTO.getProductSkuId(), "产品Id不能为空！");
        return batchInventoryQueryBL.checkIsCanPutIntoLocation(checkBatchInventoryDTO, msg);
    }

    /**
     * 通过策略查找货位
     *
     * @param orderItemDTOList
     * @return
     */
    @Override
    public Map<Long, List<BatchLocationInfoDTO>> findLocationByStrategyRule(List<OrderItemDTO> orderItemDTOList,
        String billType) {
        AssertUtils.notEmpty(orderItemDTOList, "入参集合不能为空");
        AssertUtils.notNull(billType, "单据类型不能为空");
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(orderItemDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(orderItemDTO.getChannel(), "产品渠道不能为空");
            AssertUtils.notNull(orderItemDTO.getSource(), "产品来源不能为空");

        }
        return batchInventoryQueryBL.findLocationByStrategyRule(orderItemDTOList, billType);
    }

    /**
     * 根据skuid获取库存货位（不关联location）
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public List<BatchInventoryDTO> findBatchStoreBySkuId(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        return batchInventoryQueryBL.findBatchStoreBySkuId(batchInventoryQueryDTO);
    }

    @Override
    public void checkProductInventory(List<CheckBatchInventoryDTO> checkBatchInventoryDTOS) {
        AssertUtils.notEmpty(checkBatchInventoryDTOS, "校验参数不能为空");
        checkBatchInventoryDTOS.forEach(check -> {
            AssertUtils.notNull(check.getLocationId(), "货位id不能为空");
            AssertUtils.notNull(check.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(check.getUnitTotalCount(), "数量不能为空");
        });

        batchInventoryQueryBL.checkProductInventory(checkBatchInventoryDTOS);
    }

    /**
     * 库龄产品批次库存查询
     */
    @Override
    public PageList<BatchInventoryDTO>
        pageListStockAgeProductInventory(StockAgeInventoryQueryDTO stockAgeInventoryQueryDTO) {
        AssertUtils.notNull(stockAgeInventoryQueryDTO.getWarehouseId(), "仓库id不能为空");
        return batchInventoryQueryBL.pageListStockAgeProductInventory(stockAgeInventoryQueryDTO);
    }

    /**
     * 查询库龄产品相关批次信息
     *
     * @param dto
     * @return
     */
    @Override
    public PageList<BatchInventoryDTO> findStoreAgeRefBatchInventoryList(BatchInventoryQueryDTO dto) {
        return batchInventoryQueryBL.findStoreAgeRefBatchInventoryList(dto);
    }

    /**
     * 查询所有负库存仓库
     *
     * @return
     */
    @Override
    public List<BatchInventoryNegativeDTO> listWarehouseByInventoryNegative() {
        return batchInventoryQueryBL.listWarehouseByInventoryNegative();
    }

    /**
     * 查找生产日期
     *
     * @param productionDateQueryDTOS
     * @return
     */
    @Override
    public List<ProductionDateDTO> findProductionDate(List<ProductionDateQueryDTO> productionDateQueryDTOS) {
        productionDateQueryDTOS.forEach(product -> {
            AssertUtils.notNull(product.getProductSpecificationId(), "规格id不能为空");
            AssertUtils.notNull(product.getWarehouseId(), "仓库id不能为空");
        });
        return batchInventoryQueryBL.findProductionDate(productionDateQueryDTOS);
    }

    /**
     * 根据仓库及产品规格信息查询批次生产日期
     */
    @Override
    public List<BatchProductionDateDTO>
        findProductionDateFromStoreBatch(BatchProductionDateQueryDTO productionDateQueryDTO) {
        return batchInventoryQueryBL.findProductionDateFromStoreBatch(productionDateQueryDTO);
    }

    /**
     * 根据货位、批次库存信息查询产品SKU基础信息
     */
    @Override
    public PageList<BatchInventoryDTO> findProductBaseInfoFromBatchInventory(BatchInventoryQueryDTO dto) {
        return batchInventoryQueryBL.findProductBaseInfoFromBatchInventory(dto);
    }

    @Override
    public List<ProductionDatePriceDTO> findProductionDatePriceBySkuIds(Integer warehouseId, List<Long> skuIds) {
        return batchInventoryQueryBL.findProductionDatePriceBySkuIds(warehouseId, skuIds);
    }

    @Override
    public List<ProductStoreBatchChangeRecordDTO> selectProductStoreBatchChangeRecords(
        ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO) {
        return productStoreBatchChangeRecordBL
            .selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO);
    }

    @Override
    public PageList<ProductStoreBatchDTO> listProductStoreBatch(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO, "查询参数不能为空！");
        return batchInventoryQueryBL.listProductStoreBatch(batchInventoryQueryDTO);
    }

    @Override
    public List<Long> listSkuIdByBatchInventory(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空！");
        return batchInventoryQueryBL.listSkuIdByBatchInventory(warehouseId);
    }

    @Override
    public Map<Long, List<BatchLocationInfoDTO>>
        listProductStoreBatchBySku(List<BatchLocationInfoQueryDTO> batchLocationInfoQueryDTOS) {
        validate(batchLocationInfoQueryDTOS, false);
        return batchInventoryQueryBL.listProductStoreBatchBySku(batchLocationInfoQueryDTOS);
    }

    @Override
    public List<Long> findSkuIdByBatchInventory(BatchLocationInfoQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空！");
        return batchInventoryQueryBL.findSkuIdByBatchInventory(queryDTO);
    }

    @Override
    public PageList<BatchLocationInfoDTO> findProductDateInventory(BatchLocationInfoQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库ID不能为空！");
        return batchInventoryQueryBL.findProductDateInventory(queryDTO);
    }

    /**
     * 查看批次库存信息
     *
     * @param batchInventoryQueryDTO
     * @return
     */
    @Override
    public PageList<BatchInventoryDTO> findBatchInventoryInfo(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO.getWarehouseId(), "仓库id不能为空");
        return batchInventoryQueryBL.findBatchInventoryInfo(batchInventoryQueryDTO);
    }

    @Override
    public Map<String, List<ProductStoreBatchChangeRecordDTO>>
        getProductStoreBatchChangeRecord(ProductStoreBatchChangeRecordQueryDTO qeryDTO) {
        Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdMap = new HashMap<>(16);
        List<ProductStoreBatchChangeRecordDTO> list =
            productStoreBatchChangeRecordBL.selectProductStoreBatchChangeRecords(qeryDTO);
        if (CollectionUtils.isEmpty(list)) {
            return ownerIdAndSecOwnerIdMap;
        }

        // 按规格id + 一级货主id + 二级货主id 进行分组
        ownerIdAndSecOwnerIdMap = list.stream().filter(Objects::nonNull).collect(Collectors
            .groupingBy(it -> it.getProductSpecificationId() + "_" + it.getOwnerId() + "_" + it.getSecOwnerId()));
        if (ownerIdAndSecOwnerIdMap == null || ownerIdAndSecOwnerIdMap.size() == 0) {
            return Collections.EMPTY_MAP;
        }

        LOGGER.info("返回出库批次库存生产日期：{}", JSON.toJSONString(ownerIdAndSecOwnerIdMap));

        return ownerIdAndSecOwnerIdMap;
    }

    public List<BatchInventoryDTO> findBatchInventoryInfoList(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO, "参数不能为空!");
        AssertUtils.notNull(batchInventoryQueryDTO.getWarehouseId(), "仓库id不能为空!");
        AssertUtils.notNull(batchInventoryQueryDTO.getCityId(), "城市id不能为空!");
        AssertUtils.notNull(batchInventoryQueryDTO.getLocationName(), "货位名称不能为空!");
        return batchInventoryQueryBL.findBatchInventoryInfoList(batchInventoryQueryDTO);
    }

    /**
     * 查看批次库存信息
     *
     * @param productStoreIds
     * @return
     */
    public Map<String, Date> findProductionDateByProductStoreIds(List<String> productStoreIds) {
        AssertUtils.notEmpty(productStoreIds, "参数不能为空!");
        return batchInventoryQueryBL.findProductionDateByProductStoreIds(productStoreIds);
    }

    /**
     * 查询销售库存
     *
     * @param queryDTOList
     * @return
     */
    @Override
    public List<OrderCenterSaleInventoryQueryResultDTO>
        findSaleInventoryList(List<OrderCenterSaleInventoryQueryDTO> queryDTOList) {
        AssertUtils.notEmpty(queryDTOList, "查询信息不能为空！");
        return batchInventoryOrderCenterBL.findSaleInventoryList(queryDTOList);
    }

    /**
     * 根据规格 和 订单号查询批次库存记录
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProductStoreBatchChangeInfoResultDTO>
        findChangeRecordInfoByOrderInfo(ProductStoreBatchChangeInfoQueryDTO queryDTO) {
        // AssertUtils.notNull(queryDTO.getOrderNo(), "订单信息不能为空！");

        return productStoreBatchChangeRecordBL.findChangeRecordInfoByOrderInfo(queryDTO);
    }

    /**
     * 查询一条为0的批次库存
     *
     * @param queryDTO
     * @return
     */
    @Override
    public BatchInventoryDTO queryZeroBatchInventory(ZeroBatchInventoryQueryDTO queryDTO) {
        return batchInventoryQueryBL.queryZeroBatchInventory(queryDTO);
    }

    /**
     * 查找生产日期
     *
     * @param productionDateQueryDTOS
     * @return
     */
    @Override
    public List<ProductionDateDTO>
        findProductionDateByCalculation(List<ProductionDateQueryDTO> productionDateQueryDTOS) {
        productionDateQueryDTOS.forEach(product -> {
            AssertUtils.notNull(product.getProductSpecificationId(), "规格id不能为空");
            AssertUtils.notNull(product.getWarehouseId(), "仓库id不能为空");
        });
        return batchInventoryQueryBL.findProductionDateByCalculation(productionDateQueryDTOS);
    }

    @Override
    public List<BatchInventoryDTO> listStoreBatchWithPromotion(BatchInventoryQueryDTO batchInventoryQueryDTO) {
        AssertUtils.notNull(batchInventoryQueryDTO, "查询参数不能为空！");
        return batchInventoryQueryBL.listStoreBatchWithPromotion(batchInventoryQueryDTO);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductDetailsQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.easysell.EasySellProductDetailsBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductChargeDTO;

/**
 * @author: lidengfeng
 * @date 2018/8/31 16:04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class EasySellProductDetailsBLTest {
    @Autowired
    private EasySellProductDetailsBL easySellProductDetailsBL;

    @Test
    public void findProductDetailsList() {
        ProductDetailsQueryDTO queryDTO = new ProductDetailsQueryDTO();
        queryDTO.setShopId(Long.valueOf(45));
        List<String> list = new ArrayList<>();
        list.add("11200003733840");
        list.add("11200004318784");
        queryDTO.setProductSpecificationIdList(list);
        queryDTO.setWarehouseId(1121);
        queryDTO.setPageNum(2);
        queryDTO.setPageSize(5);
        PageList<ProductDetailsDTO> productDetailsDTOPageList =
            easySellProductDetailsBL.findProductDetailsList(queryDTO);
        System.out.println(JSON.toJSONString(productDetailsDTOPageList));
    }

    @Test
    public void testBig() {
        ProductChargeDTO productChargeDTO = new ProductChargeDTO();
        productChargeDTO.setPackageNum(BigDecimal.valueOf(1));
        productChargeDTO.setPackageQuantity(4);
        productChargeDTO.setUnitNum(BigDecimal.valueOf(99));

        // 小单位数量转换的大单位数量
        BigDecimal divide =
            productChargeDTO.getUnitNum().divide(BigDecimal.valueOf(productChargeDTO.getPackageQuantity()));
        int intValue = divide.setScale(0, BigDecimal.ROUND_UP).intValue();
        // 获取到收费的数量
        BigDecimal bigNum = productChargeDTO.getPackageNum().add(BigDecimal.valueOf(intValue));
        productChargeDTO.setNum(bigNum);
        System.out.println(JSON.toJSONString(productChargeDTO));
    }
}
package com.yijiupi.himalaya.supplychain.inventory.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;

/**
 * <AUTHOR>
 * @date 2018-03-24
 */
@Component
public class NoteNoGenerator {
    private final DateFormat dateFormat = new SimpleDateFormat("yyMMdd");
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    private static final Logger LOG = LoggerFactory.getLogger(NoteNoGenerator.class);

    /**
     * 获取唯一Id
     *
     * @param key warehouseId
     * @param hashKey 单据类型+日期
     * @return
     * @throws BusinessException
     */
    private Long incrementHash(String key, String hashKey) throws BusinessException {
        try {
            // dela 增加量（不传采用1）
            return redisTemplate.opsForHash().increment(key, hashKey, 1);
        } catch (Exception e) {
            // redis宕机时采用uuid的方式生成唯一id
            int first = new Random(10).nextInt(8) + 1;
            int randNo = UUID.randomUUID().toString().hashCode();
            if (randNo < 0) {
                randNo = -randNo;
            }
            return Long.valueOf(first + String.format("%16d", randNo));
        }
    }
    //
    // public String generatorByType(String orderType) {
    // return generator(null, orderType);
    // }

    public String generator(Integer warehouseId, String orderType) {
        String dateStr = dateFormat.format(new Date());
        Long tmpId = incrementHash(String.format("supf:inventory:IdGenerator:%s", warehouseId), String.format("%s%s", orderType, dateStr));
        return String.format("%s%s%s%05d", StringUtils.isNotEmpty(orderType) ? orderType : "", warehouseId, dateStr,
            tmpId);
    }

    public String generatorId() {
        return generator(20, null);
    }
}

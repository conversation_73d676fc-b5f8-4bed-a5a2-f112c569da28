package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core;

import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductSkuZhaoShangBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryManageBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;

/**
 * 库存缓存BL. Created by Lifeng on 2017/7/14.
 */
@Service
public class InventoryKeyBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryKeyBL.class);

    /**
     * 库存ID缓存Key.
     */
    public static final String SKUID_TO_STOREINVENTORYID = "SkuIdToStoreInventoryId";
    /**
     * 产品信息规格ID转销售库存ID缓存key
     */
    private static final String PRODUCTSPECID_TO_SELLINVENTORYID = "ProductSpecIdToSellInventoryId";

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ProductSkuZhaoShangBL productSkuZhaoShangBL;
    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    private ProductInventoryPO getPoByCache(String cacheName, String cacheKey, Supplier<ProductInventoryPO> valSup) {
        ProductInventoryPO productInventoryPO = null;
        BoundHashOperations<String, String, String> hashOps = redisTemplate.boundHashOps(cacheName);
        String val = hashOps.get(cacheKey);
        if (StringUtils.isNotEmpty(val)) { // 如果有缓存
            // 根据val(productStore表的主键)查询productsotre
            productInventoryPO = warehouseInventoryQueryBL.selectInventoryByPrimaryKey(val);
        }
        if (productInventoryPO == null) { // 有缓存,但是没有查到. 或者 没有缓存
            productInventoryPO = valSup.get();
            if (productInventoryPO != null) {
                hashOps.put(cacheKey, productInventoryPO.getId());
            } else {
                LOG.warn("{}:{} 未命中缓存, 返回NULL", cacheName, cacheKey);
            }
        }
        return productInventoryPO;
    }
    //
    //
    // /**
    // * 1.分2批, 有缓存的skuid和无缓存的skuid.
    // * 2.有缓存的通过storeIdList查询.拿到了有缓存的主键id查不到值怎么办?
    // * 3.无缓存的正常查询.
    // */
    // private Map<Long, ProductInventoryPO> getPoByCache(String cacheName, List<Long> productSkuIdList, Integer
    // warehouseId, Integer channel, Long secOwnerId, Integer source) {
    // BoundHashOperations<String, String, String> hashOps = redisTemplate.boundHashOps(cacheName);
    // // redis中的缓存,key(productskuId+warehouseId+channel)->value(store表主键id)
    // //从redis中查到主键
    // Map<String, Long> existKeyMap = new HashMap<>(16);// storeId-> productSkuId
    // for (Long productSkuId : productSkuIdList) {
    // String cacheKey = productSkuId + "-" + warehouseId + "-" + channel;
    // String storeId = hashOps.get(cacheKey);//store表的主键id
    // if (StringUtils.isNotEmpty(storeId)) { //如果有缓存
    // existKeyMap.put(storeId, productSkuId);//后面批量查询.
    // }
    // }
    //
    // //根据id批量查询库存.
    // List<ProductInventoryPO> productInventoryPOList =
    // warehouseInventoryQueryBL.selectInventoryListByPrimaryKey(existKeyMap.keySet());
    // //把productSkuId补上
    // productInventoryPOList.forEach(n -> {
    // n.setProductSkuId(existKeyMap.get(n.getId()));
    // });
    //
    // //有缓存并且能取到库存的key
    // List<String> existStoreIdList = productInventoryPOList.stream().map(n -> n.getId()).collect(Collectors.toList());
    // //得到对应的skuId
    // ArrayList<Long> existProductSkuIdList = new ArrayList<>();
    // existStoreIdList.forEach(n -> {
    // existProductSkuIdList.add(existKeyMap.get(n));
    // });
    // //过滤以后得到需要重新去DB中查询的skuId集合.
    // List<Long> dbProductSkuIdList = productSkuIdList.stream().filter(n ->
    // !existProductSkuIdList.contains(n)).collect(Collectors.toList());
    // Map<Long, ProductInventoryPO> longProductInventoryPOMapAndCreateNoExits = warehouseInventoryManageBL.
    // getLongProductInventoryPOMapAndCreateNoExits(dbProductSkuIdList, warehouseId, channel, secOwnerId, source,
    // false);
    // //整合通过缓存和db查到的库存记录.
    // longProductInventoryPOMapAndCreateNoExits.putAll(productInventoryPOList.stream().collect(Collectors.toMap(n ->
    // n.getProductSkuId(), Function.identity())));
    // //更新缓存
    // for (Map.Entry<Long, ProductInventoryPO> po : longProductInventoryPOMapAndCreateNoExits.entrySet()) {
    // hashOps.put(po.getKey() + "-" + warehouseId + "-" + channel, po.getValue().getId());
    // }
    //// for (Long productSkuId : longProductInventoryPOMapAndCreateNoExits.keySet()) {
    //// hashOps.put(productSkuId + "-" + warehouseId + "-" + channel,
    // longProductInventoryPOMapAndCreateNoExits.get(productSkuId).getId());
    //// }
    // return longProductInventoryPOMapAndCreateNoExits;
    // }

    /**
     * 清除缓存
     */
    public void removeCache(String cacheName, Long productSkuId, Integer warehouseId, Integer channel, Long secOwnerId,
        Integer source) {
        BoundHashOperations<String, String, String> hashOps = redisTemplate.boundHashOps(cacheName);
        String key = productSkuId + "-" + warehouseId + "-" + channel + "-" + secOwnerId + "-" + source;
        hashOps.delete(key);
    }

    // /**
    // * 根据SKUID(productsku表)获取库存(productstore表)主键ID.
    // */
    // public ProductInventoryPO getStoreInventoryPO(Long productSkuId, Integer warehouseId, Integer channel, Long
    // secOwnerId, Integer source) {
    // return getStoreInventoryPO(productSkuId, warehouseId, channel, secOwnerId, source, true);
    // }
    //
    // //isThrowSkuNotExistException 是否需要抛异常.
    // public ProductInventoryPO getStoreInventoryPO(Long productSkuId, Integer warehouseId, Integer channel, Long
    // secOwnerId, Integer source, boolean isThrowSkuNotExistException) {
    // Long newProductSkuId = productSkuZhaoShangBL.getAnotherCitySkuByZhaoShangSku(productSkuId, warehouseId);
    // String key = newProductSkuId + "-" + warehouseId + "-" + channel;
    // return getPoByCache(SKUID_TO_STOREINVENTORYID, key, () -> {
    // ProductInventoryPO productInventoryPO = null;
    // try {
    // productInventoryPO = warehouseInventoryManageBL.getLongProductInventoryPOMapAndCreateNoExits(newProductSkuId,
    // warehouseId, channel, secOwnerId, source);
    // } finally {
    // if (productInventoryPO == null) {
    // //清理Rediskey
    // //SKU第一次扣库存，添加了一条ProductStore，扣库存失败，导致DB事务回滚，但是Redis没有回滚
    // removeCache(InventoryKeyBL.SKUID_TO_STOREINVENTORYID, productSkuId, warehouseId, channel, secOwnerId, source);
    // }
    // }
    // return productInventoryPO;
    // });
    // }

}

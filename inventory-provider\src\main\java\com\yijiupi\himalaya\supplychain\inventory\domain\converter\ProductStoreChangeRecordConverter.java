package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.batch.BatchProductStoreChangeRecordPO;

public class ProductStoreChangeRecordConverter {

    public static List<WarehouseInventoryChangeBO>
        productStoreChangeRecords2WarehouseInventoryChangeBOS(List<ProductStoreChangeRecordByOrderDTO> recordDTOS) {
        if (CollectionUtils.isEmpty(recordDTOS)) {
            return null;
        }
        List<WarehouseInventoryChangeBO> changeBOS = new ArrayList<>();
        for (ProductStoreChangeRecordByOrderDTO record : recordDTOS) {
            if (record.getTotalCount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            WarehouseInventoryChangeBO changeBO = new WarehouseInventoryChangeBO();
            changeBO.setCityId(record.getCityId());
            changeBO.setWarehouseId(record.getWarehouseId());
            changeBO.setOrderNo(record.getOrderNo());
            changeBO.setOrderId(record.getOrderId());
            changeBO.setOrderType(record.getOrderType());
            changeBO.setProductSkuId(record.getProductSkuId());
            changeBO.setProductSpecificationId(record.getSpecId());
            changeBO.setOwnId(record.getOwnerId());
            changeBO.setSecOwnerId(record.getSecOwnerId());
            changeBO.setOwnType(record.getOwnerType());
            changeBO.setCount(record.getTotalCount());
            changeBO.setJiupiEventType(record.getJiupiEventType());
            changeBO.setErpEventType(record.getErpEventType());
            changeBO.setDescription(record.getDes());

            changeBOS.add(changeBO);
        }
        return changeBOS;
    }

    public static List<WarehouseInventoryChangeBO> batchProductStoreChangeRecordPOS2WarehouseChangeBOS(
        List<BatchProductStoreChangeRecordPO> batchProductStoreChangeRecordPOS) {
        if (CollectionUtils.isEmpty(batchProductStoreChangeRecordPOS)) {
            return null;
        }
        List<WarehouseInventoryChangeBO> changeBOS = new ArrayList<>();
        batchProductStoreChangeRecordPOS.forEach(record -> {
            WarehouseInventoryChangeBO bo = new WarehouseInventoryChangeBO();
            bo.setCityId(record.getCityId());
            bo.setWarehouseId(record.getWarehouseId());
            bo.setOrderType(record.getOrderType());
            bo.setOrderId(record.getOrderId());
            bo.setOrderNo(record.getOrderNo());
            bo.setJiupiEventType(record.getJiupiEventType());
            bo.setErpEventType(record.getErpEventType());
            bo.setOwnType(record.getOwnerType());
            bo.setOwnId(record.getOwnerId());
            bo.setSecOwnerId(record.getSecOwnerId());
            bo.setProductSpecificationId(record.getSpecId());
            bo.setProductSkuId(record.getProductSkuId());
            bo.setSource(record.getSource());
            bo.setCount(record.getChangeCount());
            bo.setLocationId(record.getLocationId());
            bo.setLocationName(record.getLocationName());
            bo.setBatchTime(record.getBatchTime());
            bo.setProductionDate(record.getProductionDate());
            bo.setExpireTime(record.getExpireTime());
            bo.setProductStoreBatchId(record.getProductStoreBatchId());
            bo.setBatchNo(record.getBatchAttributeInfoNo());

            changeBOS.add(bo);
        });
        return changeBOS;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.DefectiveBatchInventorySyncDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.PickUpChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.HttpUtil;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.BaseResponse;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@Service
public class BatchInventorySyncAsyncBL {
    private static final Logger logger = LoggerFactory.getLogger(BatchInventorySyncAsyncBL.class);

    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;

    @Reference
    private IWarehouseQueryService warehouseQueryService;
    @Reference
    private OwnerService ownerService;

    @Transactional(rollbackFor = Exception.class)
    public boolean syncBatchInventoryByProductLocation(List<ProductStoreBatchPO> list,
        List<String> defectiveStoreBatchIdList, PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        List<String> storeIds = list.stream().map(p -> p.getProductStoreId()).collect(Collectors.toList());
        // 删除原货位库存
        // productStoreBatchMapper.deleteBatchInventoryByStoreIds(storeIds);
        // 将原货位库存数量更改为0
        batchInventoryProductStoreBatchMapper.updateBatchInventoryZeroByStoreIds(storeIds, defectiveStoreBatchIdList);
        // 新增货位库存
        batchInventoryProductStoreBatchMapper.insertBatchInventoryPOList(list);
        // 新增批次库存变更记录
        productStoreBatchChangeRecordBL.createStoreBatchChangeRecordByPickUp(pickUpChangeRecordDTO, list);

        return Boolean.TRUE;
    }

    /**
     * 仓库残次品库存同步erp入参
     *
     * @param queryDTO
     */
    @Async
    public void syncDefectiveBatchInventoryToErp(BatchInventoryQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "参数不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");

        logger.info("仓库残次品库存同步erp入参：{}", JSON.toJSONString(queryDTO));
        try {
            // 查询仓库不为0的残次品库存
            List<BatchInventoryPO> defectivePOS = getDefectiveBatchInventoryList(queryDTO);
            if (CollectionUtils.isEmpty(defectivePOS)) {
                logger.info("仓库不存在残次品库存，仓库id：{}", queryDTO.getWarehouseId());
                return;
            }
            logger.info("残次品库存查询结果数：{},仓库id：{}", defectivePOS.size(), queryDTO.getWarehouseId());
            // 组装残次品库存同步erp入参
            List<DefectiveBatchInventorySyncDTO> defectiveSyncDTOS = getDefectiveBatchInventorySyncDTOS(defectivePOS);
            // 同步erp
            syncDefectiveToERP(defectiveSyncDTOS);
            logger.info("仓库残次品库存同步erp完成，仓库ID：{}", queryDTO.getWarehouseId());
        } catch (Exception e) {
            logger.error("仓库残次品库存同步erp异常，仓库ID：{}", queryDTO.getWarehouseId(), e);
        }
    }

    public List<BatchInventoryPO> getDefectiveBatchInventoryList(BatchInventoryQueryDTO queryDTO) {
        // 查询仓库名称
        Warehouse warehouse = warehouseQueryService.findWarehouseById(queryDTO.getWarehouseId());
        AssertUtils.notNull(warehouse, "仓库id不存在: " + queryDTO.getWarehouseId());

        // 查询仓库不为0的残次品库存
        queryDTO.setCityId(warehouse.getCityId());
        queryDTO.setSubCategoryList(Arrays.asList(LocationAreaEnum.残次品区.getType(), LocationEnum.残次品位.getType()));
        queryDTO.setLimitSku((byte)1);
        queryDTO.setPageSize(1000);

        List<BatchInventoryPO> poList = new ArrayList<>();
        int pageCount = 1;
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            queryDTO.setPageNum(pageNum);

            // 查询批次库存
            PageResult<BatchInventoryPO> pageResult = batchInventoryProductStoreBatchMapper
                .findBatchInventoryList(queryDTO, queryDTO.getPageNum(), queryDTO.getPageSize());
            PageList<BatchInventoryPO> pageList = pageResult.toPageList();
            if (pageList == null || CollectionUtils.isEmpty(pageList.getDataList())) {
                continue;
            }

            if (pageNum == 1 && pageList.getPager() != null) {
                pageCount = pageList.getPager().getTotalPage();
            }

            poList.addAll(pageList.getDataList());
        }

        return poList;
    }

    public List<DefectiveBatchInventorySyncDTO>
        getDefectiveBatchInventorySyncDTOS(List<BatchInventoryPO> defectivePOS) {
        List<Long> wmsSecOwnerIds = defectivePOS.stream().filter(p -> p.getSecOwnerId() != null)
            .map(BatchInventoryPO::getSecOwnerId).distinct().collect(Collectors.toList());
        Map<Long, String> secOwnerMap = getSecOwnerMap(wmsSecOwnerIds);

        // 根据规格+货主+二级货主进行分组
        Map<String, List<BatchInventoryPO>> grouped = defectivePOS.stream().collect(Collectors
            .groupingBy(po -> po.getProductSpecificationId() + "_" + po.getOwnerId() + "_" + po.getSecOwnerId()));
        logger.info("残次品分组后库存数：{},仓库id：{}", grouped.size(), defectivePOS.stream().findFirst().get().getWarehouseId());

        List<DefectiveBatchInventorySyncDTO> defectiveSyncDTOS = new ArrayList<>();
        for (Map.Entry<String, List<BatchInventoryPO>> entry : grouped.entrySet()) {
            List<BatchInventoryPO> defectiveList = entry.getValue();
            BatchInventoryPO po = defectiveList.stream().findFirst().orElse(null);
            BigDecimal totalCount = defectiveList.stream().map(BatchInventoryPO::getStoreTotalCount)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            DefectiveBatchInventorySyncDTO defectiveDTO = new DefectiveBatchInventorySyncDTO();
            defectiveDTO.setWarehouseId(po.getWarehouseId());
            defectiveDTO.setProductSkuId(po.getProductSkuId());
            defectiveDTO.setOwnerId(po.getOwnerId());
            defectiveDTO.setSecOwnerId(po.getSecOwnerId());
            defectiveDTO.setProductSpecificationId(po.getProductSpecificationId());
            defectiveDTO.setUnitTotalCount(totalCount);
            fillErpSecOwnerId(defectiveDTO, secOwnerMap);
            defectiveSyncDTOS.add(defectiveDTO);
        }
        return defectiveSyncDTOS;
    }

    public Map<Long, String> getSecOwnerMap(List<Long> wmsSecOwnerIds) {
        if (CollectionUtils.isEmpty(wmsSecOwnerIds)) {
            return Collections.emptyMap(); // 或者返回一个空的Map
        }

        List<OwnerDTO> ownerList = ownerService.listOwnerByIds(wmsSecOwnerIds);
        logger.info("仓库二级货主查询结果：{}", JSON.toJSONString(ownerList));
        if (CollectionUtils.isEmpty(ownerList)) {
            return Collections.emptyMap();
        }

        Map<Long, String> ownerMap =
            ownerList.stream().filter(p -> p != null && StringUtils.hasText(p.getRefPartnerId()))
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getRefPartnerId(), (v1, v2) -> v1));
        return ownerMap;
    }

    public void fillErpSecOwnerId(DefectiveBatchInventorySyncDTO defectiveDTO, Map<Long, String> ownerMap) {
        if (defectiveDTO.getSecOwnerId() == null || ownerMap.isEmpty()) {
            return;
        }
        defectiveDTO.setErpSecOwnerId(ownerMap.get(defectiveDTO.getSecOwnerId()));
    }

    /**
     * 残次品数据通知erp
     *
     * @param defectiveSyncDTOS
     * 
     * @return
     */
    private void syncDefectiveToERP(List<DefectiveBatchInventorySyncDTO> defectiveSyncDTOS) {
        if (CollectionUtils.isEmpty(defectiveSyncDTOS)) {
            return;
        }
        logger.info("残次品库存数据通知erp请求: {}", JSON.toJSONString(defectiveSyncDTOS));
        BaseResponse baseResponse = HttpUtil.httpPost(
            "http://in-erp5-innerapi.yjp.com/erpinventorynotes/api/DefectiveProduct/BatchDefectiveProductSync",
            JSON.toJSONString(defectiveSyncDTOS), new TypeToken<BaseResponse>() {}.getType());
        logger.info("残次品库存数据通知erp响应: {}", JSON.toJSONString(baseResponse));
        if (baseResponse != null && !baseResponse.getSuccess()) {
            throw new BusinessValidateException(baseResponse.getMessage());
        } else if (baseResponse == null) {
            throw new BusinessValidateException("没有得到返回值");
        }
    }
}

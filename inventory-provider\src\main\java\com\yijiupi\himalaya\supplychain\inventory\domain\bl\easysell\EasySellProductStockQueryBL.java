package com.yijiupi.himalaya.supplychain.inventory.domain.bl.easysell;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.easysell.*;
import com.yijiupi.himalaya.supplychain.dto.product.ProductTotalRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductSkuPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStoreChangeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStoreRecordPOMapper;

/**
 *
 * 根据仓库id,经销商id,仓库库存查询BL.
 * 
 * @author: lidengfeng
 * @date: 2018/8/13 17:25
 */

@Service
public class EasySellProductStockQueryBL {

    private static final Logger LOG = LoggerFactory.getLogger(EasySellProductStockQueryBL.class);

    @Autowired
    private ProductStoreRecordPOMapper productStoreRecordPOMapper;

    @Autowired
    private ProductSkuPOMapper productSkuPOMapper;

    @Autowired
    private ProductStoreChangeRecordPOMapper productStoreChangeRecordPOMapper;

    /**
     * 易经销根据仓库id,经销商id查询库存
     * 
     * @param query
     * @return
     */
    public PageList<ProductStoreReturnDTO> findProductStoreList(ProductStoreQueryDTO query) {
        PageList<ProductStoreReturnDTO> pageList = new PageList<ProductStoreReturnDTO>();
        PageResult<ProductStoreReturnDTO> poList = productStoreRecordPOMapper.findProductStoreList(query);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;
    }

    /**
     * 根据商品id，经销商Id查询库存
     * 
     * @param query
     * @return
     */
    public PageList<ProductInfoStoreDTO> findProductInfoStoreList(ProductInfoStoreQueryDTO query) {
        PageList<ProductInfoStoreDTO> pageList = new PageList<ProductInfoStoreDTO>();
        PageResult<ProductInfoStoreDTO> poList = productSkuPOMapper.findProductInfoStoreList(query);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;
    }

    /**
     * 根据商品skuId、经销商、仓库编号查询库存出入库历史记录明细
     * 
     * @param
     * @return
     */
    public ProductTotalRecordDTO findProductStoreRecordList(ProductStoreChangeDetailQueryDTO query) {
        ProductTotalRecordDTO productTotalRecordDTO = new ProductTotalRecordDTO();
        // 获取 该产品的库存
        ProductDetailsQueryDTO productDetailsQueryDTO = new ProductDetailsQueryDTO();
        List<String> productSpecificationIdList = new ArrayList<>();
        productSpecificationIdList.add(String.valueOf(query.getProductSpecificationId()));
        productDetailsQueryDTO.setWarehouseId(query.getWarehouseId());
        productDetailsQueryDTO.setShopId(query.getShopId());
        productDetailsQueryDTO.setProductSpecificationIdList(productSpecificationIdList);
        // 得到库存
        PageResult<ProductDetailsDTO> productDetailsList =
            productSkuPOMapper.findProductDetailsList(productDetailsQueryDTO);
        // 设置库存
        if (productDetailsList != null && productDetailsList.size() > 0) {
            ProductDetailsDTO productDetailsDTO = productDetailsList.get(0);
            productTotalRecordDTO.setProductDetail(productDetailsDTO);

        }
        PageList<ProductStoreChangeDetailDTO> pageList = new PageList<ProductStoreChangeDetailDTO>();
        PageResult<ProductStoreChangeDetailDTO> poList =
            productStoreChangeRecordPOMapper.findProductStoreRecordList(query);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        // 设置 库存变更记录
        productTotalRecordDTO.setList(pageList);
        return productTotalRecordDTO;
    }

}

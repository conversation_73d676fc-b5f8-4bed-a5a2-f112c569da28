package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.framework.rabbit.delay.DelayMessageTemplate;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.ConvertOrderDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.ProductSkuReplaceService;
import com.yijiupi.himalaya.supplychain.instockorder.dto.communal.InStockOrderDispatchDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.configuration.InventoryMQProperties;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.SellInventoryChangeMessage;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.WMSSellInventoryChangeMessage;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.outstock.service.IOrderQueryService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionSkuInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.query.LocationProductQuery;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;

/**
 * 库存变更事件 Created by Lifeng on 2017/7/20.
 */
@Service
public class InventoryChangeEventFireBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryChangeEventFireBL.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private DelayMessageTemplate delayMessageTemplate;

    @Reference
    private IOrgService iOrgService;

    @Reference
    private IInStockQueryService inStockQueryService;

    private static final String DYY_RELATE_WAREHOUSE_ID_KEY = "dyy_relate_warehouse_id";

    @Reference
    private IVariableValueService iVariableValueService;

    private static final String MQ_ALL = "MQALL";
    private static final String MQ_PART = "MQPART";

    @Reference
    private ProductSkuReplaceService skuReplaceService;

    @Reference
    private IProductSkuQueryService productSkuQueryService;

    @Reference
    private IOrderQueryService orderService;

    @Reference
    private IProductLocationService iProductLocationService;

    /**
     * 销售库存变更事件
     */
    public void sellInventoryChangeEvent(List<SellInventoryChangeMessage> lstSellInventoryChangMessage) {
        if (CollectionUtils.isEmpty(lstSellInventoryChangMessage)) {
            return;
        }
        if (lstSellInventoryChangMessage.stream().anyMatch(p -> !Objects.equals(p.getOwnerId(), null))) {
            List<Integer> warehouseIds = lstSellInventoryChangMessage.stream()
                .map(SellInventoryChangeMessage::getWarehouseId).distinct().collect(Collectors.toList());
            VariableValueQueryDTO variableValue = new VariableValueQueryDTO();
            variableValue.setVariableKey(DYY_RELATE_WAREHOUSE_ID_KEY);
            variableValue.setWarehouseId(warehouseIds.get(0));
            VariableDefAndValueDTO variableDefAndValueDTO = iVariableValueService.detailVariable(variableValue);
            if (variableDefAndValueDTO != null && !StringUtils.isEmpty(variableDefAndValueDTO.getVariableData())) {
                int locationRelationConfig = Integer.valueOf(variableDefAndValueDTO.getVariableData());
                lstSellInventoryChangMessage.forEach(sell -> sell.setRelateWarehouseId(locationRelationConfig));
            }
        }
        // 过滤掉规格ID为空的数据
        List<SellInventoryChangeMessage> sellInventoryList = lstSellInventoryChangMessage.stream()
            .filter(e -> e != null && e.getProductSpecificationId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellInventoryList)) {
            LOG.info("销售库存变更事件 - 过滤规格ID为空数据后销售库存变更消息为空！");
            return;
        }
        Integer orgType = OrgConstant.ORG_TYPE_JIUPI.intValue();
        Optional<Integer> ownerTypeOpt =
            sellInventoryList.stream().filter(p -> p.getOwnerType() != null).map(p -> p.getOwnerType()).findAny();
        if (ownerTypeOpt.isPresent()) {
            Integer ownerType = ownerTypeOpt.get();
            orgType = getOrgTypeByOwnerType(orgType, ownerType);
        }
        // 来源大区id
        setFromOrgId(sellInventoryList);

        // 产品关联货位或货区业务类型
        fillLocationBusinessType(sellInventoryList);

        // // 查询是否店仓仓库
        // Integer warehouseId = sellInventoryList.get(0).getWarehouseId();
        // Boolean isDianCang = inStockQueryService.findOwnerInfoEraseWarehouseConfig(warehouseId);

        for (SellInventoryChangeMessage sellInventoryChangeMessage : sellInventoryList) {
            sellInventoryChangeMessage.setOriginOwnerId(sellInventoryChangeMessage.getOwnerId());
            sellInventoryChangeEvent(sellInventoryChangeMessage, orgType);
            // if (Objects.equals(true, isDianCang)) {
            // sellInventoryChangeMessage.setOwnerId(null);
            // }
            // sellInventoryChangeEvent(sellInventoryChangeMessage, Objects.equals(true, isDianCang) ?
            // OrgConstant.ORG_TYPE_JIUPI.intValue() : orgType);
        }
        // 单独发消息给OMS
        sellInventoryList = sellInventoryList.stream().filter(p -> Objects.equals(p.isSendMsgToOms(), true))
            .collect(Collectors.toList());
        for (SellInventoryChangeMessage sellInventoryChangeMessage : sellInventoryList) {
            WMSSellInventoryChangeMessage wmsSellInventoryChangeMessage = new WMSSellInventoryChangeMessage();
            BeanUtils.copyProperties(sellInventoryChangeMessage, wmsSellInventoryChangeMessage);
            wmsSellInventoryChangeMessage.setEventType(sellInventoryChangeMessage.getJiupiEventType());
            wmsSellInventoryChangeEvent(wmsSellInventoryChangeMessage);
        }
    }

    /**
     * 仓库库存变更事件
     */
    public void warehouseInventoryChangeEvent(List<WarehouseInventoryChangeBO> lstChangeMsg) {
        Integer orgType = (int)OrgConstant.ORG_TYPE_JIUPI;
        Optional<Integer> ownerTypeOpt =
            lstChangeMsg.stream().filter(p -> p.getOwnType() != null).map(p -> p.getOwnType()).findAny();
        if (ownerTypeOpt.isPresent()) {
            Integer ownerType = ownerTypeOpt.get();
            orgType = getOrgTypeByOwnerType(orgType, ownerType);
        }
        warehouseInventoryChangeEvent(lstChangeMsg, orgType);
    }

    private Integer getOrgTypeByOwnerType(Integer orgType, Integer ownerType) {
        if (Objects.equals(OwnerTypeConst.易酒批, ownerType) || Objects.equals(OwnerTypeConst.合作商, ownerType)
            || Objects.equals(OwnerTypeConst.入驻商, ownerType)) {
            orgType = (int)OrgConstant.ORG_TYPE_JIUPI;
        } else if (Objects.equals(OwnerTypeConst.知花知果, ownerType)) {
            orgType = (int)OrgConstant.ORG_TYPE_EASYGO;
        } else if (Objects.equals(OwnerTypeConst.微酒, ownerType)) {
            orgType = (int)OrgConstant.ORG_TYPE_WEIJIU;
        } else if (Objects.equals(OwnerTypeConst.易款连锁, ownerType)) {
            orgType = (int)OrgConstant.ORG_TYPE_YIKUAN;
        }
        return orgType;
    }

    /**
     * 获取来源大区id
     */
    private void setFromOrgId(List<SellInventoryChangeMessage> lstSellInventoryChangMessage) {
        Optional<Integer> cityIdOpt =
            lstSellInventoryChangMessage.stream().filter(p -> p.getCityId() != null).map(p -> p.getCityId()).findAny();
        if (cityIdOpt.isPresent()) {
            OrgDTO orgDTO = iOrgService.getOrg(cityIdOpt.get());
            if (null != orgDTO) {
                lstSellInventoryChangMessage.forEach(p -> p.setFromOrgId(orgDTO.getFromOrgId()));
            }
        }
    }

    /**
     * 销售库存变更事件
     */
    public void sellInventoryChangeEvent(SellInventoryChangeMessage sellInventoryChangeMessage, Integer ownerType) {
        // String payload = JSON.toJSONString(sellInventoryChangeMessage);
        // LOG.info("发送销售库存变更事件：{}", payload);
        // sendMqByOrgType(MQProperties.SELL_INVENTORY_CHANGE_EXCHANGE, sellInventoryChangeMessage, ownerType);
        delaySendMqByOrgType(InventoryMQProperties.SELL_INVENTORY_CHANGE_EXCHANGE, sellInventoryChangeMessage, ownerType, 30);
    }

    /**
     * 销售库存变更事件
     */
    public void wmsSellInventoryChangeEvent(WMSSellInventoryChangeMessage sellInventoryChangeMessage) {
        try {
            String payload = JSON.toJSONString(sellInventoryChangeMessage);
            LOG.info("发送WMS销售库存变动消息：{}", payload);
            // rabbitTemplate.convertAndSend(MQProperties.WMS_SELL_INVENTORY_CHANGE_EXCHANGE, null,
            // sellInventoryChangeMessage);
            delayMessageTemplate.convertAndSend(InventoryMQProperties.WMS_SELL_INVENTORY_CHANGE_EXCHANGE, null,
                sellInventoryChangeMessage, Duration.ofSeconds(30));
        } catch (Exception ex) {
            LOG.error("发送WMS销售库存变动消息失败！", ex);
        }
    }

    /**
     * 仓库库存数量变更事件
     */
    public void warehouseInventoryChangeEvent(List<WarehouseInventoryChangeBO> changeMessages, Integer ownerType) {
        String payload = JSON.toJSONString(changeMessages);
        // if (LOG.isInfoEnabled()) {
        // LOG.info("仓库库存数量变更事件：{}", payload);
        // }
        sendMqByOrgType(InventoryMQProperties.WAREHOUSE_INVENTORY_CHANGE_EXCHANGE, changeMessages, ownerType);
    }

    /**
     * 仓库库存变更事件
     */
    public void storeInventoryChangeEvent(List<ProductInventoryChangeRecordPO> storeInventoryChangeMessages) {
        // String payload = JSON.toJSONString(storeInventoryChangeMessages);
        // if (LOG.isInfoEnabled()) {
        // LOG.info("发送仓库批次库存变更事件：{}", payload);
        // }
        rabbitTemplate.convertAndSend(InventoryMQProperties.STORE_INVENTORY_CHANGE_EXCHANGE, null, storeInventoryChangeMessages);
    }

    /**
     * 入库单库存分配事件
     *
     * @param dispatchDTO
     */
    public void dispathRatioEvent(InStockOrderDispatchDTO dispatchDTO) {
        LOG.info("ErpSyncListener 发送库存分配事件， exchange : {}, 参数：{}", InventoryMQProperties.ADD_INSTOCKORDER_RATIOCONVERT_EXCHANGE,
            JSON.toJSONString(dispatchDTO));
        rabbitTemplate.convertAndSend(InventoryMQProperties.ADD_INSTOCKORDER_RATIOCONVERT_EXCHANGE, null, dispatchDTO);
    }

    public void sendMqByOrg(String exchange, Object obj, Integer orgId) {
        try {
            OrgDTO orgDTO = iOrgService.getOrg(orgId);
            sendMqByOrgType(exchange, obj, orgDTO.getFromOrgType().intValue());
        } catch (Exception ex) {
            LOG.error(exchange, ex);
        }
    }

    public void sendMqByOrgType(String exchange, Object obj, Integer orgType) {
        try {
            LOG.info(exchange + "-" + orgType + "：" + JSON.toJSONString(obj));
            rabbitTemplate.convertAndSend(exchange, null, obj, getMessagePostProcessor(orgType));
        } catch (Exception ex) {
            LOG.error(exchange, ex);
        }
    }

    /**
     * @param fromOrgType 来源机构类型：1一酒批城市, 2一知花知果大区, 3=易款便利
     */
    private MessagePostProcessor getMessagePostProcessor(Integer fromOrgType) {
        MessagePostProcessor mp = message -> {
            message.getMessageProperties().setHeader(MQ_ALL, MQ_ALL);
            message.getMessageProperties().setHeader(MQ_PART + "-" + fromOrgType, MQ_PART + "-" + fromOrgType);
            return message;
        };
        return mp;
    }

    /**
     * 触发转移单
     */
    public void sendAddConvertOrderEvent(ConvertOrderDTO convertOrderDTO) {
        LOG.info("发送转换单新增消息事件:{}", JSON.toJSONString(convertOrderDTO));

        rabbitTemplate.convertAndSend(InventoryMQProperties.CONVERTORDER_ADD_EXCHANGE, null, convertOrderDTO);
    }

    /**
     * 发送订单明细变更消息
     * 
     * @param warehouseChangeList
     */
    public void syncOrderDetails(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return;
        }
        LOG.info("发送订单明细变更消息事件:{}", JSON.toJSONString(warehouseChangeList));

        rabbitTemplate.convertAndSend(InventoryMQProperties.OMS_STOCKORDER_INVENTORY_CHANGE_EXCHANGE, null, warehouseChangeList);
    }

    /**
     * 库存转移到标记的replaceSku
     */
    @Async
    public void storeTransferToReplaceSku(List<ProductInventoryPO> inventoryPOS) {
        List<ProductInventoryPO> addInventoryList =
            inventoryPOS.stream().filter(inventory -> inventory.getChangeCount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(addInventoryList)) {
            return;
        }

        List<ProductSkuDTO> fromSkuList = new ArrayList<>();
        addInventoryList.forEach(inventory -> {
            if (inventory.getProductSpecificationId() == null || inventory.getCityId() == null
                || inventory.getWarehouseId() == null) {
                return;
            }
            ProductSkuDTO fromSku = new ProductSkuDTO();
            fromSku.setProductSpecificationId(inventory.getProductSpecificationId());
            fromSku.setCompany_Id(inventory.getOwnerId());
            fromSku.setCityId(inventory.getCityId());
            fromSku.setWarehouseId(inventory.getWarehouseId());
            fromSkuList.add(fromSku);
        });

        if (CollectionUtils.isEmpty(fromSkuList)) {
            return;
        }
        // 如果有sku含有Replace_To_Sku_Id字段，则进行库存转移
        if (productSkuQueryService.isHaveReplaceToSku(fromSkuList)) {
            try {
                Thread.sleep(3000);
                skuReplaceService.batchStoreTransferToReplaceSku(fromSkuList);
            } catch (InterruptedException ex) {
            }
        }
    }

    public void delaySendMqByOrgType(String exchange, Object obj, Integer orgType, long delaySeconds) {
        try {
            LOG.info(exchange + "-" + "delay：" + "-" + orgType + JSON.toJSONString(obj));
            delayMessageTemplate.convertAndSend(exchange, null, getMessagePostProcessorMap(orgType), obj,
                Duration.ofSeconds(delaySeconds));
        } catch (Exception ex) {
            LOG.error(exchange, ex);
        }
    }

    /**
     * @param fromOrgType 来源机构类型：1一酒批城市, 2一知花知果大区, 3=易款便利
     */
    private Map<String, Object> getMessagePostProcessorMap(Integer fromOrgType) {
        Map<String, Object> headers = new HashMap<>(16);
        headers.put(MQ_ALL, MQ_ALL);
        headers.put(MQ_PART + "-" + fromOrgType, MQ_PART + "-" + fromOrgType);
        return headers;
    }

    private void fillLocationBusinessType(List<SellInventoryChangeMessage> sellInventoryList) {
        if (CollectionUtils.isEmpty(sellInventoryList)) {
            return;
        }

        LOG.info("填充产品对应货位业务类型 入参：{}", JSON.toJSONString(sellInventoryList));
        try {
            SellInventoryChangeMessage sellInventory = sellInventoryList.stream()
                .filter(p -> p != null && p.getWarehouseId() != null).findFirst().orElse(null);
            if (Objects.isNull(sellInventory)) {
                return;
            }

            Integer warehouseId = sellInventory.getWarehouseId();
            // 查询产品对应关联货位或货区业务类型
            List<LocationProductQuery.ProductParam> productParams = new ArrayList<>();
            sellInventoryList.stream().forEach(p -> {
                LocationProductQuery.ProductParam productParam = new LocationProductQuery.ProductParam();
                productParam.setSpecificationId(p.getProductSpecificationId());
                productParam.setOwnerId(p.getOwnerId());
                productParams.add(productParam);
            });

            LocationProductQuery queryDTO = new LocationProductQuery();
            queryDTO.setWarehouseId(warehouseId);
            queryDTO.setProductParams(productParams);
            Map<String, Set<Byte>> businessTypeMap = iProductLocationService.findProductLocationByCondition(queryDTO)
                .stream().filter(p -> p != null && p.getProductSpecificationId() != null && p.getBusinessType() != null)
                .collect(Collectors.groupingBy(
                    p -> String.format("%s-%s-%s", p.getWarehouseId(), p.getProductSpecificationId(), p.getCompanyId()),
                    Collectors.mapping(ProductLoactionSkuInfoDTO::getBusinessType, Collectors.toSet())));
            LOG.info("填充产品对应货位业务类型 查询结果：{}", JSON.toJSONString(businessTypeMap));
            if (businessTypeMap == null || businessTypeMap.size() <= 0) {
                return;
            }

            sellInventoryList.forEach(changeMessage -> {
                if (businessTypeMap.containsKey(getSkuSign(changeMessage))) {
                    Byte locationBusinessType = businessTypeMap.get(getSkuSign(changeMessage)).stream()
                        .filter(p -> p != null).findFirst().orElse(null);
                    if (locationBusinessType != null) {
                        changeMessage.setLocationBusinessType(locationBusinessType.intValue());
                    }
                }
            });
            LOG.info("销售库存变动消息填充产品对应货位业务类型完成！");
        } catch (Exception ex) {
            LOG.error("销售库存变动消息填充产品对应货位业务类型失败！", ex);
        }
    }

    public String getSkuSign(SellInventoryChangeMessage changeMessage) {
        return String.format("%s-%s-%s", changeMessage.getWarehouseId(), changeMessage.getProductSpecificationId(),
            changeMessage.getOwnerId());
    }

}

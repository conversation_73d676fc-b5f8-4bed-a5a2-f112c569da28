package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class BatchNOProcessBO implements Serializable {

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 关联信息ID
     */
    private Long refInfoId;

    /**
     * 产品skuId
     */
    private Long productSkuId;

    /**
     * 产品规格ID
     */
    private Long productSpecificationId;

    /**
     * 批次时间【入库时间】
     */
    private Date batchTime;
    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 货主
     */
    private Integer ownerType;

    /**
     * 货主ID
     */
    private Long ownerId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 来源渠道
     */
    private Integer channel = 0;

    /**
     * 产品库存不存在时是否创建库存,默认true true - 创建 false - 不创建
     */
    private Boolean createInventory = true;

    private List<BatchNOProcessItemBO> processItemBOList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getRefInfoId() {
        return refInfoId;
    }

    public void setRefInfoId(Long refInfoId) {
        this.refInfoId = refInfoId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Boolean getCreateInventory() {
        return createInventory;
    }

    public void setCreateInventory(Boolean createInventory) {
        this.createInventory = createInventory;
    }

    public String getWarehouseIdAndChannelAndOwnerId() {
        return String.format("%s_%s_%s_%s", getWarehouseId(), getChannel(), getOwnerId(), getSecOwnerId());
    }

    public List<BatchNOProcessItemBO> getProcessItemBOList() {
        return processItemBOList;
    }

    public void setProcessItemBOList(List<BatchNOProcessItemBO> processItemBOList) {
        this.processItemBOList = processItemBOList;
    }
}

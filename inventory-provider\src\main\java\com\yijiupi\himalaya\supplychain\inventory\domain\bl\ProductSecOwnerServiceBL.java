package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSecOwnerIdQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSpecAndOwnerIdDTO;

/**
 * 二级货主处理类
 */
@Service
public class ProductSecOwnerServiceBL {

    private static final Logger LOG = LoggerFactory.getLogger(ProductSecOwnerServiceBL.class);

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;

    public Map<String, List<Long>> getSecOwnerMap(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO) {
        Map<String, List<Long>> result = new HashMap<>(16);

        List<ProductSpecAndOwnerIdDTO> lstNoOwnerSpecs = new ArrayList<>();
        // 从Redis获取缓存，获取不到的，后边从DB获取
        try {
            for (ProductSpecAndOwnerIdDTO specInfo : productSecOwnerIdQueryDTO.getSpecAndOwnerIds()) {
                String redisKey = String.format("%s-%s-%s", productSecOwnerIdQueryDTO.getWarehouseId(),
                    specInfo.getProductSpecId(), specInfo.getOwnerId());
                List<Long> lstTmp = getSecOwnerIdMapByRedis(redisKey);
                if (lstTmp == null || CollectionUtils.isEmpty(lstTmp)) {
                    lstNoOwnerSpecs.add(specInfo);
                } else {
                    String resultKey = String.format("%s-%s", specInfo.getProductSpecId(), specInfo.getOwnerId());
                    result.put(resultKey, lstTmp);
                }
            }
            // if (!result.isEmpty()) {
            // LOG.info("根据规格+货主查二级货主Redis，参数：{}，结果：{}", JSON.toJSONString(productSecOwnerIdQueryDTO),
            // JSON.toJSONString(result));
            // }
        } catch (Exception exception) {
            LOG.error("从Redis加载数据失败：" + exception.getMessage(), exception);
        }
        // Redis没取到值的，从DB获取并更新缓存
        if (CollectionUtils.isNotEmpty(lstNoOwnerSpecs)) {
            try {
                // 从DB中获取结果
                ProductSecOwnerIdQueryDTO noResultQueryDto = new ProductSecOwnerIdQueryDTO();
                noResultQueryDto.setWarehouseId(productSecOwnerIdQueryDTO.getWarehouseId());
                noResultQueryDto.setSpecAndOwnerIds(lstNoOwnerSpecs);
                Map<String, List<Long>> secOwnerIdMapByDB = getSecOwnerIdMapByDB(noResultQueryDto);
                LOG.info("根据规格+货主查二级货主DB，参数：{}，结果：{}", JSON.toJSONString(productSecOwnerIdQueryDTO),
                    JSON.toJSONString(secOwnerIdMapByDB));

                secOwnerIdMapByDB.forEach((key, value) -> {
                    if (!result.containsKey(key)) {
                        result.put(key, value);
                    }
                });

                // 处理DB中无结果的，加空缓存，避免再次查DB
                List<Long> lstEmptyResult = new ArrayList<>();
                lstEmptyResult.add(null);

                lstNoOwnerSpecs.forEach(ownerIdDTO -> {
                    String tmpResultKey =
                        String.format("%s-%s", ownerIdDTO.getProductSpecId(), ownerIdDTO.getOwnerId());
                    if (!result.containsKey(tmpResultKey)) {
                        result.put(tmpResultKey, lstEmptyResult);

                        // 结果存Redis
                        String key = String.format("%s-%s-%s", productSecOwnerIdQueryDTO.getWarehouseId(),
                            ownerIdDTO.getProductSpecId(), ownerIdDTO.getOwnerId());
                        String redisKey = String.format(SEC_OWNER_REDIS_KEY, key);
                        redisTemplate.opsForValue().set(redisKey, lstEmptyResult, 5, TimeUnit.DAYS);
                    }
                });
            } catch (Exception exception) {
                LOG.error("从DB加载数据失败：" + exception.getMessage(), exception);
            }
        }
        return result;
    }

    /**
     * redis 缓存key
     */
    public static final String SEC_OWNER_REDIS_KEY = "supc:ProductSecondOwner:%s";

    /**
     * 从Redis中 根据仓库+规格+货主查找对应的二级货主
     *
     * @return
     */
    public List<Long> getSecOwnerIdMapByRedis(String key) {
        String redisKey = String.format(SEC_OWNER_REDIS_KEY, key);
        Object objValue = redisTemplate.opsForValue().get(redisKey);
        // LOG.info(String.format("查Redis，Key：%s,mapKey:%s,content:%s", redisKey, key, JSON.toJSONString(objValue)));
        return objValue == null ? new ArrayList<>() : castList(objValue, Long.class);
    }

    public <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>)obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

    /**
     * 从数据库中 根据仓库+规格+货主查找对应的二级货主
     *
     * @return
     */
    public Map<String, List<Long>> getSecOwnerIdMapByDB(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO) {
        List<ProductInventoryPO> productInventoryPOS =
            productInventoryPOMapper.listProductSecOwnerId(productSecOwnerIdQueryDTO);
        if (CollectionUtils.isEmpty(productInventoryPOS)) {
            return Collections.EMPTY_MAP;
        }
        return processSecOwnerIdRelateInfo(productInventoryPOS);
    }

    /**
     * 根据库存，处理二级货主
     *
     * @param productInventoryPOList
     * @return
     */
    public Map<String, List<Long>> processSecOwnerIdRelateInfo(List<ProductInventoryPO> productInventoryPOList) {
        Map<String, List<Long>> result = new HashMap<>(16);
        try {
            Map<String, List<ProductInventoryPO>> lstTmpMap =
                productInventoryPOList.stream().collect(Collectors.groupingBy(
                    p -> String.format("%s-%s-%s", p.getWarehouseId(), p.getProductSpecificationId(), p.getOwnerId())));
            lstTmpMap.forEach((key, list) -> {
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                List<Long> lstSecOwnerIds =
                    list.stream().map(p -> p.getSecOwnerId()).distinct().collect(Collectors.toList());

                // 返回值
                ProductInventoryPO inventoryPO = list.get(0);
                String tmpKey =
                    String.format("%s-%s", inventoryPO.getProductSpecificationId(), inventoryPO.getOwnerId());
                result.put(tmpKey, lstSecOwnerIds);

                // 结果存Redis
                String redisKey = String.format(SEC_OWNER_REDIS_KEY, key);
                // LOG.info(String.format("存Redis，Key：%s,mapKey:%s,content:%s", redisKey, tmpKey,
                // JSON.toJSONString(lstSecOwnerIds)));
                redisTemplate.opsForValue().set(redisKey, lstSecOwnerIds, 5, TimeUnit.DAYS);
            });
        } catch (Exception exception) {
            LOG.error("存Redis失败：" + exception.getMessage(), exception);
        }
        return result;
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.enums.CheckDiffEnum;
import com.yijiupi.himalaya.supplychain.enums.CheckStateEnum;
import com.yijiupi.himalaya.supplychain.enums.StoreTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.UuidGenerator;

/**
 * @author: lidengfeng
 * @date 2018/8/31 16:04
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class InventorySyncRecordBLTest {

    @Autowired
    private InventorySyncRecordBL inventorySyncRecordBL;

    @Test
    public void saveTest() {
        List<InventorySyncRecordDTO> inventorySyncRecordDTOS = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            InventorySyncRecordDTO inventorySyncRecordDTO = new InventorySyncRecordDTO();
            inventorySyncRecordDTO.setId(UuidGenerator.generatorLongId());
            inventorySyncRecordDTO.setOrgId(999);
            inventorySyncRecordDTO.setWarehouseId(9991);
            inventorySyncRecordDTO.setTmsDeliveryedCount(BigDecimal.valueOf(1));
            inventorySyncRecordDTO.setWmsDeliveryedCount(BigDecimal.valueOf(2));
            inventorySyncRecordDTO.setErpRealCount(BigDecimal.valueOf(3));
            inventorySyncRecordDTO.setStoreCountMinUnit(BigDecimal.valueOf(1));
            inventorySyncRecordDTO.setDiffTotalCount(BigDecimal.valueOf(13));
            inventorySyncRecordDTO.setDiffMaxCount(BigDecimal.valueOf(2));
            inventorySyncRecordDTO.setDiffMinCount(BigDecimal.valueOf(1));
            inventorySyncRecordDTO.setProductSkuId(Long.valueOf("12346"));
            inventorySyncRecordDTO.setProductSpecificationId(123L);
            inventorySyncRecordDTO.setProductName("测试产品");
            inventorySyncRecordDTO.setSpecName("6瓶/盒");
            inventorySyncRecordDTO.setPackageQuantity(BigDecimal.valueOf(6));
            inventorySyncRecordDTO.setOwnerId(null);
            inventorySyncRecordDTO.setStoreType(StoreTypeEnum.仓库库存.getType());
            inventorySyncRecordDTO.setCreateTime(new Date());
            inventorySyncRecordDTO.setCreateUserId(1);
            if (inventorySyncRecordDTO.getDiffTotalCount().compareTo(BigDecimal.ZERO) != 0) {
                inventorySyncRecordDTO.setDiff(CheckDiffEnum.有差异.getType());
                inventorySyncRecordDTO.setState(CheckStateEnum.未处理.getType());
            } else {
                inventorySyncRecordDTO.setDiff(CheckDiffEnum.无差异.getType());
                inventorySyncRecordDTO.setState(CheckStateEnum.不需要处理.getType());
            }
            inventorySyncRecordDTOS.add(inventorySyncRecordDTO);
        }
        inventorySyncRecordBL.saveInventorySyncRecordList(inventorySyncRecordDTOS);
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeTemplateRelationMapper">
    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        AttributeId,
        AttributeName,
        AttributeType,
        EffectiveDigit,
        IsRequired,
        Template_id,
        IsCalculation,
        IsStoreCheck

    </sql>
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplateRelationPO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="AttributeId" property="attributeId" jdbcType="VARCHAR"/>
        <result column="AttributeName" property="attributeName" jdbcType="VARCHAR"/>
        <result column="AttributeType" property="attributeType" jdbcType="TINYINT"/>
        <result column="EffectiveDigit" property="effectiveDigit" jdbcType="INTEGER"/>
        <result column="IsRequired" property="required" jdbcType="TINYINT"/>
        <result column="Template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="IsCalculation" property="isCalculation" jdbcType="TINYINT"/>
        <result column="IsStoreCheck" property="isStoreCheck" jdbcType="TINYINT"/>
    </resultMap>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="batchAttributeTemplateRelationPO.id">
        INSERT INTO batchAttributeTemplateRelation (
        id,
        AttributeId,
        AttributeName,
        AttributeType,
        EffectiveDigit,
        IsRequired,
        Template_id,
        IsCalculation,
        IsStoreCheck
        ) VALUES (
        #{batchAttributeTemplateRelationPO.id,jdbcType=BIGINT},
        #{batchAttributeTemplateRelationPO.attributeId,jdbcType=BIGINT},
        #{batchAttributeTemplateRelationPO.attributeName,jdbcType=VARCHAR},
        #{batchAttributeTemplateRelationPO.attributeType,jdbcType=TINYINT},
        #{batchAttributeTemplateRelationPO.effectiveDigit,jdbcType=INTEGER},
        #{batchAttributeTemplateRelationPO.required,jdbcType=TINYINT},
        #{batchAttributeTemplateRelationPO.templateId,jdbcType=BIGINT},
        #{batchAttributeTemplateRelationPO.isCalculation,jdbcType=TINYINT},
        #{batchAttributeTemplateRelationPO.isStoreCheck,jdbcType=TINYINT}
        )
    </insert>

    <!--auto generated Code-->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="batchAttributeTemplateRelationPO.id">
        INSERT INTO batchAttributeTemplateRelation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchAttributeTemplateRelationPO.id!=null">id,</if>
            <if test="batchAttributeTemplateRelationPO.attributeId!=null">AttributeId,</if>
            <if test="batchAttributeTemplateRelationPO.attributeName!=null">AttributeName,</if>
            <if test="batchAttributeTemplateRelationPO.attributeType!=null">AttributeType,</if>
            <if test="batchAttributeTemplateRelationPO.effectiveDigit!=null">EffectiveDigit,</if>
            <if test="batchAttributeTemplateRelationPO.required!=null">IsRequired,</if>
            <if test="batchAttributeTemplateRelationPO.templateId!=null">Template_id,</if>
            <if test="batchAttributeTemplateRelationPO.isCalculation!=null">IsCalculation,</if>
            <if test="batchAttributeTemplateRelationPO.isStoreCheck!=null">IsStoreCheck,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchAttributeTemplateRelationPO.id!=null">#{batchAttributeTemplateRelationPO.id,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.attributeId!=null">
                #{batchAttributeTemplateRelationPO.attributeId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.attributeName!=null">
                #{batchAttributeTemplateRelationPO.attributeName,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeTemplateRelationPO.attributeType!=null">
                #{batchAttributeTemplateRelationPO.attributeType,jdbcType=TINYINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.effectiveDigit!=null">
                #{batchAttributeTemplateRelationPO.effectiveDigit,jdbcType=INTEGER},
            </if>
            <if test="batchAttributeTemplateRelationPO.required!=null">
                #{batchAttributeTemplateRelationPO.required,jdbcType=TINYINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.templateId!=null">
                #{batchAttributeTemplateRelationPO.templateId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.isCalculation!=null">
                #{batchAttributeTemplateRelationPO.isCalculation,jdbcType=INTEGER},
            </if>
            <if test="batchAttributeTemplateRelationPO.isStoreCheck!=null">
                #{batchAttributeTemplateRelationPO.isStoreCheck,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchAttributeTemplateRelation (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="batchAttributeTemplateRelationPOs" item="batchAttributeTemplateRelationPO" index="index"
                 separator=",">
            (
            #{batchAttributeTemplateRelationPO.id,jdbcType=BIGINT},
            #{batchAttributeTemplateRelationPO.attributeId,jdbcType=BIGINT},
            #{batchAttributeTemplateRelationPO.attributeName,jdbcType=VARCHAR},
            #{batchAttributeTemplateRelationPO.attributeType,jdbcType=TINYINT},
            #{batchAttributeTemplateRelationPO.effectiveDigit,jdbcType=INTEGER},
            #{batchAttributeTemplateRelationPO.required,jdbcType=TINYINT},
            #{batchAttributeTemplateRelationPO.templateId,jdbcType=BIGINT},
            #{batchAttributeTemplateRelationPO.isCalculation,jdbcType=TINYINT},
            #{batchAttributeTemplateRelationPO.isStoreCheck,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE batchAttributeTemplateRelation
        <set>
            <if test="batchAttributeTemplateRelationPO.id != null">id=
                #{batchAttributeTemplateRelationPO.id,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.attributeId != null">AttributeId=
                #{batchAttributeTemplateRelationPO.attributeId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.attributeName != null">AttributeName=
                #{batchAttributeTemplateRelationPO.attributeName,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeTemplateRelationPO.attributeType != null">AttributeType=
                #{batchAttributeTemplateRelationPO.attributeType,jdbcType=TINYINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.effectiveDigit != null">EffectiveDigit=
                #{batchAttributeTemplateRelationPO.effectiveDigit,jdbcType=INTEGER},
            </if>
            <if test="batchAttributeTemplateRelationPO.required != null">IsRequired=
                #{batchAttributeTemplateRelationPO.required,jdbcType=TINYINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.templateId != null">Template_id=
                #{batchAttributeTemplateRelationPO.templateId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.isCalculation != null">
                IsCalculation = #{batchAttributeTemplateRelationPO.isCalculation,jdbcType=TINYINT},
            </if>
            <if test="batchAttributeTemplateRelationPO.isStoreCheck != null">
                IsStoreCheck = #{batchAttributeTemplateRelationPO.isStoreCheck,jdbcType=TINYINT},
            </if>
        </set>
        WHERE id = #{batchAttributeTemplateRelationPO.id,jdbcType=BIGINT}
    </update>

    <!--auto generated by codehelper on 2018-04-10 10:50:25-->
    <delete id="deleteByTemplateId">
        delete from batchattributetemplaterelation
        where Template_id=#{templateId,jdbcType=BIGINT}
    </delete>

    <select id="findBatchAttributeTemplateRelation"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationReturnDTO">
        SELECT
        batr.id,
        batr.AttributeId as dicId,
        batr.AttributeName as attributeName,
        batr.AttributeType as attributeType,
        batr.EffectiveDigit as effectiveDigit,
        batr.IsRequired as required,
        batr.Template_id as templateId,
        batr.IsCalculation as isCalculation,
        batr.IsStoreCheck as isStoreCheck
        FROM
        batchattributerulerelation barr
        INNER JOIN batchattributerule bar ON bar.id = barr.RuleId
        INNER JOIN batchattributetemplate bat ON bat.id = bar.Template_id
        INNER JOIN batchattributetemplaterelation batr ON batr.Template_id = bat.id
        INNER JOIN batchattributedic bad ON bad.id = batr.AttributeId
        WHERE
        barr.AttributeValue_Id = #{dto.warehouseId}
    </select>
    <select id="findBatchAttributeEnable"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationReturnDTO">
        SELECT
        btr.AttributeId as dicId,
        btr.AttributeName as attributeName,
        btr.AttributeType as attributeType,
        btr.EffectiveDigit as effectiveDigit,
        btr.IsRequired as required,
        btr.IsCalculation as isCalculation,
        btr.IsStoreCheck as isStoreCheck,
        bd.AttributeValue as attributeValue
        from batchattributerulerelation b
        INNER JOIN
        (SELECT brr.RuleId from batchattributerulerelation brr
        INNER JOIN batchattributerule br on br.id = brr.RuleId and brr.AttributeValue_Id = #{dto.warehouseId}) a
        on b.RuleId = a.RuleId
        and b.AttributeValueName= #{dto.category}
        INNER JOIN batchattributerule br2 on br2.id = b.RuleId
        INNER JOIN batchattributetemplate bt on bt.id = br2.Template_id
        INNER JOIN batchattributetemplaterelation btr on bt.id = btr.Template_id
        INNER JOIN batchattributedic bd on bd.id = btr.AttributeId
        where bd.IsEnable = 1
        and bt.IsEnable =1
    </select>

    <select id="findAttributeTemplateByRuleTypeAndValueId"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRuleTypeQueryDTO"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationReturnDTO">
        SELECT
        btr.AttributeId as dicId,
        btr.AttributeName as attributeName,
        btr.AttributeType as attributeType,
        btr.EffectiveDigit as effectiveDigit,
        btr.IsRequired as required,
        btr.IsCalculation as isCalculation,
        btr.IsStoreCheck as isStoreCheck,
        bd.AttributeValue as attributeValue
        from batchattributerulerelation brr
        INNER JOIN batchattributerule br on brr.RuleId = br.id
        INNER JOIN batchattributetemplate bt on bt.id = br.Template_id
        INNER JOIN batchattributetemplaterelation btr on bt.id = btr.Template_id
        INNER JOIN batchattributedic bd on bd.id = btr.AttributeId
        where bd.IsEnable = 1
        and bt.IsEnable = 1
        and brr.AttributeValue_Id = #{attributeValueId,jdbcType=VARCHAR}
        and brr.RuleType = #{ruleType,jdbcType=TINYINT}
    </select>

    <select id="findAttributeTemplateValue"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueQueryDTO"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationReturnDTO">
        SELECT
        btr.AttributeId as dicId,
        btr.AttributeName as attributeName,
        btr.AttributeType as attributeType,
        btr.EffectiveDigit as effectiveDigit,
        btr.IsRequired as required,
        btr.IsCalculation as isCalculation,
        btr.IsStoreCheck as isStoreCheck,
        bi.AttributeValue_Id as dicAttributeValueId,
        bi.AttributeValueName as dicAttributeValueName,
        bi.BatchAttributeInfoNo as batchAttributeInfoNo,
        bi.LastUpdateTime as lastUpdateTime
        from batchattributerulerelation brr
        INNER JOIN batchattributerule br on brr.RuleId = br.id
        INNER JOIN batchattributetemplate bt on bt.id = br.Template_id
        INNER JOIN batchattributetemplaterelation btr on bt.id = btr.Template_id
        INNER JOIN batchattributedic bd on bd.id = btr.AttributeId
        INNER JOIN batchattributeinfo bi.Attribute_Id = bd.id
        where bd.IsEnable = 1
        and bt.IsEnable = 1
        and brr.AttributeValue_Id = #{attributeValueId,jdbcType=VARCHAR}
        and brr.RuleType = #{ruleType,jdbcType=TINYINT}
        and bi.BatchAttributeInfoNo in
        <foreach collection="batchNoList" item="batchNo" open="(" close=")" separator=",">
            #{batchNo,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>


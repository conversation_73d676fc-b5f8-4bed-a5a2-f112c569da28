package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch;

/**
 * <AUTHOR> 2018/4/11
 */
public class BatchLocationInfoQueryPO {
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 产品ID
     */
    private Long productSkuId;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 分配规则
     */
    private Byte assignRule;
    /**
     * 货位分配规则
     */
    private Byte locationAssignRule;
    /**
     * 货位分配类型
     */
    private Byte locationAssignType;
    /**
     * 货位分配类型限制
     */
    private Byte locationAssignLimitType;

    public String getGroupKey() {
        return String.format("%s_%s_%s_%s", this.getProductSkuId(), this.getWarehouseId(), this.getChannel(),
            this.getSource());
    }

    /**
     * 获取 仓库ID
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库ID
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品ID
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 产品ID
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 分配规则
     */
    public Byte getAssignRule() {
        return this.assignRule;
    }

    /**
     * 设置 分配规则
     */
    public void setAssignRule(Byte assignRule) {
        this.assignRule = assignRule;
    }

    /**
     * 货位分配规则
     */
    public Byte getLocationAssignRule() {
        return this.locationAssignRule;
    }

    /**
     * 货位分配规则
     */
    public void setLocationAssignRule(Byte locationAssignRule) {
        this.locationAssignRule = locationAssignRule;
    }

    /**
     * 获取 货位分配类型
     */
    public Byte getLocationAssignType() {
        return this.locationAssignType;
    }

    /**
     * 设置 货位分配类型
     */
    public void setLocationAssignType(Byte locationAssignType) {
        this.locationAssignType = locationAssignType;
    }

    /**
     * 获取 货位分配类型限制
     */
    public Byte getLocationAssignLimitType() {
        return this.locationAssignLimitType;
    }

    /**
     * 设置 货位分配类型限制
     */
    public void setLocationAssignLimitType(Byte locationAssignLimitType) {
        this.locationAssignLimitType = locationAssignLimitType;
    }
}

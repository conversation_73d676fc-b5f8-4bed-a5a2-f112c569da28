package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ApplyProductStorageAgeDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductStorePOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStorePO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockApplyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockItemApplyDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.apply.OutStockItemApplyDetailDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyStateEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockApplyTypeEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductCategoryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/16
 */
@Component
public class ApplyProductStorageAgeConvertor {

    @Reference
    private IAdminUserQueryService iAdminUserQueryService;
    @Reference
    private IProductCategoryService iProductCategoryService;

    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private ProductStorePOMapper productStorePOMapper;

    private static final String OA_APPLY_NAME = "残次品库龄延长申请";
    private static final String DAY = "天";

    public OutStockApplyDTO convertOutStockApplyDTO(ApplyProductStorageAgeDTO dto, ProductSkuDTO productSkuDTO,
        ProductSkuConfigDTO configDTO, ProductStoreBatchPO productStoreBatchPO) {

        ProductStorePO productStorePO =
            productStorePOMapper.getProductStoreById(productStoreBatchPO.getProductStoreId());

        Integer cityId = dto.getOrgId();
        Integer warehouseId = dto.getWarehouseId();

        List<OutStockItemApplyDetailDTO> detailDTOS = new ArrayList<>();
        OutStockItemApplyDetailDTO detailDTO = new OutStockItemApplyDetailDTO();
        detailDTO.setOrgId(cityId);
        detailDTO.setProductSpecificationId(productStorePO.getProductSpecificationId());
        detailDTO.setOwnerId(productStorePO.getOwnerId());
        detailDTO.setSecOwnerId(productStorePO.getSecOwnerId());
        detailDTO.setProductionDate(productStoreBatchPO.getProductionDate());
        detailDTO.setBatchAttributeInfoNo(productStoreBatchPO.getBatchAttributeInfoNo());
        detailDTO.setBatchTime(productStoreBatchPO.getBatchTime());
        detailDTO.setLocationId(productStoreBatchPO.getLocationId());
        detailDTO.setLocationName(productStoreBatchPO.getLocationName());
        detailDTO.setUnitTotalCount(productStoreBatchPO.getTotalCount());
        detailDTOS.add(detailDTO);

        List<OutStockItemApplyDTO> outStockItemApplyDTOS = new ArrayList<>();
        OutStockItemApplyDTO outStockItemApplyDTO = new OutStockItemApplyDTO();
        outStockItemApplyDTO.setOrgId(cityId);
        outStockItemApplyDTO.setWarehouseId(warehouseId);
        outStockItemApplyDTO.setProductSkuId(configDTO.getProductSkuId());
        outStockItemApplyDTO.setProductName(productSkuDTO.getName());
        outStockItemApplyDTO.setProductBrand(productSkuDTO.getProductBrand());
        ProductCategoryDTO categoryDTO =
            iProductCategoryService.getProductCategoryById(productSkuDTO.getProductInfoCategoryId());
        if (Objects.nonNull(categoryDTO)) {
            outStockItemApplyDTO.setCategoryName(categoryDTO.getStatisticsClassName());
        }
        outStockItemApplyDTO.setOwnerId(productStorePO.getOwnerId());
        outStockItemApplyDTO.setSecOwnerId(productStorePO.getSecOwnerId());
        // outStockItemApplyDTO.setSecOwnerName();
        outStockItemApplyDTO.setProductSpecificationId(productSkuDTO.getProductSpecificationId());
        outStockItemApplyDTO.setSpecName(productSkuDTO.getSpecificationName());
        outStockItemApplyDTO.setSpecQuantity(productSkuDTO.getPackageQuantity());
        outStockItemApplyDTO.setPackageName(productSkuDTO.getPackageName());
        outStockItemApplyDTO.setUnitName(productSkuDTO.getUnitName());
        BigDecimal totalCount = productStoreBatchPO.getTotalCount();
        BigDecimal[] count = totalCount.divideAndRemainder(productSkuDTO.getPackageQuantity());
        outStockItemApplyDTO.setPackageCount(count[0]);
        outStockItemApplyDTO.setUnitCount(count[1]);
        outStockItemApplyDTO.setUnitTotalCount(totalCount);
        outStockItemApplyDTO.setOverUnitTotalCount(BigDecimal.ZERO);
        outStockItemApplyDTO.setIsDefective(Boolean.TRUE);
        outStockItemApplyDTO.setPrice(configDTO.getSellingPrice());
        outStockItemApplyDTO.setPriceUnit(configDTO.getSellingPriceUnit());
        outStockItemApplyDTO
            .setSource(productStorePO.getSource() != null ? productStorePO.getSource().byteValue() : (byte)0);
        outStockItemApplyDTO
            .setChannel(productStorePO.getChannel() != null ? productStorePO.getChannel().byteValue() : (byte)0);
        outStockItemApplyDTO.setIsGift((byte)0);
        // 只能出残次品
        outStockItemApplyDTO.setIsDefective(true);
        // item的remark存放json
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("currentStorageAge", dto.getCurrentStorageAge());
        jsonObject.put("applyStorageAge", dto.getApplyStorageAge());
        outStockItemApplyDTO.setRemark(jsonObject.toJSONString());

        // 填充具体货位生产日期
        outStockItemApplyDTO.setOutStockItemApplyDetailDTOS(detailDTOS);
        outStockItemApplyDTOS.add(outStockItemApplyDTO);

        String userName = iAdminUserQueryService.queryUserNameById(dto.getOptUserId().longValue());
        Integer userId = dto.getOptUserId();
        OutStockApplyDTO outStockApplyDTO = new OutStockApplyDTO();
        outStockApplyDTO.setOrgId(cityId);
        outStockApplyDTO.setWarehouseId(warehouseId);
        outStockApplyDTO.setOrderType(OutStockApplyTypeEnum.库龄审核.getType());
        // outStockApplyDTO.setBusinessType();
        outStockApplyDTO.setState(OutStockApplyStateEnum.待审核.getType());
        outStockApplyDTO.setOverPackageAmount(BigDecimal.ZERO);
        outStockApplyDTO.setOverUnitAmount(BigDecimal.ZERO);
        outStockApplyDTO.setApplyUserName(userName);
        outStockApplyDTO.setApplyUserId(userId);
        outStockApplyDTO.setUserName(userName);
        outStockApplyDTO.setUserId(userId);
        outStockApplyDTO.setRemark(dto.getReason());
        outStockApplyDTO.setOutStockItemApplyDTOS(outStockItemApplyDTOS);

        return outStockApplyDTO;
    }

}

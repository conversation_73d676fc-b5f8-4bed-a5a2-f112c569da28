package com.yijiupi.himalaya.supplychain.inventory.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.common.constant.RedisConstant;
import com.yijiupi.himalaya.supplychain.inventory.domain.bo.ReturnOrderProductionDateCacheBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bo.ReturnOrderProductionDateTotalCacheBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ReturnOrderProductionDateTotalCacheBOConvertor;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.ReturnOrderProductDateQueryDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
@Component
public class ReturnOrderProductionDateCacheBL {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    private static final String ReturnOrderProductionDate = RedisConstant.SUP_P + ":ReturnOrderProductionDate:";
    private static final Logger LOGGER = LoggerFactory.getLogger(ReturnOrderProductionDateCacheBL.class);
    private static final Long TIME_M = 2 * 24L;

    public void putProductionCache(String key, ReturnOrderProductDateItemDTO value) {
        String result = JSON.toJSONString(value);
        redisTemplate.boundValueOps(key).set(result, TIME_M, TimeUnit.HOURS);
    }

    public void getProductionCache(String key) {
        redisTemplate.boundValueOps(key).get();
    }

    public ReturnOrderProductionDateTotalCacheBO getProductionCache(ReturnOrderProductDateQueryDTO queryDTO) {
        List<ReturnOrderProductionDateCacheBO> productionBOList =
                ReturnOrderProductionDateTotalCacheBOConvertor.convert(queryDTO);
        for (ReturnOrderProductionDateCacheBO cacheBO : productionBOList) {
            String result = redisTemplate.boundValueOps(cacheBO.getKey()).get();
            if (StringUtils.isNotEmpty(result)) {
                try {
                    ReturnOrderProductDateItemDTO returnOrderProductDateItemDTO =
                            JSON.parseObject(result, ReturnOrderProductDateItemDTO.class);
                    cacheBO.setItemDTO(returnOrderProductDateItemDTO);
                } catch (Exception e) {
                    LOGGER.warn("装换失败", e);
                }
            }
        }

        List<ReturnOrderProductionDateCacheBO> cacheBOList =
                productionBOList.stream().filter(m -> Objects.nonNull(m.getItemDTO())).collect(Collectors.toList());

        List<ReturnOrderProductionDateCacheBO> notCacheBOList =
                productionBOList.stream().filter(m -> Objects.isNull(m.getItemDTO())).collect(Collectors.toList());

        ReturnOrderProductionDateTotalCacheBO totalCacheBO = new ReturnOrderProductionDateTotalCacheBO();
        totalCacheBO.setCacheBOList(cacheBOList);
        totalCacheBO.setNotCacheBOList(notCacheBOList);

        return totalCacheBO;
    }

    public static String getProductionDateKey() {
        return "";
    }
}

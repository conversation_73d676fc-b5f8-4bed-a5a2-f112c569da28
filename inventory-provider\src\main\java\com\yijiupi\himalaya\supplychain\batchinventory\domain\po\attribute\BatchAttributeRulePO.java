package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

/**
 * 批属性配置管理(同一品牌或者类目不允许添加多条配置)
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeRulePO {
    /**
     * id
     */
    private Long id;
    /**
     * 模板表主键id
     */
    private Long templateId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 获取 id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 模板名称
     */
    public String getTemplateName() {
        return this.templateName;
    }

    /**
     * 设置 模板名称
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 模板表主键id
     */
    public Long getTemplateId() {
        return this.templateId;
    }

    /**
     * 设置 模板表主键id
     */
    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;

/**
 * <AUTHOR> 2018/4/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class, properties = {"dubbo.port=40021"})
public class InventoryOrderBizServiceImplTest {
    //
    // @Autowired
    // private InventoryOrderBizServiceImpl inventoryOrderBizService;
    //
    // @Test
    // public void batchDeliver() throws Exception {
    // String json =
    // "[{\"cityId\":999,\"deliveryMode\":0,\"fromCityId\":999,\"id\":999118062916301812,\"inventoryOptKey\":\"999118062916301812\",\"items\":[{\"buyCount\":5,\"deliverCount\":0,\"productSkuId\":99900068060965,\"saleSpecQuantity\":1,\"takeCount\":5}],\"jiupiOrderType\":0,\"orderId\":99918062916087612,\"orderNo\":\"9998180000112\",\"warehouseId\":9991}]";
    //
    // List<InventoryDeliveryJiupiOrder> inventoryDeliveryJiupiOrders =
    // JSON.parseArray(json, InventoryDeliveryJiupiOrder.class);
    //
    // inventoryOrderBizService.batchDeliver(inventoryDeliveryJiupiOrders);
    // }
    //
    // @Test
    // public void deliveryDelay() throws Exception {
    // // InventoryOrderDTO dto = new InventoryOrderDTO();
    // // dto.setor
    //
    // // inventoryOrderBizService.deliveryDelay(Collections.singletonList(dto));
    // }
    //
    // // 入库
    // @Test
    // public void inStockComplete() throws Exception {
    // ArrayList<Long> list = new ArrayList<>();
    // list.add(9991805151100179L);
    // // list.add(9991805101107964L);
    // inventoryOrderBizService.inStockComplete(list);
    // }
    //
    // @Test
    // public void affirmInStock() throws Exception {
    // String json =
    // "{\"carId\":96771695892762403,\"carName\":\"王然测试车辆A\",\"deliveryUserId\":10832,\"deliveryUserName\":\"小二测试\",\"orderList\":[{\"deliveryMarkState\":3,\"deliveryOrderType\":0,\"fromCityId\":999,\"id\":9991805151100164,\"items\":[{\"channel\":0,\"id\":9991805151100165,\"orderId\":9991805151100164,\"returnCount\":1,\"skuId\":99900000475210,\"source\":0,\"specQuantity\":6}],\"orderCreateTime\":1526353972000,\"orgId\":999,\"receiptState\":-1,\"refOrderNo\":\"************\"}],\"orgId\":999,\"returnPackageCount\":0,\"returnUnitCount\":0,\"stevedoreUserId\":147,\"stevedoreUserName\":\"胡开阳（研发）\",\"stockInTime\":1526616784548,\"taskNumber\":\"DD201805170020\",\"turnoutTime\":1526526880000,\"warehouseId\":9991}";
    // InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO =
    // JSON.parseObject(json, InventoryInStockOrderBatchDTO.class);
    //
    // inventoryOrderBizService.affirmInStock(inventoryInStockOrderBatchDTO);
    // }
    //
    // @Test
    // public void recallDeliver() throws Exception {
    // String json = "[9991805181402570, 9991805181402565, 9991805181402560, 9991805181302555]";
    // List<Long> list = JSON.parseArray(json, Long.class);
    // inventoryOrderBizService.recallDeliver(list);
    // }
    //
    // @Test
    // public void ziTiDeliver() throws Exception {
    // String json = "[9991806101602504]";
    // List<Long> list = JSON.parseArray(json, Long.class);
    // inventoryOrderBizService.ziTiDeliver(list, 7395);
    // }

}
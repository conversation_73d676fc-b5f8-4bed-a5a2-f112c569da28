package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.SellInventoryChangeMessage;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.SellInventoryChangeMessageDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryChangeEventFireService;

@Service(timeout = 60000)
public class InventoryChangeEventFireServiceImpl implements IInventoryChangeEventFireService {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryChangeEventFireServiceImpl.class);

    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;

    @Override
    public void sellInventoryChangeEvent(List<SellInventoryChangeMessageDTO> sellInventoryChangMessage) {
        if (CollectionUtils.isEmpty(sellInventoryChangMessage)) {
            LOG.info("销售库存处理消息为空！");
            return;
        }
        List<SellInventoryChangeMessage> changMessage =
            sellInventoryChangMessage.stream().filter(e -> e != null).map(e -> {
                SellInventoryChangeMessage message = new SellInventoryChangeMessage();
                BeanUtils.copyProperties(e, message);
                return message;
            }).collect(Collectors.toList());
        inventoryChangeEventFireBL.sellInventoryChangeEvent(changMessage);
    }
}

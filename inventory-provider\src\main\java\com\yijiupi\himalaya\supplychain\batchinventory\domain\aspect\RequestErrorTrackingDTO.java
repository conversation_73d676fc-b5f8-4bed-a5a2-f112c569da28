package com.yijiupi.himalaya.supplychain.batchinventory.domain.aspect;

import java.io.Serializable;

public class RequestErrorTrackingDTO implements Serializable {
    private static final long serialVersionUID = 5496395660819112088L;
    /**
     * 产品线
     */
    private Byte productLine;
    /**
     * 请求类型
     */
    private String requestType;
    /**
     * 请求内容
     */
    private String requestContent;
    /**
     * 响应内容
     */
    private String responseContent;
    /**
     * 备注
     */
    private String remo;
    /**
     * key
     */
    private String requestTypeKey;

    public Byte getProductLine() {
        return productLine;
    }

    public void setProductLine(Byte productLine) {
        this.productLine = productLine;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getRequestContent() {
        return requestContent;
    }

    public void setRequestContent(String requestContent) {
        this.requestContent = requestContent;
    }

    public String getResponseContent() {
        return responseContent;
    }

    public void setResponseContent(String responseContent) {
        this.responseContent = responseContent;
    }

    public String getRemo() {
        return remo;
    }

    public void setRemo(String remo) {
        this.remo = remo;
    }

    @Override
    public String toString() {
        return "RequestErrorTrackingDTO [productLine=" + productLine + ", requestType=" + requestType
            + ", requestContent=" + requestContent + ", responseContent=" + responseContent + ", remo=" + remo
            + ", requestTypeKey=" + requestTypeKey + "]";
    }
}

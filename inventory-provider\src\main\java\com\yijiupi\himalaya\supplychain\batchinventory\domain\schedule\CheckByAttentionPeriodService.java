package com.yijiupi.himalaya.supplychain.batchinventory.domain.schedule;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.assignment.dto.todo.ProductionDateTaskDTO;
import com.yijiupi.himalaya.assignment.enums.todo.TodoTaskBusinessType;
import com.yijiupi.himalaya.assignment.service.IProductionDateTaskService;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.ProductionDateAuditBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditQuery;
import com.yijiupi.himalaya.supplychain.batchinventory.enums.ProductionDateAuditState;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckParam;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.CategoryPeriodConfigTypeEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.InStockAlarmEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.enums.WarehouseClassEnum;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 创建关注期产品治理任务
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Service
@SuppressWarnings("NonAsciiCharacters")
public class CheckByAttentionPeriodService {

    @Resource
    private ProductionDateAuditBL productionDateAuditBL;

    @Reference(timeout = 60000)
    private IWarehouseQueryService warehouseQueryService;

    @Reference(timeout = 120000)
    private IInStockConfigService iInStockConfigService;

    @Reference(timeout = 60000)
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Reference(timeout = 60000)
    private IProductionDateTaskService iProductionDateTaskService;

    private static final Logger logger = LoggerFactory.getLogger(CheckByAttentionPeriodService.class);

    private static final List<Integer> EXCLUDE_WAREHOUSE_ID_List =
        Arrays.asList(1411, 4741, 7421, 1341, 7461, 4401, 1391, 4461, 4591, 4331, 1421, 7431, 1531, 4481, 7481, 1471,
            7251, 5002, 1351, 7501, 1301, 1381, 1431, 1481, 4361, 7271, 4501, 1461, 7291, 1451, 1511, 1401, 7381, 9981,
            1331, 4551, 7471, 4161, 4071, 4311, 1371, 1541, 7411, 7491, 4371, 7311, 7401, 1281, 7321, 7301, 7281, 4541,
            7441, 1171, 7451, 1335, 4115, 1361, 7351, 4111, 1189, 4451, 4381, 1551, 1441, 1181, 1001, 1121, 1201, 1261,
            1611, 1651, 1681, 1721, 1741, 1851, 2091, 4031, 4091, 4601, 4811, 4871, 4971, 5031, 7001, 7041, 7061, 7161,
            9000602, 9000591, 9000601, 9000551, 9000483, 9000612, 9000598, 9000634, 9000755, 9000803);

    /**
     * 每天执行一次
     */
    @XxlJob("checkProductByAttentionPeriod")
    public void checkProductByAttentionPeriod() {
        logger.info("[创建关注期产品治理任务]开始");
        List<Warehouse> warehouseList = new ArrayList<>();
        String warehouseIdStr = XxlJobContext.getXxlJobContext().getJobParam();
        if (StringUtils.isNotEmpty(warehouseIdStr) && !Objects.equals(warehouseIdStr, "{}")) {
            List<Integer> warehouseIdList = Arrays.stream(warehouseIdStr.split("、"))
                .filter(p -> !StringUtils.isEmpty(p) && StringUtils.isNumeric(p)).map(p -> Integer.valueOf(p))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(warehouseIdList)) {
                warehouseList = warehouseQueryService.listWarehouseByIds(warehouseIdList);
            }
        } else {
            // 已启用仓库
            // 城市仓库((byte) 0),总部仓库((byte) 1)
            warehouseList = warehouseQueryService.listEnableWarehouseByTypes(Arrays.asList(0, 1)).stream()
                .filter(p -> p != null && p.getCityId() != null && p.getCityId() >= 100 && p.getCityId() <= 900
                    && !Objects.equals(WarehouseClassEnum.虚仓.getType(), p.getWarehouseClass())
                    && !EXCLUDE_WAREHOUSE_ID_List.contains(p.getId()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(warehouseList)) {
            logger.info("[创建关注期产品治理任务]没有查询到任何仓库");
            return;
        }

        for (Warehouse warehouseElem : warehouseList) {
            try {
                checkByAttentionPeriod(warehouseElem);
            } catch (Exception ex) {
                logger.error("[创建关注期产品治理任务]仓库名：{}，仓库ID：{}，获取仓库关注期产品数据异常", warehouseElem.getName(),
                    warehouseElem.getId(), ex);
            }
        }
        logger.info("[创建关注期产品治理任务]结束");
    }

    /**
     * 获取单个仓库的关注期产品数据
     */
    private void checkByAttentionPeriod(Warehouse warehouseElem) {
        Integer warehouseId = warehouseElem.getId();
        // 1.查询仓库所有sku（有库存、有生产日期）
        List<Long> skuIdList = iBatchInventoryQueryService.listSkuIdByBatchInventory(warehouseId);
        if (CollectionUtils.isEmpty(skuIdList)) {
            logger.info("[创建关注期产品治理任务]仓库名：{}，仓库ID：{}，没有有库存有生产日期的产品", warehouseElem.getName(), warehouseId);
            return;
        }
        logger.info("[创建关注期产品治理任务]仓库名：{}，仓库ID：{}，有库存有生产日期的产品个数：{}", warehouseElem.getName(), warehouseId,
            skuIdList.size());

        // 2.根据类目关注期配置，查询仓库的关注期产品数量
        List<OutStockConfigCheckResultDTO> checkResults = new ArrayList<>();
        List<List<Long>> skuIdsPart = Lists.partition(skuIdList, 2000);
        for (List<Long> skuIdListElem : skuIdsPart) {
            List<OutStockConfigCheckDTO> checkProductDTOList = new ArrayList<>();
            skuIdListElem.forEach(skuIdElem -> {
                OutStockConfigCheckDTO checkElem = new OutStockConfigCheckDTO();
                checkElem.setSkuId(skuIdElem);
                checkElem.setChannel(ProductChannelType.JIUPI);
                checkElem.setSource(ProductSourceType.易酒批);
                checkProductDTOList.add(checkElem);
            });

            OutStockConfigCheckParam configCheckDTO = new OutStockConfigCheckParam();
            configCheckDTO.setWarehouseId(warehouseId);
            configCheckDTO.setCheckProductDTOList(checkProductDTOList);
            configCheckDTO.setExcludeSubcategoryList(
                Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue()));
            configCheckDTO.setCheckAllStoreFlag(true);
            configCheckDTO.setCategoryPeriodConfigType(CategoryPeriodConfigTypeEnum.ATTENTION_PERIOD.getValue());
            List<OutStockConfigCheckResultDTO> results =
                iInStockConfigService.checkByCategoryPeriodConfigType(configCheckDTO).stream()
                    .filter(elem -> elem != null && Objects.equals(elem.getAlarm(), InStockAlarmEnum.入库禁止.getType())
                        && elem.getStorageAttribute() != null)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(results)) {
                continue;
            }
            checkResults.addAll(results);
        }

        if (CollectionUtils.isEmpty(checkResults)) {
            logger.info("[创建关注期产品治理任务]仓库名：{}，仓库ID：{}，仓库无无关注期产品", warehouseElem.getName(), warehouseId);
            return;
        }
        // 生产日期治理任务白名单
        Set<String> whitelist = queryProductionDateTaskWhitelist(warehouseId);
        // 3.通知创建生产日期治理任务
        List<ProductionDateTaskDTO> taskDTOS = checkResults.stream().map(p -> {
            ProductionDateTaskDTO taskDTO = new ProductionDateTaskDTO();
            taskDTO.setWarehouseId(warehouseId);
            taskDTO.setSkuId(p.getSkuId());
            taskDTO.setProductName(p.getProductName());
            taskDTO.setProductionDate(p.getProductTimeStr());
            taskDTO.setTaskProperty(p.getStorageAttribute().intValue());
            taskDTO.setInWhitelist(isInWhitelist(whitelist, p));
            taskDTO.setStoreBatchId(p.getStoreBatchId());
            taskDTO.setBusinessType(TodoTaskBusinessType.PROMOTION.getByte());
            return taskDTO;
        }).collect(Collectors.toList());
        logger.info("[创建关注期产品治理任务]生产日期治理任务创建参数：{}", JSON.toJSONString(taskDTOS));
        iProductionDateTaskService.createTask(taskDTOS);
    }

    /**
     * 过滤出不在白名单内的数据
     *
     * @param whitelist 白名单
     * @param checkResult 入库配置检查结果
     * @return 是否需要创建任务
     */
    private boolean isInWhitelist(Set<String> whitelist, OutStockConfigCheckResultDTO checkResult) {
        Long skuId = checkResult.getSkuId();
        String productionDate = DateUtils.getDateFormat(checkResult.getProductTime());
        return whitelist.contains(String.format("%s-%s", skuId, productionDate));
    }

    private Set<String> queryProductionDateTaskWhitelist(Integer warehouseId) {
        // 审核中或者审核通过的产品, 该批次日期不再产生新的生产日期任务, 也不会产生逾期罚款
        ProductionDateAuditQuery query =
            ProductionDateAuditQuery.of(warehouseId, ProductionDateAuditState.AUDITING_AND_PASSED);
        return productionDateAuditBL.listProductionDateAudit(query).stream().map(this::getGroupKey)
            .collect(Collectors.toSet());
    }

    private String getGroupKey(ProductionDateAuditDTO dto) {
        return String.format("%s-%s", dto.getSkuId(), dto.getProductionDate());
    }

}

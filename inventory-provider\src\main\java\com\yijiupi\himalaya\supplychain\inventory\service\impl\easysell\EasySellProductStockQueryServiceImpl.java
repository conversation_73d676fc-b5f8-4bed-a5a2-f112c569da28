package com.yijiupi.himalaya.supplychain.inventory.service.impl.easysell;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.easysell.*;
import com.yijiupi.himalaya.supplychain.dto.product.ProductTotalRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.easysell.EasySellProductStockQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.service.easysell.IEasySellProductStockQueryService;

/**
 * 易经销查询库存
 * 
 * @author: lidengfeng
 * @date: 2018/8/13 17:16
 */

@Service
public class EasySellProductStockQueryServiceImpl implements IEasySellProductStockQueryService {

    @Autowired
    private EasySellProductStockQueryBL easySellProductStockQueryBL;

    /**
     * 根据仓库id,经销商id查询仓库
     * 
     * @param productStoreQueryDTO
     * @return
     */
    @Override
    public PageList<ProductStoreReturnDTO> findProductStoreList(ProductStoreQueryDTO productStoreQueryDTO) {
        AssertUtils.notNull(productStoreQueryDTO.getWarehouseId(), "仓库不能为空");
        return easySellProductStockQueryBL.findProductStoreList(productStoreQueryDTO);
    }

    /**
     * 根据商品skuid，经销商id查询仓库
     * 
     * @param productInfoStoreQueryDTO
     * @return
     */
    @Override
    public PageList<ProductInfoStoreDTO> findProductInfoStoreList(ProductInfoStoreQueryDTO productInfoStoreQueryDTO) {
        AssertUtils.notNull(productInfoStoreQueryDTO.getShopId(), "经销商id不能为空");
        PageList<ProductInfoStoreDTO> productInfoStockDTOPageList =
            easySellProductStockQueryBL.findProductInfoStoreList(productInfoStoreQueryDTO);
        return productInfoStockDTOPageList;
    }

    /**
     * 根据商品skuId，仓库id,经销商id查询商品明细
     * 
     * @param productStoreChangeDetailQueryDTO
     * @return
     */
    @Override
    public ProductTotalRecordDTO
        findProductStoreRecordList(ProductStoreChangeDetailQueryDTO productStoreChangeDetailQueryDTO) {
        AssertUtils.notNull(productStoreChangeDetailQueryDTO.getShopId(), "经销商Id不能为空");
        AssertUtils.notNull(productStoreChangeDetailQueryDTO.getWarehouseId(), "仓库id不能为空");
        ProductTotalRecordDTO productTotalRecordDTO =
            easySellProductStockQueryBL.findProductStoreRecordList(productStoreChangeDetailQueryDTO);
        return productTotalRecordDTO;
    }

}

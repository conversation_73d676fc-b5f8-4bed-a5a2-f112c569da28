package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute;

import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchAttributeDicConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeDicMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeDicPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicQueryDTO;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 自定义批属性管理(字典)
 *
 * <AUTHOR> 2018/4/9
 */
@Service
public class BatchAttributeDicBL {

    @Autowired
    private BatchAttributeDicMapper batchAttributeDicMapper;

    /**
     * 新增一条字典记录
     *
     * @param batchAttributeDicDTO
     */
    public void addBatchAttributeDic(BatchAttributeDicDTO batchAttributeDicDTO) {
        BatchAttributeDicPO batchAttributeDicPO =
            BatchAttributeDicConvert.BatchAttributeDicDTO2PO(batchAttributeDicDTO);
        batchAttributeDicPO.setId(UUIDGenerator.getUUID(batchAttributeDicPO.getClass().getName()));
        batchAttributeDicMapper.insert(batchAttributeDicPO);
    }

    /**
     * 停用,启用
     *
     * @param batchAttributeDicDTO
     */
    public void updateState(BatchAttributeDicDTO batchAttributeDicDTO) {
        batchAttributeDicMapper.updateIsEnable(batchAttributeDicDTO);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    public int deleteById(Long id) {
        return batchAttributeDicMapper.deleteById(id);
    }

    /**
     * 列表
     *
     * @param batchAttributeDicQueryDTO
     * @return
     */
    public PageList<BatchAttributeDicDTO>
        findBatchAttributeDicList(BatchAttributeDicQueryDTO batchAttributeDicQueryDTO) {
        PageResult<BatchAttributeDicDTO> batchAttributeDicDTOS = batchAttributeDicMapper.findBatchAttributeDicList(
            batchAttributeDicQueryDTO, batchAttributeDicQueryDTO.getPageNum(), batchAttributeDicQueryDTO.getPageSize());
        return batchAttributeDicDTOS.toPageList();
    }

    /**
     * 修改
     * 
     * @param batchAttributeDicDTO
     */
    public void updateBatchAttributeDic(BatchAttributeDicDTO batchAttributeDicDTO) {
        batchAttributeDicMapper.update(batchAttributeDicDTO);
    }
}

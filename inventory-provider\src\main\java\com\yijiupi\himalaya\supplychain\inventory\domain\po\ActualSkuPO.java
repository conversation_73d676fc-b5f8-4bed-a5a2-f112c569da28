package com.yijiupi.himalaya.supplychain.inventory.domain.po;

/**
 * <AUTHOR> 2018/3/26
 */
public class ActualSkuPO {
    /**
     * 下单skuid
     */
    private Long orderSkuId;
    /**
     * 实际发货城市skuid
     */
    private Long actualDeliverySkuId;

    /**
     * 产品状态 下架(0), 作废(1), 上架(2)
     */
    private Integer productState;

    public Integer getProductState() {
        return productState;
    }

    public void setProductState(Integer productState) {
        this.productState = productState;
    }

    /**
     * 获取 下单skuid
     *
     * @return orderSkuId 下单skuid
     */
    public Long getOrderSkuId() {
        return this.orderSkuId;
    }

    /**
     * 设置 下单skuid
     *
     * @param orderSkuId 下单skuid
     */
    public void setOrderSkuId(Long orderSkuId) {
        this.orderSkuId = orderSkuId;
    }

    /**
     * 获取 实际发货城市skuid
     *
     * @return actualDeliverySkuId 实际发货城市skuid
     */
    public Long getActualDeliverySkuId() {
        return this.actualDeliverySkuId;
    }

    /**
     * 设置 实际发货城市skuid
     *
     * @param actualDeliverySkuId 实际发货城市skuid
     */
    public void setActualDeliverySkuId(Long actualDeliverySkuId) {
        this.actualDeliverySkuId = actualDeliverySkuId;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.BatchInBoundChangeInventoryBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.ChangeInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IBatchInBoundChangeInventoryService;

/**
 * <AUTHOR> 批次入库 变更库存
 */
@Service(timeout = 30000)
public class IBatchInBoundChangeInventoryServiceImpl implements IBatchInBoundChangeInventoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IBatchInBoundChangeInventoryServiceImpl.class);

    @Autowired
    private BatchInBoundChangeInventoryBL batchInBoundChangeInventory;

    @Override
    public void batchAffirmInBoundAddInventory(ChangeInventoryDTO changeInventoryDTO) {
        LOGGER.info("批次确认入库增加库存 IBatchInBoundChangeInventoryServiceImpl.batchAffirmInBoundAddInventory "
            + "changeInventoryDTO={}", JSON.toJSONString(changeInventoryDTO));
        batchInBoundChangeInventory.batchAffirmInBoundAddInventory(changeInventoryDTO);
    }
}

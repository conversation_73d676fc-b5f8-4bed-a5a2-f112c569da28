package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.search.PagerCondition;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.erp.ERPStoreVO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventorySyncRecordBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryCheckBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseSaleInventoryCalculationBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.check.CheckStoreInventoryByCityInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.WareHouseDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.DisposedProductInventorDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;

/**
 * 库存对账
 * 
 * <AUTHOR>
 * @date 2019/1/8 15:21
 */
@Service(timeout = 300000)
public class WarehouseInventoryCheckServiceImpl implements IWarehouseInventoryCheckService {
    @Autowired
    private WarehouseInventoryCheckBL warehouseInventoryCheckBL;
    @Autowired
    private InventorySyncRecordBL inventorySyncRecordBL;
    @Reference
    private IStoreWareHouseService iStoreWareHouseService;
    @Autowired
    private WarehouseSaleInventoryCalculationBL saleInventoryCalculationBL;

    @Override
    public boolean isShopWarehouse(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return warehouseInventoryCheckBL.isShopWarehouse(warehouseId);
    }

    /**
     * 查询销售库存
     */
    @Override
    public List<WarehouseStoreDTO> getSaleInventoryList(ProductStoreQueryDTO productStoreQueryDTO) {
        AssertUtils.notNull(productStoreQueryDTO, "查询销售库存参数不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getWarehouseId(), "仓库id不能为空");
        return warehouseInventoryCheckBL.getSaleInventoryList(productStoreQueryDTO);
    }

    /**
     * 查询仓库库存
     */
    @Override
    public List<WarehouseStoreDTO> getInventoryList(ProductStoreQueryDTO productStoreQueryDTO) {
        AssertUtils.notNull(productStoreQueryDTO, "查询销售库存参数不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getWarehouseId(), "仓库id不能为空");
        return warehouseInventoryCheckBL.getInventoryList(productStoreQueryDTO);
    }

    /**
     * 查询仓库库存
     *
     * @param productStoreQueryDTO
     * @return
     */
    @Override
    public PageList<WarehouseStoreDTO> getWarehouseInventoryList(WarehouseProductStoreQueryDTO productStoreQueryDTO) {
        AssertUtils.notNull(productStoreQueryDTO, "查询销售库存参数不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getWarehouseId(), "仓库id不能为空");
        if (productStoreQueryDTO.getPageSize() != null && productStoreQueryDTO.getPageSize() > 1000) {
            productStoreQueryDTO.setPageSize(5000);
        }

        return warehouseInventoryCheckBL.getWarehouseInventoryList(productStoreQueryDTO);
    }

    /**
     * 根据城市id，查询仓库库存
     */
    @Override
    public List<WarehouseStoreDTO> getInventoryListByCityId(Integer cityId) {
        AssertUtils.notNull(cityId, "城市id不能为空");
        return warehouseInventoryCheckBL.getInventoryListByCityId(cityId);
    }

    /**
     * 校正销售库存（指定产品）
     */
    @Override
    public void checkSellInventory(List<Long> productSkuIds, Integer warehouseId, Integer cityId, Integer opUserId) {
        warehouseInventoryCheckBL.checkSellInventory(productSkuIds, warehouseId, cityId, opUserId);
    }

    /**
     * 获取处理品及陈列品库存（指定产品）
     */
    @Override
    public BigDecimal getDisposeProductCountBySkuId(Integer cityId, Integer warehouseId, Long productSkuId) {
        return warehouseInventoryCheckBL.getDisposeProductCountBySkuId(cityId, warehouseId, productSkuId);
    }

    /**
     * 获取处理品及陈列品库存（指定产品）
     */
    @Override
    public Map<Long, BigDecimal> getBatchDisposeProductCountBySkuId(Integer cityId, Integer warehouseId,
        List<Long> productSkuIdList, Map<Long, List<ProductSkuDTO>> relationMap) {
        return warehouseInventoryCheckBL.getBatchDisposeProductCountBySkuId(cityId, warehouseId, productSkuIdList,
            relationMap);
    }

    /**
     * 获取处理品及陈列品库存（指定产品）详情信息
     */
    @Override
    public List<DisposedProductInventorDTO> findDisposedProductInventorBySkuId(Integer cityId, Integer warehouseId,
        List<Long> productSkuIdList) {
        return warehouseInventoryCheckBL.findDisposedProductInventorBySkuId(cityId, warehouseId, productSkuIdList);
    }

    /**
     * 根据城市id，库存对账
     */
    @Override
    public void checkStoreInventoryByCityId(Integer cityId, Integer opUserId, boolean isSnap) {
        checkStoreInventoryByCityInfo(new CheckStoreInventoryByCityInfoDTO(cityId, opUserId, isSnap,
            CheckStoreInventoryByCityInfoDTO.OLD_VERSION));
    }

    @Override
    public void checkStoreInventoryByCityInfo(CheckStoreInventoryByCityInfoDTO dto) {
        AssertUtils.notNull(dto.getCityId(), "城市信息不能为空！");

        if (null == dto.getCityId() || -1 == dto.getCityId()) {
            WareHouseDTO wareHouseDTO = new WareHouseDTO();
            PagerCondition pager = new PagerCondition();
            pager.setPageSize(Integer.MAX_VALUE);
            pager.setCurrentPage(1);
            PageList<WareHouseDTO> listWareHouse = iStoreWareHouseService.getListWareHouse(wareHouseDTO, pager);
            if (listWareHouse != null && !CollectionUtils.isEmpty(listWareHouse.getDataList())) {
                List<Integer> lstCityId = listWareHouse.getDataList().stream().filter(p -> p.getCityId() != null)
                    .map(p -> p.getCityId()).distinct().collect(Collectors.toList());
                for (Integer city : lstCityId) {
                    dto.setCityId(city);
                    warehouseInventoryCheckBL.checkStoreInventoryByCityId(dto);
                }
            }
        } else {
            warehouseInventoryCheckBL.checkStoreInventoryByCityId(dto);
        }
    }

    /**
     * 有货主的产品，增加库存快照
     */
    @Override
    public void checkStoreInventoryByOwner(Integer opUserId) {
        warehouseInventoryCheckBL.checkStoreInventoryByOwner(opUserId);
    }

    /**
     * 查看库存矫正记录
     */
    @Override
    public PageList<InventorySyncRecordDTO> listInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO) {
        return inventorySyncRecordBL.listInventorySyncRecord(inventorySyncRecordSO);
    }

    /**
     * 库存矫正记录标记为已处理
     */
    @Override
    public void markInventorySyncRecord(InventorySyncRecordDTO dto) {
        inventorySyncRecordBL.markInventorySyncRecord(dto);
    }

    /**
     * 库存对账记录批量标记为已处理
     */
    @Override
    public void batchMarkInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO) {
        inventorySyncRecordBL.batchMarkInventorySyncRecord(inventorySyncRecordSO);
    }

    /**
     * 根据城市id，查询ERP库存
     */
    @Override
    public Map<String, ERPStoreVO> getERPStoreVOMap(Integer cityId) {
        return warehouseInventoryCheckBL.getERPStoreVOMap(cityId);
    }

    /**
     * 根据仓库数据计算销售库存
     */
    @Override
    public List<WarehouseStoreDTO> calculationSaleInventory(WarehouseProductStoreQueryDTO queryDTO) {
        AssertUtils.notNull(queryDTO, "计算销售库存参数不能为空");
        AssertUtils.notNull(queryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(queryDTO.getWarehouseId(), "仓库id不能为空");
        return saleInventoryCalculationBL.calculationSaleInventory(queryDTO);
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.ProductionDateAuditBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditQuery;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateTodoTaskDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IProductionDateAuditService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-14 11:52
 **/
@Service(timeout = 30000)
public class ProductionDateAuditServiceImpl implements IProductionDateAuditService {

    @Resource
    private ProductionDateAuditBL productionDateAuditBL;

    private static final Logger logger = LoggerFactory.getLogger(ProductionDateAuditServiceImpl.class);

    /**
     * 通过仓库、sku、生产日期查询关联的生产日期治理任务
     *
     * @param query 查询条件
     * @return 查询结果
     */
    @Override
    public ProductionDateTodoTaskDTO queryTodoTaskByProductionDate(ProductionDateAuditQuery query) {
        AssertUtils.notNull(query.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notNull(query.getSkuId(), "skuId 不能为空");
        AssertUtils.hasText(query.getProductionDate(), "生产日期不能为空");
        AssertUtils.hasText(query.getProductName(), "产品名称不能为空");
        return productionDateAuditBL.queryTodoTaskByProductionDate(query);
    }

    /**
     * 申请生产日期审核
     *
     * @param dto 申请参数
     */
    @Override
    public void applyProductionDateAudit(ProductionDateAuditDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notNull(dto.getSkuId(), "skuId 不能为空");
        AssertUtils.hasText(dto.getProductionDate(), "生产日期不能为空");
        AssertUtils.hasText(dto.getProductName(), "产品名称不能为空");
        AssertUtils.notNull(dto.getUnitTotalCount(), "产品库存不能为空");
        AssertUtils.notNull(dto.getRefTodoTaskId(), "关联待办任务 id 不能为空");
        AssertUtils.hasText(dto.getRefTodoTaskNo(), "关联待办任务编号不能为空");
        logger.info("申请生产日期审核: {}", JSON.toJSONString(dto));
        productionDateAuditBL.applyProductionDateAudit(dto);
    }

    /**
     * 通过仓库 id、skuId、生产日期 查询关联的生产日期审核
     *
     * @param query 查询条件
     * @return 查询结果
     */
    @Override
    public ProductionDateAuditDTO queryAuditInfo(ProductionDateAuditQuery query) {
        AssertUtils.notNull(query.getWarehouseId(), "仓库 id 不能为空");
        AssertUtils.notNull(query.getSkuId(), "skuId 不能为空");
        AssertUtils.hasText(query.getProductionDate(), "生产日期不能为空");
        return productionDateAuditBL.queryAuditInfo(query);
    }

    /**
     * 分页查询生产日期审核<br/>
     * 人工审核页面接口
     *
     * @param query 查询参数
     * @return 查询结果
     */
    @Override
    public PageList<ProductionDateAuditDTO> pageListProductionDateAudit(ProductionDateAuditQuery query) {
        return productionDateAuditBL.pageListProductionDateAudit(query);
    }

    /**
     * 按仓库、skuId、生产日期查询生产日期审核数据<br/>
     * 后台接口
     *
     * @param query 查询条件
     * @return 查询结果
     */
    @Override
    public List<ProductionDateAuditDTO> listProductionDateAudit(ProductionDateAuditQuery query) {
        return productionDateAuditBL.listProductionDateAudit(query);
    }

    /**
     * 审核生产日期审核
     *
     * @param productionDateAuditDTO 审核参数
     */
    @Override
    public void auditProductionDateAudit(ProductionDateAuditDTO productionDateAuditDTO) {
        AssertUtils.notNull(productionDateAuditDTO.getId(), "id 不能为空");
        AssertUtils.notNull(productionDateAuditDTO.getApplyPassed(), "审核结果不能为空");
        // 审核拒绝必须填备注
        if (!productionDateAuditDTO.getApplyPassed()) {
            AssertUtils.hasText(productionDateAuditDTO.getAuditRemark(), "审核备注不能为空");
            AssertUtils.isTrue(productionDateAuditDTO.getAuditRemark().length() < 50, "审核备注长度最大为 50 字符");
        }
        productionDateAuditBL.auditProductionDateAudit(productionDateAuditDTO);
    }
}

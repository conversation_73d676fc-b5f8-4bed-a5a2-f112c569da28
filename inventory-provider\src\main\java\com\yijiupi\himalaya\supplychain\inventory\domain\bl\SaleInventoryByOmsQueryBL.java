package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryResultDTO;
import com.yijiupi.himalaya.supplychain.dto.product.OmsInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.InventoryOrderCenterBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.InventoryConvertor;
import com.yijiupi.himalaya.supplychain.inventory.domain.manager.OmsSaleInventoryManager;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.SaleInventoryQueryConvertor;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSpecAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WareHoseInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.OmsInventoryInfo;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.OmsInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据oms查销售库存
 *
 * 销售库存 = oms销售库存
 *
 * <AUTHOR>
 * @date 2020-07-07 16:31
 */
@Service
public class SaleInventoryByOmsQueryBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(SaleInventoryByOmsQueryBL.class);

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private InventoryOrderCenterBL inventoryOrderCenterBL;
    @Autowired
    private SaleInventoryQueryConvertor saleInventoryQueryConvertor;

    @Resource
    private OmsSaleInventoryManager omsSaleInventoryManager;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    /**
     * 查询销售库存
     */
    public List<WarehouseStoreDTO> getSaleInventoryList(ProductStoreQueryDTO productStoreQueryDTO) {
        // LOGGER.info("根据OMS查销售库存参数：{}", JSON.toJSONString(productStoreQueryDTO));
        AssertUtils.notNull(productStoreQueryDTO, "查询销售库存参数不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getWarehouseId(), "仓库id不能为空");

        // 1、按skuId查询转成规格Id+货主Id查询
        if (!CollectionUtils.isEmpty(productStoreQueryDTO.getProductSkuIds())) {
            List<ProductSkuDTO> productSkuDTOList =
                iProductSkuQueryService.findBySku(productStoreQueryDTO.getProductSkuIds());
            if (CollectionUtils.isEmpty(productSkuDTOList)) {
                return Collections.EMPTY_LIST;
            }
            List<ProductSpecAndOwnerIdDTO> specAndOwnerIdDTOS = productSkuDTOList.stream().map(p -> {
                ProductSpecAndOwnerIdDTO specAndOwnerIdDTO = new ProductSpecAndOwnerIdDTO();
                specAndOwnerIdDTO.setOwnerId(p.getCompany_Id());
                specAndOwnerIdDTO.setProductSpecId(p.getProductSpecificationId());
                return specAndOwnerIdDTO;
            }).collect(Collectors.toList());
            productStoreQueryDTO.setSpecAndOwnerIds(specAndOwnerIdDTOS);
        }
        // 查询的规格id+货主id
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIdQueryList = productStoreQueryDTO.getSpecAndOwnerIds();

        // 2、如果同规格库存需要合并，则只按规格id查询
        if (Objects.equals(productStoreQueryDTO.getMergeFlag(), true)) {
            if (!CollectionUtils.isEmpty(productStoreQueryDTO.getSpecAndOwnerIds())) {
                List<Long> specIdList = productStoreQueryDTO.getSpecAndOwnerIds().stream()
                    .map(p -> p.getProductSpecId()).distinct().collect(Collectors.toList());
                productStoreQueryDTO.setProductSpecIds(specIdList);
                productStoreQueryDTO.setSpecAndOwnerIds(null);
            }
        }

        // 3、查仓库库存
        List<WarehouseStoreDTO> warehouseStoreDTOList = getInventoryAll(productStoreQueryDTO);

        // 4、查销售库存
        if (Objects.equals(productStoreQueryDTO.getMergeFlag(), true)) {
            // 计算销售库存（同规格库存需要合并）
            processSaleInventoryByMerge(productStoreQueryDTO, warehouseStoreDTOList);
        } else {
            // 计算销售库存（不需要合并）
            processSaleInventory(productStoreQueryDTO, warehouseStoreDTOList);
        }

        // 5、处理没有入过库的，销售库存数量传null
        fillNotWarehouseStoreDTOList(warehouseStoreDTOList, specAndOwnerIdQueryList, productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId());
        return warehouseStoreDTOList;
    }

    /**
     * 处理没有入过库的，返回销售库存数量传null
     */
    public void fillNotWarehouseStoreDTOList(List<WarehouseStoreDTO> warehouseStoreDTOList,
        List<ProductSpecAndOwnerIdDTO> specAndOwnerIdQueryList, Integer cityId, Integer warehouseId) {
        if (!CollectionUtils.isEmpty(specAndOwnerIdQueryList)) {
            List<WarehouseStoreDTO> notWarehouseStoreDTOList = new ArrayList<>();
            specAndOwnerIdQueryList.forEach(specAndOwnerIdDTO -> {
                if (!warehouseStoreDTOList.stream()
                    .anyMatch(p -> Objects.equals(p.getProductSpecId(), specAndOwnerIdDTO.getProductSpecId())
                        && Objects.equals(p.getOwnerId(), specAndOwnerIdDTO.getOwnerId()))) {
                    WarehouseStoreDTO warehouseStoreDTO = new WarehouseStoreDTO();
                    warehouseStoreDTO.setCityId(cityId);
                    warehouseStoreDTO.setWarehouseId(warehouseId);
                    warehouseStoreDTO.setProductSpecId(specAndOwnerIdDTO.getProductSpecId());
                    warehouseStoreDTO.setOwnerId(specAndOwnerIdDTO.getOwnerId());
                    warehouseStoreDTO.setSaleStoreTotalCount(BigDecimal.ZERO);
                    warehouseStoreDTO.setWarehouseTotalCount(BigDecimal.ZERO);
                    notWarehouseStoreDTOList.add(warehouseStoreDTO);
                }
            });
            if (!CollectionUtils.isEmpty(notWarehouseStoreDTOList)) {
                // LOGGER.info("没有入过库的销售库存：{}", JSON.toJSONString(notWarehouseStoreDTOList));
                warehouseStoreDTOList.addAll(notWarehouseStoreDTOList);
            }
        }
    }

    /**
     * 计算销售库存（不需要合并） 【销售库存 = oms销售库存】
     */
    private void processSaleInventory(ProductStoreQueryDTO productStoreQueryDTO,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        // 1、查OMS销售库存
        Map<String, BigDecimal> omsSaleCountMap = getOmsSaleCountMap(productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId(), warehouseStoreDTOList);

        // 2、查询sku信息
        Map<String, ProductSkuDTO> skuMap = getProductSkuMap(productStoreQueryDTO.getCityId(), warehouseStoreDTOList);

        // 3、计算销售库存
        warehouseStoreDTOList.forEach(p -> {
            String specIdAndOwnerId = getSpecIdAndOwnerIdKey(p.getProductSpecId(), p.getOwnerId());
            // 销售库存 = OMS销售库存
            BigDecimal omsSaleCount = (omsSaleCountMap != null && omsSaleCountMap.containsKey(specIdAndOwnerId))
                ? omsSaleCountMap.get(specIdAndOwnerId) : BigDecimal.ZERO;
            // 销售库存
            p.setSaleStoreTotalCount(omsSaleCount);
            // 补全sku信息
            setSkuInfo(skuMap, p);
            // LOGGER.info(String.format("【根据OMS查销售库存】warehouseId:%s, skuId:%s, specIdAndOwnerId:%s, omsSaleStore:%s",
            // productStoreQueryDTO.getWarehouseId(), p.getProductSkuId(), specIdAndOwnerId, omsSaleCount));
        });
    }

    /**
     * 计算销售库存（同规格库存需要合并） 【销售库存 = oms销售库存】
     */
    private void processSaleInventoryByMerge(ProductStoreQueryDTO productStoreQueryDTO,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        // 1、按规格合并仓库库存
        Map<Long, BigDecimal> warehouseSaleCountMap = getWarehouseStoreCountMap(warehouseStoreDTOList);

        // 2、查OMS销售库存
        Map<Long, BigDecimal> omsSaleCountMap = getOmsSaleCountMapByMerge(productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId(), warehouseStoreDTOList);

        // 3、查询sku信息
        Map<String, ProductSkuDTO> skuMap = getProductSkuMap(productStoreQueryDTO.getCityId(), warehouseStoreDTOList);

        // 4、计算销售库存
        warehouseStoreDTOList.forEach(p -> {
            Long specId = p.getProductSpecId();
            // 仓库库存
            BigDecimal warehouseStoreCount =
                (warehouseSaleCountMap != null && warehouseSaleCountMap.containsKey(specId))
                    ? warehouseSaleCountMap.get(specId) : BigDecimal.ZERO;
            // 销售库存 = OMS销售库存
            BigDecimal omsSaleCount = (omsSaleCountMap != null && omsSaleCountMap.containsKey(specId))
                ? omsSaleCountMap.get(specId) : BigDecimal.ZERO;
            // 销售库存和仓库库存
            p.setWarehouseTotalCount(warehouseStoreCount);
            p.setSaleStoreTotalCount(omsSaleCount);
            // 补全sku信息
            setSkuInfo(skuMap, p);
            // LOGGER.info(String.format("【根据OMS查销售库存】(同规格合并)warehouseId:%s, skuId:%s, specId:%s, ownerId:%s,
            // warehouseStore:%s, omsSaleStore:%s", productStoreQueryDTO.getWarehouseId(), p.getProductSkuId(), specId,
            // p.getOwnerId(), warehouseStoreCount, omsSaleCount));
        });
    }

    /**
     * 补全sku信息
     */
    private void setSkuInfo(Map<String, ProductSkuDTO> skuMap, WarehouseStoreDTO storeDTO) {
        // 补全sku信息
        String specIdAndOwnerId = getSpecIdAndOwnerIdKey(storeDTO.getProductSpecId(), storeDTO.getOwnerId());
        if (skuMap != null && skuMap.get(specIdAndOwnerId) != null) {
            ProductSkuDTO skuDTO = skuMap.get(specIdAndOwnerId);
            storeDTO.setProductSkuId(skuDTO.getProductSkuId());
            storeDTO.setSaleModel(skuDTO.getSaleModel() != null ? skuDTO.getSaleModel().intValue() : null);
            storeDTO.setSource(skuDTO.getSource() != null ? skuDTO.getSource().intValue() : null);
            storeDTO.setStatisticsClass(skuDTO.getStatisticsClassId());
            storeDTO.setStatisticsClassName(skuDTO.getStatisticsClass());
            storeDTO.setOwnerName(skuDTO.getOwnerName());
        }
    }

    /**
     * 根据规格id+货主id查询sku信息
     * 
     * @return
     */
    private Map<String, ProductSkuDTO> getProductSkuMap(Integer cityId, List<WarehouseStoreDTO> warehouseStoreDTOList) {
        Map<String, ProductSkuDTO> skuDTOMap = new HashMap<>(16);
        ProductSkuBySpecificationSO productSkuBySpecSO = new ProductSkuBySpecificationSO();
        productSkuBySpecSO.setCityId(cityId);
        List<List<WarehouseStoreDTO>> WarehouseStoreDTOList =
            InventoryConvertor.splitListNew(warehouseStoreDTOList, 100);
        for (List<WarehouseStoreDTO> warehouseStoreDTOS : WarehouseStoreDTOList) {
            List<ProductSkuBySpecificationQueryDTO> specList = warehouseStoreDTOS.stream().map(p -> {
                ProductSkuBySpecificationQueryDTO specDTO = new ProductSkuBySpecificationQueryDTO();
                specDTO.setOwnerId(p.getOwnerId());
                specDTO.setProductSpecificationId(p.getProductSpecId());
                return specDTO;
            }).collect(Collectors.toList());
            productSkuBySpecSO.setSpecList(specList);
            // 查询sku信息
            List<ProductSkuDTO> skuDTOList = iProductSkuQueryService.findBySpec(productSkuBySpecSO);
            if (!CollectionUtils.isEmpty(skuDTOList)) {
                skuDTOList.forEach(p -> {
                    skuDTOMap.put(getSpecIdAndOwnerIdKey(p.getProductSpecificationId(), p.getCompany_Id()), p);
                });
            }
        }
        return skuDTOMap;
    }

    /**
     * 按规格合并仓库库存
     */
    private Map<Long, BigDecimal> getWarehouseStoreCountMap(List<WarehouseStoreDTO> warehouseStoreDTOList) {
        Map<Long, BigDecimal> warehouseSaleCountMap = new HashMap<>(16);
        // 按规格分组
        Map<Long, List<WarehouseStoreDTO>> warehosueStoreMap =
            warehouseStoreDTOList.stream().collect(Collectors.groupingBy(p -> p.getProductSpecId()));
        warehosueStoreMap.forEach((specId, list) -> {
            // 仓库库存
            BigDecimal warehouseStoreCount =
                list.stream().map(p -> p.getWarehouseTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            warehouseSaleCountMap.put(specId, warehouseStoreCount);
        });
        return warehouseSaleCountMap;
    }

    /**
     * 查询仓库库存
     */
    private List<WarehouseStoreDTO> getInventoryAll(ProductStoreQueryDTO productStoreQueryDTO) {
        List<WarehouseStoreDTO> inventoryDTOList = new ArrayList<>();
        // (1)如果按产品规格和ownerId查询库存，100个一组分别查询
        if (!CollectionUtils.isEmpty(productStoreQueryDTO.getSpecAndOwnerIds())) {
            List<List<ProductSpecAndOwnerIdDTO>> specAndOwnerIdList =
                InventoryConvertor.splitListNew(productStoreQueryDTO.getSpecAndOwnerIds(), 100);
            for (List<ProductSpecAndOwnerIdDTO> specIdList : specAndOwnerIdList) {
                // 按规格和ownerId分组去查询
                productStoreQueryDTO.setSpecAndOwnerIds(specIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getInventoryList(productStoreQueryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    inventoryDTOList.addAll(splitStoreDTOList);
                }
            }
            // (2)如果按规格查询库存，100个一组分别查询
        } else if (!CollectionUtils.isEmpty(productStoreQueryDTO.getProductSpecIds())) {
            List<List<Long>> specIdAllList =
                InventoryConvertor.splitListNew(productStoreQueryDTO.getProductSpecIds(), 100);
            for (List<Long> specIdList : specIdAllList) {
                productStoreQueryDTO.setProductSpecIds(specIdList);
                List<WarehouseStoreDTO> splitStoreDTOList = getInventoryList(productStoreQueryDTO);
                if (!CollectionUtils.isEmpty(splitStoreDTOList)) {
                    inventoryDTOList.addAll(splitStoreDTOList);
                }
            }
            // (3)按仓库id查询库存
        } else {
            inventoryDTOList = getInventoryList(productStoreQueryDTO);
        }
        return inventoryDTOList;
    }

    /**
     * 分页查询仓库库存
     * 
     * @return
     */
    private List<WarehouseStoreDTO> getInventoryList(ProductStoreQueryDTO productStoreQueryDTO) {
        List<WarehouseStoreDTO> inventoryDTOList = new ArrayList<>();
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setCityId(productStoreQueryDTO.getCityId());
        wareHoseInventoryQueryDTO.setWarehouseId(productStoreQueryDTO.getWarehouseId());
        wareHoseInventoryQueryDTO.setProductSpecIds(productStoreQueryDTO.getProductSpecIds());
        wareHoseInventoryQueryDTO.setSpecAndOwnerIds(productStoreQueryDTO.getSpecAndOwnerIds());
        wareHoseInventoryQueryDTO.setPageSize(1000);
        wareHoseInventoryQueryDTO.setWarehouseAllocationType(productStoreQueryDTO.getWarehouseAllocationType());

        Integer totalPage = 1;
        for (int i = 1; i <= totalPage; i++) {
            wareHoseInventoryQueryDTO.setCurrentPage(i);
            PageList<WarehouseStoreDTO> pagerResult = warehouseInventoryQueryBL
                .getProductInventorysFromProductInfoSpecificationByPager(wareHoseInventoryQueryDTO);
            if (pagerResult != null && !CollectionUtils.isEmpty(pagerResult.getDataList())) {
                totalPage = pagerResult.getPager().getTotalPage();
                inventoryDTOList.addAll(pagerResult.getDataList());
            }
        }
        return inventoryDTOList;
    }

    /**
     * 查询OMS销售库存
     * 
     * @return
     */
    private List<OmsInventoryInfoDTO> listOmsSaleInventory(Integer cityId, Integer warehouseId,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        List<OmsInventoryInfo> omsSaleInventoryList = new ArrayList<>();
        List<OmsInventoryInfoDTO> totalList = new ArrayList<>();
        OmsInventoryQueryDTO omsInventoryQueryDTO = new OmsInventoryQueryDTO();
        omsInventoryQueryDTO.setOrgId(cityId);
        omsInventoryQueryDTO.setWarehouseId(warehouseId);
        List<List<WarehouseStoreDTO>> warehouseStoreList = InventoryConvertor.splitListNew(warehouseStoreDTOList, 50);
        for (List<WarehouseStoreDTO> warehouseStoreDTOS : warehouseStoreList) {
            omsInventoryQueryDTO.setInternalKeys(
                warehouseStoreDTOS.stream().map(t -> getSpecIdAndOwnerIdKey(t.getProductSpecId(), t.getOwnerId()))
                    .distinct().collect(Collectors.toList()));
            // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 换接口 替换中

            List<OrderCenterSaleInventoryQueryDTO> queryDTOList =
                saleInventoryQueryConvertor.convertQueryList(warehouseStoreDTOS, cityId, warehouseId);
            // LOGGER.info("查OMS销售库存参数：{}", JSON.toJSONString(queryDTO));
            List<OrderCenterSaleInventoryQueryResultDTO> queryResultDTOList =
                inventoryOrderCenterBL.findSaleInventoryList(queryDTOList);
            if (CollectionUtils.isEmpty(queryResultDTOList)) {
                continue;
            }
            List<OmsInventoryInfoDTO> omsInventoryInfoDTOList = queryResultDTOList.stream().map(m -> {
                OmsInventoryInfoDTO omsInventoryInfoDTO = new OmsInventoryInfoDTO();
                omsInventoryInfoDTO.setOrgId(m.getCityId());
                omsInventoryInfoDTO.setWarehouseId(m.getWarehouseId());
                omsInventoryInfoDTO.setSpecificationId(m.getProductSpecId());
                omsInventoryInfoDTO.setOwnerId(m.getOwnerId());
                omsInventoryInfoDTO.setSaleInventoryCount(m.getSaleInventoryCount());
                // TODO 待确认
                omsInventoryInfoDTO.setInternalKey(m.getInternalKey());
                omsInventoryInfoDTO.setSecOwnerId(m.getSecOwnerId());

                // omsInventoryInfoDTO.setProductSkuId();
                // omsInventoryInfoDTO.setSpecificationName();
                // omsInventoryInfoDTO.setPackageName();
                // omsInventoryInfoDTO.setUnitName();
                // omsInventoryInfoDTO.setPackageQuantity();
                // omsInventoryInfoDTO.setOwnerName();
                // omsInventoryInfoDTO.setProductName();
                return omsInventoryInfoDTO;
            }).collect(Collectors.toList());

            // List<OmsInventoryInfo> omsSaleInventory =
            // iOmsInventoryService.querySaleInventoryInternal(omsInventoryQueryDTO);
            // if (omsSaleInventory != null) {
            // // 规格id + 货主id
            // omsSaleInventory.forEach(p -> {
            // Long[] arr = getSplitArr(p.getInternalKey());
            // p.setSpecificationId(arr[0]);
            // p.setOwnerId(arr[1]);
            // });
            // omsSaleInventoryList.addAll(omsSaleInventory);
            // }
            LOGGER.info("查OMS销售库存结果：{}", JSON.toJSONString(omsInventoryInfoDTOList));
            totalList.addAll(omsInventoryInfoDTOList);
        }
        return totalList;
    }

    private Long[] getSplitArr(String internalKey) {
        Long[] arr = new Long[2];
        if (internalKey != null) {
            String[] str = internalKey.split("-");
            if (str != null) {
                if (str.length > 0) {
                    arr[0] = (str[0] != null && !Objects.equals(str[0], "null")) ? Long.valueOf(str[0]) : null;
                }
                if (str.length > 1) {
                    arr[1] = (str[1] != null && !Objects.equals(str[1], "null")) ? Long.valueOf(str[1]) : null;
                }
            }
        }
        return arr;
    }

    /**
     * OMS销售库存集合
     * 
     * @return
     */
    private Map<String, BigDecimal> getOmsSaleCountMap(Integer cityId, Integer warehouseId,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        // 查询OMS销售库存
        List<OmsInventoryInfoDTO> omsSaleInventoryList =
            listOmsSaleInventory(cityId, warehouseId, warehouseStoreDTOList);
        if (CollectionUtils.isEmpty(omsSaleInventoryList)) {
            return Collections.EMPTY_MAP;
        }
        // 同规格ID+货主Id合并
        Map<String, BigDecimal> omsSaleCountMap = new HashMap<>(16);
        Map<String, List<OmsInventoryInfoDTO>> omsInventoryInfoMap = omsSaleInventoryList.stream()
            .collect(Collectors.groupingBy(p -> getSpecIdAndOwnerIdKey(p.getSpecificationId(), p.getOwnerId())));
        omsInventoryInfoMap.forEach((specIdAndOwnerId, list) -> {
            omsSaleCountMap.put(specIdAndOwnerId, list.stream().filter(p -> p.getSaleInventoryCount() != null)
                .map(p -> p.getSaleInventoryCount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        });
        return omsSaleCountMap;
    }

    /**
     * OMS销售库存集合(同规格合并)
     * 
     * @return
     */
    private Map<Long, BigDecimal> getOmsSaleCountMapByMerge(Integer cityId, Integer warehouseId,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        // 查询OMS销售库存
        List<OmsInventoryInfoDTO> omsSaleInventoryList =
            listOmsSaleInventory(cityId, warehouseId, warehouseStoreDTOList);
        if (CollectionUtils.isEmpty(omsSaleInventoryList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, BigDecimal> omsSaleCountMap = new HashMap<>(16);
        // 同规格的库存合并
        Map<Long, List<OmsInventoryInfoDTO>> omsInventoryInfoMap =
            omsSaleInventoryList.stream().collect(Collectors.groupingBy(p -> p.getSpecificationId()));
        omsInventoryInfoMap.forEach((specId, list) -> {
            omsSaleCountMap.put(specId, list.stream().filter(p -> p.getSaleInventoryCount() != null)
                .map(p -> p.getSaleInventoryCount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        });
        return omsSaleCountMap;
    }

    /**
     * 获取规格Id+货主ID唯一键
     * 
     * @return
     */
    private String getSpecIdAndOwnerIdKey(Long specId, Long ownerId) {
        return String.format("%s-%s", specId, ownerId);
    }
}

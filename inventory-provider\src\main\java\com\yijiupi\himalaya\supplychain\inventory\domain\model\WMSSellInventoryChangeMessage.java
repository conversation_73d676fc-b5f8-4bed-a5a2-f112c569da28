package com.yijiupi.himalaya.supplychain.inventory.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * WMS 销售库存变更事件消息
 */
public class WMSSellInventoryChangeMessage implements Serializable {
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 产品信息规格ID(必填)
     */
    private Long productSpecificationId;
    /**
     * 库存所有者类型(必填) 酒批(0), 合作商(1),入驻商(2), NULL(null);
     */
    private Integer ownerType;
    /**
     * 库存所有者ID(非必填)
     */
    private Long ownerId;
    /**
     * 订单类型(非必填)
     */
    private Integer orderType;
    /**
     * 订单ID(非必填)
     */
    private String orderId;
    /**
     * 订单编号(非必填)
     */
    private String orderNo;
    /**
     * 酒批事件类型(必填)
     */
    private Integer eventType;
    /**
     * 库存变更数量(小单位, 必填)
     */
    private BigDecimal totalCount;
    /**
     * 描述(非必填, 最大宽度VARCHAR(2000))
     */
    private String description;
    /**
     * 创建时间(必填)
     */
    private Date createTime;
    /**
     * 创建人ID(必填)
     */
    private String createUserId;
    /**
     * 创建人名称(必填)
     */
    private String createUserName;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 获取 城市id
     *
     * @return cityId 城市id
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 城市id
     *
     * @param cityId 城市id
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库Id
     *
     * @return warehouseId 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     *
     * @param warehouseId 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 产品信息规格ID(必填)
     *
     * @return productSpecificationId 产品信息规格ID(必填)
     */
    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    /**
     * 设置 产品信息规格ID(必填)
     *
     * @param productSpecificationId 产品信息规格ID(必填)
     */
    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    /**
     * 获取 库存所有者类型(必填) 酒批(0) 合作商(1)入驻商(2) NULL(null);
     *
     * @return ownerType 库存所有者类型(必填) 酒批(0) 合作商(1)入驻商(2) NULL(null);
     */
    public Integer getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置 库存所有者类型(必填) 酒批(0) 合作商(1)入驻商(2) NULL(null);
     *
     * @param ownerType 库存所有者类型(必填) 酒批(0) 合作商(1)入驻商(2) NULL(null);
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 库存所有者ID(非必填)
     *
     * @return ownerId 库存所有者ID(非必填)
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 库存所有者ID(非必填)
     *
     * @param ownerId 库存所有者ID(非必填)
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 订单类型(非必填)
     *
     * @return orderType 订单类型(非必填)
     */
    public Integer getOrderType() {
        return this.orderType;
    }

    /**
     * 设置 订单类型(非必填)
     *
     * @param orderType 订单类型(非必填)
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**
     * 获取 订单ID(非必填)
     *
     * @return orderId 订单ID(非必填)
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置 订单ID(非必填)
     *
     * @param orderId 订单ID(非必填)
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取 订单编号(非必填)
     *
     * @return orderNo 订单编号(非必填)
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单编号(非必填)
     *
     * @param orderNo 订单编号(非必填)
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 酒批事件类型(必填)
     *
     * @return jiupiEventType 酒批事件类型(必填)
     */
    public Integer getEventType() {
        return this.eventType;
    }

    /**
     * 设置 酒批事件类型(必填)
     *
     * @param eventType 酒批事件类型(必填)
     */
    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    /**
     * 获取 库存变更数量(小单位 必填)
     *
     * @return totalCount 库存变更数量(小单位 必填)
     */
    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 库存变更数量(小单位 必填)
     *
     * @param totalCount 库存变更数量(小单位 必填)
     */
    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 描述(非必填 最大宽度VARCHAR(2000))
     *
     * @return description 描述(非必填 最大宽度VARCHAR(2000))
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * 设置 描述(非必填 最大宽度VARCHAR(2000))
     *
     * @param description 描述(非必填 最大宽度VARCHAR(2000))
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取 创建时间(必填)
     *
     * @return createTime 创建时间(必填)
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间(必填)
     *
     * @param createTime 创建时间(必填)
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 创建人ID(必填)
     *
     * @return createUserId 创建人ID(必填)
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置 创建人ID(必填)
     *
     * @param createUserId 创建人ID(必填)
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取 创建人名称(必填)
     *
     * @return createUserName 创建人名称(必填)
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置 创建人名称(必填)
     *
     * @param createUserName 创建人名称(必填)
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

}

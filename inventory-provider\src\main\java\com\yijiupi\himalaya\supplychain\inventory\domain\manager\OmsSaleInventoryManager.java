package com.yijiupi.himalaya.supplychain.inventory.domain.manager;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.OmsInventoryInfo;
import com.yijiupi.himalaya.supplychain.omsorderquery.dto.inventory.OmsInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.omsorderquery.service.IOmsInventoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024-06-11 14:20
 **/
@Service
public class OmsSaleInventoryManager {

    @Reference(timeout = 600000)
    private IOmsInventoryService omsInventoryService;

    private static final Logger logger = LoggerFactory.getLogger(OmsSaleInventoryManager.class);

    public List<OmsInventoryInfo> querySaleInventoryInternal(OmsInventoryQueryDTO query) {
        return runCaching(() -> omsInventoryService.querySaleInventoryInternal(query), Collections::emptyList);
    }

    private <T> T runCaching(Supplier<T> supplier, Supplier<T> defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.warn("出现异常", e);
        }
        return defaultValue.get();
    }

}

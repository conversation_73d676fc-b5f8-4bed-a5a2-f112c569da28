package com.yijiupi.himalaya.supplychain.inventory.domain.bo;

import java.util.List;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
public class ReturnOrderProductionDateTotalCacheBO {

    private List<ReturnOrderProductionDateCacheBO> cacheBOList;

    private List<ReturnOrderProductionDateCacheBO> notCacheBOList;

    /**
     * 获取
     *
     * @return cacheBOList
     */
    public List<ReturnOrderProductionDateCacheBO> getCacheBOList() {
        return this.cacheBOList;
    }

    /**
     * 设置
     *
     * @param cacheBOList
     */
    public void setCacheBOList(List<ReturnOrderProductionDateCacheBO> cacheBOList) {
        this.cacheBOList = cacheBOList;
    }

    /**
     * 获取
     *
     * @return notCacheBOList
     */
    public List<ReturnOrderProductionDateCacheBO> getNotCacheBOList() {
        return this.notCacheBOList;
    }

    /**
     * 设置
     *
     * @param notCacheBOList
     */
    public void setNotCacheBOList(List<ReturnOrderProductionDateCacheBO> notCacheBOList) {
        this.notCacheBOList = notCacheBOList;
    }
}

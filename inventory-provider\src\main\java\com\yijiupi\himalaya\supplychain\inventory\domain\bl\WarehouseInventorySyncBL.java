package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.base.Objects;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.outInType;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuStateSyncDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuManageService;

/**
 * 仓库库存异步BL
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WarehouseInventorySyncBL {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseInventorySyncBL.class);

    @Reference
    private IProductSkuManageService iProductSkuManageService;

    /**
     * 异步同步sku上架状态
     *
     * @param warehouseChangeList 库存变动信息
     */
    @Async("inventoryTaskExecutor")
    public void updateProductSkuState(List<WarehouseInventoryChangeBO> warehouseChangeList,
        boolean isUpdateProductStore) {
        if (!isUpdateProductStore || CollectionUtils.isEmpty(warehouseChangeList)) {
            return;
        }

        Integer warehouseId = warehouseChangeList.stream().findFirst().get().getWarehouseId();
        List<Long> skuIds = warehouseChangeList.stream()
            .filter(p -> p.getProductSkuId() != null && p.getCount().compareTo(BigDecimal.ZERO) > 0
                && Objects.equal(p.getOutInType(), outInType.in))
            .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        ProductSkuStateSyncDTO syncDTO = new ProductSkuStateSyncDTO();
        syncDTO.setWarehouseId(warehouseId);
        syncDTO.setSkuIdList(skuIds);
        iProductSkuManageService.updateProductSkuState(syncDTO);
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @since 2025-03-14 16:07
**/
@Mapper
public interface ProductionDateAuditPOMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(ProductionDateAuditPO record);

    int insertOrUpdate(ProductionDateAuditPO record);

    int insertOrUpdateSelective(ProductionDateAuditPO record);

    int insertSelective(ProductionDateAuditPO record);

    ProductionDateAuditPO selectByPrimaryKey(Long id);

    List<ProductionDateAuditPO> selectListByPrimaryKey(@Param("list") List<Long> id);

    int updateByPrimaryKeySelective(ProductionDateAuditPO record);

    int updateByPrimaryKey(ProductionDateAuditPO record);

    int updateBatch(@Param("list") List<ProductionDateAuditPO> list);

    int updateBatchSelective(@Param("list") List<ProductionDateAuditPO> list);

    int batchInsert(@Param("list") List<ProductionDateAuditPO> list);

    int batchInsertOrUpdate(@Param("list") List<ProductionDateAuditPO> list);

    PageResult<ProductionDateAuditPO> pageList(ProductionDateAuditQuery query);

}
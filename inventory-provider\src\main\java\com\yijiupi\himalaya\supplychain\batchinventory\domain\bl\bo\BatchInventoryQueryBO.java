package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo;

import java.util.List;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/13
 */
public class BatchInventoryQueryBO {
    /**
     * 仓库
     */
    private Integer warehouseId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * skuId
     */
    private Long productSkuId;
    /**
     * 货位完成名称
     */
    private String locationFullName;
    /**
     * 货区/货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     */
    private List<Integer> subCategoryList;
    /**
     ** 是否限制产品范围（1：表示只查本仓库的产品）
     */
    private Byte limitSku;
    /**
     * 产品Id集合
     */
    private List<Long> skuIds;

    private Integer cityId;

    /**
     * 获取 仓库
     *
     * @return warehouseId 仓库
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库
     *
     * @param warehouseId 仓库
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 货位名称
     *
     * @return locationName 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     *
     * @param locationName 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 商品名称
     *
     * @return productSkuName 商品名称
     */
    public String getProductSkuName() {
        return this.productSkuName;
    }

    /**
     * 设置 商品名称
     *
     * @param productSkuName 商品名称
     */
    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    /**
     * 获取 skuId
     *
     * @return productSkuId skuId
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 skuId
     *
     * @param productSkuId skuId
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 货位完成名称
     *
     * @return locationFullName 货位完成名称
     */
    public String getLocationFullName() {
        return this.locationFullName;
    }

    /**
     * 设置 货位完成名称
     *
     * @param locationFullName 货位完成名称
     */
    public void setLocationFullName(String locationFullName) {
        this.locationFullName = locationFullName;
    }

    /**
     * 获取 货区货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     *
     * @return subCategoryList 货区货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54，
     *         暂存区=55，待检区=56）
     */
    public List<Integer> getSubCategoryList() {
        return this.subCategoryList;
    }

    /**
     * 设置 货区货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54， 暂存区=55，待检区=56）
     *
     * @param subCategoryList 货区货区类型，货位类型（整货位=0，零货位=1， 收货暂存=2， 操作台=3），货区类型（存储区=50， 拣货区=51， 零拣区=52， 周转区=53， 退货区=54，
     *            暂存区=55，待检区=56）
     */
    public void setSubCategoryList(List<Integer> subCategoryList) {
        this.subCategoryList = subCategoryList;
    }

    /**
     * 获取 是否限制产品范围（1：表示只查本仓库的产品）
     *
     * @return limitSku 是否限制产品范围（1：表示只查本仓库的产品）
     */
    public Byte getLimitSku() {
        return this.limitSku;
    }

    /**
     * 设置 是否限制产品范围（1：表示只查本仓库的产品）
     *
     * @param limitSku 是否限制产品范围（1：表示只查本仓库的产品）
     */
    public void setLimitSku(Byte limitSku) {
        this.limitSku = limitSku;
    }

    /**
     * 获取 产品Id集合
     *
     * @return skuIds 产品Id集合
     */
    public List<Long> getSkuIds() {
        return this.skuIds;
    }

    /**
     * 设置 产品Id集合
     *
     * @param skuIds 产品Id集合
     */
    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    /**
     * 获取
     *
     * @return cityId
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置
     *
     * @param cityId
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}

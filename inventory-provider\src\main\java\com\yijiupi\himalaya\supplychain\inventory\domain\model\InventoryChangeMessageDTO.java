package com.yijiupi.himalaya.supplychain.inventory.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.AwardOrderReleteInfo;

/**
 * Created by 余明 on 2018-08-24.
 */
public class InventoryChangeMessageDTO implements Serializable {

    private Integer warehouseId;

    private Long productSkuId;

    private BigDecimal count;

    private List<AwardOrderReleteInfo> lstOrderNo;

    public List<AwardOrderReleteInfo> getLstOrderNo() {
        return lstOrderNo;
    }

    public void setLstOrderNo(List<AwardOrderReleteInfo> lstOrderNo) {
        this.lstOrderNo = lstOrderNo;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }
}

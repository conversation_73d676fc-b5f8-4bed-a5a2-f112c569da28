package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderBusinessType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.*;
import com.yijiupi.himalaya.supplychain.pushorder.enums.pushCapabilityTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2018/5/10
 */
public class OrderDTOConvert {

    public static List<InventoryDeliveryJiupiOrder>
        orderDTOS2InventoryDeliveryJiupiOrderS(List<OrderDTO> orderDTOList) {
        List<InventoryDeliveryJiupiOrder> deliveryJiupiOrderS = new ArrayList<>();
        boolean isCalculated = orderDTOList.get(0).getCalculatedProductionDate() != null
            ? orderDTOList.get(0).getCalculatedProductionDate() : false;
        for (OrderDTO orderDTO : orderDTOList) {
            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
            inventoryDeliveryJiupiOrder.setOrderId(orderDTO.getId());
            inventoryDeliveryJiupiOrder.setOrderNo(orderDTO.getRefOrderNo());
            inventoryDeliveryJiupiOrder.setOmsOrderId(StringUtils.isNumeric(orderDTO.getBusinessId())
                ? Long.valueOf(orderDTO.getBusinessId()) : orderDTO.getId());
            inventoryDeliveryJiupiOrder.setRelationOrderId(orderDTO.getRelationOrderId());
            inventoryDeliveryJiupiOrder.setCityId(orderDTO.getOrgId());
            inventoryDeliveryJiupiOrder.setFromCityId(orderDTO.getFromCityId());
            inventoryDeliveryJiupiOrder.setWarehouseId(orderDTO.getWarehouseId());
            inventoryDeliveryJiupiOrder
                .setItems(orderItemDTOList2InventoryDeliveryJiupiOrderItem(orderDTO.getItems(), isCalculated));

            int orderType = orderDTO.getOrderType() == null ? 0 : orderDTO.getOrderType();
            // jiuPiEventType转换
            inventoryDeliveryJiupiOrder.setJiupiEventType(getJiuPiEventType(orderDTO.getBusinessType(),
                orderDTO.getOrderType().intValue(), orderDTO.getRegistrationPromotion(), orderDTO.getCapabilityType()));

            inventoryDeliveryJiupiOrder.setJiupiOrderType(orderType);
            inventoryDeliveryJiupiOrder.setOrderType(orderType);
            // 记录订单能力类型
            inventoryDeliveryJiupiOrder.setCapabilityType(orderDTO.getCapabilityType());
            inventoryDeliveryJiupiOrder.setOrderSourceType(orderDTO.getOrderSourceType());

            deliveryJiupiOrderS.add(inventoryDeliveryJiupiOrder);
        }
        return deliveryJiupiOrderS;
    }

    private static List<InventoryDeliveryJiupiOrderItem>
        orderItemDTOList2InventoryDeliveryJiupiOrderItem(List<OrderItemDTO> orderItemDTOList, boolean isCalculated) {
        ArrayList<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            if (CollectionUtils.isNotEmpty(orderItemDTO.getItemDetailList())) {
                orderItemDTO.getItemDetailList().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem =
                        new InventoryDeliveryJiupiOrderItem();
                    inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItemDTO.getSkuId());
                    inventoryDeliveryJiupiOrderItem.setTakeCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItemDTO.getId());
                    inventoryDeliveryJiupiOrderItem
                        .setOmsOrderItemId(StringUtils.isNumeric(orderItemDTO.getBusinessItemId())
                            ? Long.valueOf(orderItemDTO.getBusinessItemId()) : orderItemDTO.getId());
                    inventoryDeliveryJiupiOrderItem.setOrderItemDetailId(detail.getId());
                    inventoryDeliveryJiupiOrderItem.setRelationOrderItemId(orderItemDTO.getRelationOrderItemId());
                    inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(detail.getProductSpecificationId());
                    inventoryDeliveryJiupiOrderItem.setOwnerId(detail.getOwnerId());
                    inventoryDeliveryJiupiOrderItem.setSecOwnerId(detail.getSecOwnerId());
                    inventoryDeliveryJiupiOrderItem.setLocationId(detail.getLocationId());
                    inventoryDeliveryJiupiOrderItem.setLocationName(detail.getLocationName());
                    if (isCalculated && detail.getProductionDate() != null) {
                        inventoryDeliveryJiupiOrderItem.setProductionDate(detail.getProductionDate());
                    }
                    inventoryDeliveryJiupiOrderItem.setDefective(detail.getDefective());
                    inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
                });
            } else {
                InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
                inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItemDTO.getSkuId());
                inventoryDeliveryJiupiOrderItem.setTakeCount(orderItemDTO.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItemDTO.getId());
                inventoryDeliveryJiupiOrderItem
                    .setOmsOrderItemId(StringUtils.isNumeric(orderItemDTO.getBusinessItemId())
                        ? Long.valueOf(orderItemDTO.getBusinessItemId()) : orderItemDTO.getId());
                inventoryDeliveryJiupiOrderItem.setRelationOrderItemId(orderItemDTO.getRelationOrderItemId());
                inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(orderItemDTO.getProductSpecificationId());
                inventoryDeliveryJiupiOrderItem.setOwnerId(orderItemDTO.getOwnerId());
                inventoryDeliveryJiupiOrderItem.setSecOwnerId(orderItemDTO.getSecOwnerId());
                inventoryDeliveryJiupiOrderItem.setLocationId(orderItemDTO.getLocationId());
                inventoryDeliveryJiupiOrderItem.setLocationName(orderItemDTO.getLocationName());
                if (isCalculated && orderItemDTO.getProductionDate() != null) {
                    inventoryDeliveryJiupiOrderItem.setProductionDate(orderItemDTO.getProductionDate());
                }
                inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
            }
        }
        return inventoryDeliveryJiupiOrderItems;
    }

    private static Integer getJiuPiEventType(Byte orderBusinessType, Integer OrderType, Boolean registrationPromotion,
        Byte capabilityType) {
        Integer jiupiEvenType = JiupiEventType.仓库发货扣仓库库存.getType();
        if (orderBusinessType != null) {
            boolean isReturnOrder = orderBusinessType == InStockOrderBusinessType.退货订单.getType()
                || (orderBusinessType == InStockOrderBusinessType.酒批业务退货单.getType());
            if (orderBusinessType == InStockOrderBusinessType.部分配送订单.getType()) {
                jiupiEvenType = JiupiEventType.部分配送返仓库库存.getType();
            } else if (orderBusinessType == InStockOrderBusinessType.延迟配送订单.getType()) {
                jiupiEvenType = JiupiEventType.订单延迟配送.getType();
            } else if (orderBusinessType == InStockOrderBusinessType.配送失败订单.getType()) {
                jiupiEvenType = JiupiEventType.配送失败返仓库库存.getType();
            } else if (isReturnOrder) {
                jiupiEvenType = JiupiEventType.退货单返仓库库存.getType();
            } else if (JiupiOrderTypeEnum.ORDER_TYPE_OEFLINE == OrderType) {
                jiupiEvenType = JiupiEventType.易款便利线下单扣仓库库存.getType();
            } else if (JiupiOrderTypeEnum.ORDER_TYPE_RTURNOFFLINE == OrderType) {
                jiupiEvenType = JiupiEventType.易款便利线下退货单返仓库库存.getType();
            }

        }
        if (registrationPromotion != null && registrationPromotion) {
            jiupiEvenType = JiupiEventType.注册有礼订单扣仓库库存.getType();
        }

        if (null != capabilityType) {
            if (capabilityType == pushCapabilityTypeEnum.调拨强制完成.getType()) {
                jiupiEvenType = JiupiEventType.供应链调拨.getType();
            } else if (capabilityType == pushCapabilityTypeEnum.调拨强制取消.getType()) {
                jiupiEvenType = JiupiEventType.供应链调拨.getType();
            }
        }
        return jiupiEvenType;
    }

    public static InventoryDeliveryJiupiOrder
        outStockOrderDTO2InventoryDeliveryJiupiOrder(OutStockOrderDTO outStockOrderDTO) {
        if (outStockOrderDTO == null) {
            return null;
        }
        InventoryDeliveryJiupiOrder deliveryJiupiOrder = new InventoryDeliveryJiupiOrder();
        deliveryJiupiOrder.setOrderId(Long.valueOf(outStockOrderDTO.getId()));
        deliveryJiupiOrder.setOrderNo(outStockOrderDTO.getRefOrderNo());
        deliveryJiupiOrder.setOmsOrderId(outStockOrderDTO.getBusinessId() == null
            ? Long.valueOf(outStockOrderDTO.getId()) : Long.valueOf(outStockOrderDTO.getBusinessId()));
        deliveryJiupiOrder.setCityId(outStockOrderDTO.getOrgId());
        deliveryJiupiOrder.setFromCityId(outStockOrderDTO.getFromCityId());
        deliveryJiupiOrder.setWarehouseId(outStockOrderDTO.getWarehouseId());
        deliveryJiupiOrder.setItems(outStockOrderItemDetailDTOS2InventoryDeliveryJiupiOrderItems(
            outStockOrderDTO.getRefOrderNo(), outStockOrderDTO.getOutStockOrderItemDTOS()));

        int orderType = outStockOrderDTO.getOrderType() == null ? 0 : outStockOrderDTO.getOrderType();
        // jiuPiEventType转换
        deliveryJiupiOrder
            .setJiupiEventType(getJiuPiEventType(outStockOrderDTO.getBusinessType(), orderType, null, null));

        deliveryJiupiOrder.setJiupiOrderType(orderType);

        return deliveryJiupiOrder;
    }

    private static List<InventoryDeliveryJiupiOrderItem> outStockOrderItemDetailDTOS2InventoryDeliveryJiupiOrderItems(
        String orderNo, List<OutStockOrderItemDTO> outStockOrderItemDTOS) {
        List<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
        for (OutStockOrderItemDTO item : outStockOrderItemDTOS) {
            // 2022-07-28，Item数量为0的，跳过处理
            if (item.getUnitTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (item.getOutStockOrderItemDetailDTOS() == null
                || CollectionUtils.isEmpty(item.getOutStockOrderItemDetailDTOS())) {
                InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
                inventoryDeliveryJiupiOrderItem.setProductSkuId(item.getSkuId());
                inventoryDeliveryJiupiOrderItem.setTakeCount(item.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setOrderItem_Id(Long.valueOf(item.getId()));
                inventoryDeliveryJiupiOrderItem.setOmsOrderItemId(item.getBusinessItemId() == null
                    ? Long.valueOf(item.getId()) : Long.valueOf(item.getBusinessItemId()));
                inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(item.getProductSpecificationId());
                inventoryDeliveryJiupiOrderItem.setOwnerId(item.getOwnerId());
                inventoryDeliveryJiupiOrderItem.setSecOwnerId(item.getSecOwnerId());
                inventoryDeliveryJiupiOrderItem.setDeliverCount(item.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItem.setBuyCount(item.getUnitTotalCount());
                inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
            } else {
                // 2022-07-28，Item数量与Detail数量不一致的，拦截出库
                BigDecimal detailTotalCount = item.getOutStockOrderItemDetailDTOS().stream()
                    .filter(dt -> dt != null && dt.getUnitTotalCount() != null)
                    .map(OutStockOrderItemDetailDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (item.getUnitTotalCount().compareTo(detailTotalCount) != 0) {
                    throw new BusinessException(
                        "订单项与Detail数量不一致，请联系技术支持！OrderNo:" + orderNo + ",产品：" + item.getProductName());
                }
                item.getOutStockOrderItemDetailDTOS().forEach(detail -> {
                    InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem =
                        new InventoryDeliveryJiupiOrderItem();
                    inventoryDeliveryJiupiOrderItem.setProductSkuId(item.getSkuId());
                    inventoryDeliveryJiupiOrderItem.setTakeCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setOrderItem_Id(Long.valueOf(item.getId()));
                    inventoryDeliveryJiupiOrderItem.setOmsOrderItemId(item.getBusinessItemId() == null
                        ? Long.valueOf(item.getId()) : Long.valueOf(item.getBusinessItemId()));
                    inventoryDeliveryJiupiOrderItem.setProductSpecification_Id(item.getProductSpecificationId());
                    inventoryDeliveryJiupiOrderItem.setOwnerId(detail.getOwnerId());
                    inventoryDeliveryJiupiOrderItem.setSecOwnerId(detail.getSecOwnerId());
                    inventoryDeliveryJiupiOrderItem.setLocationId(detail.getLocationId());
                    inventoryDeliveryJiupiOrderItem.setLocationName(detail.getLocationName());
                    inventoryDeliveryJiupiOrderItem.setDeliverCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItem.setBuyCount(detail.getUnitTotalCount());
                    inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
                });
            }
        }
        return inventoryDeliveryJiupiOrderItems;
    }

    public static List<InventoryDeliveryJiupiOrder>
        outStockOrderDTOS2InventoryDeliveryJiupiOrders(List<OutStockOrderDTO> outStockOrderDTOS) {
        if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
            return null;
        }
        List<InventoryDeliveryJiupiOrder> jiupiOrders = new ArrayList<>();
        outStockOrderDTOS.forEach(order -> jiupiOrders.add(outStockOrderDTO2InventoryDeliveryJiupiOrder(order)));
        return jiupiOrders;
    }
}

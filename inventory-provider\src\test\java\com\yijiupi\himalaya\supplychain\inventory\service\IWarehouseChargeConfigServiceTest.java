package com.yijiupi.himalaya.supplychain.inventory.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseChargeConfigBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWareHouseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWarehouseQuery;

/**
 * @author: lidengfeng
 * @date 2018/9/25 16:36
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class IWarehouseChargeConfigServiceTest {

    @Reference
    private IWarehouseChargeConfigService iWarehouseChargeConfigService;

    @Autowired
    private WarehouseChargeConfigBL warehouseChargeConfigBL;

    @Test
    public void findInStockTotalCharge() {

        ProductChargeDTO productChargeDTO1 = new ProductChargeDTO();
        productChargeDTO1.setNum(BigDecimal.valueOf(24));
        productChargeDTO1.setProductSpecificationId(66133L);
        List<ProductChargeDTO> items = new ArrayList<>();
        items.add(productChargeDTO1);

        ShopChargeDTO shopChargeDTO = new ShopChargeDTO();
        shopChargeDTO.setWarehouseId(2);
        shopChargeDTO.setShopId(169818873699270079L);
        shopChargeDTO.setItems(items);
        InStockCharge inStockTotalCharge = warehouseChargeConfigBL.findInStockTotalCharge(shopChargeDTO);
        System.out.println(JSON.toJSONString(inStockTotalCharge));

    }

    @Test
    public void findOutStockTotalCharge() {

        ProductChargeDTO productChargeDTO1 = new ProductChargeDTO();
        productChargeDTO1.setNum(BigDecimal.valueOf(24));
        productChargeDTO1.setProductSpecificationId(66133L);
        List<ProductChargeDTO> items = new ArrayList<>();
        items.add(productChargeDTO1);

        ShopChargeDTO shopChargeDTO = new ShopChargeDTO();
        shopChargeDTO.setWarehouseId(2);
        shopChargeDTO.setShopId(169818873699270078L);
        shopChargeDTO.setItems(items);
        OutStockCharge inStockTotalCharge = warehouseChargeConfigBL.findOutStockTotalCharge(shopChargeDTO);
        System.out.println(JSON.toJSONString(inStockTotalCharge));

    }

    @Test
    public void findWarehouseChooseList() {
        WarehouseChooseDTO warehouseChooseDTO = new WarehouseChooseDTO();
        warehouseChooseDTO.setShopId(169818873699269954L);
        // warehouseChooseDTO.setFacilitatorId(6);
        warehouseChooseDTO.setWarehouseChooseType(2);
        warehouseChooseDTO.setCityId(999);
        PageList<WarehouseChooseReturnDTO> warehouseChooseList =
            warehouseChargeConfigBL.findWarehouseChooseList(warehouseChooseDTO);
        System.out.println(JSON.toJSONString(warehouseChooseList));

    }

    /**
     * 城市下服务商的仓库查询
     */
    @Test
    public void findWarehouseChargeDetailList() {
        CityWarehouseQuery cityWarehouseQuery = new CityWarehouseQuery();
        cityWarehouseQuery.setFacilitatorId(Long.valueOf(6));
        cityWarehouseQuery.setStatus((byte)1);
        PageList<WarehouseChargeConfigDTO> warehouseChargeDetailList =
            warehouseChargeConfigBL.findWarehouseChargeDetailList(cityWarehouseQuery);
        System.out.println(JSON.toJSONString(warehouseChargeDetailList));

    }

    @Test
    public void findWarehouseChargeList() {
        AgencyStockWarehouseQuery warehouseQuery = new AgencyStockWarehouseQuery();
        warehouseQuery.setCityId(103);
        warehouseQuery.setFacilitatorId(Long.valueOf(6));

        PageList<AgencyStockWareHouseDTO> warehouseChargeList =
            warehouseChargeConfigBL.findWarehouseChargeList(warehouseQuery);
        System.out.println(JSON.toJSONString(warehouseChargeList));

    }
}
package com.yijiupi.himalaya.supplychain.batchinventory.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.product.ProductSkuStorageApplyBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ApplyProductStorageAgeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.base.BaseResult;
import com.yijiupi.supplychain.serviceutils.ThreadLocalUtil;

/**
 * Copyright © 2025 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2025/7/16
 */
@RestController
public class ProductStorageAgeApplyController {

    @Autowired
    private ProductSkuStorageApplyBL productSkuStorageApplyBL;

    /**
     * 申请商品库龄
     * 
     * @param dto
     * @return
     */
    @RequestMapping(value = "/productSku/applyProductStorageAge", method = RequestMethod.POST)
    public BaseResult applyProductStorageAge(@RequestBody ApplyProductStorageAgeDTO dto) {
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空");
        AssertUtils.notNull(dto.getOrgId(), "组织机构信息不能为空");
        AssertUtils.notNull(dto.getSkuId(), "sku信息不能为空");
        AssertUtils.notNull(dto.getCurrentStorageAge(), "当前库龄信息不能为空");
        AssertUtils.notNull(dto.getApplyStorageAge(), "申请库龄信息不能为空");
        AssertUtils.notNull(dto.getStoreBatchId(), "批次库存信息不能为空");
        AssertUtils.notNull(dto.getReason(), "原因不能为空");

        dto.setOptUserId(ThreadLocalUtil.getUserId());
        productSkuStorageApplyBL.applyProductStorageAge(dto);
        return BaseResult.getSuccessResult();
    }

}

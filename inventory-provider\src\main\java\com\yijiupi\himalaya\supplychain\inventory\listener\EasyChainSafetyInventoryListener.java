package com.yijiupi.himalaya.supplychain.inventory.listener;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.EasyChainSafetyInventoryChangeMessageDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;

/**
 * 易款店仓安全库存变更监听
 *
 * <AUTHOR>
 * @date 2020-03-12 15:55
 */
@Component
public class EasyChainSafetyInventoryListener {

    private static final Logger LOG = LoggerFactory.getLogger(EasyChainSafetyInventoryListener.class);

    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;

    @Autowired
    private IdempotenceConsumer idempotenceConsumer;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

//    @RabbitListener(queues = "${mq.supplychain.easyChain.safetyInventoryChange}")
    public void syncApply(EasyChainSafetyInventoryChangeMessageDTO changeMessageDTO, Message message) {
//        try {
//            LOG.info("易款店仓安全库存变更消息：{}", JSON.toJSONString(changeMessageDTO));
//            String messageId = message.getMessageProperties().getMessageId();
//            idempotenceConsumer.apply("SafetyInventorySync:" + messageId, () -> {
//                processSafetyChange(changeMessageDTO);
//            });
//        } catch (Exception e) {
//            LOG.error("易款店仓安全库存消息处理失败，错误信息：" + e.getMessage(), e);
//        }
    }

    /**
     * 易款店仓安全库存变更触发交易销售库存
     */
    private void processSafetyChange(EasyChainSafetyInventoryChangeMessageDTO changeMessageDTO) {
        AssertUtils.notNull(changeMessageDTO, "易款店仓安全库存变更对象不能为空");
        AssertUtils.notNull(changeMessageDTO.getWarehouseId(), "仓库ID不能为空");
        AssertUtils.notNull(changeMessageDTO.getProductSpecificationId(), "规格ID不能为空");
        AssertUtils.notNull(changeMessageDTO.getTotalCount(), "变更数量不能为空");

        Warehouse warehouse = warehouseQueryService.findWarehouseById(changeMessageDTO.getWarehouseId());
        if (warehouse == null) {
            throw new BusinessException(String.format("找不到仓库信息：%s", changeMessageDTO.getWarehouseId()));
        }

        List<InventoryDeliveryJiupiOrder> deliveryOrders = new ArrayList<>();
        InventoryDeliveryJiupiOrder order = new InventoryDeliveryJiupiOrder();
        order.setWarehouseId(changeMessageDTO.getWarehouseId());
        order.setCityId(warehouse.getCityId());
        order.setJiupiOrderType(JiupiOrderTypeEnum.ORDER_TYPE_NORMAL);
        order.setJiupiEventType(JiupiEventType.手动修改.getType());
        order.setDescription("连锁调整安全库存");

        List<InventoryDeliveryJiupiOrderItem> items = new ArrayList<>();
        InventoryDeliveryJiupiOrderItem orderItem = new InventoryDeliveryJiupiOrderItem();
        orderItem.setBuyCount(changeMessageDTO.getTotalCount().multiply(new BigDecimal("-1")));
        orderItem.setSaleSpecQuantity(BigDecimal.ONE);
        orderItem.setProductSpecification_Id(changeMessageDTO.getProductSpecificationId());
        items.add(orderItem);

        order.setItems(items);
        deliveryOrders.add(order);

        // 扣交易系统销售库存
        inventoryOrderBizBL.returnSaleInventoryByOrder(deliveryOrders);
    }

}

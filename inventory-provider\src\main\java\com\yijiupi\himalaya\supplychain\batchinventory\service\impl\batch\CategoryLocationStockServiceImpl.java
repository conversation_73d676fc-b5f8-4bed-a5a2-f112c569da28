package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.CategoryLocationStockBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.ICategoryLocationStockService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service(timeout = 60000)
public class CategoryLocationStockServiceImpl implements ICategoryLocationStockService {

    @Autowired
    private CategoryLocationStockBL categoryLocationStockBL;

    /**
     * 查询计算好策略
     *
     * @param orderItemDTOList
     * @param billType
     * @return
     */
    @Override
    public List<OrderItemDTO> findStrategyRuleForCategory(List<OrderItemDTO> orderItemDTOList, String billType,
        Integer orgId, Byte deliveryMode) {
        AssertUtils.notEmpty(orderItemDTOList, "入参集合不能为空");
        AssertUtils.notNull(billType, "单据类型不能为空");
        AssertUtils.notNull(orgId, "城市id不能为空");
        AssertUtils.notNull(deliveryMode, "配送模式不能为空");
        Integer warehouseId = orderItemDTOList.get(0).getWarehouseId();
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        return categoryLocationStockBL.findStrategyRuleForCategory(orderItemDTOList, billType, warehouseId, orgId,
            deliveryMode);
    }

    /**
     * 通过分配策略查询货位(销售、调拨、其他、第三方出库)，支持按类目开启货位库存
     */
    @Override
    public Map<Long, List<BatchLocationInfoDTO>> findLocationForCategory(List<OrderItemDTO> orderItemDTOList,
        String billType, Integer warehouseId) {
        AssertUtils.notEmpty(orderItemDTOList, "入参集合不能为空");
        AssertUtils.notNull(billType, "单据类型不能为空");
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            AssertUtils.notNull(orderItemDTO.getProductSkuId(), "SKUID不能为空");
            AssertUtils.notNull(orderItemDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(orderItemDTO.getChannel(), "产品渠道不能为空");
            AssertUtils.notNull(orderItemDTO.getSource(), "产品来源不能为空");

        }
        return categoryLocationStockBL.findLocationForCategory(orderItemDTOList, billType, warehouseId);
    }
}

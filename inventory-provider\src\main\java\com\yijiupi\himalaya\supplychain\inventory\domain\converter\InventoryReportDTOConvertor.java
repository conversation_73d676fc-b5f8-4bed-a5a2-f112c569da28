package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.inventory.dto.report.InventoryReportDTO;
import com.yijiupi.himalaya.supplychain.search.StockReportSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
@Component
public class InventoryReportDTOConvertor {

    @Reference
    private OwnerService ownerService;

    private final static long DEFAULT_AGE = 0L;

    public void initShopInfo(List<InventoryReportDTO> inventoryReportDTOList) {
        if (CollectionUtils.isEmpty(inventoryReportDTOList)) {
            return;
        }
        List<Long> secondOwnerIds = inventoryReportDTOList.stream().map(InventoryReportDTO::getSecOwnerId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(secondOwnerIds)) {
            return;
        }
        Map<Long, String> map = ownerService.getOwnerNameMap(secondOwnerIds);
        if (org.springframework.util.CollectionUtils.isEmpty(map)) {
            return;
        }

        inventoryReportDTOList.forEach(dto -> {
            dto.setSecOwnerName(map.getOrDefault(dto.getSecOwnerId(), ""));
        });
    }

    public void initStockAverageInfo(List<InventoryReportDTO> inventoryReportDTOList,
        List<InventoryReportDTO> storeAverageList) {
        if (CollectionUtils.isEmpty(storeAverageList)) {
            return;
        }
        Map<String, Long> averageMap = storeAverageList.stream()
            .collect(Collectors.toMap(InventoryReportDTO::getProductStoreId, InventoryReportDTO::getAverageStockAge));
        inventoryReportDTOList.forEach(dto -> {
            dto.setAverageStockAge(averageMap.get(dto.getProductStoreId()));
        });
    }

    public boolean isSingleStockAgeQuery(StockReportSO so) {
        if (Objects.isNull(so.getStartStockAge()) || Objects.isNull(so.getEndStockAge())) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

}

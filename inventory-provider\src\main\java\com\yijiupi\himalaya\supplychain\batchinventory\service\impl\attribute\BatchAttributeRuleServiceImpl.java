package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.attribute;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.attribute.BatchAttributeRuleBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleReturnDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchAttributeRuleService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 2018/4/9
 */
@Service
public class BatchAttributeRuleServiceImpl implements IBatchAttributeRuleService {

    @Autowired
    private BatchAttributeRuleBL batchAttributeRuleBL;

    /**
     * 新增
     *
     * @param batchAttributeRuleDTO
     */
    @Override
    public void addBatchAttributeRule(BatchAttributeRuleDTO batchAttributeRuleDTO) {
        batchAttributeRuleBL.addBatchAttributeRule(batchAttributeRuleDTO);
    }

    /**
     * 编辑
     *
     * @param batchAttributeRuleDTO
     */
    @Override
    public void updateBatchAttributeRule(BatchAttributeRuleDTO batchAttributeRuleDTO) {
        AssertUtils.notNull(batchAttributeRuleDTO.getId(), "id不能为空");
        AssertUtils.notNull(batchAttributeRuleDTO.getTemplateId(), "模板id不能为空");
        batchAttributeRuleBL.updateBatchAttributeRule(batchAttributeRuleDTO);
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteById(Long id) {
        batchAttributeRuleBL.deleteById(id);
    }

    /**
     * 列表
     *
     * @param batchAttributeRuleQueryDTO
     * @return
     */
    @Override
    public PageList<BatchAttributeRuleReturnDTO>
        findBatchAttributeRuleList(BatchAttributeRuleQueryDTO batchAttributeRuleQueryDTO) {
        return batchAttributeRuleBL.findBatchAttributeRuleList(batchAttributeRuleQueryDTO);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import com.yijiupi.himalaya.supplychain.inventory.util.UUIDUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 产品库存变更记录
 *
 * <AUTHOR>
 */
public class ProductInventoryChangeRecordPO implements Serializable {
    /**
     * 主键ID
     */
    private String id;
    /**
     * 仓库id
     */
    private Integer warehouseId;
    /**
     * 库存ID
     */
    private String productStoreId;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 酒批时间类型
     */
    private Integer jiupiEventType;
    /**
     * ERP事件类型
     */
    private Integer erpEventType;
    /**
     * 库存变更数量(大单位,设置为0)
     */
    private BigDecimal countMaxUnit;
    /**
     * 库存变更数量(小单位)
     */
    private BigDecimal countMinUnit;
    /**
     * 库存变更数量总计
     */
    private BigDecimal totalCount;
    /**
     * 变更前原库存
     */
    private BigDecimal sourceTotalCount;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 变更类型(1:销售库存 2:仓库库存)
     */
    private Integer storeType;

    private String createTimeStr;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * '操作来源,0:供应链，1:ERP,2:易经销
     */
    private Integer systemSource;
    /**
     * 批属性JSON
     */
    private String attributeList;

    /**
     * 出入库类型. 入库单(1),出库单(2)
     */
    private Integer outInType;
    /**
     * 配送方式（0=酒批配送，1=合作商配送，2=配送商配送，4=客户自提，5=总部物流，6=区域代配送，20=门店转配送，-1=不配送）
     */
    private Integer deliveryMode;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * '货位名称'
     */
    private String locationName;
    /**
     * 配送状态 -1未标记0全部配送 1部分发货2部分配送3延迟配送4配送失败5延迟配送已入库
     */
    private Integer deliveryMarkState;

    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 所属人类型
     */
    private Integer ownerType;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;

    /**
     * 批次库存表ID
     */
    private String productStoreBatchId;

    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;

    /**
     * 原订单ID
     */
    private String oldOrderId;

    /**
     * 原订单编号
     */
    private String oldOrderNo;

    /**
     * skuid
     */
    private Long productSkuId;

    /**
     * 是否临期
     */
    private Boolean isAdvent;

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getOldOrderId() {
        return oldOrderId;
    }

    public void setOldOrderId(String oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public Integer getDeliveryMarkState() {
        return deliveryMarkState;
    }

    public void setDeliveryMarkState(Integer deliveryMarkState) {
        this.deliveryMarkState = deliveryMarkState;
    }

    public String getCreateTimeStr() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(getCreateTime());
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public String getErpOrderKey() {
        return UUIDUtil.MD5(orderNo + "_" + productStoreId);
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public BigDecimal getCountMaxUnit() {
        return countMaxUnit;
    }

    public void setCountMaxUnit(BigDecimal countMaxUnit) {
        this.countMaxUnit = countMaxUnit;
    }

    public BigDecimal getCountMinUnit() {
        return countMinUnit;
    }

    public void setCountMinUnit(BigDecimal countMinUnit) {
        this.countMinUnit = countMinUnit;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getSourceTotalCount() {
        return sourceTotalCount;
    }

    public void setSourceTotalCount(BigDecimal sourceTotalCount) {
        this.sourceTotalCount = sourceTotalCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getSystemSource() {
        return systemSource;
    }

    public void setSystemSource(Integer systemSource) {
        this.systemSource = systemSource;
    }

    public String getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(String attributeList) {
        this.attributeList = attributeList;
    }

    public Integer getOutInType() {
        return outInType;
    }

    public void setOutInType(Integer outInType) {
        this.outInType = outInType;
    }

    public Integer getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(Integer deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getProductStoreBatchId() {
        return productStoreBatchId;
    }

    public void setProductStoreBatchId(String productStoreBatchId) {
        this.productStoreBatchId = productStoreBatchId;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Boolean getIsAdvent() {
        return isAdvent;
    }

    public void setIsAdvent(Boolean isAdvent) {
        this.isAdvent = isAdvent;
    }
}

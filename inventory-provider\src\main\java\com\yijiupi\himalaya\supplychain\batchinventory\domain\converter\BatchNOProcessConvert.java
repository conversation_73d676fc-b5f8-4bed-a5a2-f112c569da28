package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchNOProcessBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchNOProcessItemBO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueSaveItemDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueSaveItemDetailDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchNOQueryItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class BatchNOProcessConvert {

    public static List<BatchNOProcessBO> createBatchInfoNOProcessBO(BatchNOQueryDTO queryDTO,
        List<ProductSkuDTO> skuDTOList) {
        Integer warehouseId = queryDTO.getWarehouseId();
        Integer cityId = queryDTO.getCityId();
        Map<Long, ProductSkuDTO> skuDTOMap = skuDTOList.stream().filter(e -> e != null)
            .collect(Collectors.toMap(e -> e.getProductSkuId(), Function.identity()));
        List<BatchNOProcessBO> result = new ArrayList<>();
        for (BatchNOQueryItemDTO itemDTO : queryDTO.getAddItemDTOList()) {
            if (itemDTO == null) {
                continue;
            }
            ProductSkuDTO itemSkuDTO = skuDTOMap.get(itemDTO.getSkuId());
            if (itemSkuDTO == null) {
                continue;
            }
            BatchNOProcessBO processBO = new BatchNOProcessBO();
            processBO.setCityId(cityId);
            processBO.setWarehouseId(warehouseId);
            processBO.setProductSkuId(itemDTO.getSkuId());
            processBO.setProductSpecificationId(itemSkuDTO.getProductSpecificationId());
            processBO.setOwnerId(itemSkuDTO.getCompany_Id());
            processBO.setProductionDate(itemDTO.getProductionDate());
            processBO.setBatchTime(itemDTO.getInStockTime());
            processBO.setChannel(ProductChannelType.JIUPI);
            processBO.setSecOwnerId(itemSkuDTO.getSecOwnerId());
            result.add(processBO);
        }
        return result;
    }

    public static List<BatchNOProcessBO> convertBatchSaveItemTOBatchProcessBO(Integer cityId, Integer warehouseId,
        List<BatchAttributeValueSaveItemDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        return itemList.stream().filter(Objects::nonNull).map(item -> {
            if (item.getProductSkuId() == null || item.getProductSpecificationId() == null) {
                return null;
            }
            BatchNOProcessBO processBO = new BatchNOProcessBO();
            processBO.setCityId(cityId);
            processBO.setWarehouseId(warehouseId);
            processBO.setRefInfoId(item.getRefInfoId());
            processBO.setProductSkuId(item.getProductSkuId());
            processBO.setProductSpecificationId(item.getProductSpecificationId());
            processBO.setOwnerId(item.getOwnerId());
            processBO.setSecOwnerId(item.getSecOwnerId());
            processBO.setChannel(ProductChannelType.JIUPI);
            processBO.setProductionDate(item.getProductionDate());
            processBO.setBatchTime(item.getBatchTime());
            List<BatchAttributeValueSaveItemDetailDTO> itemDetailList = item.getItemDetailList();
            if (CollectionUtils.isNotEmpty(itemDetailList)) {
                List<BatchNOProcessItemBO> processItemBOS =
                    itemDetailList.stream().filter(Objects::nonNull).map(detailDTO -> {
                        BatchNOProcessItemBO processItemBO = new BatchNOProcessItemBO();
                        BeanUtils.copyProperties(detailDTO, processItemBO);
                        return processItemBO;
                    }).collect(Collectors.toList());
                processBO.setProcessItemBOList(processItemBOS);
            }
            return processBO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}

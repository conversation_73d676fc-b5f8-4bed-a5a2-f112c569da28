package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStoreWareHouseService;

/**
 * 仓库库存查询BL. Created by Lifeng on 2017/7/20.
 */
@Service
public class WarehouseCityBL {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseCityBL.class);

    @Reference
    private IStoreWareHouseService iStoreWareHouseService;

    /**
     * 根据仓库Id获取仓库所属城市Id
     *
     * @param warehouseId
     * @return
     */
    public Integer getCityIdByWarehouseId(Integer warehouseId) {
        Integer defaultCityId = null;
        // 根据仓库Id获取所有将当前仓库设置为默认仓库的城市Id集合
        List<Integer> lstCityIds = iStoreWareHouseService.getAllCityByWarehouseId(warehouseId);
        // 如果只有一个城市将仓库设置为默认，则取当前城市
        if (lstCityIds.size() == 1) {
            defaultCityId = lstCityIds.get(0);
        } else {// 如果存在多个城市公用当前仓库，且都将当前仓库设置为默认，则取仓库信息中的主要城市Id
            defaultCityId = iStoreWareHouseService.getMajorCityByWarehouseId(warehouseId);
        }
        if (defaultCityId == null) {
            // 根据仓库Id获取城市，如果多个城市公用一个仓库，取仓库主城市，如果还是找不到，取仓库前三位
            defaultCityId = Integer.parseInt(warehouseId.toString().substring(0, 3));
        }
        return defaultCityId;
    }
}

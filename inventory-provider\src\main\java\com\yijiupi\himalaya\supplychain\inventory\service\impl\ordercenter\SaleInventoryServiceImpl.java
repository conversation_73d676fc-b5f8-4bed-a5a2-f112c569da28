package com.yijiupi.himalaya.supplychain.inventory.service.impl.ordercenter;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.ProductOwnerInventoryQueryParam;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryQueryParam;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.InventoryOrderCenterBL;
import com.yijiupi.himalaya.supplychain.inventory.service.ordercenter.ISaleInventoryService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-12 14:15
 **/
@Service(timeout = 30000)
public class SaleInventoryServiceImpl implements ISaleInventoryService {

    @Resource
    private InventoryOrderCenterBL inventoryOrderCenterBL;

    /**
     * 根据城市id和规格查询销售库存接口
     *
     * @param param 查询条件
     * @return 查询结果
     */
    @Override
    public List<SaleInventoryInfoDTO> findSaleInventoryByCityIdAndProductSpecId(SaleInventoryQueryParam param) {
        return inventoryOrderCenterBL.findSaleInventoryByCityIdAndProductSpecId(param);
    }

    /**
     * 根据仓库和商品规格集合查询销售库存信息
     *
     * @param param 查询条件
     * @return 查询结果
     */
    @Override
    public List<SaleInventoryInfoDTO> findInventoryByProductOwners(ProductOwnerInventoryQueryParam param) {
        return inventoryOrderCenterBL.findInventoryByProductOwners(param);
    }

}

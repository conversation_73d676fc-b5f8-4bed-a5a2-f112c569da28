package com.yijiupi.himalaya.supplychain.batchinventory.service.impl.batch;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventorySyncBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventorySyncService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;

/**
 * 货位库存同步
 *
 * <AUTHOR>
 * @date 2019/2/23 18:19
 */
@Service(timeout = 300000)
public class BatchInventorySyncServiceImpl implements IBatchInventorySyncService {

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Autowired
    private BatchInventorySyncBL batchInventorySyncBL;
    private final static Logger LOGGER = LoggerFactory.getLogger(IBatchInventorySyncService.class);

    /**
     * 根据产品货位配置同步货位库存
     */
    @Override
    public void syncBatchInventoryByProductLocaion(BatchInventorySyncQueryDTO queryDTO) {
        StopWatch stopWatch = new StopWatch("syncBatchInventoryByProductLocation");
        stopWatch.start("开始同步");
        // queryDTO.setCurrentPage(1);
        // queryDTO.setPageSize(1000);
        // Warehouse warehouse = iWarehouseQueryService.findWarehouseById(queryDTO.getWarehouseId());
        // queryDTO.setCityId(warehouse.getCityId());
        //
        // PageList<BatchInventorySyncPO> pageList = batchInventorySyncBL.getPageList(queryDTO);
        // if (Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getDataList())) {
        // LOGGER.warn("没有数据需要初始化");
        // }
        // int i = 0;
        // while (queryDTO.getCurrentPage() <= pageList.getPager().getTotalPage()) {
        // if (CollectionUtils.isEmpty(pageList.getDataList())) {
        // break;
        // }
        // if (i > pageList.getPager().getTotalPage()) {
        // LOGGER.warn("同步库区库存 , 当前页面值 : {} ; 总页面值 : {} ; i 值 : {} ;", pageList.getPager().getCurrentPage(),
        // pageList.getPager().getTotalPage(), i);
        // break;
        // }
        // if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pageList.getDataList())) {
        // queryDTO.setCurrentPage(queryDTO.getCurrentPage() + 1);
        // pageList = batchInventorySyncBL.getPageList(queryDTO);
        // i++;
        // continue;
        // }
        //
        // batchInventorySyncBL.syncBatchInventoryByProductLocation(pageList.getDataList(), queryDTO);
        // queryDTO.setCurrentPage(queryDTO.getCurrentPage() + 1);
        // pageList = batchInventorySyncBL.getPageList(queryDTO);
        // i++;
        // }

        //
        // LOGGER.info("同步库区库存信息成功，入参:{}, 耗时 : {}", JSON.toJSONString(queryDTO), stopWatch.prettyPrint());
        batchInventorySyncBL.syncBatchInventoryByProductLocationList(queryDTO);
        stopWatch.stop();
        LOGGER.info("同步库区库存信息成功，入参:{}, 耗时 : {}", JSON.toJSONString(queryDTO), stopWatch.prettyPrint());
    }

    /**
     * 清除负货位库存
     */
    @Override
    public BatchInventoryClearNegativeLogicDTO
        clearBatchInventoryByProductLocaion(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        return batchInventorySyncBL.clearBatchInventoryByProductLocaion(batchInventorySyncQueryDTO);
    }

    /**
     * 清除总库存为0的货位库存不为0的项
     */
    @Override
    public void clearZeroBatchInventoryByWarehouseId(Integer warehouseId) {
        batchInventorySyncBL.clearZeroBatchInventoryByWarehouseId(warehouseId);
    }

    /**
     * 校正总库存与货位库存
     */
    @Override
    public void adjustBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        batchInventorySyncBL.adjustBatchInventory(batchInventorySyncQueryDTO);
    }

    /**
     * 转移指定货位或者货区的库存
     */
    @Override
    public void transferBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        batchInventorySyncBL.transferBatchInventory(batchInventorySyncQueryDTO);
    }

    /**
     * 将所选库区中所有产品移动到产品关联货位
     */
    @Override
    public void batchInventoryTransferByLocationArea(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        AssertUtils.notNull(batchInventorySyncQueryDTO, "同步货位库存参数不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getLocationAreaList(), "货区不能为空");
        batchInventorySyncBL.batchInventoryTransferByLocationArea(batchInventorySyncQueryDTO);
    }

    /**
     * 残次品货位库存校正
     */
    @Override
    public void adjustDisposedProductLocationInventory(BatchInventoryAdjustDTO batchInventoryAdjustDTO) {
        AssertUtils.notNull(batchInventoryAdjustDTO.getOrgId(), "城市id不能为空");
        AssertUtils.notNull(batchInventoryAdjustDTO.getWarehouseId(), "仓库id不能为空");
        batchInventorySyncBL.adjustDisposedProductLocationInventory(batchInventoryAdjustDTO);
    }

    @Override
    public void adjustDisposedProductLocationInventoryByERP(BatchInventoryAdjustDTO batchInventoryAdjustDTO) {
        AssertUtils.notNull(batchInventoryAdjustDTO.getWarehouseId(), "仓库id不能为空");
        batchInventorySyncBL.adjustDisposedProductLocationInventoryByERP(batchInventoryAdjustDTO);
    }

    /**
     * 根据ERP校正货位库存的生产日期
     */
    @Override
    public void syncBatchInventoryProductionDateByERP(BatchInventoryProductionDateSyncQueryDTO queryDTO) {
        batchInventorySyncBL.syncBatchInventoryProductionDateByERP(queryDTO);
    }

    /**
     * 校正批次编号
     */
    @Override
    public void syncBatchInventoryBatchNo(BatchInventoryProductionDateSyncQueryDTO queryDTO) {
        batchInventorySyncBL.syncBatchInventoryBatchNo(queryDTO);
    }

    @Override
    public void addBatchInventoryByWarehouseId(Integer warehouseId, String locationName) {
        AssertUtils.notNull(warehouseId, "仓库id不能为空");
        AssertUtils.notNull(locationName, "货位不能为空");
        batchInventorySyncBL.addBatchInventoryByWarehouseId(warehouseId, locationName);
    }

    /**
     * 根据拣货统计库存计算周转区库存
     */
    @Override
    public void adjustCHKBatchInventoryByPicking(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        AssertUtils.notNull(batchInventorySyncQueryDTO, "矫正周转区货位库存参数不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getWarehouseId(), "仓库id不能为空");
        batchInventorySyncBL.adjustCHKBatchInventoryByPicking(batchInventorySyncQueryDTO);
    }

    /**
     * 批量清除负货位库存
     */
    @Override
    public List<BatchInventoryClearNegativeLogicDTO>
        batchClearBatchInventoryByProductLocaion(List<Integer> warehouseIds) {
        AssertUtils.notEmpty(warehouseIds, "仓库ids不能为空");
        List<BatchInventoryClearNegativeLogicDTO> dtoList = new ArrayList<>();
        warehouseIds.stream().forEach(id -> {
            BatchInventorySyncQueryDTO batchInventorySyncQueryDTO = new BatchInventorySyncQueryDTO();
            batchInventorySyncQueryDTO.setWarehouseId(id);
            BatchInventoryClearNegativeLogicDTO dto =
                batchInventorySyncBL.clearBatchInventoryByProductLocaion(batchInventorySyncQueryDTO);
            if (dto != null) {
                dtoList.add(dto);
            }
        });
        return dtoList;
    }

    /**
     * 转移补货上限库存到指定货位
     */
    @Override
    public void batchInventoryTransferNoOrder(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        LOGGER.info("转移补货上限库存到指定货位，入参:{}, 耗时 : {}", JSON.toJSONString(batchInventorySyncQueryDTO));
        AssertUtils.notNull(batchInventorySyncQueryDTO, "同步货位库存参数不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getWarehouseId(), "仓库id不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getLocationAreaList(), "货区不能为空");
        AssertUtils.notNull(batchInventorySyncQueryDTO.getToLocationName(), "目的货位不能为空");
        batchInventorySyncBL.batchInventoryTransferNoOrder(batchInventorySyncQueryDTO);
    }

    /**
     * 指定货位/货区间库存转移,目前仅支持2.5+
     */
    @Override
    public void batchInventoryMove(BatchInventoryMoveDTO moveDTO) {
        batchInventorySyncBL.batchInventoryMove(moveDTO);
    }

    /**
     * 批量清除总库存为0的货位库存不为0的项
     */
    @Override
    public void batchClearZeroBatchInventory(List<Integer> warehouseIds) {
        warehouseIds.stream().forEach(warehouseId -> {
            batchInventorySyncBL.clearZeroBatchInventoryByWarehouseId(warehouseId);
        });
    }

    /**
     * 批量校正总库存与货位库存
     */
    @Override
    public void batchAdjustBatchInventory(List<Integer> warehouseIds) {
        warehouseIds.stream().forEach(warehouseId -> {
            BatchInventorySyncQueryDTO syncQueryDTO = new BatchInventorySyncQueryDTO();
            syncQueryDTO.setWarehouseId(warehouseId);
            batchInventorySyncBL.adjustBatchInventory(syncQueryDTO);
        });
    }

    /**
     * 清除负货位库存
     */
    @Override
    public BatchInventoryClearNegativeLogicDTO
        clearBatchInventory(BatchInventorySyncQueryDTO batchInventorySyncQueryDTO) {
        return batchInventorySyncBL.clearBatchInventory(batchInventorySyncQueryDTO);
    }

    /**
     * 残次品库存批量同步erp
     */
    @Override
    public void syncDefectiveToErpByWarehouseIds(List<Integer> warehouseIds) {
        batchInventorySyncBL.syncDefectiveToErpByWarehouseIds(warehouseIds);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeInfoMapper">
    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        BatchAttributeinfoNo,
        Attribute_Id,
        AttributeName,
        AttributeValue_Id,
        AttributeValueName,
        Remark,
        CreateUser
    </sql>
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeInfoPO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="BatchAttributeinfoNo" property="batchAttributeInfoNo" jdbcType="VARCHAR"/>
        <result column="Attribute_Id" property="attributeId" jdbcType="BIGINT"/>
        <result column="AttributeName" property="attributeName" jdbcType="VARCHAR"/>
        <result column="AttributeValue_Id" property="attributeValueId" jdbcType="VARCHAR"/>
        <result column="AttributeValueName" property="attributeValueName" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="batchAttributeInfoPO.id">
        INSERT INTO batchattributeinfo (
        id,
        BatchAttributeinfoNo,
        Attribute_Id,
        AttributeName,
        AttributeValue,
        Remark,
        CreateUser
        ) VALUES (
        #{batchAttributeInfoPO.id,jdbcType=BIGINT},
        #{batchAttributeInfoPO.batchAttributeInfoNo,jdbcType=VARCHAR},
        #{batchAttributeInfoPO.attributeId,jdbcType=BIGINT},
        #{batchAttributeInfoPO.attributeName,jdbcType=VARCHAR},
        #{batchAttributeInfoPO.attributeValue,jdbcType=VARCHAR},
        #{batchAttributeInfoPO.remark,jdbcType=VARCHAR},
        #{batchAttributeInfoPO.createUser,jdbcType=VARCHAR}
        )
    </insert>


    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchattributeinfo (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="batchAttributeInfoPOs" item="batchAttributeInfoPO" index="index" separator=",">
            (
            #{batchAttributeInfoPO.id,jdbcType=BIGINT},
            #{batchAttributeInfoPO.batchAttributeInfoNo,jdbcType=VARCHAR},
            #{batchAttributeInfoPO.attributeId,jdbcType=BIGINT},
            #{batchAttributeInfoPO.attributeName,jdbcType=VARCHAR},
            #{batchAttributeInfoPO.attributeValueId,jdbcType=VARCHAR},
            #{batchAttributeInfoPO.attributeValueName,jdbcType=VARCHAR},
            #{batchAttributeInfoPO.remark,jdbcType=VARCHAR},
            #{batchAttributeInfoPO.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE batchattributeinfo
        <set>
            <if test="batchAttributeInfoPO.id != null">id= #{batchAttributeInfoPO.id,jdbcType=BIGINT},</if>
            <if test="batchAttributeInfoPO.batchAttributeInfoNo != null">BatchAttributeinfoNo=
                #{batchAttributeInfoPO.batchAttributeInfoNo,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeInfoPO.attributeId != null">Attribute_Id=
                #{batchAttributeInfoPO.attributeId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeInfoPO.attributeName != null">AttributeName=
                #{batchAttributeInfoPO.attributeName,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeInfoPO.attributeValue != null">AttributeValue=
                #{batchAttributeInfoPO.attributeValue,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeInfoPO.remark != null">Remark= #{batchAttributeInfoPO.remark,jdbcType=VARCHAR},</if>
            <if test="batchAttributeInfoPO.createUser != null">CreateUser=
                #{batchAttributeInfoPO.createUser,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{batchAttributeInfoPO.id,jdbcType=BIGINT}
    </update>

    <select id="findAttributeInfoByNo"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeInfoDTO">
        select
        id,
        BatchAttributeinfoNo,
        Attribute_Id as attributeId,
        AttributeName,
        AttributeValue_Id as attributeValueId,
        AttributeValueName,
        Remark,
        CreateUser
        from batchattributeinfo
        where BatchAttributeinfoNo = #{batchAttributeInfoNo,jdbcType=VARCHAR}
    </select>

    <!--auto generated by codehelper on 2018-04-12 21:01:56-->
    <select id="findByBatchAttributeInfoNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchattributeinfo
        where BatchAttributeinfoNo=#{batchAttributeInfoNo,jdbcType=VARCHAR}
    </select>

    <select id="findByBatchAttributeInfoNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchattributeinfo
        where BatchAttributeinfoNo in
        <foreach collection="batchAttrNoList" item="batchNo" open="(" close=")" separator=",">
            #{batchNo,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>


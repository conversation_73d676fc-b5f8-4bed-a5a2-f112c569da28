package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.framework.businessaudit.BusinessAuditEntry;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;

/**
 * <AUTHOR> 自提订单
 */
@Service
public class PickUpInStoreOrderBL {
    private static final Logger LOG = LoggerFactory.getLogger(PickUpInStoreOrderBL.class);
    @Autowired
    private IdempotenceConsumer idempotenceConsumer;

    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;

    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    /**
     * 自提订单确认出库变更库存
     *
     * @param inventoryDeliveryOrders
     */
    public List<InventoryDeliveryJiupiOrder>
        pickUpInStoreOrderAffirmChangeInventory(List<InventoryDeliveryJiupiOrder> inventoryDeliveryOrders) {
        List<String> noProcessOrderNos = new ArrayList<>();
        inventoryDeliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, inventoryDeliveryOrders,
                    OrderDeliveryOpType.ZITI_OUT_STOCK_ORDER_TYPE, "自提订单");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });
        // 扣库存
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : inventoryDeliveryOrders) {
                warehouseChangListBOConverter.processZiTiOrderItemToBO(warehouseChangeList,
                    inventoryDeliveryJiupiOrder);
            }
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, true, false, true, true,
                false);
        });
        // 添加日志审计
        for (InventoryDeliveryJiupiOrder deliveryOrder : inventoryDeliveryOrders) {
            BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "outstockorder");
            builder.businessId(String.valueOf(deliveryOrder.getId())).addFeature("OrderNo", deliveryOrder.getOrderNo());
            if (deliveryOrder.getOrderType() != null) {
                builder.addFeature("OrderType", String.valueOf(deliveryOrder.getOrderType()));
                OutStockOrderTypeEnum outStockOrderTypeEnum =
                    OutStockOrderTypeEnum.getEnum(deliveryOrder.getOrderType().byteValue());
                if (outStockOrderTypeEnum != null) {
                    builder.content(String.format("%s-自提出库", outStockOrderTypeEnum.name()));
                } else {
                    builder.content("自提出库");
                }
            } else {
                builder.content("自提出库");
            }
            builder.done();
        }

        // 返回实际库存处理结果
        return getInventoryProcessResults(inventoryDeliveryOrders, warehouseChangeList);
    }

    /**
     * 组装库存实际处理结果
     *
     * @param deliveryOrders 原订单
     * @param warehouseChangeList 库存处理结果
     * @return
     */
    private List<InventoryDeliveryJiupiOrder> getInventoryProcessResults(
        List<InventoryDeliveryJiupiOrder> deliveryOrders, List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return null;
        }
        List<InventoryDeliveryJiupiOrder> orders = new ArrayList<>();
        Map<Long, List<WarehouseInventoryChangeBO>> warehouseChangeMap =
            warehouseChangeList.stream().collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getOmsOrderItemId));
        deliveryOrders.forEach(order -> {
            InventoryDeliveryJiupiOrder newOrder = new InventoryDeliveryJiupiOrder();
            BeanUtils.copyProperties(order, newOrder);

            List<InventoryDeliveryJiupiOrderItem> items = new ArrayList<>();
            order.getItems().stream()
                .filter(StreamUtils.distinctByKey(InventoryDeliveryJiupiOrderItem::getOmsOrderItemId)).forEach(item -> {
                    List<WarehouseInventoryChangeBO> changeBOS = warehouseChangeMap.get(item.getOmsOrderItemId());
                    if (CollectionUtils.isNotEmpty(changeBOS)) {
                        changeBOS.forEach(bo -> {
                            InventoryDeliveryJiupiOrderItem newItem = new InventoryDeliveryJiupiOrderItem();
                            BeanUtils.copyProperties(item, newItem);

                            newItem.setOrderItemDetailId(bo.getOrderItemDetailId());
                            newItem.setDeliverCount(bo.getCount().abs());
                            newItem.setOwnerId(bo.getOwnId());
                            newItem.setSecOwnerId(bo.getSecOwnerId());
                            newItem.setOriginOrderItemDetailId(bo.getOriginOrderItemDetailId());

                            items.add(newItem);
                        });
                    }
                });

            if (CollectionUtils.isNotEmpty(items)) {
                newOrder.setItems(items);
            }
            orders.add(newOrder);
        });

        LOG.info("自提出库库存实际处理订单数据:{}", JSON.toJSONString(orders));
        return orders;
    }
}

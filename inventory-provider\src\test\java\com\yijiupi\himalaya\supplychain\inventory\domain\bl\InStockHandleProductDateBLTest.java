package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.List;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.instockorder.query.InStockOrderQueryDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate.InStockHandleProductDateBL;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.InOutStockQueryService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InStockHandleProductDateBLTest {

    @Autowired
    private InStockHandleProductDateBL inStockHandleProductDateBL;

    @Reference
    private IInStockQueryService iInStockQueryService;
    @Reference
    private InOutStockQueryService inOutStockQueryService;

    @Test
    public void processOrderProductDateTest() {
        InStockOrderQueryDTO inStockOrderQueryDTO = new InStockOrderQueryDTO();
        inStockOrderQueryDTO.setOrgId(998);
        inStockOrderQueryDTO.setBoundBatchNo("CK111111");
        List<OrderDTO> orderList1 = iInStockQueryService.listInStockOrderByBoundNo(inStockOrderQueryDTO);

        inStockOrderQueryDTO.setOrgId(704);
        inStockOrderQueryDTO.setBoundBatchNo("CK22222");
        List<OrderDTO> orderList2 = iInStockQueryService.listInStockOrderByBoundNo(inStockOrderQueryDTO);

        inStockOrderQueryDTO.setOrgId(998);
        inStockOrderQueryDTO.setBoundBatchNo("CK33333");
        List<OrderDTO> orderList3 = iInStockQueryService.listInStockOrderByBoundNo(inStockOrderQueryDTO);

//        List<OrderDTO> returnOrderList = iOutStockCommQueryService.findOrderByNos(998, 9843, Arrays.asList("998326400004"));
//        List<OrderDTO> dtOrderList = iOutStockCommQueryService.findOrderByNos(998, 9981, Arrays.asList("DT998322700004"));
//        List<OrderDTO> prefixOrderList = iOutStockCommQueryService.findOrderByNos(998, 9981, Arrays.asList("CR70420230927014"));



        List<OrderDTO> orderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderList1)) {
            orderList.addAll(orderList1);
        }
        if (CollectionUtils.isNotEmpty(orderList2)) {
            orderList.addAll(orderList2);
        }
        if (CollectionUtils.isNotEmpty(orderList3)) {
            orderList.addAll(orderList3);
        }

        inStockHandleProductDateBL.processOrderProductDate(orderList);

        Assertions.assertThat(orderList).isNotNull();
    }

}

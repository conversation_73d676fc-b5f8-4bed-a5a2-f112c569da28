package com.yijiupi.himalaya.supplychain.batchinventory.util;

import java.util.Date;

public class StringFormatUtil {

    public static String format(String format, Object... args) {
        String[] strArgs = new String[args.length];
        for (int i = 0; i < args.length; i++) {
            strArgs[i] = toEmptyString(args[i]);
        }
        return String.format(format, strArgs);
    }

    private static String toEmptyString(Object obj) {
        if (obj instanceof Date) {
            return String.valueOf(((Date)obj).getTime());
        }
        return obj == null ? "" : String.valueOf(obj);
    }
}
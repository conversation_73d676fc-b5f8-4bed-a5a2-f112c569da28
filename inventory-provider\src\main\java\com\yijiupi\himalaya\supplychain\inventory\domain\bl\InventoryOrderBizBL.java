package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.constants.InStockStrategyConstants;
import com.yijiupi.himalaya.supplychain.constants.WarehouseConfigConstants;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.SecOwnerDetailDTO;
import com.yijiupi.himalaya.supplychain.enums.DefaultTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.enums.OutStockMode;
import com.yijiupi.himalaya.supplychain.framework.businessaudit.BusinessAuditEntry;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.InStockDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.putawayTypeEnum;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IInStockTaskProcessService;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.*;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockCommService;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.instockorder.utils.constant.AllotOrderTypeConstans;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.async.InStockTransferOrderAsyncBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.async.OutStockTransferOrderAsyncBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.SellInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.erp.ErpInOutStockBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.TrainsImportOutStockBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate.InStockHandleProductDateBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.InventoryInStockOrderBatchDTOConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.OrderDTOConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ZiTiConfirmDeliverDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.*;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.*;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.*;
import com.yijiupi.himalaya.supplychain.pushorder.enums.pushCapabilityTypeEnum;
import com.yijiupi.himalaya.supplychain.service.IDefaultLocationConfigService;
import com.yijiupi.himalaya.supplychain.service.IFinanceService;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.*;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productSourceCode.UpdateProductSourceCodeDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productcontrolconfig.ProductSourceCodeRecordSyncDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productsupplier.ProductSupplierDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ProductSourceCodeRecordStateEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.SupplierTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.*;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchManageService;
import com.yijiupi.himalaya.supplychain.waves.batch.IPackageOrderService;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderPickTypeConstant;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * 库存订单业务BL.
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class InventoryOrderBizBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryOrderBizBL.class);

    @Autowired
    private IdempotenceConsumer idempotenceConsumer;
    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;
    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;
    @Reference(timeout = 60000)
    private IInStockOrderService iInStockOrderService;
    @Reference
    private IOutStockQueryService iOutStockQueryService;
    @Reference
    private IZiTiOrderService iZiTiOrderService;
    @Reference
    private IOMSOrderSyncService iomsOrderSyncService;
    @Reference
    private IOutStockService iOutStockService;

    @Reference
    private IInStockTaskProcessService iInStockTaskProcessService;
    @Reference
    private IProductLocationService iProductLocationService;
    @Reference
    private IDefaultLocationConfigService iDefaultLocationConfigService;
    @Reference
    private WarehouseConfigService warehouseConfigService;
    @Reference
    private IPackageOrderService iPackageOrderService;

    @Reference
    private IBatchManageService iBatchManageService;

    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;

    @Reference
    private IContentConfigurationService iContentConfigurationService;
    @Reference
    private IOutStockCommManageService outStockCommManageService;
    @Reference
    private IProductSkuQueryService productSkuQueryService;
    @Reference
    private IOutStockCommQueryService outStockCommQueryService;
    @Reference
    private IInStockCommService iInStockCommService;

    @Reference(timeout = 60000)
    private IInStockQueryService iInStockQueryService;

    @Reference
    private IProductSupplierService iProductSupplierService;

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Reference
    private IFinanceService iFinanceService;

    @Autowired
    private InStockTransferOrderAsyncBL inStockTransferOrderAsyncBL;

    @Autowired
    private OutStockTransferOrderAsyncBL outStockTransferOrderAsyncBL;

    @Reference
    private IProductSourceService iProductSourceService;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Autowired
    private TrainsImportOutStockBL trainsImportOutStockBL;

    @Reference
    private ILocationService iLocationService;

    @Autowired
    private BatchInBoundChangeInventoryBL batchInBoundChangeInventoryBL;
    @Autowired
    private InStockHandleProductDateBL inStockHandleProductDateBL;

    @Resource
    private ErpInOutStockBL erpInOutStockBL;

    /**
     * 订单缺货标记.（返销售库存）
     */
    public void outOfStockRemark(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        // List<String> noProcessOrderIds = idempotenceConsumer.getNoProcessOrderIds(deliveryOrders,
        // OrderDeliveryOpType.orderOutOfStockMessageType, "订单缺货标记");
        //
        // if (CollectionUtils.isEmpty(noProcessOrderIds)) {
        // LOG.error(String.format("订单缺货标记-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
        // return;
        // }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        // idempotenceConsumer.apply(noProcessOrderIds, () -> {

        // 遍历添加批次所有BO到list中
        for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {

            // //招商订单需要将下单SKU转为发货城市的SKU
            // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);

            warehouseChangListBOConverter.processOutOfStockOrderItemToOrderDeliveryBO(sellChangeList,
                inventoryDeliveryJiupiOrder);
        }
        // });

        // 移除生成内配单的订单，内配退入库时再加销售库存
        setNotReturnSaleInventoryBySellBO(sellChangeList);

        // 处理销售库存发消息
        warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);
    }

    /**
     * 扣交易系统销售库存
     */
    public void returnSaleInventoryByOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();

        for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
            warehouseChangListBOConverter.processReturnSaleInventoryOrderItemToOrderDeliveryBO(sellChangeList,
                inventoryDeliveryJiupiOrder);
        }

        LOG.info(String.format("扣交易销售库存对象-2：%s", JSON.toJSONString(sellChangeList)));

        // //移除生成内配单的订单，内配退入库时再加销售库存
        // setNotReturnSaleInventoryBySellBO(sellChangeList);

        // 处理销售库存发消息
        warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);
    }

    /**
     * 移除生成内配单的订单，等到内配退入库时，再返销售库存
     *
     * @param sellChangeList
     */
    private void setNotReturnSaleInventoryBySellBO(List<SellInventoryChangeBO> sellChangeList) {
        List<String> lstOrderNos = sellChangeList.stream().filter(p -> StringUtil.isNotEmpty(p.getOrderNo()))
            .map(p -> p.getOrderNo()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstOrderNos)) {
            return;
        }
        Integer orgId = sellChangeList.get(0).getCityId();
        List<String> lstNotReturnOrderNos = findNotReturnSaleInventoryOrderByIds(orgId, lstOrderNos);
        if (CollectionUtils.isNotEmpty(lstNotReturnOrderNos)) {
            sellChangeList
                .removeIf(p -> StringUtil.isNotEmpty(p.getOrderNo()) && lstNotReturnOrderNos.contains(p.getOrderNo()));
        }
    }

    /**
     * 生成内配单的订单，不需要返销售库存 如果有部分配送或者退货的情况，到内配退入库时再处理
     *
     * @param warehouseChangeList
     */
    private void setNotReturnSaleInventoryByChangeBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
        List<String> lstExOrderNos) {
        List<String> lstOrderNos = warehouseChangeList.stream()
            .filter(p -> p.getHasUpdateOPInventory() && StringUtil.isNotEmpty(p.getOrderNo())).map(p -> p.getOrderNo())
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstOrderNos)) {
            return;
        }
        Integer orgId = warehouseChangeList.get(0).getCityId();
        List<String> lstNotReturnOrderNos = findNotReturnSaleInventoryOrderByIds(orgId, lstOrderNos);
        if (CollectionUtils.isNotEmpty(lstExOrderNos)) {
            lstNotReturnOrderNos.addAll(lstExOrderNos);
        }
        if (CollectionUtils.isNotEmpty(lstNotReturnOrderNos)) {
            warehouseChangeList.forEach(p -> {
                if (StringUtil.isNotEmpty(p.getOrderNo()) && lstNotReturnOrderNos.contains(p.getOrderNo())) {
                    p.setHasUpdateOPInventory(false);
                }
            });
        }
    }

    private List<String> findNotReturnSaleInventoryOrderByIds(Integer orgId, List<String> orderNos) {
        List<String> lstOrderNo = new ArrayList<>();
        // 2019-07-05 前置仓统一返销售库存，交易系统自己处理
        // // 获取订单是否生成内配单
        // List<OrderDTO> outStockOrders = iOutStockQueryService.findOutStockOrderByNoS(orgId, orderNos);
        // if (CollectionUtils.isNotEmpty(outStockOrders)) {
        // lstOrderNo = outStockOrders.stream().filter(p -> p.getCreateAllocation() != null &&
        // p.getCreateAllocation()).map(p -> p.getRefOrderNo()).distinct().collect(Collectors.toList());
        // if (CollectionUtils.isNotEmpty(lstOrderNo)) {
        // LOG.info(String.format("配送单已生成内配单，不需要处理销售库存！OrderNo：%s", JSON.toJSONString(lstOrderNo)));
        // }
        // }
        return lstOrderNo;
    }

    /**
     * 订单配送出库: 扣除仓库库存 todo
     */
    public List<InventoryDeliveryJiupiOrder> orderDeliveryNew(List<InventoryDeliveryJiupiOrder> deliveryOrders,
        boolean checkWarehouseInventory, boolean checkOrder) {
        Integer warehouseId = deliveryOrders.get(0).getWarehouseId();
        Integer cityId = deliveryOrders.get(0).getCityId();
        List<String> noProcessOrderIds = idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders,
            OrderDeliveryOpType.ORDER_DELIVER_MESSAGE_TYPE, "订单配送出库");

        if (CollectionUtils.isEmpty(noProcessOrderIds)) {
            LOG.info(String.format("订单配送出库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            return null;
        }

        if (checkWarehouseInventory) {
            // 检验是否店仓或者配置可以负库存发货仓库
            Boolean allowNegative = checkContentConfig(warehouseId, "AllowNegativeDelivery");
            if (allowNegative) {
                checkWarehouseInventory = false;
            }
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        boolean finalCheckWarehouseInventory = checkWarehouseInventory;
        idempotenceConsumer.apply(noProcessOrderIds, () -> {

            // 普通发货allotType转为普通，避免影响后续单据校验
            deliveryOrders.forEach(order -> order.setAllotType(OrderConstant.ALLOT_TYPE_DEFAULT));
            if (checkOrder) {
                checkOrder(deliveryOrders);
            }

            // 遍历添加批次所有BO到list中
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {

                // //招商订单需要将下单SKU转为发货城市的SKU
                // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);
                // 跨库订单不处理
                if (inventoryDeliveryJiupiOrder.getCrossWareHouse() == null
                    || !inventoryDeliveryJiupiOrder.getCrossWareHouse()) {
                    warehouseChangListBOConverter.processOrderItemToOrderDeliveryBO(sellChangeList, warehouseChangeList,
                        inventoryDeliveryJiupiOrder, finalCheckWarehouseInventory);
                }
            }

            if (CollectionUtils.isEmpty(warehouseChangeList)) {
                LOG.info(String.format("订单配送出库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            }

            // 订单直发不需要发货数量记录
            Boolean isUpdateDeliveryCount = true;
            if (deliveryOrders.get(0).getPackageAttribute() != null
                && deliveryOrders.get(0).getPackageAttribute().equals(OrderPickTypeConstant.PICKTYPE_LOGIC)) {
                isUpdateDeliveryCount = false;
            }
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, finalCheckWarehouseInventory,
                isUpdateDeliveryCount, true, true, false);

        });
        // 添加审计日志
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "outstockorder");
                builder.businessId(String.valueOf(deliveryOrder.getId())).addFeature("OrderNo",
                    deliveryOrder.getOrderNo());
                if (deliveryOrder.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(deliveryOrder.getOrderType()));
                    OutStockOrderTypeEnum outStockOrderTypeEnum =
                        OutStockOrderTypeEnum.getEnum(deliveryOrder.getOrderType().byteValue());
                    if (outStockOrderTypeEnum != null) {
                        builder.content(String.format("%s-订单按批次发货", outStockOrderTypeEnum.name()));
                    } else {
                        builder.content("订单按批次发货");
                    }
                } else {
                    builder.content("订单按批次发货");
                }
                builder.done();
            }
        }

        // 更新订单状态为已出库
        updateOutStockOrderState(deliveryOrders, OutStockOrderStateEnum.已出库.getType());

        // 删除掉入库单.
        List<String> deliveryOrderNOs =
            deliveryOrders.stream().filter(e -> e != null && StringUtils.isNotEmpty(e.getOrderNo()))
                .map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deliveryOrderNOs)) {
            iInStockOrderService.deleteInStockOrderByNOList(cityId, warehouseId, deliveryOrderNOs);
        }

        // 移除生成内配单的订单，内配退入库时再加销售库存
        // setNotReturnSaleInventoryBySellBO(sellChangeList);

        // 处理生成内配单的订单，不需要返销售库存
        setNotReturnSaleInventoryByChangeBO(warehouseChangeList, null);

        // 处理销售库存发消息(由OMS处理)
        // warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);

        // 更新波次状态为已出库
        List<String> orderNos = warehouseChangeList.stream().filter(p -> StringUtils.isNotEmpty(p.getOrderNo()))
            .map(WarehouseInventoryChangeBO::getOrderNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderNos)) {
            BatchUpdateDTO batchUpdateDTO = new BatchUpdateDTO();
            batchUpdateDTO.setOrgId(warehouseChangeList.get(0).getCityId());
            batchUpdateDTO.setWarehouseId(warehouseChangeList.get(0).getWarehouseId());
            batchUpdateDTO.setOrderNos(orderNos);
            batchUpdateDTO.setBatchState(BatchStateEnum.ALREADYOUTOFSTORE.getType());
            batchUpdateDTO.setOperateUser(warehouseChangeList.get(0).getCreateUserName());
            iBatchManageService.updateBatchStateByOrderNos(batchUpdateDTO);
        }

        // 返回实际库存处理结果
        return getInventoryProcessResults(deliveryOrders, warehouseChangeList);
    }

    /**
     * 组装库存实际处理结果
     *
     * @param deliveryOrders 原订单
     * @param warehouseChangeList 库存处理结果
     * @return
     */
    private List<InventoryDeliveryJiupiOrder> getInventoryProcessResults(
        List<InventoryDeliveryJiupiOrder> deliveryOrders, List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return null;
        }
        List<InventoryDeliveryJiupiOrder> orders = new ArrayList<>();
        Map<Long, List<WarehouseInventoryChangeBO>> warehouseChangeMap =
            warehouseChangeList.stream().collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getOmsOrderItemId));
        deliveryOrders.forEach(order -> {
            InventoryDeliveryJiupiOrder newOrder = new InventoryDeliveryJiupiOrder();
            BeanUtils.copyProperties(order, newOrder);

            List<InventoryDeliveryJiupiOrderItem> items = new ArrayList<>();
            order.getItems().stream()
                .filter(StreamUtils.distinctByKey(InventoryDeliveryJiupiOrderItem::getOmsOrderItemId)).forEach(item -> {
                    List<WarehouseInventoryChangeBO> changeBOS = warehouseChangeMap.get(item.getOmsOrderItemId());
                    if (CollectionUtils.isNotEmpty(changeBOS)) {
                        changeBOS.forEach(bo -> {
                            InventoryDeliveryJiupiOrderItem newItem = new InventoryDeliveryJiupiOrderItem();
                            BeanUtils.copyProperties(item, newItem);

                            newItem.setOrderItemDetailId(bo.getOrderItemDetailId());
                            newItem.setDeliverCount(bo.getCount().abs());
                            newItem.setOwnerId(bo.getOwnId());
                            newItem.setSecOwnerId(bo.getSecOwnerId());

                            items.add(newItem);
                        });
                    }
                });

            if (CollectionUtils.isNotEmpty(items)) {
                newOrder.setItems(items);
            }
            orders.add(newOrder);
        });

        LOG.info("库存实际处理订单数据:{}", JSON.toJSONString(orders));
        return orders;
    }

    /**
     * 订单数据与WMS订单数据比对
     *
     * @param deliveryOrders
     */
    private void checkOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }

        // 按照规则过滤不会下推的订单
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 代码删除
        List<InventoryDeliveryJiupiOrder> orders = deliveryOrders;
        // .stream().filter(order ->
        // orderPushRoolService.canWmsPush(order.getOmsOrderId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<Byte> allotTypes = new ArrayList<>();
        allotTypes.add(OrderConstant.ALLOT_TYPE_ALLOCATION);
        allotTypes.add(OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN);
        allotTypes.add(OrderConstant.ALLOT_TYPE_DELIVERY);
        allotTypes.add(OrderConstant.ALLOT_TYPE_DELIVERY_RETURN);

        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();

        orders.stream().filter(order -> allotTypes.contains(order.getAllotType()))
            .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getFromWarehouseId))
            .forEach((fromWarehouseId, orderList) -> {
                List<String> orderNos =
                    orderList.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderQueryDTO outStockOrderQueryDTO = new OutStockOrderQueryDTO();
                outStockOrderQueryDTO.setRefOrderNos(orderNos);
                outStockOrderQueryDTO.setWarehouseId(fromWarehouseId);
                outStockOrderQueryDTO.setOrgId(orderList.get(0).getFromCityId());
                List<OutStockOrderDTO> dataList =
                    outStockCommQueryService.pageListOutStockOrder(outStockOrderQueryDTO).getDataList();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    outStockOrderDTOS.addAll(dataList);
                }
            });

        orders.stream().filter(order -> !allotTypes.contains(order.getAllotType()))
            .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orderList) -> {
                List<String> orderNos =
                    orderList.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderQueryDTO outStockOrderQueryDTO = new OutStockOrderQueryDTO();
                outStockOrderQueryDTO.setRefOrderNos(orderNos);
                outStockOrderQueryDTO.setWarehouseId(warehouseId);
                outStockOrderQueryDTO.setOrgId(orderList.get(0).getCityId());
                List<OutStockOrderDTO> dataList =
                    outStockCommQueryService.pageListOutStockOrder(outStockOrderQueryDTO).getDataList();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    outStockOrderDTOS.addAll(dataList);
                }
            });

        List<String> errOrderNos = new ArrayList<>();
        List<Long> noOmsOrderItemIds = new ArrayList<>();
        List<Long> noOrderItemIds = new ArrayList<>();
        List<Long> errOrderItemIds = new ArrayList<>();

        if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
            String errNos =
                orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.joining(","));
            throw new BusinessException("发货数据异常！OrderNo:" + errNos);
        }

        Map<String, List<OutStockOrderDTO>> outStockOrderMap =
            outStockOrderDTOS.stream().collect(Collectors.groupingBy(OutStockOrderDTO::getRefOrderNo));

        for (InventoryDeliveryJiupiOrder order : orders) {
            String orderNo = order.getOrderNo();
            List<OutStockOrderDTO> outStockOrders = outStockOrderMap.get(orderNo);

            if (CollectionUtils.isEmpty(outStockOrders)) {
                errOrderNos.add(orderNo);
                continue;
            }
            order.setOrderId(Long.valueOf(outStockOrders.get(0).getId()));
            order.setCrossWareHouse(outStockOrders.get(0).getCrossWareHouse());

            List<InventoryDeliveryJiupiOrderItem> orderItems = order.getItems().stream()
                .filter(item -> item.getDeliverCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Map<Long, List<InventoryDeliveryJiupiOrderItem>> orderItemMap =
                orderItems.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getOmsOrderItemId));
            List<OutStockOrderItemDTO> outStockOrderItems = outStockOrders.stream()
                .flatMap(outStockOrderDTO -> outStockOrderDTO.getOutStockOrderItemDTOS().stream())
                .filter(item -> item.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Map<String, OutStockOrderItemDTO> outStockOrderBusinessItemMap =
                outStockOrderItems.stream().filter(item -> item.getBusinessItemId() != null).collect(Collectors
                    .toMap(OutStockOrderItemDTO::getBusinessItemId, Function.identity(), (key1, key2) -> key2));
            Map<String, OutStockOrderItemDTO> outStockOrderItemMap =
                outStockOrderItems.stream().filter(item -> item.getBusinessItemId() != null)
                    .collect(Collectors.toMap(OutStockOrderItemDTO::getId, Function.identity(), (key1, key2) -> key2));

            for (Map.Entry<Long, List<InventoryDeliveryJiupiOrderItem>> entry : orderItemMap.entrySet()) {
                BigDecimal deliverCount = entry.getValue().stream()
                    .map(InventoryDeliveryJiupiOrderItem::getDeliverCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                OutStockOrderItemDTO outStockOrderItemDTO =
                    outStockOrderBusinessItemMap.get(entry.getKey().toString()) == null
                        ? outStockOrderItemMap.get(entry.getKey().toString())
                        : outStockOrderBusinessItemMap.get(entry.getKey().toString());
                if (outStockOrderItemDTO == null) {
                    noOmsOrderItemIds.add(entry.getKey());
                    continue;
                }
                BigDecimal unitTotalCount = outStockOrderItemDTO.getUnitTotalCount();

                if (!outStockOrderItemDTO.getProductSpecificationId()
                    .equals(entry.getValue().get(0).getProductSpecification_Id())
                    || !Objects.equals(outStockOrderItemDTO.getOwnerId(), entry.getValue().get(0).getOwnerId())
                    || unitTotalCount.compareTo(deliverCount) != 0) {
                    errOrderItemIds.add(entry.getKey());
                    continue;
                }
                entry.getValue().forEach(item -> item.setOrderItem_Id(Long.valueOf(outStockOrderItemDTO.getId())));
            }

            for (OutStockOrderItemDTO outStockOrderItem : outStockOrderItems) {
                Long itemId = outStockOrderItem.getBusinessItemId() == null ? Long.valueOf(outStockOrderItem.getId())
                    : Long.valueOf(outStockOrderItem.getBusinessItemId());
                List<InventoryDeliveryJiupiOrderItem> deliveryOrderItems = orderItemMap.get(itemId) != null
                    ? orderItemMap.get(itemId) : orderItemMap.get(Long.valueOf(outStockOrderItem.getId()));
                if (CollectionUtils.isEmpty(deliveryOrderItems)) {
                    noOrderItemIds.add(itemId);
                    continue;
                }
                BigDecimal deliverCount = deliveryOrderItems.stream()
                    .map(InventoryDeliveryJiupiOrderItem::getDeliverCount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal unitTotalCount = outStockOrderItem.getUnitTotalCount();
                if (!deliveryOrderItems.get(0).getProductSpecification_Id()
                    .equals(outStockOrderItem.getProductSpecificationId())
                    || !Objects.equals(deliveryOrderItems.get(0).getOwnerId(), outStockOrderItem.getOwnerId())
                    || deliverCount.compareTo(unitTotalCount) != 0) {
                    errOrderItemIds.add(itemId);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(errOrderNos)) {
            errOrderNos = errOrderNos.stream().distinct().collect(Collectors.toList());
            throw new BusinessException("订单在WMS中不存在！OrderNo:" + StringUtils.join(errOrderNos, ","));
        }

        if (CollectionUtils.isNotEmpty(noOmsOrderItemIds)) {
            String noOmsOrderItem =
                noOmsOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单项在WMS中不存在！OmsItemId:" + noOmsOrderItem);
        }

        if (CollectionUtils.isNotEmpty(noOrderItemIds)) {
            String noOrderItem =
                noOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单项在OMS中不存在！WmsItemId:" + noOrderItem);
        }

        if (CollectionUtils.isNotEmpty(errOrderItemIds)) {
            String errOrderItem =
                errOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("OMS与WMS订单项数量不一致！ItemId:" + errOrderItem);
        }

    }

    /**
     * 订单配送出库(内配单): 扣除仓库库存
     */
    @Transactional(rollbackFor = Exception.class)
    public List<InventoryDeliveryJiupiOrder>
        internalDistributionOrderDelivery(InternalDistributionOrderDeliveryDTO deliveryOrders) {
        // 生成酒批订单
        List<InventoryDeliveryJiupiOrder> jiupiOrders = deliveryOrders.getDeliveryJiupiOrders();

        // 将订单id，订单项id转换成wms数据
        jiupiOrders.forEach(order -> {
            order.setCityId(deliveryOrders.getDeliveryCity());
            order.setWarehouseId(deliveryOrders.getDeliveryWarehouseId());
        });
        // inventoryDeliveryJiuPiOrderToWMSData(jiupiOrders, deliveryOrders.getDeliveryCity(),
        // deliveryOrders.getDeliveryWarehouseId());

        List<String> noProcessOrderIds =
            idempotenceConsumer.getNoProcessOrderNos(deliveryOrders.getDeliveryWarehouseId(), jiupiOrders,
                OrderDeliveryOpType.ALLCATION_DISTRIBUTION_ORDER_DELIVERY_MESSAGE_TYPE, "内配单配送出库");
        LOG.info("内配单-发货配送信息: {}", JSON.toJSONString(jiupiOrders));
        if (CollectionUtils.isEmpty(noProcessOrderIds)) {
            LOG.info(String.format("内配单配送出库-内配已经全部处理过！%s", JSON.toJSONString(jiupiOrders)));
            return null;
        }

        boolean checkWarehouseInventory = true;
        // 检验是否店仓或者配置可以负库存发货仓库
        Integer warehouseId = jiupiOrders.get(0).getWarehouseId();
        Boolean allowNegative = checkContentConfig(warehouseId, "AllowNegativeDelivery");
        if (allowNegative) {
            checkWarehouseInventory = false;
        }

        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        boolean finalCheckWarehouseInventory = checkWarehouseInventory;
        idempotenceConsumer.apply(noProcessOrderIds, () -> {

            checkOrder(jiupiOrders);

            // 遍历添加批次所有BO到list中
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : jiupiOrders) {
                warehouseChangListBOConverter.processOrderItemToAllocationOrderDeliveryBO(warehouseChangeList,
                    inventoryDeliveryJiupiOrder, finalCheckWarehouseInventory);
            }

            if (CollectionUtils.isEmpty(warehouseChangeList)) {
                LOG.info(String.format("内配单配送出库-订单已经全部处理过！%s", JSON.toJSONString(jiupiOrders)));
            }
            // 订单直发不需要发货数量记录
            Boolean isUpdateDeliveryCount = true;
            if (Objects.equals(jiupiOrders.get(0).getPackageAttribute(), OrderPickTypeConstant.PICKTYPE_LOGIC)) {
                isUpdateDeliveryCount = false;
            }
            // 内配单不发送发货数量记录
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, finalCheckWarehouseInventory,
                isUpdateDeliveryCount, true, true, false);

            // 更新订单状态为已出库
            updateOutStockOrderState(jiupiOrders, OutStockOrderStateEnum.已出库.getType());

            // 生成新的入库单
            List<String> orderNos =
                jiupiOrders.stream().filter(d -> d != null && StringUtils.isNotEmpty(d.getOrderNo()))
                    .map(InventoryDeliveryJiupiOrder::getOrderNo).distinct().collect(Collectors.toList());
            // 删除发货城市入库单
            if (CollectionUtils.isNotEmpty(orderNos)) {
                iInStockOrderService.deleteInStockOrderByNOList(deliveryOrders.getDeliveryCity(),
                    deliveryOrders.getDeliveryWarehouseId(), orderNos);
            }
            // 创建收货城市入库单-中转配送不生成二级仓库入库单
            inStockTransferOrderAsyncBL.batchAddInStockTransferOrder(deliveryOrders, warehouseChangeList);
            // 内配单不需要处理销售库存
            // 内配单需要生成二级仓订单(排除调拨单)
            List<InventoryDeliveryJiupiOrder> outOrders = jiupiOrders.stream()
                .filter(order -> (order.getAllotType().equals(OrderConstant.ALLOT_TYPE_ALLOCATION)
                    || order.getAllotType().equals(OrderConstant.ALLOT_TYPE_DELIVERY))
                    && !Objects.equals(order.getOrderType().byteValue(), OrderConstant.ORDER_TYPE_ALLOT))
                .collect(Collectors.toList());
            List<OutStockOrderDTO> transferOutStockOrderDTOS = createTransferOrders(outOrders,
                deliveryOrders.getReceiptCity(), deliveryOrders.getReceiptWarehouseId());
            if (CollectionUtils.isNotEmpty(transferOutStockOrderDTOS)) {
                List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS =
                    warehouseChangListBOConverter.processChangeBOSToOutStockOrderItemDetailDTOS(warehouseChangeList);
                outStockTransferOrderAsyncBL.asyncBatchAddTransferOrder(transferOutStockOrderDTOS,
                    outStockOrderItemDetailDTOS);
            }
            // 添加日志审计
            if (CollectionUtils.isNotEmpty(transferOutStockOrderDTOS)) {
                for (OutStockOrderDTO outStockOrderDTO : transferOutStockOrderDTOS) {
                    BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "outstockorder");
                    builder.businessId(String.valueOf(outStockOrderDTO.getId())).addFeature("OrderNo",
                        outStockOrderDTO.getRefOrderNo());
                    if (outStockOrderDTO.getOrderType() != null) {
                        builder.addFeature("OrderType", String.valueOf(outStockOrderDTO.getOrderType()));
                        OutStockOrderTypeEnum outStockOrderTypeEnum =
                            OutStockOrderTypeEnum.getEnum(outStockOrderDTO.getOrderType());
                        if (outStockOrderTypeEnum != null) {
                            builder.content(String.format("%s-内配单按批次发货", outStockOrderTypeEnum.name()));
                        } else {
                            builder.content("内配单按批次发货");
                        }
                    } else {
                        builder.content("内配单按批次发货");
                    }
                    builder.done();
                }
            }

        });

        // 返回实际库存处理结果
        return getInventoryProcessResults(deliveryOrders.getDeliveryJiupiOrders(), warehouseChangeList);
    }

    /**
     * 根据出库单生成入库单
     */
    public List<InStockOrderDTO> createInStockOrderByOrderNo(InternalDistributionOrderDeliveryDTO deliveryOrders,
        List<String> orderNoList, boolean useDeliver) {
        return inStockTransferOrderAsyncBL.createInStockOrderByOrderNo(deliveryOrders, orderNoList, useDeliver, null);
    }

    /**
     * 修改订单状态
     *
     * @param jiupiOrders
     * @param state
     */
    private void updateOutStockOrderState(List<InventoryDeliveryJiupiOrder> jiupiOrders, byte state) {
        updateOutStockOrderState(jiupiOrders, state, false);
    }

    @SuppressWarnings({"SameParameterValue", "unused"})
    private void updateOutStockOrderState(List<InventoryDeliveryJiupiOrder> jiupiOrders, byte state, boolean dummy) {
        jiupiOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                InventoryDeliveryJiupiOrder order = orders.stream().findFirst().get();
                List<String> orderNos =
                    orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderUpdateDTO outStockOrderUpdateDTO = new OutStockOrderUpdateDTO();
                outStockOrderUpdateDTO.setOrgId(order.getCityId());
                outStockOrderUpdateDTO.setWarehouseId(warehouseId);
                outStockOrderUpdateDTO.setRefOrderNos(orderNos);
                outStockOrderUpdateDTO.setState(state);
                if (state == OutStockOrderStateEnum.已出库.getType()) {
                    outStockOrderUpdateDTO.setOutStockTime(new Date());
                }
                if(Objects.equals(pushCapabilityTypeEnum.直接出库.getType(), order.getCapabilityType())
                        && Objects.equals(order.getOrderSourceType(), SourceType.WRONG_DELIVERY.getValue())){
                    outStockOrderUpdateDTO.setSyncTraceFlag(false);
                }
                outStockCommManageService.updateOutStockOrderInfo(outStockOrderUpdateDTO);
            });
    }

    private List<OutStockOrderDTO> createTransferOrders(List<InventoryDeliveryJiupiOrder> jiupiOrders, Integer toOrgId,
        Integer toWarehouseId) {
        List<OutStockOrderDTO> transferOutStockOrderDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(jiupiOrders)) {
            return transferOutStockOrderDTOS;
        }

        // 查询仓库版本信息
        Byte scmVersionByWarehouseId = warehouseConfigService.getScmVersionByWarehouseId(toWarehouseId);
        Byte scmVersion = toWarehouseId == null || scmVersionByWarehouseId == null ? 0 : scmVersionByWarehouseId;

        // 如果开启3.0或者2.5，订单默认状态需要更改，需要走拣货流程
        boolean isOpenSCMV3 = !WarehouseConfigConstants.SCM_VERSION_2.equals(scmVersion);
        // 查询是否需要拣货
        Boolean notNeedPicking = checkContentConfig(toWarehouseId, "NP_NOT_NEED_PICKING");

        jiupiOrders.forEach(order -> {
            OutStockOrderDTO outStockOrderDTO = new OutStockOrderDTO();
            outStockOrderDTO.setRefOrderNo(order.getOrderNo());
            outStockOrderDTO.setWarehouseId(order.getWarehouseId());
            outStockOrderDTO.setOrgId(order.getCityId());
            outStockOrderDTO.setToWarehouseId(toWarehouseId);
            outStockOrderDTO.setToOrgId(toOrgId);

            // 前置仓订单默认为调拨中.
            // 2.0的仓库默认已拣货
            outStockOrderDTO.setState(OutStockOrderStateEnum.调拨中.getType());
            if (isOpenSCMV3) {
                if (order.getNeedWavePicking()) {
                    outStockOrderDTO.setState(OutStockOrderStateEnum.待调度.getType());
                }
            } else {
                outStockOrderDTO.setState(OutStockOrderStateEnum.已拣货.getType());
            }

            // 不拣货配置优先级最高
            if (notNeedPicking) {
                outStockOrderDTO.setState(OutStockOrderStateEnum.已拣货.getType());
            }

            transferOutStockOrderDTOS.add(outStockOrderDTO);
        });
        return transferOutStockOrderDTOS;
    }

    /**
     * 召回->发货批次
     */
    public void recallDeliver(RecallDeliverDTO recallDeliverDTO) {
        // 召回逻辑
        // 1、召回已经延迟配送加过库存的入库单（待审核），返还库存，并删除对应的入库单（全部，包含待审核及已处理）
        // 2、其他订单不做任何处理，因为直接召回到已发货状态，已发货数量也不用处理
        // 3、出库单状态还是已出库，无需修改
        // 4、入库批次中的单号要移除掉。
        // 查询入库单.(延迟配送)
        List<OrderDTO> orderList = iInStockOrderService.findInStockOrderByOrderNOSAndState(recallDeliverDTO.getOrgId(),
            recallDeliverDTO.getWarehouseId(), recallDeliverDTO.getRefOrderNoList(), null);
        if (CollectionUtils.isEmpty(orderList)) {
            LOG.warn("城市[{}]仓库[{}]召回->发货批次没有找到入库单据信息！", recallDeliverDTO.getOrgId(), recallDeliverDTO.getWarehouseId());
            return;
        }
        // 处理【待审核的入库单】InStockOrderState.待审核.getType()
        List<OrderDTO> needAuditOrders =
            orderList.stream().filter(e -> e != null && Objects.equals(InStockOrderState.已入库.getType(), e.getState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needAuditOrders)) {
            // 直接将延迟配送的入库单进行出库扣库存
            List<InventoryDeliveryJiupiOrder> deliveryOrders =
                OrderDTOConvert.orderDTOS2InventoryDeliveryJiupiOrderS(needAuditOrders);
            deliveryOrders.forEach(n -> {
                n.getItems().forEach(item -> {
                    item.setTakeCount(item.getTakeCount().multiply(new BigDecimal(-1)));
                });
                n.setJiupiEventType(JiupiEventType.订单召回.getType());
            });

            List<String> noProcessOrderNos = new ArrayList<>();
            deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
                .forEach((warehouseId, orders) -> {
                    List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders,
                        OrderDeliveryOpType.ORDER_DELIVERY_DELAY_TYPE, "延迟配送召回");
                    if (CollectionUtils.isNotEmpty(noProcess)) {
                        noProcessOrderNos.addAll(noProcess);
                    }
                });

            // 扣库存
            List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
            idempotenceConsumer.apply(noProcessOrderNos, () -> {

                for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                    // //招商订单需要将下单SKU转为发货城市的SKU
                    // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);

                    warehouseChangListBOConverter.processRecallDeliverOrderItemToBO(warehouseChangeList,
                        inventoryDeliveryJiupiOrder);
                }
                // 需要修改配送中数量
                warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, true, true, true, true,
                    false);
            });
        }
        // 供应链入库单ID
        List<Long> inStockOrderIds = orderList.stream().filter(e -> e != null).map(e -> e.getRelationOrderId())
            .distinct().collect(Collectors.toList());
        // oms订单ID
        List<Long> businessIdList =
            orderList.stream().filter(e -> e != null).map(e -> e.getId()).distinct().collect(Collectors.toList());
        // 删除掉入库单.
        iInStockOrderService.deleteByIdS(inStockOrderIds);
        // 删除已入库批次中的订单号
        iInStockOrderService.deteleByOrderIds(businessIdList);
        // 出库单状态改为已出库
        OutStockOrderUpdateDTO outStockOrderUpdateDTO = new OutStockOrderUpdateDTO();
        outStockOrderUpdateDTO.setOrgId(recallDeliverDTO.getOrgId());
        outStockOrderUpdateDTO.setWarehouseId(recallDeliverDTO.getWarehouseId());
        outStockOrderUpdateDTO.setRefOrderNos(recallDeliverDTO.getRefOrderNoList());
        outStockOrderUpdateDTO.setState(OutStockOrderStateEnum.已出库.getType());
        outStockOrderUpdateDTO.setOutStockTime(new Date());
        outStockCommManageService.updateOutStockOrderInfo(outStockOrderUpdateDTO);
        // 添加日志审计
        List<OrderDTO> outStockOrderList = outStockCommQueryService.findOrderByNos(outStockOrderUpdateDTO.getOrgId(),
            outStockOrderUpdateDTO.getWarehouseId(), outStockOrderUpdateDTO.getRefOrderNos());
        if (CollectionUtils.isNotEmpty(outStockOrderList)) {
            for (OrderDTO outStockOrderDTO : outStockOrderList) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "outstockorder");
                builder.businessId(String.valueOf(outStockOrderDTO.getId())).addFeature("OrderNo",
                    outStockOrderDTO.getRefOrderNo());
                if (outStockOrderDTO.getBusinessType() != null) {
                    builder.addFeature("BusinessType", String.valueOf(outStockOrderDTO.getBusinessType()));
                }
                if (outStockOrderDTO.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(outStockOrderDTO.getOrderType()));
                    OutStockOrderTypeEnum outStockOrderTypeEnum =
                        OutStockOrderTypeEnum.getEnum(outStockOrderDTO.getOrderType());
                    if (outStockOrderTypeEnum != null) {
                        builder.content(String.format("%s-订单召回发货批次", outStockOrderTypeEnum.name()));
                    } else {
                        builder.content("订单召回发货批次");
                    }
                } else {
                    builder.content("订单召回发货批次");
                }
                builder.done();
            }
        }
    }

    /**
     * 自提订单出库(改状态的逻辑在client里面)
     *
     * @param businessIdList
     */
    @Deprecated
    public void ziTiDeliver(List<Long> businessIdList, List<Long> productSourceCodeIdList, Integer userId) {
        ziTiConfirmDeliver(new ZiTiConfirmDeliverDTO(businessIdList, productSourceCodeIdList, userId));
    }

    public void ziTiConfirmDeliver(ZiTiConfirmDeliverDTO dto) {
        // 召回逻辑
        // 1、 出库单状态更新为已出库。
        // 2、扣库存+批次库存
        // 3、已发货数量需要更新
        OutStockOrderQueryConditionDTO queryConditionDTO = new OutStockOrderQueryConditionDTO();
        queryConditionDTO.setIdList(dto.getBusinessIdList());
        List<OutStockOrderDTO> outStockOrderDTOS =
            outStockCommQueryService.findOutStockOrderAllInfoByCondition(queryConditionDTO);
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            OrderDTOConvert.outStockOrderDTOS2InventoryDeliveryJiupiOrders(outStockOrderDTOS);
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            throw new BusinessException("WMS中找不到对应的出库单，请联系技术支持！Id：" + JSON.toJSONString(dto.getBusinessIdList()));
        }

        deliveryOrders.forEach(n -> {
            n.getItems().forEach(item -> {
                item.setTakeCount(item.getTakeCount().multiply(new BigDecimal(-1)));
            });
        });

        List<String> noProcessOrderNos = new ArrayList<>();
        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders,
                    OrderDeliveryOpType.ZITI_OUT_STOCK_ORDER_TYPE, "自提订单");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });

        // 扣库存
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {

            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                // //招商订单需要将下单SKU转为发货城市的SKU
                // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);

                warehouseChangListBOConverter.processZiTiOrderItemToBO(warehouseChangeList,
                    inventoryDeliveryJiupiOrder);
            }

            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, true, false, true, true,
                false);
        });
        // 添加日志审计
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "outstockorder");
                builder.businessId(String.valueOf(deliveryOrder.getId())).addFeature("OrderNo",
                    deliveryOrder.getOrderNo());
                if (deliveryOrder.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(deliveryOrder.getOrderType()));
                    OutStockOrderTypeEnum outStockOrderTypeEnum =
                        OutStockOrderTypeEnum.getEnum(deliveryOrder.getOrderType().byteValue());
                    if (outStockOrderTypeEnum != null) {
                        builder.content(String.format("%s-自提出库", outStockOrderTypeEnum.name()));
                    } else {
                        builder.content("自提出库");
                    }
                } else {
                    builder.content("自提出库");
                }
                builder.done();
            }
        }

        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            outStockOrderZiTiOut(deliveryOrders, OutStockOrderStateEnum.已出库.getType(), dto.getRemark());
            updateProductSourceCode(dto.getProductSourceCodeIdList(), deliveryOrders.get(0).getWarehouseId(),
                dto.getUserId());
            saveProductSourceCodeTrace(dto.getProductSourceCodeIdList(), deliveryOrders.get(0), dto.getUserId());
        }

        omsOrderComplete(warehouseChangeList);
    }

    private void outStockOrderZiTiOut(List<InventoryDeliveryJiupiOrder> jiupiOrders, byte state, String remark) {
        jiupiOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<String> orderNos =
                    orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderUpdateDTO outStockOrderUpdateDTO = new OutStockOrderUpdateDTO();
                outStockOrderUpdateDTO.setOrgId(orders.get(0).getCityId());
                outStockOrderUpdateDTO.setWarehouseId(warehouseId);
                outStockOrderUpdateDTO.setRefOrderNos(orderNos);
                outStockOrderUpdateDTO.setState(state);
                if (state == OutStockOrderStateEnum.已出库.getType()) {
                    outStockOrderUpdateDTO.setOutStockTime(new Date());
                }
                List<ZiTiOrderOutStockDTO> dtoList = orders.stream().map(m -> {
                    ZiTiOrderOutStockDTO ziTiOrderOutStockDTO = new ZiTiOrderOutStockDTO();
                    ziTiOrderOutStockDTO.setRefOrderNo(m.getOrderNo());
                    ziTiOrderOutStockDTO.setRemark(remark);
                    ziTiOrderOutStockDTO.setWarehouseId(warehouseId);
                    ziTiOrderOutStockDTO.setState(state);
                    ziTiOrderOutStockDTO.setOrgId(m.getCityId());
                    if (state == OutStockOrderStateEnum.已出库.getType()) {
                        ziTiOrderOutStockDTO.setOutStockTime(new Date());
                    }
                    return ziTiOrderOutStockDTO;
                }).collect(Collectors.toList());
                outStockCommManageService.ziTiOrderOutStock(dtoList);
            });

    }

    /**
     * 溯源码出库
     */
    public void updateProductSourceCode(List<Long> productSourceCodeIdList, Integer warehouseId, Integer userId) {
        if (CollectionUtils.isNotEmpty(productSourceCodeIdList)) {
            List<UpdateProductSourceCodeDTO> updateProductSourceCodeDTOS = Lists.newArrayList();
            productSourceCodeIdList.forEach(productSourceCodeId -> {
                UpdateProductSourceCodeDTO updateProductSourceCodeDTO = new UpdateProductSourceCodeDTO();
                updateProductSourceCodeDTO.setProductSourceCodeId(productSourceCodeId);
                updateProductSourceCodeDTO.setBusinessType((byte)2);
                updateProductSourceCodeDTO.setWarehouseId(warehouseId);
                updateProductSourceCodeDTO.setOperateUserId(userId);
                updateProductSourceCodeDTOS.add(updateProductSourceCodeDTO);
            });
            LOG.info("自提订单溯源码出库:{}", JSON.toJSONString(updateProductSourceCodeDTOS));
            iProductSourceService.updateProductSourceCodeInfo(updateProductSourceCodeDTOS);
        }
    }

    /**
     * 溯源码出库变更记录
     */
    public void saveProductSourceCodeTrace(List<Long> sourceCodeIdList, InventoryDeliveryJiupiOrder jiupiOrder,
        Integer userId) {
        if (CollectionUtils.isEmpty(sourceCodeIdList)) {
            return;
        }
        ProductSourceCodeRecordSyncDTO recordSyncDTO = new ProductSourceCodeRecordSyncDTO();
        recordSyncDTO.setProductSourceCodeIds(sourceCodeIdList);
        recordSyncDTO.setAfterState(ProductSourceCodeRecordStateEnum.出库.getType());
        recordSyncDTO.setBusinessType((byte)2);

        recordSyncDTO.setBusinessId(String.valueOf(jiupiOrder.getOrderId()));
        recordSyncDTO.setBusinessNo(jiupiOrder.getOrderNo());
        recordSyncDTO.setLastUpdateUser(userId);

        LOG.info("自提订单溯源码出库变更记录:{}", JSON.toJSONString(recordSyncDTO));
        iProductSourceService.syncProductSourceCodeTrace(recordSyncDTO);
    }

    /**
     * oms订单完成
     */
    public void omsOrderComplete(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        List<SecOwnerDetailDTO> omsSecOwnerDetailDTOs =
            warehouseChangListBOConverter.processBOToOMSSecOwnerDetailDTO(warehouseChangeList);
        if (CollectionUtils.isNotEmpty(omsSecOwnerDetailDTOs)) {
            omsSecOwnerDetailDTOs.stream().collect(Collectors.groupingBy(SecOwnerDetailDTO::getOmsOrderId))
                .forEach((omsOrderId, list) -> {
                    LOG.info("oms订单出库二级货主数据:{}", JSON.toJSONString(list));
                    iFinanceService.zitiOrderCompleteById(omsOrderId, list);
                });
        }
    }

    /**
     * 财务收款确认入库完成
     */
    public void inStockComplete(InStockCompleteDTO completeDTO) {
        // 查询待入库的入库单.(延迟配送是待审核状态)
        // List<OrderDTO> inStockOrderByIdS = iInStockOrderService.findDaiRuKuInStockOrderByIdS(businessIdList);
        InventoryOrderBizDTO waitingInStockOrder = iInStockQueryService.findWaitingInStockOrderByNOList(
            completeDTO.getOrgId(), completeDTO.getWarehouseId(), completeDTO.getRefOrderNoList());

        if (waitingInStockOrder != null) {
            // 2021.7.12 当completeDTO 的locationId 有参数的时候,为虚仓实配退货入库单
            if (StringUtils.isNotEmpty(completeDTO.getLocationId())) {
                fixOrderLocation(completeDTO, waitingInStockOrder);
            }

            // 入库单信息 - OrderDTO 数据结构
            List<OrderDTO> inStockOrderByIdS = waitingInStockOrder.getOrderList();
            if (CollectionUtils.isNotEmpty(inStockOrderByIdS)) {
                // tms 告知哪些产品是内配退，内配退不需要生成上架任务
                List<Long> skipSkuList = null;
                List<Long> tmsOrderIds = inStockOrderByIdS.stream().filter(Objects::nonNull).map(e -> e.getId())
                    .collect(Collectors.toList());
                // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 未处理
                if (CollectionUtils.isNotEmpty(tmsOrderIds)) {
                    try {
                        // skipSkuList = iNptProductService.findNptProductSku(tmsOrderIds);
                    } catch (Exception e) {
                        throw new BusinessException("财务收款确认入库 - 调用TMS接口查询内配产品发生异常！", e);
                    }
                }
                // 生成内配退货产品：优先找仓库配置的内配退货暂存位，如果仓库有配置则用仓库的内配退货位，没有配置则保持原单货位
                processNPTProductLocation(waitingInStockOrder, skipSkuList);
                // 如果没有除延迟配送以外的入库单,直接放过.
                List<InventoryDeliveryJiupiOrder> deliveryOrders =
                    OrderDTOConvert.orderDTOS2InventoryDeliveryJiupiOrderS(inStockOrderByIdS);

                List<String> noProcessOrderNos = idempotenceConsumer.getNoProcessOrderNos(completeDTO.getWarehouseId(),
                    deliveryOrders, OrderDeliveryOpType.IN_STOCK_ORDER_TYPE, "入库单");
                List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
                idempotenceConsumer.apply(noProcessOrderNos, () -> {

                    for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                        // //招商订单需要将下单SKU转为发货城市的SKU
                        // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);

                        warehouseChangListBOConverter.processInStockOrderItemToBO(warehouseChangeList,
                            inventoryDeliveryJiupiOrder);
                    }

                    warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, false, true,
                        true, false);
                });

                // 处理生成内配单的订单，不需要返销售库存
                // 1、部分配送或者部分发货或者配送失败等内配单
                // 2、用户申请退货的退货单，如果需要生内配单，也不需要返销售库存
                List<String> lstExSaleInventoryOrderNos =
                    inStockOrderByIdS.stream().filter(p -> p.getCreateAllocation() != null && p.getCreateAllocation())
                        .map(p -> p.getRefOrderNo()).collect(Collectors.toList());
                setNotReturnSaleInventoryByChangeBO(warehouseChangeList, lstExSaleInventoryOrderNos);
                // 返回上架后处理销售库存的产品
                List<Long> putAwayTaskProcessSaleSkuIdList =
                    createPutAwayTaskByOrderAndScmVersion(waitingInStockOrder.getInStockOrderList(), skipSkuList,
                        InStockOrderState.已入库.getType(), putawayTypeEnum.退货上架.getType());
                // 移除上架处理销售库存的产品
                warehouseChangeList
                    .removeIf(e -> e != null && putAwayTaskProcessSaleSkuIdList.contains(e.getProductSkuId()));
                if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
                    List<SimpleOrderInfoDTO> simpleOrderInfoDTOS = completeDTO.getSimpleOrderInfoDTOS();
                    if (CollectionUtils.isNotEmpty(simpleOrderInfoDTOS)) {
                        Map<String,
                            SimpleOrderInfoDTO> orderInfoDTOMap = simpleOrderInfoDTOS.stream()
                                .filter(orderInfo -> StringUtils.isNotEmpty(orderInfo.getOrderNo())).collect(Collectors
                                    .toMap(SimpleOrderInfoDTO::getOrderNo, Function.identity(), (key1, key2) -> key1));
                        if (orderInfoDTOMap.size() > 0) {
                            warehouseChangeList.forEach(change -> {
                                SimpleOrderInfoDTO simpleOrderInfoDTO = orderInfoDTOMap.get(change.getOrderNo());
                                if (simpleOrderInfoDTO != null) {
                                    change.setAllotType(simpleOrderInfoDTO.getAllotType());
                                }
                            });
                        }
                    }
                    // 处理销售库存发消息
                    warehouseInventoryManageBL.processSellInventory(warehouseChangeList, null);
                }

                // 处理入库单中的残次品
                affirmRelatedIInStock(waitingInStockOrder.getInStockOrderList());
            }

            // 添加日志审计
            if (CollectionUtils.isNotEmpty(waitingInStockOrder.getInStockOrderList())) {
                for (InStockOrderDTO inStockOrderDTO : waitingInStockOrder.getInStockOrderList()) {
                    BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "instockorder");
                    builder.businessId(String.valueOf(inStockOrderDTO.getId())).addFeature("OrderNo",
                        inStockOrderDTO.getRefOrderNo());
                    if (inStockOrderDTO.getBusinessType() != null) {
                        builder.addFeature("BusinessType", String.valueOf(inStockOrderDTO.getBusinessType()));
                    }
                    if (inStockOrderDTO.getOrderType() != null) {
                        builder.addFeature("OrderType", String.valueOf(inStockOrderDTO.getOrderType()));
                        InStockOrderTypeEnum inStockOrderTypeEnum =
                            InStockOrderTypeEnum.getEnum(inStockOrderDTO.getOrderType());
                        if (inStockOrderTypeEnum != null) {
                            builder.content(String.format("%s-财务确认收款", inStockOrderTypeEnum.name()));
                        } else {
                            builder.content("财务确认收款");
                        }
                    } else {
                        builder.content("财务确认收款");
                    }
                    builder.done();
                }
            }
        }
        // 将入库单状态改为待上架.
        iInStockOrderService.orderCompleteUpdateState(completeDTO.getOrgId(), completeDTO.getWarehouseId(),
            completeDTO.getRefOrderNoList());
    }

    private void fixOrderLocation(InStockCompleteDTO completeDTO, InventoryOrderBizDTO orderBizDTO) {
        String locationId = completeDTO.getLocationId();
        String locationName = completeDTO.getLocationName();
        List<OrderDTO> orderList = orderBizDTO.getOrderList();
        if (CollectionUtils.isNotEmpty(orderList)) {
            orderList.stream().filter(Objects::nonNull).forEach(order -> {
                order.getItems().stream().filter(Objects::nonNull).forEach(item -> {
                    List<OrderItemDetailDTO> itemDetailList = item.getItemDetailList();
                    if (CollectionUtils.isNotEmpty(itemDetailList)) {
                        itemDetailList.stream().filter(Objects::nonNull).forEach(detail -> {
                            detail.setLocationId(Long.valueOf(locationId));
                            detail.setLocationName(locationName);
                        });
                    } else {
                        List<OrderItemDetailDTO> orderItemDetailDTOS = new ArrayList<>();
                        OrderItemDetailDTO orderItemDetailDTO = new OrderItemDetailDTO();
                        orderItemDetailDTO.setLocationId(Long.valueOf(locationId));
                        orderItemDetailDTO.setLocationName(locationName);
                        orderItemDetailDTO.setOwnerId(item.getOwnerId());
                        orderItemDetailDTO.setOrgId(item.getOrgId());
                        orderItemDetailDTO.setOrderItemId(item.getId());
                        orderItemDetailDTO.setProductSpecificationId(item.getProductSpecificationId());
                        orderItemDetailDTO.setUnitTotalCount(item.getUnitTotalCount());
                        orderItemDetailDTO.setSecOwnerId(item.getSecOwnerId());
                        orderItemDetailDTOS.add(orderItemDetailDTO);
                        item.setItemDetailList(orderItemDetailDTOS);
                    }

                });
            });
        }

    }

    /**
     * 生成内配退货产品：优先找仓库配置的内配退货暂存位，如果仓库有配置则用仓库的内配退货位，没有配置则保持原单货位
     */
    private void processNPTProductLocation(InventoryOrderBizDTO waitingInStockOrder, List<Long> nptSkuList) {
        if (CollectionUtils.isEmpty(nptSkuList)) {
            return;
        }
        List<InStockOrderDTO> inStockOrderList = waitingInStockOrder.getInStockOrderList();
        List<OrderDTO> orderList = waitingInStockOrder.getOrderList();
        // waitingInStockOrder 中 orderList 是 inStockOrderList 转化，两者是等价的
        if (CollectionUtils.isEmpty(inStockOrderList)) {
            return;
        }
        Map<Integer, DefaultLocationConfigDTO> warehouseNptLocationMap = new HashMap<>(16);
        // 查找npt配置货位
        inStockOrderList.stream().filter(order -> order != null && order.getWarehouseId() != null)
            .map(InStockOrderDTO::getWarehouseId).distinct().forEach(warehouseId -> {
                List<DefaultLocationConfigDTO> defaultLocationConfig =
                    iDefaultLocationConfigService.findDefaultLocationConfigByWarehouseId(warehouseId);
                if (CollectionUtils.isNotEmpty(defaultLocationConfig)) {
                    DefaultLocationConfigDTO nptLocation = defaultLocationConfig.stream()
                        .filter(location -> location != null
                            && Objects.equals(DefaultTypeEnum.内配单退货暂存位.getType(), location.getTemporaryLocationType()))
                        .findAny().orElse(null);
                    if (nptLocation != null) {
                        warehouseNptLocationMap.put(warehouseId, nptLocation);
                    }
                }
            });
        if (warehouseNptLocationMap.isEmpty()) {
            return;
        }
        // 设置入库单NPT货位
        processInStockOrderNPTLocation(warehouseNptLocationMap, inStockOrderList, nptSkuList);
        // 设置订单NPT货位
        processOrderNPTLocation(warehouseNptLocationMap, orderList, nptSkuList);
    }

    private void processInStockOrderNPTLocation(Map<Integer, DefaultLocationConfigDTO> warehouseNptLocationMap,
        List<InStockOrderDTO> inStockOrderList, List<Long> nptSkuList) {
        if (CollectionUtils.isEmpty(inStockOrderList)) {
            return;
        }
        // 设置npt货位
        inStockOrderList.stream().filter(order -> order != null && order.getWarehouseId() != null)
            .collect(Collectors.groupingBy(InStockOrderDTO::getWarehouseId)).forEach((warehouseId, list) -> {
                DefaultLocationConfigDTO defaultLocationConfigDTO = warehouseNptLocationMap.get(warehouseId);
                if (defaultLocationConfigDTO != null && CollectionUtils.isNotEmpty(list)) {
                    list.stream().filter(order -> CollectionUtils.isNotEmpty(order.getInStockOrderItemDTOList()))
                        .forEach(order -> {
                            order.getInStockOrderItemDTOList().stream()
                                .filter(item -> item != null && nptSkuList.contains(item.getSkuId()))
                                .forEach(itemDTO -> {
                                    LOG.info("财务确认收款- 仓库 {} 入库单 {} 产品 {} - {} 因内配退强制入库到仓库配置的[内配单退货暂存位]", warehouseId,
                                        order.getRefOrderNo(), itemDTO.getSkuId(), itemDTO.getProductName());
                                    List<InStockOrderItemDetailDTO> itemDetailDTOList = itemDTO.getItemDetailDTOList();
                                    if (CollectionUtils.isEmpty(itemDetailDTOList)) {
                                        itemDTO.setLocationId(defaultLocationConfigDTO.getTemporaryLocationId());
                                        itemDTO.setLocationName(defaultLocationConfigDTO.getTemporaryLocationName());
                                    } else {
                                        itemDetailDTOList.stream().filter(Objects::nonNull).forEach(detail -> {
                                            detail.setLocationId(defaultLocationConfigDTO.getTemporaryLocationId());
                                            detail.setLocationName(defaultLocationConfigDTO.getTemporaryLocationName());
                                        });
                                    }
                                });
                        });
                }
            });
    }

    private void processOrderNPTLocation(Map<Integer, DefaultLocationConfigDTO> warehouseNptLocationMap,
        List<OrderDTO> orderList, List<Long> nptSkuList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.stream().filter(order -> order != null && order.getWarehouseId() != null)
            .collect(Collectors.groupingBy(OrderDTO::getWarehouseId)).forEach((warehouseId, list) -> {
                DefaultLocationConfigDTO defaultLocationConfigDTO = warehouseNptLocationMap.get(warehouseId);
                if (defaultLocationConfigDTO != null && CollectionUtils.isNotEmpty(list)) {
                    list.stream().filter(order -> CollectionUtils.isNotEmpty(order.getItems())).forEach(order -> {
                        order.getItems().stream().filter(item -> item != null && nptSkuList.contains(item.getSkuId()))
                            .forEach(itemDTO -> {
                                LOG.info("财务确认收款- 仓库 {} 入库单 {} 产品 {} - {} 因内配退强制入库到仓库配置的[内配单退货暂存位]", warehouseId,
                                    order.getRefOrderNo(), itemDTO.getSkuId(), itemDTO.getProductName());
                                List<OrderItemDetailDTO> detailList = itemDTO.getItemDetailList();
                                if (CollectionUtils.isEmpty(detailList)) {
                                    itemDTO.setLocationId(defaultLocationConfigDTO.getTemporaryLocationId());
                                    itemDTO.setLocationName(defaultLocationConfigDTO.getTemporaryLocationName());
                                } else {
                                    detailList.stream().filter(Objects::nonNull).forEach(detail -> {
                                        detail.setLocationId(defaultLocationConfigDTO.getTemporaryLocationId());
                                        detail.setLocationName(defaultLocationConfigDTO.getTemporaryLocationName());
                                    });
                                }
                            });
                    });
                }
            });
    }

    /**
     * 确认入库同步入库单及同步批次表信息
     *
     * @param inventoryInStockOrderBatchDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void affirmInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        // 过滤掉延迟配送订单
        List<InventoryOrderDTO> filterOrder = inventoryInStockOrderBatchDTO.getOrderList().stream()
            .filter(o -> !(Objects.equals(DeliveryOrderConstant.DELIVERYSTATE_DELAY, o.getDeliveryMarkState())
                || Objects.equals(DeliveryOrderConstant.RECEIPTSTATE_DELAY, o.getReceiptState())))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterOrder)) {
            LOG.warn("InventoryOrderBizBL.affirmInStock 延迟配送订单 订单信息 is null");
            return;
        }
        inventoryInStockOrderBatchDTO.setOrderList(filterOrder);
        // 查询订单信息
        List<String> refOrderNoList = inventoryInStockOrderBatchDTO.getOrderList().stream()
            .map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
        // 变更单据信息
        List<OrderDTO> orderList = changeOrderStatus(inventoryInStockOrderBatchDTO);
        // 变更库存信息
        changeInventory(inventoryInStockOrderBatchDTO.getOrgId(), inventoryInStockOrderBatchDTO.getWarehouseId(),
            refOrderNoList, orderList);
    }

    private void changeInventory(Integer orgId, Integer orderWarehouseId, List<String> refOrderNoList,
        List<OrderDTO> orderList) {
        boolean isCalculated = orderList.get(0).getCalculatedProductionDate() != null
            ? orderList.get(0).getCalculatedProductionDate() : false;
        // 组装参数
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            OrderDTOConvert.orderDTOS2InventoryDeliveryJiupiOrderS(orderList);

        List<String> noProcessOrderNos = new ArrayList<>();
        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders,
                    OrderDeliveryOpType.IN_STOCK_ORDER_TYPE, "订单确认入库");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });

        // 部分配送及配送失败返回库存批次日期处理
        if (CollectionUtils.isNotEmpty(deliveryOrders) && !isCalculated) {
            processProductDate(deliveryOrders);
        }

        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            LOG.info(String.format("订单确认入库订单列表：%s", JSON.toJSONString(deliveryOrders)));
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                warehouseChangListBOConverter.processOrderItemToOrderDeliveryDelayBO(warehouseChangeList,
                    inventoryDeliveryJiupiOrder);
            }
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, true, true, true,
                false);
        });
        LOG.info(String.format("warehouseChangeList 库存处理结果：%s", JSON.toJSONString(warehouseChangeList)));
        // 深度拷贝来源数据
        List<WarehouseInventoryChangeBO> originalWarehouseChangeList =
            batchInBoundChangeInventoryBL.getOriginalWarehouseChangeList(warehouseChangeList);
        List<InStockOrderDTO> inStockOrderByOrderNoList =
            iInStockQueryService.findInStockOrderByOrderNoList(orgId, orderWarehouseId, refOrderNoList);
        if (CollectionUtils.isNotEmpty(inStockOrderByOrderNoList)) {
            // 返回上架后处理销售库存的产品
            List<Long> putAwayTaskProcessSaleSkuIdList = createPutAwayTaskByOrderAndScmVersion(
                inStockOrderByOrderNoList, null, InStockOrderState.已入库.getType(), putawayTypeEnum.退货上架.getType());
            // 移除上架处理销售库存的产品
            warehouseChangeList
                .removeIf(e -> e != null && putAwayTaskProcessSaleSkuIdList.contains(e.getProductSkuId()));

            // 处理入库单中的残次品
            affirmRelatedIInStock(inStockOrderByOrderNoList);
        } else {
            LOG.info(String.format("未取到入库单数据，残次品及上架任务处理失败！单号：%s", JSON.toJSONString(refOrderNoList)));
        }
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            // 过滤无需发送销售库存消息数据
            setNotSendSaleInventoryByChangeBO(warehouseChangeList);
            // 处理销售库存发消息
            warehouseInventoryManageBL.processSellInventory(warehouseChangeList, null);
        }
        // 获取退货入残次品位时，因产生上架任务被过滤掉的销售库存消息
        List<WarehouseInventoryChangeBO> defectiveWarehouseChangeList = batchInBoundChangeInventoryBL
            .getDefectiveSellChangeBO(inStockOrderByOrderNoList, originalWarehouseChangeList, warehouseChangeList);
        LOG.info("batchAffirmInBoundAddInventory 残次品位库存处理结果defectiveWarehouseChangeList={}",
            JSON.toJSONString(defectiveWarehouseChangeList));
        if (CollectionUtils.isNotEmpty(defectiveWarehouseChangeList)) {
            // 处理销售库存发消息
            warehouseInventoryManageBL.processSellInventory(defectiveWarehouseChangeList, null);
        }
    }

    /**
     * 变更单据信息
     *
     * @param inventoryInStockOrderBatchDTO
     * @return
     */
    private List<OrderDTO> changeOrderStatus(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        // 同步生成入库单并生产批次表.调用outstock服务
        InStockOrderBatchDTO inStockOrderBatchDTO = InventoryInStockOrderBatchDTOConvert
            .inventoryInStockOrderBatchDTO2InStockOrderBatchDTO(inventoryInStockOrderBatchDTO);
        List<OrderDTO> orderList = inStockOrderBatchDTO.getOrderList();
        // 单据状态变成已入库
        orderList.forEach(n -> {
            n.setState(InStockOrderState.已入库.getType());
            n.setCalculatedProductionDate(true);
        });
        // 根据原单查询生产日期并赋值
        inStockHandleProductDateBL.processOrderProductDate(orderList);
        // 确认入库同步入库单及同步批次表信息
        iInStockOrderService.affirmInStock(inStockOrderBatchDTO);
        // 添加日志审计
        affirmInStockAddLogAudit(orderList);
        // 修改出库单的标记状态
        updateDeliveryMarkState(inventoryInStockOrderBatchDTO.getOrderList());
        return orderList;
    }

    /**
     * 修改出库单的标记状态
     */
    private void updateDeliveryMarkState(List<InventoryOrderDTO> orderList) {
        orderList.stream().collect(Collectors.groupingBy(InventoryOrderDTO::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<UpdateDeliveryMarkStateDTO> updateDeliveryMarkStateDTOS = new ArrayList<>();

                orders.forEach(order -> {
                    if (!Objects.equals(DeliveryOrderConstant.DELIVERYSTATE_NO, order.getDeliveryMarkState())) {
                        UpdateDeliveryMarkStateDTO updateDeliveryMarkStateDTO = new UpdateDeliveryMarkStateDTO();
                        updateDeliveryMarkStateDTO.setOrderNo(order.getRefOrderNo());
                        updateDeliveryMarkStateDTO.setDeliveryMarkState(order.getDeliveryMarkState());
                        updateDeliveryMarkStateDTOS.add(updateDeliveryMarkStateDTO);
                    }
                });

                if (CollectionUtils.isNotEmpty(updateDeliveryMarkStateDTOS)) {
                    iomsOrderSyncService.updateDeliveryMarkStateByNo(updateDeliveryMarkStateDTOS,
                        orders.get(0).getOrgId(), warehouseId);
                }
            });
    }

    /**
     * 恢复出库单
     */
    public void resetOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        LOG.info("恢复出库单：{}", JSON.toJSONString(deliveryOrders));
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {

            // 删除所有已经执行过的消息Id
            deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
                .forEach((warehouseId, orders) -> {
                    List<String> orderNos = orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).distinct()
                        .collect(Collectors.toList());
                    idempotenceConsumer.deleteAllKey(warehouseId, orderNos);
                });

            deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
                .forEach((warehouseId, orders) -> {
                    List<String> delayOrderNos =
                        orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                    // 修改出库单状态 为待调度.
                    outStockCommManageService.outStockOrderInitByNo(delayOrderNos, orders.get(0).getCityId(),
                        warehouseId);
                    // 删除装箱
                    iPackageOrderService.removePackageByRefOrderNos(delayOrderNos, orders.get(0).getCityId(),
                        warehouseId);
                });
        }
    }

    /**
     * 内配单入库库存、单据操作
     */
    public List<WarehouseInventoryChangeBO> internalDeliveryInStockChange(
        InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO, Boolean isVehicleTransshipment) {
        List<InventoryOrderDTO> inventoryOrderDTOS = inventoryInStockOrderBatchDTO.getOrderList();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();

        // 转换成仓库变更BO
        Integer toOrgId = inventoryInStockOrderBatchDTO.getToOrgId();
        Integer toWarehouseId = inventoryInStockOrderBatchDTO.getToWarehouseId();

        List<String> noProcessOrderIds = new ArrayList<>();

        // 内配订单入库与内配退货订单入库分批处理 todo 后续需要在订单里明确告知下单仓库和目的仓库
        Map<Byte, List<InventoryOrderDTO>> allotMap =
            inventoryOrderDTOS.stream().collect(Collectors.groupingBy(InventoryOrderDTO::getAllotType));
        for (Map.Entry<Byte, List<InventoryOrderDTO>> entry : allotMap.entrySet()) {
            Byte allotType = entry.getKey();
            List<InventoryOrderDTO> orders = entry.getValue();
            if (OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN.equals(allotType)
                || OrderConstant.ALLOT_TYPE_DELIVERY_RETURN_NPTR.equals(allotType)
                || OrderConstant.ALLOT_TYPE_DELIVERY_RETURN.equals(allotType)) {
                // 幂等过滤
                noProcessOrderIds = idempotenceConsumer.getNoProcessInternalDeliveryOrderNos(toWarehouseId, orders,
                    OrderDeliveryOpType.INTERNAL_DELIVERY_ORDER_IN_STOCK_TYPE, "内配退货订单入库");
                // 将订单id，订单项id转换成wms数据
                if (CollectionUtils.isNotEmpty(inventoryOrderDTOS)) {
                    // nptr的单子对应的下单仓库id是warehouseId
                    inventoryOrderDTOS.stream()
                        .filter(order -> OrderConstant.ALLOT_TYPE_DELIVERY_RETURN_NPTR.equals(allotType))
                        .collect(Collectors.groupingBy(InventoryOrderDTO::getWarehouseId))
                        .forEach((warehouseId, returnOrders) -> {
                            inventoryOrderToWMSData(returnOrders, returnOrders.get(0).getOrgId(), warehouseId);
                        });

                    inventoryOrderDTOS.stream()
                        .filter(order -> !OrderConstant.ALLOT_TYPE_DELIVERY_RETURN_NPTR.equals(allotType))
                        .collect(Collectors.groupingBy(InventoryOrderDTO::getFromWarehouseId))
                        .forEach((fromWarehouseId, returnOrders) -> {
                            inventoryOrderToWMSData(returnOrders, returnOrders.get(0).getFromCityId(), fromWarehouseId);
                        });
                }
            } else if (OrderConstant.ALLOT_TYPE_ALLOCATION.equals(allotType)) {
                // 幂等过滤
                noProcessOrderIds = idempotenceConsumer.getNoProcessInternalDeliveryOrderNos(toWarehouseId, orders,
                    OrderDeliveryOpType.INTERNAL_DELIVERY_ORDER_IN_STOCK_TYPE, "内配订单入库");
                // 将订单id，订单项id转换成wms数据
                if (CollectionUtils.isNotEmpty(inventoryOrderDTOS)) {
                    internalInStockInventoryOrderToWMSData(inventoryOrderDTOS, toOrgId, toWarehouseId);
                }
            } else {
                // 幂等过滤
                noProcessOrderIds = idempotenceConsumer.getNoProcessInternalDeliveryOrderNos(toWarehouseId, orders,
                    OrderDeliveryOpType.INTERNAL_DELIVERY_ORDER_IN_STOCK_TYPE, "内配订单入库");
                // 将订单id，订单项id转换成wms数据
                if (CollectionUtils.isNotEmpty(inventoryOrderDTOS)) {
                    inventoryOrderToWMSData(inventoryOrderDTOS, toOrgId, toWarehouseId);
                }
            }
        }

        idempotenceConsumer.apply(noProcessOrderIds, () -> {

            inventoryOrderDTOS.forEach(inventoryOrder -> {
                warehouseChangListBOConverter.processOrderItemToOrderInternalDeliveryBO(warehouseChangeList,
                    inventoryOrder, toOrgId, toWarehouseId, isVehicleTransshipment,
                    inventoryInStockOrderBatchDTO.getRemark());
            });

            // 处理仓库库存和货位库存
            // LOG.info("仓库库存变更参数warehouseChangeList:{}", JSON.toJSONString(warehouseChangeList));
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, false, true, true,
                false);

            // 处理销售库存发消息 : 只有内配退货入才处理销售库存且内配退货入需要上架,在这里不处理
            // warehouseInventoryManageBL.processSellInventory(warehouseChangeList, null);

            // 未开启货位库存仓库的内配普通入库产品需要关联内配收货暂存位
            Boolean openLocationStock = warehouseConfigService.isOpenLocationStock(toWarehouseId);
            List<WarehouseInventoryChangeBO> associateProductLocationList = warehouseChangeList.stream()
                .filter(WarehouseInventoryChangeBO::getAssociateProductLocation).collect(Collectors.toList());
            if (!isVehicleTransshipment && !openLocationStock
                && CollectionUtils.isNotEmpty(associateProductLocationList)) {
                associateProductLocation(associateProductLocationList);
            }

            // 更新原单的出库位
            inventoryOrderDTOS.stream().flatMap(order -> order.getItems().stream())
                .filter(item -> item.getLocationId() != null)
                .collect(Collectors.groupingBy(InventoryOrderItemDTO::getLocationId)).forEach((locationId, items) -> {
                    List<Long> lstNeedToUpdateItemIds = items.stream().filter(item -> item.getId() != null)
                        .map(InventoryOrderItemDTO::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(lstNeedToUpdateItemIds)) {
                        String locationName = items.get(0).getLocationName();
                        // LOG.info(String.format("更新出库单出库位，OrgID：%s,WarehouseId:%s,货位:%s-%s，BusinessItemIds:%s",
                        // toOrgId, toWarehouseId, locationId, locationName,
                        // JSON.toJSONString(lstNeedToUpdateItemIds)));
                        // 根据BusinessItemId和原单OrgId，更新原单订单项的出库位信息
                        Lists.partition(lstNeedToUpdateItemIds, 500).forEach(part -> {
                            outStockCommManageService.updateOutStockOrderItemLocationByIds(toOrgId, locationId,
                                locationName, part);
                        });
                    }
                });
        });
        return warehouseChangeList;
    }

    /**
     * 将部分数据替换成wms数据
     */
    private void inventoryOrderToWMSData(List<InventoryOrderDTO> inventoryOrderDTOS, Integer orgId,
        Integer warehouseId) {
        if (CollectionUtils.isEmpty(inventoryOrderDTOS)) {
            return;
        }

        List<InventoryOrderDTO> orders = inventoryOrderDTOS;
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 代码删除
        // .stream().filter(order ->
        // orderPushRoolService.canWmsPush(order.getOmsOrderId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<String> refOrderNos = orders.stream().map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
        OutStockOrderQueryDTO queryDTO = new OutStockOrderQueryDTO();
        queryDTO.setOrgId(orgId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setRefOrderNos(refOrderNos);

        List<OutStockOrderDTO> outStockOrderDTOS =
            outStockCommQueryService.pageListOutStockOrder(queryDTO).getDataList();
        if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
            throw new BusinessException("单据在WMS系统中不存在！");
        }

        Map<String, InventoryOrderDTO> inventoryOrderMap = orders.stream()
            .collect(Collectors.toMap(InventoryOrderDTO::getRefOrderNo, Function.identity(), (key1, key2) -> key2));
        outStockOrderDTOS.stream()
            .collect(Collectors.toMap(OutStockOrderDTO::getRefOrderNo, Function.identity(), (key1, key2) -> key2))
            .forEach((orderNo, outStockOrder) -> {
                InventoryOrderDTO inventoryOrderDTO = inventoryOrderMap.get(orderNo);
                if (inventoryOrderDTO != null) {
                    inventoryOrderDTO.setId(Long.valueOf(outStockOrder.getId()));

                    inventoryOrderDTO.getItems().forEach(item -> {
                        String itemId = item.getOmsOrderItemId().toString();
                        for (OutStockOrderItemDTO outStockOrderItem : outStockOrder.getOutStockOrderItemDTOS()) {
                            if (outStockOrderItem.getId().equals(itemId)
                                || outStockOrderItem.getBusinessItemId().equals(itemId)) {
                                item.setId(Long.valueOf(outStockOrderItem.getId()));
                                break;
                            }
                        }
                    });
                } else {
                    throw new BusinessException(orderNo + ",订单在WMS系统不存在");
                }
            });

        // 按查询内配入单据的生产日期赋值
        List<InStockOrderDTO> inStockOrderDTOS =
            iInStockQueryService.findInStockOrderByOrderNoList(orgId, warehouseId, refOrderNos);
        if (!CollectionUtils.isEmpty(inStockOrderDTOS)) {
            inStockOrderDTOS.stream()
                .collect(Collectors.toMap(InStockOrderDTO::getRefOrderNo, Function.identity(), (key1, key2) -> key2))
                .forEach((orderNo, inStockOrder) -> {
                    InventoryOrderDTO inventoryOrderDTO = inventoryOrderMap.get(orderNo);
                    if (inventoryOrderDTO != null) {
                        inventoryOrderDTO.getItems().forEach(item -> {
                            String itemId = item.getOmsOrderItemId().toString();
                            for (InStockOrderItemDTO inStockOrderItem : inStockOrder.getInStockOrderItemDTOList()) {
                                if (inStockOrderItem.getId().equals(item.getOmsOrderItemId())
                                    || inStockOrderItem.getRefOrderItemId().equals(itemId)) {
                                    List<Date> productionDateList = inStockOrderItem.getItemDetailDTOList().stream()
                                        .filter(p -> null != p && null != p.getProductionDate())
                                        .map(InStockOrderItemDetailDTO::getProductionDate).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(productionDateList)) {
                                        item.setProductionDate(productionDateList.get(0));
                                    }
                                }
                            }
                        });
                    }
                });
        }

        // 处理内配退入库生产日期
        for (Map.Entry<String, InventoryOrderDTO> entry : inventoryOrderMap.entrySet()) {
            String orderNo = entry.getKey();
            boolean existProductionDate =
                entry.getValue().getItems().stream().allMatch(p -> null == p.getProductionDate());
            if (!orderNo.matches("^[0-9]+$") && existProductionDate) {
                ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO =
                    new ProductStoreBatchChangeRecordQueryDTO();
                productStoreBatchChangeRecordQueryDTO.setCityId(orgId);
                productStoreBatchChangeRecordQueryDTO.setWarehouseId(warehouseId);
                productStoreBatchChangeRecordQueryDTO.setOrderNo(orderNo);
                LOG.info("inventoryOrderToWMSData 内配退查询生产日期-入参：{}",
                    JSON.toJSONString(productStoreBatchChangeRecordQueryDTO));
                List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOS = iBatchInventoryQueryService
                    .selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO);
                LOG.info("inventoryOrderToWMSData 内配退查询生产日期-结果：{}",
                    JSON.toJSONString(productStoreBatchChangeRecordDTOS));
                if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOS)) {
                    // 按规格id + 一级货主id + 二级货主id 进行分组
                    Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdMap =
                        productStoreBatchChangeRecordDTOS.stream()
                            .filter(p -> null != p && null != p.getProductionDate())
                            .collect(Collectors.groupingBy(it -> it.getProductSpecificationId() + "_" + it.getOwnerId()
                                + "_" + it.getSecOwnerId()));
                    if (ownerIdAndSecOwnerIdMap != null && ownerIdAndSecOwnerIdMap.size() > 0) {
                        entry.getValue().getItems().forEach(d -> {
                            List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOList =
                                ownerIdAndSecOwnerIdMap.get(
                                    d.getProductSpecification_Id() + "_" + d.getOwnerId() + "_" + d.getSecOwnerId());
                            LOG.info("inventoryOrderToWMSData 内配退查询生产日期，获取map：{}",
                                JSON.toJSONString(productStoreBatchChangeRecordDTOList));
                            if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOList)) {
                                d.setProductionDate(productStoreBatchChangeRecordDTOList.get(0).getProductionDate());
                            }
                        });
                    }
                }
            }
        }

        List<String> errOrderNos = new ArrayList<>();
        for (InventoryOrderDTO order : orders) {
            for (InventoryOrderItemDTO item : order.getItems()) {
                if (item.getId() == null) {
                    errOrderNos.add(order.getRefOrderNo());
                    break;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errOrderNos)) {
            throw new BusinessException("订单项与WMS系统不同:" + StringUtils.join(errOrderNos, ","));
        }
    }

    /**
     * 内配单整车转运
     */
    public void vehicleTransshipmentInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        Integer toOrgId = inventoryInStockOrderBatchDTO.getToOrgId();
        Integer toWarehouseId = inventoryInStockOrderBatchDTO.getToWarehouseId();

        // 操作内配入库库存
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS =
            internalDeliveryInStockChange(inventoryInStockOrderBatchDTO, true);

        if (CollectionUtils.isNotEmpty(warehouseInventoryChangeBOS)) {
            // 处理销售库存发消息
            warehouseInventoryManageBL.processSellInventory(warehouseInventoryChangeBOS, null);
        }

        // 修改内配入库单单据
        List<String> orderNos = inventoryInStockOrderBatchDTO.getOrderList().stream()
            .map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderNos)) {
            updateInStockOrder(toOrgId, toWarehouseId, orderNos, InStockOrderState.已入库.getType(),
                inventoryInStockOrderBatchDTO.getStockInTime());

            // 修改订单已拣货状态
            iomsOrderSyncService.updateOutStockOrderNotPick(toOrgId, toWarehouseId, orderNos);
        }

        // 添加日志审计
        if (CollectionUtils.isNotEmpty(inventoryInStockOrderBatchDTO.getOrderList())) {
            for (InventoryOrderDTO inventoryOrderDTO : inventoryInStockOrderBatchDTO.getOrderList()) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "instockorder");
                builder.businessId(String.valueOf(inventoryOrderDTO.getId())).addFeature("OrderNo",
                    inventoryOrderDTO.getRefOrderNo());
                if (inventoryOrderDTO.getBusinessType() != null) {
                    builder.addFeature("BusinessType", String.valueOf(inventoryOrderDTO.getBusinessType()));
                }
                if (inventoryOrderDTO.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(inventoryOrderDTO.getOrderType()));
                }
                builder.content("内配单入库(整车转运)");
                builder.done();
            }
        }

        // 记录内配入库产品供应商信息
        saveInStockProductSupplier(inventoryInStockOrderBatchDTO);
    }

    /**
     * 内配单入库
     */
    public void internalDeliveryInStock(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        Integer toOrgId = inventoryInStockOrderBatchDTO.getToOrgId();
        Integer toWarehouseId = inventoryInStockOrderBatchDTO.getToWarehouseId();

        // 处理入库库存
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS =
            internalDeliveryInStockChange(inventoryInStockOrderBatchDTO, false);

        // 内配订单入库与内配退货订单入库分批处理
        List<InventoryOrderDTO> internalDeliveryOrder = inventoryInStockOrderBatchDTO.getOrderList().stream()
            .filter(order -> OrderConstant.ALLOT_TYPE_ALLOCATION.equals(order.getAllotType())
                || OrderConstant.ALLOT_TYPE_DELIVERY.equals(order.getAllotType()))
            .collect(Collectors.toList());
        List<InventoryOrderDTO> internalDeliveryReturnOrder = inventoryInStockOrderBatchDTO.getOrderList().stream()
            .filter(order -> OrderConstant.ALLOT_TYPE_DELIVERY_RETURN_NPTR.equals(order.getAllotType())
                || OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN.equals(order.getAllotType())
                || OrderConstant.ALLOT_TYPE_DELIVERY_RETURN.equals(order.getAllotType()))
            .collect(Collectors.toList());

        // 内配订单直接入内配收货暂存位
        if (CollectionUtils.isNotEmpty(internalDeliveryOrder)) {
            // 修改入库单状态
            List<String> orderNos =
                internalDeliveryOrder.stream().map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
            updateInStockOrder(toOrgId, toWarehouseId, orderNos, InStockOrderState.已入库.getType(),
                inventoryInStockOrderBatchDTO.getStockInTime());

            // 将调拨中的订单修改为待调度
            OutStockOrderUpdateDTO outStockOrderUpdateDTO = new OutStockOrderUpdateDTO();
            outStockOrderUpdateDTO.setState(OutStockOrderStateEnum.待调度.getType());
            outStockOrderUpdateDTO.setOrgId(toOrgId);
            outStockOrderUpdateDTO.setWarehouseId(toWarehouseId);
            List<String> orderNoList = orderNos.stream().filter(orderNo -> StringUtil.isNotEmpty(orderNo))
                .map(orderNo -> orderNo.startsWith(AllotOrderTypeConstans.NPQZ_NOTENO)
                    ? orderNo.replaceAll(AllotOrderTypeConstans.NPQZ_NOTENO, "") : orderNo)
                .distinct().collect(Collectors.toList());
            outStockOrderUpdateDTO.setRefOrderNos(orderNoList);
            outStockOrderUpdateDTO.setStates(Arrays.asList(OutStockOrderStateEnum.调拨中.getType()));
            outStockCommManageService.updateOutStockOrderInfo(outStockOrderUpdateDTO);
        }

        // 内配退货单需生上架单
        if (CollectionUtils.isNotEmpty(internalDeliveryReturnOrder)) {
            List<String> orderNos =
                internalDeliveryReturnOrder.stream().map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
            // 记录退货入库残次品数量
            Map<String, BigDecimal> orderDefectiveMap = createProductDefectiveInfo(internalDeliveryReturnOrder);
            // 修改入库单状态
            updateInStockOrder(toOrgId, toWarehouseId, orderNos, InStockOrderState.已入库.getType(),
                inventoryInStockOrderBatchDTO.getStockInTime());

            // 查询入库单
            InternalDeliveryInStockDTO internalDeliveryInStockDTO = new InternalDeliveryInStockDTO();
            internalDeliveryInStockDTO.setToOrgId(toOrgId);
            internalDeliveryInStockDTO.setToWarehouseId(toWarehouseId);
            internalDeliveryInStockDTO.setOrderNos(orderNos);
            LOG.info("内配退货入库单查询参数:{}", JSON.toJSONString(internalDeliveryInStockDTO));
            List<InStockOrderDTO> inStockOrderDTOS =
                iInStockOrderService.findInStockOrderByOrderNos(internalDeliveryInStockDTO);
            inStockOrderDTOS.forEach(inStockOrderDTO -> {
                inStockOrderDTO.getInStockOrderItemDTOList()
                    .forEach(item -> item.setShelfType(putawayTypeEnum.内配单退货上架.getType()));
            });
            // 处理入库单残次品信息
            createInStockOrderDefectiveInfo(orderDefectiveMap, inStockOrderDTOS);
            // 生成上架任务
            List<Long> putAwayTaskProcessSaleSkuIdList = createPutAwayTaskByOrderAndScmVersion(inStockOrderDTOS, null,
                InStockOrderState.已入库.getType(), putawayTypeEnum.内配单退货上架.getType());
            // 移除上架处理销售库存的产品
            warehouseInventoryChangeBOS
                .removeIf(e -> e != null && putAwayTaskProcessSaleSkuIdList.contains(e.getProductSkuId()));
        }
        if (CollectionUtils.isNotEmpty(warehouseInventoryChangeBOS)) {
            // 处理销售库存发消息
            warehouseInventoryManageBL.processSellInventory(warehouseInventoryChangeBOS, null);
        }

        // 添加日志审计
        if (CollectionUtils.isNotEmpty(inventoryInStockOrderBatchDTO.getOrderList())) {
            for (InventoryOrderDTO inventoryOrderDTO : inventoryInStockOrderBatchDTO.getOrderList()) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "instockorder");
                builder.businessId(String.valueOf(inventoryOrderDTO.getId())).addFeature("OrderNo",
                    inventoryOrderDTO.getRefOrderNo());
                if (inventoryOrderDTO.getBusinessType() != null) {
                    builder.addFeature("BusinessType", String.valueOf(inventoryOrderDTO.getBusinessType()));
                }
                if (inventoryOrderDTO.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(inventoryOrderDTO.getOrderType()));
                }
                builder.content("内配单确认入库");
                builder.done();
            }
        }

        // 记录内配入库产品供应商信息
        saveInStockProductSupplier(inventoryInStockOrderBatchDTO);
        // 入库通知 erp
        erpInOutStockBL.inStockNotifyErp(inventoryInStockOrderBatchDTO);
    }

    /**
     * 根据订单信息按单号、sku、二级货组装残次品
     */
    private Map<String, BigDecimal> createProductDefectiveInfo(List<InventoryOrderDTO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyMap();
        }
        Map<String, BigDecimal> orderDefectiveMap = new HashMap<>(50);
        orderList.stream().filter(order -> order != null && CollectionUtils.isNotEmpty(order.getItems()))
            .forEach(order -> {
                // 按单号 + 项ID进行汇总：入库单中会记录Oms单据ID及项ID
                Map<String,
                    BigDecimal> itemDefectiveMap = order.getItems().stream()
                        .filter(item -> item != null
                            && ObjectUtils.defaultIfNull(item.getDefectiveTotalCount(), BigDecimal.ZERO)
                                .compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.groupingBy(
                            item -> String.format("%s-%s", order.getRefOrderNo(),
                                (item.getOmsOrderItemId() != null ? item.getOmsOrderItemId() : item.getId())),
                            Collectors.mapping(item -> item.getDefectiveTotalCount(),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                orderDefectiveMap.putAll(itemDefectiveMap);
            });

        return orderDefectiveMap;
    }

    /**
     * 设置入库单残次品数量
     */
    private void createInStockOrderDefectiveInfo(Map<String, BigDecimal> orderDefectiveMap,
        List<InStockOrderDTO> inStockOrderDTOS) {
        if (orderDefectiveMap == null || orderDefectiveMap.isEmpty() || CollectionUtils.isEmpty(inStockOrderDTOS)) {
            return;
        }
        inStockOrderDTOS.stream()
            .filter(order -> order != null && CollectionUtils.isNotEmpty(order.getInStockOrderItemDTOList()))
            .forEach(order -> {
                order.getInStockOrderItemDTOList().stream().filter(Objects::nonNull)
                    .collect(Collectors
                        .groupingBy(item -> String.format("%s-%s", order.getRefOrderNo(), item.getRefOrderItemId())))
                    .forEach((key, products) -> {
                        BigDecimal defectiveCount = orderDefectiveMap.get(key);
                        if (defectiveCount == null || BigDecimal.ZERO.compareTo(defectiveCount) == 0) {
                            return;
                        }
                        BigDecimal skuCount = products.stream().filter(it -> it.getUnitTotalCount() != null)
                            .map(InStockOrderItemDTO::getUnitTotalCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        LOG.info("入库单 {} 产品 {} 入库单小单位总数量：{}，残次品数量:{}", order.getRefOrderNo(), key, skuCount,
                            defectiveCount);
                        if (skuCount == null || skuCount.compareTo(defectiveCount) < 0) {
                            products.forEach(item -> {
                                if (item != null && ObjectUtils.defaultIfNull(item.getUnitTotalCount(), BigDecimal.ZERO)
                                    .compareTo(BigDecimal.ZERO) >= 0) {
                                    productDefectiveCount(item.getUnitTotalCount(), item);
                                }
                            });
                        } else {
                            for (InStockOrderItemDTO itemDTO : products) {
                                if (itemDTO != null
                                    && ObjectUtils.defaultIfNull(itemDTO.getUnitTotalCount(), BigDecimal.ZERO)
                                        .compareTo(BigDecimal.ZERO) >= 0) {
                                    defectiveCount = productDefectiveCount(defectiveCount, itemDTO);
                                }
                            }
                        }
                    });
            });
    }

    private BigDecimal productDefectiveCount(BigDecimal defectiveCount, InStockOrderItemDTO itemDTO) {
        if (ObjectUtils.defaultIfNull(defectiveCount, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0
            || itemDTO == null || ObjectUtils.defaultIfNull(itemDTO.getUnitTotalCount(), BigDecimal.ZERO)
                .compareTo(BigDecimal.ZERO) <= 0) {
            return defectiveCount;
        }
        // 设置项最小单位数量
        itemDTO.setUnitTotalCount(ObjectUtils.defaultIfNull(itemDTO.getUnitTotalCount(), BigDecimal.ZERO));
        BigDecimal shareCount = BigDecimal.ZERO;
        BigDecimal unitTotalCount = itemDTO.getUnitTotalCount();
        defectiveCount = ObjectUtils.defaultIfNull(defectiveCount, BigDecimal.ZERO);
        BigDecimal subtract = unitTotalCount.subtract(defectiveCount);
        if (subtract.compareTo(BigDecimal.ZERO) >= 0) {
            shareCount = defectiveCount;
            defectiveCount = BigDecimal.ZERO;
        } else {
            shareCount = unitTotalCount;
            defectiveCount = subtract.abs();
        }
        OrderItemRelatedDTO itemRelatedDTO = new OrderItemRelatedDTO();
        itemRelatedDTO.setUnitTotalCount(shareCount);
        InStockOrderItemExtContentDTO extContentDTO = new InStockOrderItemExtContentDTO();
        extContentDTO.setItemRelatedDTOList(Collections.singletonList(itemRelatedDTO));
        itemDTO.setExtContent(JSON.toJSONString(extContentDTO));
        return defectiveCount;
    }

    /**
     * 记录内配入库产品供应商信息
     */
    private void saveInStockProductSupplier(InventoryInStockOrderBatchDTO inventoryInStockOrderBatchDTO) {
        if (inventoryInStockOrderBatchDTO == null) {
            return;
        }
        Integer supplier = inventoryInStockOrderBatchDTO.getWarehouseId();
        Integer toWarehouseId = inventoryInStockOrderBatchDTO.getToWarehouseId();
        Integer toOrgId = inventoryInStockOrderBatchDTO.getToOrgId();
        if (supplier == null || toWarehouseId == null) {
            return;
        }
        List<InventoryOrderDTO> orderList = inventoryInStockOrderBatchDTO.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        Warehouse warehouse = iWarehouseQueryService.findWarehouseById(supplier);
        // 只记录内配入供应商(内配退不记录)
        List<ProductSupplierDTO> addProductSupplierList = orderList.stream()
            .filter(e -> e != null
                && !(OrderConstant.ALLOT_TYPE_DELIVERY_RETURN_NPTR.equals(e.getAllotType())
                    || OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN.equals(e.getAllotType())
                    || OrderConstant.ALLOT_TYPE_DELIVERY_RETURN.equals(e.getAllotType()))
                && CollectionUtils.isNotEmpty(e.getItems()))
            .flatMap(e -> e.getItems().stream()).filter(e -> e != null && e.getProductSpecification_Id() != null)
            .map(e -> {
                ProductSupplierDTO supplierDTO = new ProductSupplierDTO();
                supplierDTO.setCityId(toOrgId);
                supplierDTO.setWarehouseId(toWarehouseId);
                supplierDTO.setProductSkuId(e.getSkuId());
                supplierDTO.setProductSpecificationId(e.getProductSpecification_Id());
                supplierDTO.setOwnerId(e.getOwnerId());
                supplierDTO.setSecOwnerId(e.getSecOwnerId());
                supplierDTO.setSupplierType(SupplierTypeEnum.WAREHOUSE.getValue());
                supplierDTO.setSupplierId(String.valueOf(supplier));
                supplierDTO.setSupplierName(warehouse != null ? warehouse.getName() : null);
                return supplierDTO;
            }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addProductSupplierList)) {
            LOG.info("城市[{}]仓库[{}]内配入库没有需要记录的供应商信息", toOrgId, toWarehouseId);
            return;
        }
        iProductSupplierService.batchSaveProductSupplier(addProductSupplierList);
    }

    /**
     * 根据key检查内配配置
     *
     * @param toWarehouseId
     * @return
     */
    private Boolean checkContentConfig(Integer toWarehouseId, String key) {
        Boolean flag = false;
        if (toWarehouseId == null) {
            return flag;
        }
        String contentValue = iContentConfigurationService.getContentValue(key, null, "");
        String[] warehouseIds = contentValue.split("、");

        for (String id : warehouseIds) {
            if (id.equals(toWarehouseId.toString())) {
                flag = true;
                break;
            }
        }

        return flag;
    }

    /**
     * 查找内配单相关暂存位
     */
    public DefaultLocationConfigDTO findDefaultLocationConfig(Integer warehouseId, Byte defaultLocationType) {
        DefaultLocationQueryDTO defaultLocationQueryDTO = new DefaultLocationQueryDTO();
        defaultLocationQueryDTO.setWarehouseId(warehouseId);
        defaultLocationQueryDTO.setTemporaryLocationType(defaultLocationType);
        List<DefaultLocationConfigDTO> defaultLocationConfigDTOS =
            iDefaultLocationConfigService.findDefaultLocationConfig(defaultLocationQueryDTO);
        if (CollectionUtils.isNotEmpty(defaultLocationConfigDTOS)) {
            return defaultLocationConfigDTOS.get(0);
        } else {
            throw new BusinessValidateException(DefaultTypeEnum.getEnum(defaultLocationType.intValue()) + "不能为空");
        }

    }

    /**
     * 修改入库单
     */
    private void updateInStockOrder(Integer orgId, Integer warehouseId, List<String> orderNos, Byte state,
        Date stockInTime) {
        InternalDeliveryInStockDTO internalDeliveryInStockDTO = new InternalDeliveryInStockDTO();
        internalDeliveryInStockDTO.setToOrgId(orgId);
        internalDeliveryInStockDTO.setToWarehouseId(warehouseId);
        internalDeliveryInStockDTO.setOrderNos(orderNos);
        internalDeliveryInStockDTO.setStockInTime(stockInTime);
        internalDeliveryInStockDTO.setState(state);
        iInStockOrderService.updateStateForInternalDeliveryOrder(internalDeliveryInStockDTO);
    }

    /**
     * 内配入库关联货位
     */
    private void associateProductLocation(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            List<ProductLocationDTO> productLocationList = new ArrayList<>();

            // 根据规格货主查找对应城市产品
            ProductSkuBySpecificationSO productSkuBySpecificationSO = new ProductSkuBySpecificationSO();
            productSkuBySpecificationSO.setCityId(warehouseChangeList.get(0).getCityId());
            List<ProductSkuBySpecificationQueryDTO> specList = new ArrayList<>();
            warehouseChangeList.forEach(warehouseInventoryChangeBO -> {
                ProductSkuBySpecificationQueryDTO productSkuBySpecificationQueryDTO =
                    new ProductSkuBySpecificationQueryDTO();
                productSkuBySpecificationQueryDTO
                    .setProductSpecificationId(warehouseInventoryChangeBO.getProductSpecificationId());
                productSkuBySpecificationQueryDTO.setOwnerId(warehouseInventoryChangeBO.getOwnId());

                specList.add(productSkuBySpecificationQueryDTO);
            });
            productSkuBySpecificationSO.setSpecList(specList);

            List<ProductSkuDTO> productSkuDTOS = productSkuQueryService.findBySpec(productSkuBySpecificationSO);
            if (CollectionUtils.isEmpty(productSkuDTOS)) {
                LOG.warn("内配入库关联货位产品不存在，参数:{}", JSON.toJSONString(productSkuBySpecificationSO));
                return;
            }
            List<Long> skuIds =
                productSkuDTOS.stream().map(ProductSkuDTO::getProductSkuId).collect(Collectors.toList());
            List<ProductLocationDTO> productLocationDTOS =
                iProductLocationService.listProductLocationPOBySkuIdList(skuIds);

            if (CollectionUtils.isEmpty(productLocationDTOS)) {
                productLocationDTOS = new ArrayList<>();
            }

            for (WarehouseInventoryChangeBO item : warehouseChangeList) {
                Optional<Long> skuId = productSkuDTOS.stream().filter(sku -> {
                    // LOG.info("sku:{},item:{}", JSON.toJSONString(sku), JSON.toJSONString(item));
                    return Objects.equals(item.getOwnId(), sku.getCompany_Id())
                        && Objects.equals(sku.getProductSpecificationId(), item.getProductSpecificationId())
                        && Objects.equals(sku.getSource().intValue(), item.getSource());
                }).map(ProductSkuDTO::getProductSkuId).findFirst();

                if (skuId.isPresent()) {
                    Long locationId = item.getLocationId();
                    Optional<ProductLocationDTO> locationDTO = productLocationDTOS.stream()
                        .filter(productLocation -> productLocation.getProductSkuId().equals(skuId.get())
                            && productLocation.getLocationId().equals(locationId))
                        .findFirst();
                    if (!locationDTO.isPresent()) {
                        ProductLocationDTO productLocationDTO = new ProductLocationDTO();
                        productLocationDTO.setCityId(item.getCityId());
                        productLocationDTO.setLocationId(locationId);
                        productLocationDTO.setLocationName(item.getLocationName());
                        productLocationDTO.setProductSkuId(skuId.get());
                        productLocationDTO.setWarehouseId(item.getWarehouseId());
                        productLocationDTO.setUserId(0);

                        productLocationList.add(productLocationDTO);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(productLocationList)) {
                productLocationList =
                    productLocationList.stream().filter(StreamUtils.distinctByKey(ProductLocationDTO::getProductSkuId))
                        .collect(Collectors.toList());
                LOG.info("内配入库关联货位新增信息:{}", JSON.toJSONString(productLocationList));
                iProductLocationService.insertBatch(productLocationList);
            }
        }
    }

    /**
     * 创建入库单
     */
    public void saveInStockOrderList(List<InStockOrderDTO> inStockOrderDTOList) {
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(inStockOrderDTOList)) {
            LOG.info("按单号生成入库信息: {}", JSON.toJSONString(inStockOrderDTOList));
            inStockOrderDTOList.stream().filter(Objects::nonNull).forEach(order -> order.setCreateBatchNo(false));
            iInStockOrderService.saveInStockOrderList(inStockOrderDTOList);
        }
    }

    /**
     * 直接出库
     */
    public void directCompleteOutStockOrder(List<OrderDTO> orders) {
        LOG.info("店仓订单直接出库:{}", JSON.toJSONString(orders));
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            OrderDTOConvert.orderDTOS2InventoryDeliveryJiupiOrderS(orders);
        List<String> noProcessOrderNos = new ArrayList<>();
        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orderList) -> {
                List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, orderList,
                    OrderDeliveryOpType.ORDER_DIRECTLY_TYPE, "订单直接出库");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });

        if (CollectionUtils.isEmpty(noProcessOrderNos)) {
            LOG.info(String.format("订单直接出库-订单已经全部处理过！%s", JSON.toJSONString(orders)));
            return;
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            // 遍历添加批次所有BO到list中
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                warehouseChangListBOConverter.directProcessingOrderToOrderDeliveryBO(sellChangeList,
                    warehouseChangeList, inventoryDeliveryJiupiOrder, false);
            }

            if (CollectionUtils.isEmpty(warehouseChangeList)) {
                LOG.info(String.format("订单直接出库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            }

            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, true, true, true,
                true);
        });

        // 处理销售库存发消息
        warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);

        // 修改订单状态
        updateOutStockOrderState(deliveryOrders, OutStockOrderStateEnum.已出库.getType());

        // if(orders.get(0) != null &&
        // !Objects.equals(orders.get(0).getCapabilityType(),pushCapabilityTypeEnum.直接出库.getType())) {
        if (orders.get(0) != null && null == orders.get(0).getCapabilityType()) {
            // 原流程发送oms订单完成
            omsOrderComplete(warehouseChangeList);
        }
    }

    /**
     * 直接入库
     */
    public void directCompleteInStockOrder(List<OrderDTO> orders) {
        LOG.info("店仓订单直接入库:{}", JSON.toJSONString(orders));
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            OrderDTOConvert.orderDTOS2InventoryDeliveryJiupiOrderS(orders);

        List<String> noProcessOrderNos = new ArrayList<>();
        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orderList) -> {
                List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, orderList,
                    OrderDeliveryOpType.IN_STOCK_ORDER_TYPE, "订单直接入库");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });

        if (CollectionUtils.isEmpty(noProcessOrderNos)) {
            LOG.info(String.format("订单直接入库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            return;
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            // 遍历添加批次所有BO到list中
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                warehouseChangListBOConverter.directProcessingOrderToOrderDeliveryBO(sellChangeList,
                    warehouseChangeList, inventoryDeliveryJiupiOrder, false);
            }

            if (CollectionUtils.isEmpty(warehouseChangeList)) {
                LOG.info(String.format("订单直接入库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            }

            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, false, true, true,
                true);
        });

        // 处理销售库存发消息
        if (!Objects.equals(orders.get(0).getCapabilityType(), pushCapabilityTypeEnum.奖券入库单.getType())) {
            warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);
        }

        // 修改订单状态
        List<Long> orderIds =
            deliveryOrders.stream().map(InventoryDeliveryJiupiOrder::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            if (Objects.equals(orders.get(0).getCapabilityType(), pushCapabilityTypeEnum.直接入库.getType())
                || Objects.equals(orders.get(0).getCapabilityType(), pushCapabilityTypeEnum.调拨强制取消.getType())
                || Objects.equals(orders.get(0).getCapabilityType(), pushCapabilityTypeEnum.调拨强制完成.getType())) {
                // 直接入库修改状态及入库时间
                iInStockOrderService.completePushOrderUpdateState(orderIds);
            } else {
                iInStockOrderService.completeOrderUpdateState(orderIds);
            }
        }

        // if(orders.get(0) != null &&
        // !Objects.equals(orders.get(0).getCapabilityType(),pushCapabilityTypeEnum.直接入库.getType())) {
        if (orders.get(0) != null && null == orders.get(0).getCapabilityType()) {
            // 原流程发送oms订单完成
            omsOrderComplete(warehouseChangeList);
        }

    }

    /**
     * 确认入库同步残次品陈列品信息
     *
     * @param inventoryRelatedOrders
     */
    public void affirmRelatedIInStock(List<InStockOrderDTO> inventoryRelatedOrders) {
        List<InStockOrderDTO> inStockOrderDTOS =
            InventoryInStockOrderBatchDTOConvert.inventoryOrderDTOS2InStockOrderDTOS(inventoryRelatedOrders);
        if (CollectionUtils.isNotEmpty(inStockOrderDTOS)) {
            LOG.info("包含残次品的入库单:{}", JSON.toJSONString(inStockOrderDTOS));
            iInStockCommService.processStockOrderRelatedItems(inStockOrderDTOS);
        }
    }

    /**
     * 根据仓库版本生成上架任务
     */
    public List<Long> createPutAwayTaskByOrderAndScmVersion(List<InStockOrderDTO> inStockOrderList,
        List<Long> skipProductSkuList, Byte orderState, Byte shelfType) {
        List<Long> lstResult = new ArrayList<>();
        if (CollectionUtils.isEmpty(inStockOrderList)) {
            LOG.info("createPutAwayTaskByOrderAndScmVersion - 入库单信息为空，不生成上架任务！");
            return lstResult;
        }
        // 不需要生成上架任务产品
        List<Long> skipSkuList = skipProductSkuList == null ? Collections.EMPTY_LIST : skipProductSkuList;
        InStockOrderDTO inStockOrderDTO = inStockOrderList.get(0);
        Byte scmVersion = warehouseConfigService.getScmVersionByWarehouseId(inStockOrderDTO.getWarehouseId());
        // 如果开启3.0或者2.5，则需要生成上架任务
        boolean needCreatePutAwayTask = !WarehouseConfigConstants.SCM_VERSION_2.equals(scmVersion);
        if (!needCreatePutAwayTask) {
            LOG.info("createPutAwayTaskByOrderAndScmVersion - 仓库[{}]版本:{} 不需要生成上架任务！", inStockOrderDTO.getWarehouseId(),
                scmVersion);
        }
        LOG.info("createPutAwayTaskByOrderAndScmVersion - 仓库[{}] 需要跳过生上架任务的 SKU 集合：{}",
            inStockOrderDTO.getWarehouseId(), JSON.toJSONString(skipSkuList));
        // 深度拷贝来源数据
        List<InStockOrderDTO> inStockOrderDTOS = null;
        try {
            inStockOrderDTOS = StreamUtils.deepCopy(inStockOrderList);
        } catch (Exception e) {
            LOG.error(String.format("createPutAwayTaskByOrderAndScmVersion -仓库[%s]拷贝来源上架信息时失败！",
                inStockOrderDTO.getWarehouseId()), e);
            return lstResult;
        }
        // 有上架任务的入库单不生成新的上架任务
        List<InStockOrderDTO> needCreatePutAwayOrders = inStockOrderDTOS.stream()
            .filter(e -> e != null
                && (org.apache.commons.lang3.StringUtils.isBlank(e.getPutawayNo())
                    && org.apache.commons.lang3.StringUtils.isBlank(e.getPutawayId()))
                && CollectionUtils.isNotEmpty(e.getInStockOrderItemDTOList()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needCreatePutAwayOrders)) {
            LOG.info("createPutAwayTaskByOrderAndScmVersion - 入库单已经有上架任务，不需要重复生成！");
            return lstResult;
        }
        // 扣除残次品数量
        needCreatePutAwayOrders.stream().filter(e -> e != null).forEach(e -> {
            // 此处生成的上架任务状态应该为[待上架],则 DirectChangeStock 设置成 true
            e.setNotUpdateProductStore(true);
            e.setDirectChangeStock(true);
            e.setState(orderState);
            e.setShelfType(shelfType);
            e.getInStockOrderItemDTOList().stream().filter(item -> item != null).forEach(item -> {
                // 设置子项上架类型
                item.setShelfType(shelfType);
                BigDecimal relatedCount = BigDecimal.ZERO;
                String extContent = item.getExtContent();
                BigDecimal unitTotalCount = ObjectUtils.defaultIfNull(item.getUnitTotalCount(), BigDecimal.ZERO);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(extContent)) {
                    InStockOrderItemExtContentDTO extContentDTO =
                        JSON.parseObject(extContent, InStockOrderItemExtContentDTO.class);
                    if (extContentDTO != null && CollectionUtils.isNotEmpty(extContentDTO.getItemRelatedDTOList())) {
                        relatedCount = extContentDTO.getItemRelatedDTOList().stream()
                            .filter(related -> related != null && related.getUnitTotalCount() != null)
                            .map(related -> related.getUnitTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
                if (relatedCount.compareTo(BigDecimal.ZERO) != 0) {
                    // lstResult.add(item.getSkuId());
                    // 重新计算扣除关联数据[残次品]数量
                    BigDecimal newCount = unitTotalCount.subtract(relatedCount);
                    BigDecimal[] newCuntArr = newCount.divideAndRemainder(item.getSpecQuantity());
                    item.setInUnitTotalCount(newCount);
                    item.setUnitTotalCount(newCount);
                    item.setPackageCount(newCuntArr[0]);
                    item.setUnitCount(newCuntArr[1]);
                    // 二级货主支持 detail 之后需要重新计算
                    List<InStockOrderItemDetailDTO> itemDetailDTOList = item.getItemDetailDTOList();
                    if (CollectionUtils.isNotEmpty(itemDetailDTOList)
                        && item.getUnitTotalCount().compareTo(BigDecimal.ZERO) > 0) {
                        List<InStockOrderItemDetailDTO> newDetailList = new ArrayList<>();
                        // 数量从大到小 return Collections.EMPTY_LIST;
                        // 排序
                        Collections.sort(itemDetailDTOList,
                            Comparator.comparing(InStockOrderItemDetailDTO::getUnitTotalCount,
                                Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
                        // 需要分配的数量
                        BigDecimal newUnitTotalCount = item.getUnitTotalCount();
                        for (int i = 0; i < itemDetailDTOList.size(); i++) {
                            InStockOrderItemDetailDTO oldDetail = itemDetailDTOList.get(i);
                            if (oldDetail == null) {
                                continue;
                            }
                            if (newUnitTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                                break;
                            }
                            if (i == itemDetailDTOList.size() - 1) {
                                // 最后一个货位，如果还有需要扣除的则直接改货位扣除
                                oldDetail.setUnitTotalCount(newUnitTotalCount);
                                oldDetail.setInUnitTotalCount(newUnitTotalCount);
                                newUnitTotalCount = BigDecimal.ZERO;
                                // 记录新 detail
                                newDetailList.add(oldDetail);
                                continue;
                            }
                            if (oldDetail.getUnitTotalCount() == null
                                || oldDetail.getUnitTotalCount().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            BigDecimal surplus = newUnitTotalCount.subtract(oldDetail.getUnitTotalCount());
                            if (surplus.compareTo(BigDecimal.ZERO) <= 0) {
                                // 刚够分这个批次则直接都给这个批次
                                oldDetail.setUnitTotalCount(newUnitTotalCount);
                                oldDetail.setInUnitTotalCount(newUnitTotalCount);
                                newUnitTotalCount = BigDecimal.ZERO;
                            } else {
                                // 还能分给其他批次
                                newUnitTotalCount = surplus;
                            }
                            // 记录新 detail
                            newDetailList.add(oldDetail);
                        }
                        // 重新设置 detail
                        item.setItemDetailDTOList(newDetailList);
                    }
                }
            });
            // 去掉不需要生成上架任务的产品和扣除残次品数量后总数小于等于0的产品
            e.getInStockOrderItemDTOList()
                .removeIf(item -> item != null && (skipSkuList.contains(item.getSkuId()) || ObjectUtils
                    .defaultIfNull(item.getUnitTotalCount(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0));
            // 计算小件总数量
            BigDecimal unitAmount =
                e.getInStockOrderItemDTOList().stream().filter(item -> item != null && item.getUnitCount() != null)
                    .map(item -> item.getUnitCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算大件总数量
            BigDecimal packageAmount =
                e.getInStockOrderItemDTOList().stream().filter(item -> item != null && item.getPackageCount() != null)
                    .map(item -> item.getPackageCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            e.setUnitAmount(unitAmount);
            e.setPackageAmount(packageAmount);
        });
        // 只获取子项不为空的入库单
        List<InStockOrderDTO> realCreatePutawayList = needCreatePutAwayOrders.stream()
            .filter(e -> e != null && CollectionUtils.isNotEmpty(e.getInStockOrderItemDTOList()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(realCreatePutawayList)) {
            LOG.info("createPutAwayTaskByOrderAndScmVersion - 扣除关联[残次品]数量后没有需要上架产品！入库单NO：{}",
                needCreatePutAwayOrders.stream().filter(e -> e != null).map(e -> e.getRefOrderNo()).distinct()
                    .collect(Collectors.joining(",")));
        } else {
            // 生成上架单
            InStockDTO inStockDTO = new InStockDTO();
            inStockDTO.setOrgId(inStockOrderDTO.getOrgId());
            inStockDTO.setWarehouseId(inStockOrderDTO.getWarehouseId());
            inStockDTO.setInStockOrderDTOList(realCreatePutawayList);
            try {
                List<InStockOrderDTO> putawayTaskReturnDTO = iInStockTaskProcessService.createPutawayTask(inStockDTO);
                if (CollectionUtils.isNotEmpty(putawayTaskReturnDTO)) {
                    // 找出上架后才处理销售库存产品
                    lstResult.addAll(putawayTaskReturnDTO.stream()
                        .filter(e -> e != null && CollectionUtils.isNotEmpty(e.getInStockOrderItemDTOList()))
                        .flatMap(e -> e.getInStockOrderItemDTOList().stream())
                        .filter(e -> Objects.equals(InStockStrategyConstants.PROCESS_SALESSTOCK_AFTERPUTAWAY_YES,
                            e.getIsProcessSalesStockAfterPutAway()))
                        .map(e -> e.getSkuId()).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                LOG.error(String.format("createPutAwayTaskByOrderAndScmVersion -仓库[%s]生成上架任务失败！",
                    inStockOrderDTO.getWarehouseId()), e);
            }
        }
        LOG.info("createPutAwayTaskByOrderAndScmVersion - 仓库[{}] 不需要加销售库存 SKU 集合：{}", inStockOrderDTO.getWarehouseId(),
            JSON.toJSONString(lstResult));
        return lstResult;
    }

    /**
     * 根据类型校验发货库存数量
     */
    public List<DeliveryProductInventoryDTO> validateOrderDeliveryProductInventory(
        List<InventoryDeliveryJiupiOrder> deliveryOrders, String orderDeliveryOpType) {
        checkOrder(deliveryOrders);

        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getFromWarehouseId))
            .forEach((warehouseId, orders) -> {
                idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders, orderDeliveryOpType, "发货库存数量校验");
            });

        List<DeliveryProductInventoryDTO> deliveryProductInventoryDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            LOG.info(String.format("订单配送出库校验发货库存数量-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            return deliveryProductInventoryDTOS;
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();

        for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
            inventoryDeliveryJiupiOrder.setCityId(inventoryDeliveryJiupiOrder.getFromCityId());
            inventoryDeliveryJiupiOrder.setWarehouseId(inventoryDeliveryJiupiOrder.getFromWarehouseId());
            warehouseChangListBOConverter.processOrderItemToOrderDeliveryBO(sellChangeList, warehouseChangeList,
                inventoryDeliveryJiupiOrder, true);
        }

        Map<Long, BigDecimal> errSkuIdMap = new HashMap<>(16);
        ArrayList<ProductInventoryPO> productInventoryPOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            warehouseInventoryManageBL.validateOrderDeliveryProductStore(warehouseChangeList, errSkuIdMap,
                productInventoryPOS, true, false);
        }

        if (errSkuIdMap.size() > 0) {
            List<Long> errSkuIds = new ArrayList<>(errSkuIdMap.keySet());
            List<ProductSkuPO> productSkuPOList = productSkuQueryBL.getProductSkuListByIds(errSkuIds);
            Map<Long, ProductSkuPO> skuMap = productSkuPOList.stream()
                .collect(Collectors.toMap(ProductSkuPO::getProductSkuId, Function.identity(), (key1, key2) -> key2));
            deliveryOrders.forEach(order -> {
                order.getItems().forEach(item -> {
                    item.setOrderNo(order.getOrderNo());
                });
            });
            deliveryOrders.stream().flatMap(order -> order.getItems().stream())
                .filter(item -> errSkuIds.contains(item.getProductSkuId()))
                .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getProductSkuId))
                .forEach((skuId, items) -> {
                    // LOG.info("校验发货库存items:{}", JSON.toJSONString(items));
                    DeliveryProductInventoryDTO deliveryProductInventoryDTO = new DeliveryProductInventoryDTO();
                    deliveryProductInventoryDTO.setProductSkuId(skuId);
                    ProductSkuPO productSkuPO = skuMap.get(skuId);
                    if (productSkuPO != null) {
                        deliveryProductInventoryDTO.setOwnerId(productSkuPO.getCompanyId());
                        deliveryProductInventoryDTO.setSecOwnerId(productSkuPO.getSecOwnerId());
                        deliveryProductInventoryDTO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
                        deliveryProductInventoryDTO.setSpecQuantity(productSkuPO.getPackageQuantity());
                        deliveryProductInventoryDTO.setSpecName(productSkuPO.getSpecificationName());
                        deliveryProductInventoryDTO.setProductName(productSkuPO.getName());
                        deliveryProductInventoryDTO.setPackageName(productSkuPO.getPackageName());
                        deliveryProductInventoryDTO.setUnitName(productSkuPO.getUnitName());
                    }
                    deliveryProductInventoryDTO.setSaleSpecQuantity(items.get(0).getSaleSpecQuantity());
                    BigDecimal lakeCount = errSkuIdMap.get(skuId);
                    deliveryProductInventoryDTO.setLakeCount(lakeCount);
                    BigDecimal deliverCount = items.stream().map(InventoryDeliveryJiupiOrderItem::getDeliverCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    deliveryProductInventoryDTO.setDeliverCount(deliverCount);
                    deliveryProductInventoryDTO.setWarehouseInventory(
                        deliverCount.multiply(deliveryProductInventoryDTO.getSaleSpecQuantity()).add(lakeCount));
                    List<String> orderNos = items.stream().map(InventoryDeliveryJiupiOrderItem::getOrderNo).distinct()
                        .collect(Collectors.toList());
                    deliveryProductInventoryDTO.setOrderNos(orderNos);
                    deliveryProductInventoryDTO.setOrderCount(orderNos.size());

                    deliveryProductInventoryDTOS.add(deliveryProductInventoryDTO);
                });
        }

        LOG.info("校验发货库存数量:{}", JSON.toJSONString(deliveryProductInventoryDTOS));
        return deliveryProductInventoryDTOS;
    }

    /**
     * 订单发货
     */
    public void orderDelivery(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        List<String> noProcessOrderIds =
            idempotenceConsumer.getNoProcessOrderNos(deliveryOrders.get(0).getWarehouseId(), deliveryOrders,
                OrderDeliveryOpType.ORDER_DELIVER_MESSAGE_TYPE, "订单配送出库");

        if (CollectionUtils.isEmpty(noProcessOrderIds)) {
            LOG.info(String.format("订单发货-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            return;
        }

        // 检验是否店仓或者配置可以负库存发货仓库
        Boolean checkWarehouseInventory =
            !checkContentConfig(deliveryOrders.get(0).getWarehouseId(), "AllowNegativeDelivery");
        idempotenceConsumer.apply(noProcessOrderIds, () -> {
            checkOrderNew(deliveryOrders);

            // 根据出库方式区分中心仓发货还是本地仓发货
            List<InventoryDeliveryJiupiOrder> intercityOrders =
                deliveryOrders.stream().filter(order -> order.getOutStockMode() == OutStockMode.INTERCITY_DISTRIBUTION)
                    .collect(Collectors.toList());
            List<InventoryDeliveryJiupiOrder> localOrders = deliveryOrders.stream()
                .filter(order -> order.getOutStockMode() == OutStockMode.LOCAL_DELIVERY).collect(Collectors.toList());

            // 内配订单处理
            if (CollectionUtils.isNotEmpty(intercityOrders)) {
                List<WarehouseInventoryChangeBO> intercityWarehouseChangeList = warehouseChangListBOConverter
                    .InventoryDeliveryJiupiOrders2WarehouseInventoryChangeBOS(intercityOrders, checkWarehouseInventory);
                // 订单直发不需要发货数量记录
                Boolean isUpdateDeliveryCount = true;
                if (Objects.equals(intercityOrders.get(0).getPackageAttribute(),
                    OrderPickTypeConstant.PICKTYPE_LOGIC)) {
                    isUpdateDeliveryCount = false;
                }
                warehouseInventoryManageBL.validateAndProcessProductStore(intercityWarehouseChangeList,
                    checkWarehouseInventory, isUpdateDeliveryCount, true, true, false);

                // 内配单据后续流程处理
                processIntercityOrders(intercityOrders, intercityWarehouseChangeList);
            }

            // 本地仓订单处理
            if (CollectionUtils.isNotEmpty(localOrders)) {
                List<WarehouseInventoryChangeBO> localWarehouseChangeList = warehouseChangListBOConverter
                    .InventoryDeliveryJiupiOrders2WarehouseInventoryChangeBOS(localOrders, checkWarehouseInventory);
                // 订单直发不需要发货数量记录
                Boolean isUpdateDeliveryCount = true;
                if (Objects.equals(localOrders.get(0).getPackageAttribute(), OrderPickTypeConstant.PICKTYPE_LOGIC)) {
                    isUpdateDeliveryCount = false;
                }
                warehouseInventoryManageBL.validateAndProcessProductStore(localWarehouseChangeList,
                    checkWarehouseInventory, isUpdateDeliveryCount, true, true, false);

                // 普通单据后续流程处理
                processLocalOrders(localOrders, localWarehouseChangeList);
            }

            // 更新订单状态为已出库
            updateOutStockOrderState(deliveryOrders, OutStockOrderStateEnum.已出库.getType());

        });
    }

    /**
     * 内配单据后续处理
     *
     * @param intercityOrders 单据数据
     * @param intercityWarehouseChangeList 库存实际处理数据
     */
    private void processIntercityOrders(List<InventoryDeliveryJiupiOrder> intercityOrders,
        List<WarehouseInventoryChangeBO> intercityWarehouseChangeList) {
        if (CollectionUtils.isEmpty(intercityOrders) || CollectionUtils.isEmpty(intercityWarehouseChangeList)) {
            return;
        }

        intercityOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getFromWarehouseId))
            .forEach((receiptWarehouseId, orders) -> {
                Integer receiptCity = orders.get(0).getFromCityId();

                // 内配单需要生成二级仓订单(排除调拨单)
                List<InventoryDeliveryJiupiOrder> outOrders = orders.stream()
                    .filter(order -> (order.getAllotType().equals(OrderConstant.ALLOT_TYPE_ALLOCATION)
                        || order.getAllotType().equals(OrderConstant.ALLOT_TYPE_DELIVERY))
                        && !Objects.equals(order.getOrderType().byteValue(), OrderConstant.ORDER_TYPE_ALLOT))
                    .collect(Collectors.toList());
                List<OutStockOrderDTO> transferOutStockOrderDTOS =
                    createTransferOrders(outOrders, receiptCity, receiptWarehouseId);

                // 处理普通内配前置仓出库单数据
                processOutStockOrderData(transferOutStockOrderDTOS, orders);

                if (CollectionUtils.isNotEmpty(transferOutStockOrderDTOS)) {
                    List<String> orderNos = transferOutStockOrderDTOS.stream().map(OutStockOrderDTO::getRefOrderNo)
                        .collect(Collectors.toList());
                    List<WarehouseInventoryChangeBO> warehouseChangeList = intercityWarehouseChangeList.stream()
                        .filter(changeBO -> orderNos.contains(changeBO.getOrderNo())).collect(Collectors.toList());
                    List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS = warehouseChangListBOConverter
                        .processChangeBOSToOutStockOrderItemDetailDTOS(warehouseChangeList);
                    outStockTransferOrderAsyncBL.asyncBatchAddTransferOrder(transferOutStockOrderDTOS,
                        outStockOrderItemDetailDTOS);
                }
            });
        // 内配入库操作
        List<InventoryDeliveryJiupiOrder> filterOrderResults = intercityOrders.stream()
            .filter(d -> d != null && StringUtils.isNotEmpty(d.getOrderNo())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterOrderResults)) {
            // 删除发货城市入库单
            filterOrderResults.stream().collect(Collectors.groupingBy(order -> order.getWarehouseId()))
                .forEach((warehouseId, orderList) -> {
                    // 发货城市
                    Integer deliveryCity = orderList.get(0).getCityId();
                    // 生成新的入库单
                    List<String> orderNos = orderList.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).distinct()
                        .collect(Collectors.toList());
                    // 删除发货城市入库单
                    if (CollectionUtils.isNotEmpty(orderNos)) {
                        iInStockOrderService.deleteInStockOrderByNOList(deliveryCity, warehouseId, orderNos);
                    }
                });
            // 生成收货城市入库单
            filterOrderResults.stream()
                .collect(Collectors
                    .groupingBy(order -> String.format("%s-%s", order.getWarehouseId(), order.getFromWarehouseId())))
                .forEach((key, orderList) -> {
                    InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder = orderList.get(0);
                    // 发货城市
                    Integer deliveryCity = inventoryDeliveryJiupiOrder.getCityId();
                    // 发货仓库
                    Integer deliveryWarehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();
                    // 收货城市ID
                    Integer receiptCity = inventoryDeliveryJiupiOrder.getFromCityId();
                    // 收货仓库ID
                    Integer receiptWarehouseId = inventoryDeliveryJiupiOrder.getFromWarehouseId();
                    // 创建收货城市入库单-中转配送不生成二级仓库入库单
                    InternalDistributionOrderDeliveryDTO deliveryOrder = new InternalDistributionOrderDeliveryDTO();
                    deliveryOrder.setDeliveryCity(deliveryCity);
                    deliveryOrder.setDeliveryWarehouseId(deliveryWarehouseId);
                    deliveryOrder.setReceiptCity(receiptCity);
                    deliveryOrder.setReceiptWarehouseId(receiptWarehouseId);
                    deliveryOrder.setDeliveryJiupiOrders(orderList);
                    inStockTransferOrderAsyncBL.batchAddInStockTransferOrder(deliveryOrder,
                        intercityWarehouseChangeList);
                });
        }
        // 出库通知 erp
        erpInOutStockBL.outStockNotifyErp(intercityOrders);
    }

    /**
     * 本地仓单据后续处理
     */
    private void processLocalOrders(List<InventoryDeliveryJiupiOrder> localOrders,
        List<WarehouseInventoryChangeBO> localWarehouseChangeList) {
        if (CollectionUtils.isEmpty(localOrders)) {
            return;
        }

        // 删除掉入库单.
        List<String> deliveryOrderNOs =
            localOrders.stream().filter(e -> e != null && StringUtils.isNotEmpty(e.getOrderNo()))
                .map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deliveryOrderNOs)) {
            iInStockOrderService.deleteInStockOrderByNOList(localOrders.get(0).getCityId(),
                localOrders.get(0).getWarehouseId(), deliveryOrderNOs);
        }

        // 更新波次状态为已出库
        List<String> orderNos = localWarehouseChangeList.stream().map(WarehouseInventoryChangeBO::getOrderNo)
            .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderNos)) {
            BatchUpdateDTO batchUpdateDTO = new BatchUpdateDTO();
            batchUpdateDTO.setOrgId(localWarehouseChangeList.get(0).getCityId());
            batchUpdateDTO.setWarehouseId(localWarehouseChangeList.get(0).getWarehouseId());
            batchUpdateDTO.setOrderNos(orderNos);
            batchUpdateDTO.setBatchState(BatchStateEnum.ALREADYOUTOFSTORE.getType());
            batchUpdateDTO.setOperateUser(localWarehouseChangeList.get(0).getCreateUserName());
            iBatchManageService.updateBatchStateByOrderNos(batchUpdateDTO);
        }
        erpInOutStockBL.frontOutStockNotifyErp(localOrders);
    }

    private void checkOrderNew(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }
        List<InventoryDeliveryJiupiOrder> orders = deliveryOrders;
        // 按照规则过滤不会下推的订单
        // List<InventoryDeliveryJiupiOrder> orders = deliveryOrders.stream()
        // .filter(order -> orderPushRoolService.canWmsPush(order.getOmsOrderId())).collect(Collectors.toList());
        //
        // if (CollectionUtils.isEmpty(orders)) {
        // return;
        // }

        List<String> orderNos =
            deliveryOrders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
        OutStockOrderQueryDTO outStockOrderQueryDTO = new OutStockOrderQueryDTO();
        outStockOrderQueryDTO.setRefOrderNos(orderNos);
        outStockOrderQueryDTO.setWarehouseId(deliveryOrders.get(0).getWarehouseId());
        outStockOrderQueryDTO.setOrgId(deliveryOrders.get(0).getCityId());
        List<OutStockOrderDTO> outStockOrderDTOS =
            outStockCommQueryService.pageListOutStockOrder(outStockOrderQueryDTO).getDataList();

        if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
            String errNos =
                orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.joining(","));
            throw new BusinessException("发货数据异常！OrderNo:" + errNos);
        }

        List<String> errOrderNos = new ArrayList<>();
        List<Long> noOmsOrderItemIds = new ArrayList<>();
        List<Long> noOrderItemIds = new ArrayList<>();
        List<Long> errOrderItemIds = new ArrayList<>();

        Map<String, List<OutStockOrderDTO>> outStockOrderMap =
            outStockOrderDTOS.stream().collect(Collectors.groupingBy(OutStockOrderDTO::getRefOrderNo));

        for (InventoryDeliveryJiupiOrder order : orders) {
            String orderNo = order.getOrderNo();
            List<OutStockOrderDTO> outStockOrders = outStockOrderMap.get(orderNo);

            if (CollectionUtils.isEmpty(outStockOrders)) {
                errOrderNos.add(orderNo);
                continue;
            }
            order.setOrderId(Long.valueOf(outStockOrders.get(0).getId()));
            order.setCrossWareHouse(outStockOrders.get(0).getCrossWareHouse());

            List<InventoryDeliveryJiupiOrderItem> orderItems = order.getItems().stream()
                .filter(item -> item.getDeliverCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Map<Long, List<InventoryDeliveryJiupiOrderItem>> orderItemMap =
                orderItems.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getOmsOrderItemId));
            List<OutStockOrderItemDTO> outStockOrderItems = outStockOrders.stream()
                .flatMap(outStockOrderDTO -> outStockOrderDTO.getOutStockOrderItemDTOS().stream())
                .filter(item -> item.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Map<String, OutStockOrderItemDTO> outStockOrderBusinessItemMap =
                outStockOrderItems.stream().filter(item -> item.getBusinessItemId() != null).collect(Collectors
                    .toMap(OutStockOrderItemDTO::getBusinessItemId, Function.identity(), (key1, key2) -> key2));
            Map<String, OutStockOrderItemDTO> outStockOrderItemMap =
                outStockOrderItems.stream().filter(item -> item.getBusinessItemId() != null)
                    .collect(Collectors.toMap(OutStockOrderItemDTO::getId, Function.identity(), (key1, key2) -> key2));

            for (Map.Entry<Long, List<InventoryDeliveryJiupiOrderItem>> entry : orderItemMap.entrySet()) {
                BigDecimal deliverCount = entry.getValue().stream()
                    .map(InventoryDeliveryJiupiOrderItem::getDeliverCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                OutStockOrderItemDTO outStockOrderItemDTO =
                    outStockOrderBusinessItemMap.get(entry.getKey().toString()) == null
                        ? outStockOrderItemMap.get(entry.getKey().toString())
                        : outStockOrderBusinessItemMap.get(entry.getKey().toString());
                if (outStockOrderItemDTO == null) {
                    noOmsOrderItemIds.add(entry.getKey());
                    continue;
                }
                BigDecimal unitTotalCount = outStockOrderItemDTO.getUnitTotalCount();

                if (!outStockOrderItemDTO.getProductSpecificationId()
                    .equals(entry.getValue().get(0).getProductSpecification_Id())
                    || !Objects.equals(outStockOrderItemDTO.getOwnerId(), entry.getValue().get(0).getOwnerId())
                    || unitTotalCount.compareTo(deliverCount) != 0) {
                    errOrderItemIds.add(entry.getKey());
                    continue;
                }
                entry.getValue().forEach(item -> item.setOrderItem_Id(Long.valueOf(outStockOrderItemDTO.getId())));
            }

            for (OutStockOrderItemDTO outStockOrderItem : outStockOrderItems) {
                Long itemId = outStockOrderItem.getBusinessItemId() == null ? Long.valueOf(outStockOrderItem.getId())
                    : Long.valueOf(outStockOrderItem.getBusinessItemId());
                List<InventoryDeliveryJiupiOrderItem> deliveryOrderItems = orderItemMap.get(itemId) != null
                    ? orderItemMap.get(itemId) : orderItemMap.get(Long.valueOf(outStockOrderItem.getId()));
                if (CollectionUtils.isEmpty(deliveryOrderItems)) {
                    noOrderItemIds.add(itemId);
                    continue;
                }
                BigDecimal deliverCount = deliveryOrderItems.stream()
                    .map(InventoryDeliveryJiupiOrderItem::getDeliverCount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal unitTotalCount = outStockOrderItem.getUnitTotalCount();
                if (!deliveryOrderItems.get(0).getProductSpecification_Id()
                    .equals(outStockOrderItem.getProductSpecificationId())
                    || !Objects.equals(deliveryOrderItems.get(0).getOwnerId(), outStockOrderItem.getOwnerId())
                    || deliverCount.compareTo(unitTotalCount) != 0) {
                    errOrderItemIds.add(itemId);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(errOrderNos)) {
            errOrderNos = errOrderNos.stream().distinct().collect(Collectors.toList());
            throw new BusinessException("订单在WMS中不存在！OrderNo:" + StringUtils.join(errOrderNos, ","));
        }

        if (CollectionUtils.isNotEmpty(noOmsOrderItemIds)) {
            String noOmsOrderItem =
                noOmsOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单项在WMS中不存在！OmsItemId:" + noOmsOrderItem);
        }

        if (CollectionUtils.isNotEmpty(noOrderItemIds)) {
            String noOrderItem =
                noOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单项在OMS中不存在！WmsItemId:" + noOrderItem);
        }

        if (CollectionUtils.isNotEmpty(errOrderItemIds)) {
            String errOrderItem =
                errOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("OMS与WMS订单项数量不一致！ItemId:" + errOrderItem);
        }
    }

    private void affirmInStockAddLogAudit(List<OrderDTO> orderList) {
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (OrderDTO orderDTO : orderList) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "instockorder");
                builder.businessId(String.valueOf(orderDTO.getId())).addFeature("OrderNo", orderDTO.getRefOrderNo());
                if (orderDTO.getBusinessType() != null) {
                    builder.addFeature("BusinessType", String.valueOf(orderDTO.getBusinessType()));
                }
                if (orderDTO.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(orderDTO.getOrderType()));
                }
                builder.content("订单确认入库操作并提交财务");
                builder.done();
            }
        }
    }

    public void financeCashInventoryChange(InStockCompleteDTO completeDTO) {
        InventoryOrderBizDTO waitingInStockOrder = iInStockQueryService.findWaitingInStockOrderByNOList(
            completeDTO.getOrgId(), completeDTO.getWarehouseId(), completeDTO.getRefOrderNoList());
        if (null == waitingInStockOrder) {
            throw new BusinessException("未查询到订单信息");
        }
        if (StringUtils.isNotEmpty(completeDTO.getLocationId())) {
            fixOrderLocation(completeDTO, waitingInStockOrder);
        }
        // 根据入库货位标记是否残次品标识
        orderMarkDefective(waitingInStockOrder.getOrderList());
        changeInventory(completeDTO.getOrgId(), completeDTO.getWarehouseId(), completeDTO.getRefOrderNoList(),
            waitingInStockOrder.getOrderList());

        // 添加日志审计
        if (CollectionUtils.isNotEmpty(waitingInStockOrder.getInStockOrderList())) {
            for (InStockOrderDTO inStockOrderDTO : waitingInStockOrder.getInStockOrderList()) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "instockorder");
                builder.businessId(String.valueOf(inStockOrderDTO.getId())).addFeature("OrderNo",
                    inStockOrderDTO.getRefOrderNo());
                if (inStockOrderDTO.getBusinessType() != null) {
                    builder.addFeature("BusinessType", String.valueOf(inStockOrderDTO.getBusinessType()));
                }
                if (inStockOrderDTO.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(inStockOrderDTO.getOrderType()));
                    InStockOrderTypeEnum inStockOrderTypeEnum =
                        InStockOrderTypeEnum.getEnum(inStockOrderDTO.getOrderType());
                    if (inStockOrderTypeEnum != null) {
                        builder.content(String.format("%s-财务确认收款", inStockOrderTypeEnum.name()));
                    } else {
                        builder.content("财务确认收款");
                    }
                } else {
                    builder.content("财务确认收款");
                }
                builder.done();
            }
        }
        iInStockOrderService.orderCompleteUpdateState(completeDTO.getOrgId(), completeDTO.getWarehouseId(),
            completeDTO.getRefOrderNoList());
    }

    /**
     * 标记订单中指定残次品入库标识
     *
     * @param orderList
     */
    private void orderMarkDefective(List<OrderDTO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        // 处理入库货位标记是否残次品
        List<OrderItemDTO> itemList = orderList.stream().filter(p -> CollectionUtils.isNotEmpty(p.getItems()))
            .map(OrderDTO::getItems).flatMap(list -> list.stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }

        List<OrderItemDetailDTO> itemDetailList = itemList.stream()
            .filter(p -> CollectionUtils.isNotEmpty(p.getItemDetailList())).map(OrderItemDTO::getItemDetailList)
            .flatMap(list -> list.stream()).filter(p -> p.getLocationId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemDetailList)) {
            return;
        }

        List<Long> locationIds = itemDetailList.stream().filter(p -> p.getLocationId() != null)
            .map(OrderItemDetailDTO::getLocationId).distinct().collect(Collectors.toList());
        List<LoactionDTO> loactionDTOS = iLocationService.findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(loactionDTOS)) {
            return;
        }

        List<Long> defLocationIds = loactionDTOS.stream()
            .filter(l -> LocationEnum.残次品位.getType().equals(l.getSubcategory().intValue())
                || LocationAreaEnum.残次品区.getType().equals(l.getSubcategory().intValue()))
            .map(LoactionDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defLocationIds)) {
            return;
        }

        itemDetailList.forEach(detail -> {
            if (defLocationIds.contains(detail.getLocationId())) {
                detail.setDefective(true);
            }
        });
    }

    /**
     * 根据类型校验发货库存数量（订单数据无需与oms进行校验，前期已与订单中台进行校验）
     */
    public List<DeliveryProductInventoryDTO> validateOrderDeliveryProductInventoryNew(
        List<InventoryDeliveryJiupiOrder> deliveryOrders, String orderDeliveryOpType) {

        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders, orderDeliveryOpType, "发货库存数量校验");
            });

        List<DeliveryProductInventoryDTO> deliveryProductInventoryDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            LOG.info(String.format("订单配送出库校验发货库存数量-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            return deliveryProductInventoryDTOS;
        }

        Integer warehouseId = deliveryOrders.get(0).getWarehouseId();
        boolean checkWarehouseInventory = true;
        if (checkWarehouseInventory) {
            // 检验是否店仓或者配置可以负库存发货仓库
            Boolean allowNegative = checkContentConfig(warehouseId, "AllowNegativeDelivery");
            if (allowNegative) {
                checkWarehouseInventory = false;
            }
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        boolean finalCheckWarehouseInventory = checkWarehouseInventory;

        for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
            // inventoryDeliveryJiupiOrder.setCityId(inventoryDeliveryJiupiOrder.getFromCityId());
            // inventoryDeliveryJiupiOrder.setWarehouseId(inventoryDeliveryJiupiOrder.getFromWarehouseId());
            warehouseChangListBOConverter.processOrderItemToOrderDeliveryBO(sellChangeList, warehouseChangeList,
                inventoryDeliveryJiupiOrder, finalCheckWarehouseInventory);
        }

        Map<Long, BigDecimal> errSkuIdMap = new HashMap<>(16);
        ArrayList<ProductInventoryPO> productInventoryPOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            warehouseInventoryManageBL.validateOrderDeliveryProductStore(warehouseChangeList, errSkuIdMap,
                productInventoryPOS, finalCheckWarehouseInventory, false);
        }

        if (errSkuIdMap.size() > 0) {
            List<Long> errSkuIds = new ArrayList<>(errSkuIdMap.keySet());
            List<ProductSkuPO> productSkuPOList = productSkuQueryBL.getProductSkuListByIds(errSkuIds);
            Map<Long, ProductSkuPO> skuMap = productSkuPOList.stream()
                .collect(Collectors.toMap(ProductSkuPO::getProductSkuId, Function.identity(), (key1, key2) -> key2));
            deliveryOrders.forEach(order -> {
                order.getItems().forEach(item -> {
                    item.setOrderNo(order.getOrderNo());
                });
            });
            deliveryOrders.stream().flatMap(order -> order.getItems().stream())
                .filter(item -> errSkuIds.contains(item.getProductSkuId()))
                .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getProductSkuId))
                .forEach((skuId, items) -> {
                    // LOG.info("校验发货库存items:{}", JSON.toJSONString(items));
                    DeliveryProductInventoryDTO deliveryProductInventoryDTO = new DeliveryProductInventoryDTO();
                    deliveryProductInventoryDTO.setProductSkuId(skuId);
                    ProductSkuPO productSkuPO = skuMap.get(skuId);
                    if (productSkuPO != null) {
                        deliveryProductInventoryDTO.setOwnerId(productSkuPO.getCompanyId());
                        deliveryProductInventoryDTO.setSecOwnerId(productSkuPO.getSecOwnerId());
                        deliveryProductInventoryDTO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
                        deliveryProductInventoryDTO.setSpecQuantity(productSkuPO.getPackageQuantity());
                        deliveryProductInventoryDTO.setSpecName(productSkuPO.getSpecificationName());
                        deliveryProductInventoryDTO.setProductName(productSkuPO.getName());
                        deliveryProductInventoryDTO.setPackageName(productSkuPO.getPackageName());
                        deliveryProductInventoryDTO.setUnitName(productSkuPO.getUnitName());
                    }
                    deliveryProductInventoryDTO.setSaleSpecQuantity(items.get(0).getSaleSpecQuantity());
                    BigDecimal lakeCount = errSkuIdMap.get(skuId);
                    deliveryProductInventoryDTO.setLakeCount(lakeCount);
                    BigDecimal deliverCount = items.stream().map(InventoryDeliveryJiupiOrderItem::getDeliverCount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    deliveryProductInventoryDTO.setDeliverCount(deliverCount);
                    deliveryProductInventoryDTO.setWarehouseInventory(
                        deliverCount.multiply(deliveryProductInventoryDTO.getSaleSpecQuantity()).add(lakeCount));
                    List<String> orderNos = items.stream().map(InventoryDeliveryJiupiOrderItem::getOrderNo).distinct()
                        .collect(Collectors.toList());
                    deliveryProductInventoryDTO.setOrderNos(orderNos);
                    deliveryProductInventoryDTO.setOrderCount(orderNos.size());

                    deliveryProductInventoryDTOS.add(deliveryProductInventoryDTO);
                });
        }

        LOG.info("校验发货库存数量:{}", JSON.toJSONString(deliveryProductInventoryDTOS));
        return deliveryProductInventoryDTOS;
    }

    /**
     * 回填生产日期
     */
    private void processProductDate(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        LOG.info("部分配送配送失败保存生产日期，入参：{}", JSON.toJSONString(deliveryOrders));
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }

        List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOS = new ArrayList<>();

        // deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId)).forEach((warehouseId,
        // orders) -> {
        // //获取单号
        // List<String> orderNos = orders.stream().filter(o -> o != null && !StringUtils.isBlank(o.getOrderNo())).map(o
        // -> o.getOrderNo()).distinct().collect(Collectors.toList());
        // ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO = new
        // ProductStoreBatchChangeRecordQueryDTO();
        // productStoreBatchChangeRecordQueryDTO.setCityId(orders.get(0).getCityId());
        // productStoreBatchChangeRecordQueryDTO.setWarehouseId(orders.get(0).getWarehouseId());
        // productStoreBatchChangeRecordQueryDTO.setOrderNos(orderNos);
        // LOG.info("部分配送配送失败保存生产日期，查询批次库存变更记录-入参：{}", JSON.toJSONString(productStoreBatchChangeRecordQueryDTO));
        // productStoreBatchChangeRecordDTOS.addAll(iBatchInventoryQueryService.selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO));
        // LOG.info("部分配送配送失败保存生产日期，查询批次库存变更记录-结果：{}", JSON.toJSONString(productStoreBatchChangeRecordDTOS));
        // });
        //
        // if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOS)) {
        // // 按规格id + 一级货主id + 二级货主id 进行分组
        // Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdMap =
        // productStoreBatchChangeRecordDTOS.stream().filter(p -> null != p && null != p.getProductionDate())
        // .collect(Collectors.groupingBy(it -> it.getProductSpecificationId() + "_" + it.getOwnerId() + "_" +
        // it.getSecOwnerId()));
        // if (ownerIdAndSecOwnerIdMap != null && ownerIdAndSecOwnerIdMap.size() > 0) {
        // List<InventoryDeliveryJiupiOrderItem> itemList = deliveryOrders.stream().filter(d -> d != null &&
        // CollectionUtils.isNotEmpty(d.getItems()))
        // .flatMap(d -> d.getItems().stream()).collect(Collectors.toList());
        // itemList.stream().filter(d -> d != null).forEach(d -> {
        // List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOList =
        // ownerIdAndSecOwnerIdMap.get(d.getProductSpecification_Id() + "_" + d.getOwnerId() + "_" + d.getSecOwnerId());
        // LOG.info("部分配送配送失败保存生产日期，获取map：{}", JSON.toJSONString(productStoreBatchChangeRecordDTOList));
        // if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOList)) {
        // d.setProductionDate(productStoreBatchChangeRecordDTOList.get(0).getProductionDate());
        // }
        // });
        // }
        // }

        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                // 获取单号
                List<String> orderNos = orders.stream().filter(o -> o != null && !StringUtils.isBlank(o.getOrderNo()))
                    .map(o -> o.getOrderNo()).distinct().collect(Collectors.toList());
                // 含前缀订单去掉前缀
                List<String> replaceOrderNos = orders.stream()
                    .filter(
                        o -> o != null && !StringUtils.isBlank(o.getOrderNo()) && !o.getOrderNo().matches("^[0-9]+$"))
                    .map(o -> o.getOrderNo().replaceAll("[a-zA-Z]", "")).distinct().collect(Collectors.toList());
                orderNos.addAll(replaceOrderNos);
                ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO =
                    new ProductStoreBatchChangeRecordQueryDTO();
                productStoreBatchChangeRecordQueryDTO.setCityId(orders.get(0).getCityId());
                productStoreBatchChangeRecordQueryDTO.setWarehouseId(orders.get(0).getWarehouseId());
                productStoreBatchChangeRecordQueryDTO
                    .setOrderNos(orderNos.stream().distinct().collect(Collectors.toList()));
                LOG.info("部分配送配送失败保存生产日期，查询批次库存变更记录-入参：{}", JSON.toJSONString(productStoreBatchChangeRecordQueryDTO));
                productStoreBatchChangeRecordDTOS.addAll(iBatchInventoryQueryService
                    .selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO));
                LOG.info("部分配送配送失败保存生产日期，查询批次库存变更记录-结果：{}", JSON.toJSONString(productStoreBatchChangeRecordDTOS));
            });

        if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOS)) {
            // 按规格id + 一级货主id + 二级货主id +订单号 进行分组
            Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdOrderNoMap =
                productStoreBatchChangeRecordDTOS.stream().filter(p -> null != p && null != p.getProductionDate())
                    .collect(Collectors.groupingBy(it -> it.getProductSpecificationId() + "_" + it.getOwnerId() + "_"
                        + it.getSecOwnerId() + "_" + it.getOrderNo()));
            if (ownerIdAndSecOwnerIdOrderNoMap != null && ownerIdAndSecOwnerIdOrderNoMap.size() > 0) {
                deliveryOrders.stream().forEach(order -> {
                    List<InventoryDeliveryJiupiOrderItem> itemList = order.getItems();
                    itemList.stream().filter(d -> d != null).forEach(d -> {
                        List<ProductStoreBatchChangeRecordDTO> orderStoreChangeRecordDTOList =
                            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecification_Id() + "_" + d.getOwnerId()
                                + "_" + d.getSecOwnerId() + "_" + order.getOrderNo());
                        List<ProductStoreBatchChangeRecordDTO> replaceOrderStorechangeRecordDTOList =
                            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecification_Id() + "_" + d.getOwnerId()
                                + "_" + d.getSecOwnerId() + "_" + order.getOrderNo().replaceAll("[a-zA-Z]", ""));
                        LOG.info(
                            "部分配送配送失败保存生产日期，获取orderStoreChangeRecordDTOList：{},获取replaceOrderStorechangeRecordDTOList：{}",
                            JSON.toJSONString(orderStoreChangeRecordDTOList),
                            JSON.toJSONString(replaceOrderStorechangeRecordDTOList));
                        if (CollectionUtils.isNotEmpty(orderStoreChangeRecordDTOList)) {
                            d.setProductionDate(orderStoreChangeRecordDTOList.get(0).getProductionDate());
                        } else if (CollectionUtils.isEmpty(orderStoreChangeRecordDTOList)
                            && CollectionUtils.isNotEmpty(replaceOrderStorechangeRecordDTOList)
                            && !order.getOrderNo().matches("^[0-9]+$")) {
                            d.setProductionDate(replaceOrderStorechangeRecordDTOList.get(0).getProductionDate());
                        }
                    });
                });
            }
        }
    }

    /**
     * 经销商订单入库直接处理库存
     */
    public void directCompleteInStockOrderByDealer(List<OrderDTO> orders, String messageType) {
        LOG.info("directCompleteInStockOrderByDealer 经销商订单入库直接处理库存:{}", JSON.toJSONString(orders));
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            OrderDTOConvert.orderDTOS2InventoryDeliveryJiupiOrderS(orders);

        List<String> noProcessOrderNos = new ArrayList<>();
        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orderList) -> {
                List<String> noProcess =
                    idempotenceConsumer.getNoProcessOrderNos(warehouseId, orderList, messageType, "订单直接入库");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });

        if (CollectionUtils.isEmpty(noProcessOrderNos)) {
            LOG.info(String.format("directCompleteInStockOrderByDealer 订单直接入库-订单已经全部处理过！%s",
                JSON.toJSONString(deliveryOrders)));
            return;
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            // 遍历添加批次所有BO到list中
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                warehouseChangListBOConverter.directProcessingOrderToOrderDeliveryBO(sellChangeList,
                    warehouseChangeList, inventoryDeliveryJiupiOrder, false);
            }

            if (CollectionUtils.isEmpty(warehouseChangeList)) {
                LOG.info(String.format("directCompleteInStockOrderByDealer 订单直接入库-订单已经全部处理过！%s",
                    JSON.toJSONString(deliveryOrders)));
            }

            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, false, true, true,
                true);
        });

        // 处理销售库存发消息
        warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);

        // 修改订单状态
        List<Long> orderIds =
            deliveryOrders.stream().map(InventoryDeliveryJiupiOrder::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderIds)) {
            // 直接入库修改状态及入库时间
            iInStockOrderService.completePushOrderUpdateState(orderIds);
        }

        //// if(orders.get(0) != null &&
        //// !Objects.equals(orders.get(0).getCapabilityType(),pushCapabilityTypeEnum.直接入库.getType())) {
        // if(orders.get(0) != null && null == orders.get(0).getCapabilityType()) {
        // //原流程发送oms订单完成
        // omsOrderComplete(warehouseChangeList);
        // }

    }

    /**
     * 根据原单回填生产日期
     *
     * @see InStockHandleProductDateBL#processOrderProductDate
     */
    @Deprecated
    private void processOrderProductDate(List<OrderDTO> orderList) {
        LOG.info("查询原单生产日期并赋值，入参：{}", JSON.toJSONString(orderList));
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOS = new ArrayList<>();

        orderList.stream().collect(Collectors.groupingBy(OrderDTO::getWarehouseId)).forEach((warehouseId, orders) -> {
            // 获取单号
            List<String> orderNos = orders.stream().filter(o -> o != null && !StringUtils.isBlank(o.getRefOrderNo()))
                .map(o -> o.getRefOrderNo()).distinct().collect(Collectors.toList());
            // 含前缀订单去掉前缀
            List<String> replaceOrderNos = orders.stream()
                .filter(
                    o -> o != null && !StringUtils.isBlank(o.getRefOrderNo()) && !o.getRefOrderNo().matches("^[0-9]+$"))
                .map(o -> o.getRefOrderNo().replaceAll("[a-zA-Z]", "")).distinct().collect(Collectors.toList());
            orderNos.addAll(replaceOrderNos);
            ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO =
                new ProductStoreBatchChangeRecordQueryDTO();
            productStoreBatchChangeRecordQueryDTO.setCityId(orders.get(0).getOrgId());
            productStoreBatchChangeRecordQueryDTO.setWarehouseId(warehouseId);
            productStoreBatchChangeRecordQueryDTO
                .setOrderNos(orderNos.stream().distinct().collect(Collectors.toList()));
            LOG.info("查询原单生产日期并赋值，查询批次库存变更记录-入参：{}", JSON.toJSONString(productStoreBatchChangeRecordQueryDTO));
            productStoreBatchChangeRecordDTOS.addAll(iBatchInventoryQueryService
                .selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO));
            LOG.info("查询原单生产日期并赋值，查询批次库存变更记录-结果：{}", JSON.toJSONString(productStoreBatchChangeRecordDTOS));
        });

        if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOS)) {
            // 按规格id + 一级货主id + 二级货主id +订单号 进行分组
            Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdOrderNoMap =
                productStoreBatchChangeRecordDTOS.stream().filter(p -> null != p && null != p.getProductionDate())
                    .collect(Collectors.groupingBy(it -> it.getProductSpecificationId() + "_" + it.getOwnerId() + "_"
                        + it.getSecOwnerId() + "_" + it.getOrderNo()));
            if (ownerIdAndSecOwnerIdOrderNoMap != null && ownerIdAndSecOwnerIdOrderNoMap.size() > 0) {
                orderList.stream().forEach(order -> {
                    List<OrderItemDTO> itemList = order.getItems();
                    itemList.stream().filter(d -> d != null).forEach(d -> {
                        List<ProductStoreBatchChangeRecordDTO> orderStoreChangeRecordDTOList =
                            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecificationId() + "_" + d.getOwnerId()
                                + "_" + d.getSecOwnerId() + "_" + order.getRefOrderNo());
                        List<ProductStoreBatchChangeRecordDTO> replaceOrderStorechangeRecordDTOList =
                            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecificationId() + "_" + d.getOwnerId()
                                + "_" + d.getSecOwnerId() + "_" + order.getRefOrderNo().replaceAll("[a-zA-Z]", ""));
                        LOG.info(
                            "查询原单生产日期并赋值，获取orderStoreChangeRecordDTOList：{},获取replaceOrderStorechangeRecordDTOList：{}",
                            JSON.toJSONString(orderStoreChangeRecordDTOList),
                            JSON.toJSONString(replaceOrderStorechangeRecordDTOList));
                        if (CollectionUtils.isNotEmpty(orderStoreChangeRecordDTOList)) {
                            d.setProductionDate(orderStoreChangeRecordDTOList.get(0).getProductionDate());
                        } else if (CollectionUtils.isEmpty(orderStoreChangeRecordDTOList)
                            && CollectionUtils.isNotEmpty(replaceOrderStorechangeRecordDTOList)
                            && !order.getRefOrderNo().matches("^[0-9]+$")) {
                            d.setProductionDate(replaceOrderStorechangeRecordDTOList.get(0).getProductionDate());
                        }
                    });
                });
            }
        }
    }

    /**
     * 处理普通内配前置仓出库单数据
     */
    private void processOutStockOrderData(List<OutStockOrderDTO> outStockOrderDTOS,
        List<InventoryDeliveryJiupiOrder> intercityOrders) {
        LOG.info("InventoryOrderBizBL.processOutStockOrderData-入参outStockOrderDTOS：{}, intercityOrders：{}",
            JSON.toJSONString(outStockOrderDTOS), JSON.toJSONString(intercityOrders));
        if (CollectionUtils.isEmpty(outStockOrderDTOS) || Objects.isNull(intercityOrders)) {
            return;
        }

        List<String> internalOutOrderNos = intercityOrders.stream()
            .filter(order -> order != null && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(order.getAllotType()))
            .map(e -> e.getOrderNo()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalOutOrderNos)) {
            return;
        }

        List<OutStockOrderDTO> newOutStockOrderDTOS = outStockOrderDTOS.stream()
            .filter(p -> internalOutOrderNos.contains(p.getRefOrderNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newOutStockOrderDTOS)) {
            return;
        }

        LOG.info("InventoryOrderBizBL.processOutStockOrderData-仓库灰度：， 待处理数据newOutStockOrderDTOS：{}",
            JSON.toJSONString(newOutStockOrderDTOS));
        // if (isOpenOrderCenter) {
        // newOutStockOrderDTOS.forEach(order -> {
        // order.setOutBoundType(OutBoundTypeEnum.SALE_ORDER.getCode());
        // });
        // }

        LOG.info("InventoryOrderBizBL.processOutStockOrderData-数据处理结果newOutStockOrderDTOS：{}",
            JSON.toJSONString(newOutStockOrderDTOS));
    }

    /**
     * 普通内配前置仓入库单将部分数据替换成wms数据
     */
    private void internalInStockInventoryOrderToWMSData(List<InventoryOrderDTO> inventoryOrderDTOS, Integer orgId,
        Integer warehouseId) {
        LOG.info("InventoryOrderBizBL.internalInStockInventoryOrderToWMSData-普通内配前置仓入库单数据处理，入参inventoryOrderDTOS：{}",
            JSON.toJSONString(inventoryOrderDTOS));
        if (CollectionUtils.isEmpty(inventoryOrderDTOS)) {
            return;
        }

        List<InventoryOrderDTO> orders = inventoryOrderDTOS;
        // List<InventoryOrderDTO> orders = inventoryOrderDTOS.stream()
        // .filter(order -> orderPushRoolService.canWmsPush(order.getOmsOrderId())).collect(Collectors.toList());
        // if (CollectionUtils.isEmpty(orders)) {
        // return;
        // }

        orders.forEach(order -> {
            order.setRelationOrderNo(order.getRefOrderNo());
            order.setRefOrderNo(AllotOrderTypeConstans.NPQZ_NOTENO + order.getRefOrderNo());
        });

        List<String> refOrderNos = orders.stream().map(InventoryOrderDTO::getRefOrderNo).collect(Collectors.toList());
        List<InStockOrderDTO> inStockOrderDTOS =
            iInStockQueryService.findInStockOrderByOrderNoList(orgId, warehouseId, refOrderNos);
        LOG.info("InventoryOrderBizBL.internalInStockInventoryOrderToWMSData-普通内配前置仓入库单数据处理，入参inventoryOrderDTOS：{}",
            JSON.toJSONString(inventoryOrderDTOS));
        if (CollectionUtils.isEmpty(inStockOrderDTOS)) {
            // throw new BusinessException("前置仓内配入库单据在WMS系统中不存在！");
            LOG.info(
                "InventoryOrderBizBL.internalInStockInventoryOrderToWMSData-前置仓NPQZ内配入库单据在WMS系统中不存在，refOrderNos：{}",
                JSON.toJSONString(refOrderNos));
            // 还原数据
            orders.forEach(order -> {
                order.setRefOrderNo(order.getRelationOrderNo());
                order.setRelationOrderNo("");
            });
            // 兼容历史数据走老逻辑
            inventoryOrderToWMSData(inventoryOrderDTOS, orgId, warehouseId);
            return;
        }

        // 按查询内配入单据的订单id及生产日期赋值
        Map<String, InventoryOrderDTO> inventoryOrderMap = orders.stream()
            .collect(Collectors.toMap(InventoryOrderDTO::getRefOrderNo, Function.identity(), (key1, key2) -> key2));
        inStockOrderDTOS.stream()
            .collect(Collectors.toMap(InStockOrderDTO::getRefOrderNo, Function.identity(), (key1, key2) -> key2))
            .forEach((orderNo, inStockOrder) -> {
                InventoryOrderDTO inventoryOrderDTO = inventoryOrderMap.get(orderNo);
                if (inventoryOrderDTO != null) {
                    inventoryOrderDTO.setId(Long.valueOf(inStockOrder.getId()));

                    inventoryOrderDTO.getItems().forEach(item -> {
                        String itemId = item.getOmsOrderItemId().toString();
                        for (InStockOrderItemDTO inStockOrderItem : inStockOrder.getInStockOrderItemDTOList()) {
                            if (inStockOrderItem.getRelatedItemId().equals(itemId)
                                || inStockOrderItem.getBusinessItemId().equals(itemId)) {
                                item.setId(Long.valueOf(inStockOrderItem.getId()));
                                if (CollectionUtils.isNotEmpty(inStockOrderItem.getItemDetailDTOList())) {
                                    List<Date> productionDateList = inStockOrderItem.getItemDetailDTOList().stream()
                                        .filter(p -> null != p && null != p.getProductionDate())
                                        .map(InStockOrderItemDetailDTO::getProductionDate).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(productionDateList)) {
                                        item.setProductionDate(productionDateList.get(0));
                                    }
                                }
                            }
                        }
                    });
                } else {
                    throw new BusinessException(orderNo + ",订单在WMS系统不存在");
                }
            });

        LOG.info("InventoryOrderBizBL.internalInStockInventoryOrderToWMSData-普通内配前置仓入库单数据处理，结果inventoryOrderDTOS：{}",
            JSON.toJSONString(inventoryOrderDTOS));

        List<String> errOrderNos = new ArrayList<>();
        for (InventoryOrderDTO order : orders) {
            for (InventoryOrderItemDTO item : order.getItems()) {
                if (item.getId() == null) {
                    errOrderNos.add(order.getRefOrderNo());
                    break;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errOrderNos)) {
            throw new BusinessException("内配订单项与WMS系统不同:" + StringUtils.join(errOrderNos, ","));
        }
    }

    /**
     * 前置仓入库不发送销售库存消息
     *
     * @param warehouseChangeList
     */
    public void setNotSendSaleInventoryByChangeBO(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        LOG.info(String.format("setNotSendSaleInventoryByChangeBO 入参warehouseChangeList：%s",
            JSON.toJSONString(warehouseChangeList)));
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return;
        }

        List<String> notSendOrderNoList = warehouseChangeList.stream()
            .filter(p -> p.getHasUpdateOPInventory() && StringUtil.isNotEmpty(p.getOrderNo())
                && p.getOrderNo().startsWith(AllotOrderTypeConstans.NPQZ_NOTENO))
            .map(p -> p.getOrderNo()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notSendOrderNoList)) {
            return;
        }

        LOG.info(String.format("配送单已生成内配单，不需要处理销售库存！OrderNo：%s", JSON.toJSONString(notSendOrderNoList)));
        warehouseChangeList.forEach(p -> {
            if (StringUtil.isNotEmpty(p.getOrderNo()) && notSendOrderNoList.contains(p.getOrderNo())) {
                p.setHasUpdateOPInventory(false);
            }
        });
    }

}

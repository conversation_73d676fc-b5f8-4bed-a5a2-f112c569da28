package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class ERPCityMergeDTO implements Serializable {
    private static final long serialVersionUID = -4906853189773166603L;
    private Integer cityId;
    private Integer warehouseId;
    private Long productSpecificationId;
    private Long productOwnerId;
    private Long productSecOwnerId;
    private BigDecimal count;
    private String businessNo;

    public ERPCityMergeDTO() {}

    public Integer getCityId() {
        return this.cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getCount() {
        return this.count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public Long getProductSpecificationId() {
        return this.productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Long getProductOwnerId() {
        return this.productOwnerId;
    }

    public void setProductOwnerId(Long productOwnerId) {
        this.productOwnerId = productOwnerId;
    }

    public String getBusinessNo() {
        return this.businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public Long getProductSecOwnerId() {
        return this.productSecOwnerId;
    }

    public void setProductSecOwnerId(Long productSecOwnerId) {
        this.productSecOwnerId = productSecOwnerId;
    }
}

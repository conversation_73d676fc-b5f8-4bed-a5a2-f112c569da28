package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStoreBatchPO;

/**
 * <AUTHOR> 2018/2/1
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryBatchInventoryProductStoreBatchMapperTest {

    @Autowired
    private InventoryProductStoreBatchMapper mapper;

    // 根据storeId查询批次库存信息 2/1通过
    @Test
    public void findProductStoreBatch() {
        List<ProductStoreBatchPO> productStoreBatch = mapper.findProductStoreBatch("25e7bdee14bd40baa773f3a850c7bb1c");
    }

}

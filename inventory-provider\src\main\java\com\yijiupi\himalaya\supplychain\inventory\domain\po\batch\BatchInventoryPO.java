package com.yijiupi.himalaya.supplychain.inventory.domain.po.batch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 批次库存po
 *
 * <AUTHOR> 2018/3/28
 */
public class BatchInventoryPO implements Serializable {
    /**
     * 批次库存表主键
     */
    private String storeBatchId;
    /**
     * 产品库存ID
     */
    private String productStoreId;
    /**
     * 商品sku
     */
    private Long productSkuId;
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 仓库Id
     */
    private Integer warehouseId;
    /**
     * 所属人ID
     */
    private Long ownerId;
    /**
     * 所属人类型
     */
    private Integer ownerType;
    /**
     * 所属人名称
     */
    private String ownerName;
    /**
     * 库存总数量
     */
    private BigDecimal storeTotalCount;
    /**
     * 库存大数量
     */
    private BigDecimal storeCountMax;
    /**
     * 库存小数量
     */
    private BigDecimal storeCountMin;
    /**
     * 库存渠道,0:酒批，1:大宗产品
     */
    private Integer channel;
    /**
     * 包装规格名称
     */
    private String specificationName;
    /**
     * 包装规格大单位
     */
    private String packageName;
    /**
     * 包装规格小单位
     */
    private String unitName;
    /**
     * 包装规格大小单位转换系数
     */
    private BigDecimal packageQuantity;
    /**
     * 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    private Integer source;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 货位id
     */
    private Long locationId;
    /**
     * 货位名称
     */
    private String locationName;
    /**
     * 批次时间
     */
    private Date batchTime;
    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 获取 产品库存ID
     */
    public String getProductStoreId() {
        return this.productStoreId;
    }

    /**
     * 设置 产品库存ID
     */
    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    /**
     * 获取 商品sku
     */
    public Long getProductSkuId() {
        return this.productSkuId;
    }

    /**
     * 设置 商品sku
     */
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取 商品名称
     */
    public String getProductSkuName() {
        return this.productSkuName;
    }

    /**
     * 设置 商品名称
     */
    public void setProductSkuName(String productSkuName) {
        this.productSkuName = productSkuName;
    }

    /**
     * 获取 仓库Id
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库Id
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 所属人ID
     */
    public Long getOwnerId() {
        return this.ownerId;
    }

    /**
     * 设置 所属人ID
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * 获取 所属人类型
     */
    public Integer getOwnerType() {
        return this.ownerType;
    }

    /**
     * 设置 所属人类型
     */
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    /**
     * 获取 所属人名称
     */
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * 设置 所属人名称
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * 获取 库存总数量
     */
    public BigDecimal getStoreTotalCount() {
        return this.storeTotalCount;
    }

    /**
     * 设置 库存总数量
     */
    public void setStoreTotalCount(BigDecimal storeTotalCount) {
        this.storeTotalCount = storeTotalCount;
    }

    /**
     * 获取 库存大数量
     */
    public BigDecimal getStoreCountMax() {
        return this.storeCountMax;
    }

    /**
     * 设置 库存大数量
     */
    public void setStoreCountMax(BigDecimal storeCountMax) {
        this.storeCountMax = storeCountMax;
    }

    /**
     * 获取 库存小数量
     */
    public BigDecimal getStoreCountMin() {
        return this.storeCountMin;
    }

    /**
     * 设置 库存小数量
     */
    public void setStoreCountMin(BigDecimal storeCountMin) {
        this.storeCountMin = storeCountMin;
    }

    /**
     * 获取 库存渠道,0:酒批，1:大宗产品
     */
    public Integer getChannel() {
        return this.channel;
    }

    /**
     * 设置 库存渠道,0:酒批，1:大宗产品
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * 获取 包装规格名称
     */
    public String getSpecificationName() {
        return this.specificationName;
    }

    /**
     * 设置 包装规格名称
     */
    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    /**
     * 获取 包装规格大单位
     */
    public String getPackageName() {
        return this.packageName;
    }

    /**
     * 设置 包装规格大单位
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * 获取 包装规格小单位
     */
    public String getUnitName() {
        return this.unitName;
    }

    /**
     * 设置 包装规格小单位
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * 获取 包装规格大小单位转换系数
     */
    public BigDecimal getPackageQuantity() {
        return this.packageQuantity;
    }

    /**
     * 设置 包装规格大小单位转换系数
     */
    public void setPackageQuantity(BigDecimal packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    /**
     * 获取 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public Integer getSource() {
        return this.source;
    }

    /**
     * 设置 产品来源，0:酒批，1:微酒（贷款/抵押）
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取 二级货主Id
     */
    public Long getSecOwnerId() {
        return this.secOwnerId;
    }

    /**
     * 设置 二级货主Id
     */
    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    /**
     * 获取 货位id
     */
    public Long getLocationId() {
        return this.locationId;
    }

    /**
     * 设置 货位id
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     * 获取 货位名称
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * 设置 货位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * 获取 批次时间
     */
    public Date getBatchTime() {
        return this.batchTime;
    }

    /**
     * 设置 批次时间
     */
    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    /**
     * 获取 过期时间
     */
    public Date getExpireTime() {
        return this.expireTime;
    }

    /**
     * 设置 过期时间
     */
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 获取 批次库存表主键
     */
    public String getStoreBatchId() {
        return this.storeBatchId;
    }

    /**
     * 设置 批次库存表主键
     */
    public void setStoreBatchId(String storeBatchId) {
        this.storeBatchId = storeBatchId;
    }
}

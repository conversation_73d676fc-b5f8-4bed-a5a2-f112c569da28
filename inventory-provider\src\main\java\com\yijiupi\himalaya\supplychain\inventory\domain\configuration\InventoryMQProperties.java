package com.yijiupi.himalaya.supplychain.inventory.domain.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 消息队列名常量类 Created by wa<PERSON><PERSON> on 2017-10-27
 */
@Component
public class InventoryMQProperties {

    // /**
    // * 异步扣除DB销售库存消息
    // */
    // public static final String DEDUCT_SELL_ASYNC_QUEUE = "mq.trading.inventory.DeductSellAsync";
    // /**
    // * 库存扣除检查消息
    // */
    // public static final String DEDUCT_SELL_CHECK = "mq.trading.inventory.DeductSellCheck";
    // /**
    // * 配送失败消息
    // */
    // public static final String ORDER_DELIVERY_FAIL_QUEUE = "mq.trading.inventory.OrderDeliveryFail";
    // /**
    // * 配送完成确认消息
    // */
    // public static final String ORDER_DELIVERY_COMPLETE_QUEUE = "mq.trading.inventory.OrderDeliveryComplete";
    // /**
    // * 退货单配送完成确认消息
    // */
    // public static final String RETURNORDER_DELIVERY_COMPLETE_QUEUE =
    // "mq.trading.inventory.ReturnOrderDeliveryComplete";
    // /**
    // * 订单取消扣库存消息
    // */
    // public static final String ORDER_CANCEL_QUEUE = "mq.trading.inventory.OrderCancel";
    /**
     * ERP同步库存消息
     */
    public static String SYNC_ERPINVENTORY_QUEUE;
    // /**
    // * 城市库存变化Routingkey
    // */
    // public static final String CITY_INVENTORY_CHANGE_ROUTINGKEY = "CityInventoryChange";
    /**
     * 销售库存变化Routingkey
     */
    public static String SELL_INVENTORY_CHANGE_EXCHANGE;
    /**
     * 销售库存变化Routingkey
     */
    public static String WMS_SELL_INVENTORY_CHANGE_EXCHANGE;
    /**
     * 仓库库存变化Routingkey（外部）
     */
    public static String WAREHOUSE_INVENTORY_CHANGE_EXCHANGE;
    /**
     * 仓库库存变更事件Routingkey-ERP库存处理逻辑
     */
    public static String STORE_INVENTORY_CHANGE_EXCHANGE;

    /**
     * 盘盈盘亏单直接通过监听队列
     */
    public static String STORECHECK_RESULT_DIRECT_PASS_QUEUE;

    /**
     * 入库单库存分配生成转移单 exchange
     */
    public static String ADD_INSTOCKORDER_RATIOCONVERT_EXCHANGE;

    /**
     * 转移单新增同步EXCHANGE
     */
    public static String CONVERTORDER_ADD_EXCHANGE;

    /**
     * 订单明细变更EXCHANGE
     */
    public static String OMS_STOCKORDER_INVENTORY_CHANGE_EXCHANGE;

    /**
     * 库存差异同步EXCHANGE
     */
    public static String INVENTORY_SYNC_CHANGE_EXCHANGE;

    /**
     * ERP 出入库通知 EXCHANGE
     */
    public static String ERP_IN_OUT_EXCHANGE;

    @Value("${mq.supplychain.inventory.syncInventory}")
    public void setErpQuenue(String string) {
        InventoryMQProperties.SYNC_ERPINVENTORY_QUEUE = string;
    }

    @Value("${ex.supplychain.inventory.SellInventoryChange}")
    public void setSellInventoryChangeExchange(String string) {
        InventoryMQProperties.SELL_INVENTORY_CHANGE_EXCHANGE = string;
    }

    @Value("${ex.supplychain.inventory.WMSSellInventoryChange}")
    public void setWmsSellInventoryChangeExchange(String string) {
        InventoryMQProperties.WMS_SELL_INVENTORY_CHANGE_EXCHANGE = string;
    }

    @Value("${ex.supplychain.inventory.StoreInventoryChange}")
    public void setWarehouseInventoryChangeExchange(String string) {
        InventoryMQProperties.WAREHOUSE_INVENTORY_CHANGE_EXCHANGE = string;
    }

    @Value("${ex.supplychain.inventory.productstorechange}")
    public void setStoreInventoryChangeExchange(String string) {
        InventoryMQProperties.STORE_INVENTORY_CHANGE_EXCHANGE = string;
    }

    @Value("${mq.supplychain.storecheckResult.directPass}")
    public void setStorecheckResultDirectPassQueue(String queue) {
        InventoryMQProperties.STORECHECK_RESULT_DIRECT_PASS_QUEUE = queue;
    }

    @Value("${ex.supplychain.inStockOrderRatioConvert.convertOrderAdd}")
    public void setAddInStockOrderRatioConvertExchange(String exchange) {
        InventoryMQProperties.ADD_INSTOCKORDER_RATIOCONVERT_EXCHANGE = exchange;
    }

    @Value("${ex.supplychain.convertOrder.add}")
    public void setConvertorderAddExchange(String string) {
        InventoryMQProperties.CONVERTORDER_ADD_EXCHANGE = string;
    }

    @Value("${ex.supplychain.omsStockOrder.inventoryChange}")
    public void setOmsStockOrderInventoryChangeExchange(String string) {
        InventoryMQProperties.OMS_STOCKORDER_INVENTORY_CHANGE_EXCHANGE = string;
    }

    @Value("${ex.supplychain.InventorySyncRecord.sync}")
    public void setInventorySyncExchange(String string) {
        InventoryMQProperties.INVENTORY_SYNC_CHANGE_EXCHANGE = string;
    }

    @Value("${ex.supplychain.outstockordersnyc.inboundAndOutbound}")
    public void setErpInOutExchange(String erpInOutExchange) {
        InventoryMQProperties.ERP_IN_OUT_EXCHANGE = erpInOutExchange;
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl.erp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.erp.ErpInOutStockDTO;
import com.yijiupi.himalaya.supplychain.dto.erp.ErpInOutStockTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.variable.VariableManager;
import com.yijiupi.himalaya.supplychain.inventory.domain.configuration.InventoryMQProperties;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryInStockOrderBatchDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-18 10:18
 **/
@Service
public class ErpInOutStockBL {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private VariableManager variableManager;

    private static final String NOTIFY_ERP_SWITCH_TO_WMS = "NOTIFY_ERP_SWITCH_TO_WMS";

    private static final Logger logger = LoggerFactory.getLogger(ErpInOutStockBL.class);

    /**
     * 二级仓入库通知 erp
     *
     * @param inStockOrderBatch 订单信息
     */
    @Async
    public void inStockNotifyErp(InventoryInStockOrderBatchDTO inStockOrderBatch) {
        logger.info("前置仓入库通知 erp, 入参: {}", JSON.toJSONString(inStockOrderBatch));
    }

    /**
     * 中心仓出库通知 erp
     *
     * @param deliveryOrders 订单信息
     */
    @Async
    public void outStockNotifyErp(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        logger.info("中心仓出库通知 erp, 入参: {}", JSON.toJSONString(deliveryOrders));
    }

    /**
     * 前置仓 NPT 出库通知 erp
     *
     * @param deliveryOrders 订单信息
     */
    @Async
    public void frontOutStockNotifyErp(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        logger.info("前置仓出库订单信息: {}", JSON.toJSONString(deliveryOrders));
        List<InventoryDeliveryJiupiOrder> frontNPTOrders =
                deliveryOrders.stream().filter(it -> it.getOrderNo().startsWith("NPT")).collect(Collectors.toList());
        if (frontNPTOrders.isEmpty()) {
            return;
        }
        logger.info("前置仓 NPT 出库通知 erp, 入参: {}", JSON.toJSONString(frontNPTOrders));
    }

    /**
     * 出入库推送 erp
     *
     * @param erpInOutStock 消息内容
     */
    private void inOutStockNotifyErp(ErpInOutStockDTO erpInOutStock) {
        // 入库取当前仓库 id, 出库取发货仓库 id
        Integer warehouseId = ErpInOutStockTypeEnum.IN_STOCK.valueEquals(erpInOutStock.getType())
                ? erpInOutStock.getToStoreHouseId() : erpInOutStock.getFromStoreHouseId();
        String postBody = JSON.toJSONString(erpInOutStock, SerializerFeature.WriteMapNullValue);
        if (doNotNeedSendNotifyMessage(warehouseId)) {
            logger.info("仓库: {}, 没开配置或者配置了 false, 不向 erp 发送通知: {}", warehouseId, postBody);
            return;
        }
        logger.info("推送 MQ 消息给 erp, exchange: {}, body: {}", InventoryMQProperties.ERP_IN_OUT_EXCHANGE, postBody);
        rabbitTemplate.convertAndSend(InventoryMQProperties.ERP_IN_OUT_EXCHANGE, null, erpInOutStock);
    }

    /**
     * 是否需要发送通知消息给 erp
     *
     * @param warehouseId 仓库 id
     */
    private boolean doNotNeedSendNotifyMessage(Integer warehouseId) {
        Boolean needSendNotifyMessage = variableManager.getVariableByKey(NOTIFY_ERP_SWITCH_TO_WMS, warehouseId)
                .map(VariableDefAndValueDTO::getVariableData).filter(StringUtils::hasText).map(Boolean::valueOf)
                .filter(it -> it).orElse(false);
        return !needSendNotifyMessage;
    }

}

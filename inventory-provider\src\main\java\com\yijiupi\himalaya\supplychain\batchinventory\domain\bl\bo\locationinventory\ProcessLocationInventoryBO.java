package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory;

import java.util.List;

import com.yijiupi.himalaya.supplychain.batchinventory.constant.ProcessLocationInventoryTypeConstants;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
public class ProcessLocationInventoryBO {

    private ProductInventoryChangeRecordPO productInventoryChangeRecordPO;
    private Integer locationType;
    private List<Integer> exSubcategory;

    private boolean isRandom;

    /**
     * @see ProcessLocationInventoryTypeConstants
     */
    private Integer processType;

    public ProcessLocationInventoryBO() {}

    public ProcessLocationInventoryBO(ProductInventoryChangeRecordPO productInventoryChangeRecordPO,
        Integer locationType, List<Integer> exSubcategory, boolean isRandom, Integer processType) {
        this.productInventoryChangeRecordPO = productInventoryChangeRecordPO;
        this.locationType = locationType;
        this.exSubcategory = exSubcategory;
        this.isRandom = isRandom;
        this.processType = processType;
    }

    /**
     * 获取
     *
     * @return productInventoryChangeRecordPO
     */
    public ProductInventoryChangeRecordPO getProductInventoryChangeRecordPO() {
        return this.productInventoryChangeRecordPO;
    }

    /**
     * 设置
     *
     * @param productInventoryChangeRecordPO
     */
    public void setProductInventoryChangeRecordPO(ProductInventoryChangeRecordPO productInventoryChangeRecordPO) {
        this.productInventoryChangeRecordPO = productInventoryChangeRecordPO;
    }

    /**
     * 获取
     *
     * @return locationType
     */
    public Integer getLocationType() {
        return this.locationType;
    }

    /**
     * 设置
     *
     * @param locationType
     */
    public void setLocationType(Integer locationType) {
        this.locationType = locationType;
    }

    /**
     * 获取
     *
     * @return exSubcategory
     */
    public List<Integer> getExSubcategory() {
        return this.exSubcategory;
    }

    /**
     * 设置
     *
     * @param exSubcategory
     */
    public void setExSubcategory(List<Integer> exSubcategory) {
        this.exSubcategory = exSubcategory;
    }

    /**
     * 获取
     *
     * @return isRandom
     */
    public boolean isIsRandom() {
        return this.isRandom;
    }

    /**
     * 设置
     *
     * @param isRandom
     */
    public void setIsRandom(boolean isRandom) {
        this.isRandom = isRandom;
    }

    /**
     * 获取 @see ProcessLocationInventoryTypeConstants
     *
     * @return processType @see ProcessLocationInventoryTypeConstants
     */
    public Integer getProcessType() {
        return this.processType;
    }

    /**
     * 设置 @see ProcessLocationInventoryTypeConstants
     *
     * @param processType @see ProcessLocationInventoryTypeConstants
     */
    public void setProcessType(Integer processType) {
        this.processType = processType;
    }
}

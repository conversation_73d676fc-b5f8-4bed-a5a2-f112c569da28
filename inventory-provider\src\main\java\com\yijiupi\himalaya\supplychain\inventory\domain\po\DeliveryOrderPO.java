package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2017/11/20
 */
public class DeliveryOrderPO implements Serializable {
    /**
     *
     */
    private String id;
    /**
     * cityid
     */
    private Integer cityId;
    /**
     * 仓库
     */
    private Integer warehouseId;
    /**
     * 订单号/退货单号
     */
    private String orderNo;
    /**
     * 订单项
     */
    private List<DeliveryOrderItemPO> DeliveryOrderItem;

    /**
     * 获取
     *
     * @return id
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置
     *
     * @param id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取 cityid
     *
     * @return cityId cityid
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * 设置 cityid
     *
     * @param cityId cityid
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 获取 仓库
     *
     * @return warehouseId 仓库
     */
    public Integer getWarehouseId() {
        return this.warehouseId;
    }

    /**
     * 设置 仓库
     *
     * @param warehouseId 仓库
     */
    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * 获取 订单号退货单号
     *
     * @return orderNo 订单号退货单号
     */
    public String getOrderNo() {
        return this.orderNo;
    }

    /**
     * 设置 订单号退货单号
     *
     * @param orderNo 订单号退货单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 获取 订单项
     *
     * @return DeliveryOrderItem 订单项
     */
    public List<DeliveryOrderItemPO> getDeliveryOrderItem() {
        return this.DeliveryOrderItem;
    }

    /**
     * 设置 订单项
     *
     * @param DeliveryOrderItem 订单项
     */
    public void setDeliveryOrderItem(List<DeliveryOrderItemPO> DeliveryOrderItem) {
        this.DeliveryOrderItem = DeliveryOrderItem;
    }
}

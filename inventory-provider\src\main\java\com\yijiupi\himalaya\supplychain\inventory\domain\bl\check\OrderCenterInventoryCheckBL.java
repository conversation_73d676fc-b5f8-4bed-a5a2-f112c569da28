package com.yijiupi.himalaya.supplychain.inventory.domain.bl.check;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ordercenter.sdk.serviceability.ServiceAbilityClient;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterResponseDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterUnConfirmOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderCenterUnConfirmOrderInventoryResultDTO;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Service
public class OrderCenterInventoryCheckBL {

    @Autowired
    private ServiceAbilityClient serviceAbilityClient;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Value("${ordercenter.sdk.timeout}")
    private Integer timeout;
    public static final String ORDERCENTER_STOCKQUERY_URL =
            "/aggregatequery/OrderInventoryQueryService/findUnConfirmOrderInventory";
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCenterInventoryCheckBL.class);

    public Map<Integer, Boolean> openOrderCenterMap(List<Integer> warehouseIds, Integer version) {
        Map<Integer, Boolean> isOpenOrderCenterMap = new HashMap<>();
        warehouseIds.stream().forEach(m -> {
            // boolean isOpenOrderCenter = iWarehouseQueryService.isOpenOrderCenter(m);
            isOpenOrderCenterMap.put(m, Boolean.TRUE);
        });

        return isOpenOrderCenterMap;
    }


    public Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>>
    getOpenCenterCount(List<Integer> warehouseIdList) {
        Map<Integer, Boolean> openCenterMap = new HashMap<>();
        warehouseIdList.forEach(m -> openCenterMap.put(m, Boolean.TRUE));
        return getOpenCenterCount(warehouseIdList);
    }

    /**
     * 获取已发货未完成的数量
     *
     * @param cityId
     * @return
     */
    public Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>>
    getOpenCenterCount(List<Integer> warehouseIdList, Map<Integer, Boolean> openCenterMap) {
        Map<Integer, List<OrderCenterUnConfirmOrderInventoryResultDTO>> deliveryCountMap = new HashMap<>(16);

        List<OrderCenterUnConfirmOrderInventoryResultDTO> resultDTOList = new ArrayList<>();
        warehouseIdList.stream().filter(m -> BooleanUtils.isTrue(openCenterMap.get(m))).forEach(warehouseId -> {
            OrderCenterUnConfirmOrderInventoryDTO dto = new OrderCenterUnConfirmOrderInventoryDTO();
            dto.setWarehouseId(warehouseId);
            try {
                String invoke =
                        serviceAbilityClient.invoke(ORDERCENTER_STOCKQUERY_URL, timeout, String.class, warehouseId);
                LOGGER.info("获取已发货未完成的数量 getOpenCenterCount invoke={},convert={}", invoke, JSON.toJSONString(dto));
                OrderCenterInventoryResultDTO<List<OrderCenterUnConfirmOrderInventoryResultDTO>> resultDTO =
                        JSON.parseObject(invoke, new TypeReference<
                                OrderCenterInventoryResultDTO<List<OrderCenterUnConfirmOrderInventoryResultDTO>>>() {
                        });
                if (BooleanUtils.isFalse(resultDTO.isSuccess())) {
                    LOGGER.warn("获取已发货未完成的数量 getOpenCenterCount 失败，入参: {} 返回值 : {}",
                            JSON.toJSONString(JSON.toJSONString(dto)), JSON.toJSON(resultDTO));
                }
                List<OrderCenterUnConfirmOrderInventoryResultDTO> resultList = resultDTO.getData();
                if (!CollectionUtils.isEmpty(resultList)) {
                    resultDTOList.addAll(resultList);
                }
            } catch (Exception e) {
                LOGGER.error("获取已发货未完成的数量 getOpenCenterCount", e);
            }
            // if (!CollectionUtils.isEmpty(resultDTOList)) {
            // resultDTOList.forEach(n -> {
            // deliveryCountMap.put(String.format("%s-%s-%s-%s", n.getProductSpecificationId(),
            // n.getOwnerId(), n.getWarehouseId(), n.getSecOwnerId()), n.getCount());
            // });
            // resultDTOList.clear();
            // }
        });
        if (CollectionUtils.isEmpty(resultDTOList)) {
            return deliveryCountMap;
        }

        return resultDTOList.stream()
                .collect(Collectors.groupingBy(OrderCenterUnConfirmOrderInventoryResultDTO::getWarehouseId));
    }

    /**
     * 判断是否开启了订单中台
     *
     * @return true=已开启,false=未开启
     */
    public boolean isOpenOrderCenter(Integer warehouseId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");

        try {
            OrderCenterResponseDTO<Integer> response = serviceAbilityClient.invoke(
                    "/yijiupi/WarehouseCheckService/findGrayWarehosueIdList", timeout, OrderCenterResponseDTO.class);
            LOGGER.info("获取中台灰度配置信息 response={}", JSON.toJSONString(response));
            return response.getData().stream().anyMatch(e -> Objects.equals(warehouseId, e));
        } catch (Exception e) {
            LOGGER.warn("调用中台异常", e);
            return false;
        }
    }
}

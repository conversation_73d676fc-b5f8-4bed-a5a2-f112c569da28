package com.yijiupi.himalaya.supplychain.batchinventory.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 消息队列名常量类
 */
@Component
public class BatchInventoryMQProperties {

    /**
     * 新增加补货任务消息发送
     */
    public static String ADD_REPLENISHMENT_EXCHANGE;

    /**
     * 同步批次库存生产日期消息发送
     */
    public static String SYN_PRODUCTION_DATE_EXCHANGE;

    /**
     * 完成生产日期治理任务消息发送
     */
    public static String COMPLETE_PRODUCTION_DATE_TASK_EXCHANGE;

    @Value("${ex.supplychain.replenishment.add}")
    public void setOutStockOrderAddQuenue(String string) {
        BatchInventoryMQProperties.ADD_REPLENISHMENT_EXCHANGE = string;
    }

    @Value("${ex.supplychain.inventory.SynProductionDate}")
    public void setSynProductionDateQuenue(String string) {
        BatchInventoryMQProperties.SYN_PRODUCTION_DATE_EXCHANGE = string;
    }

    @Value("${ex.supplychain.batchinventory.productiondatetaskcomplete}")
    public void setProductionDateTaskCompleteQuenue(String string) {
        BatchInventoryMQProperties.COMPLETE_PRODUCTION_DATE_TASK_EXCHANGE = string;
    }
}

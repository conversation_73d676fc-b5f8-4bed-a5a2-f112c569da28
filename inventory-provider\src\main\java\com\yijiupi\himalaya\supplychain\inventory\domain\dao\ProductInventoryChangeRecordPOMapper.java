package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.InventoryProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStoreChangeRecordByOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryStorageDTO;
import com.yijiupi.himalaya.supplychain.search.ProductStoreChangeRecordByOrderSO;
import com.yijiupi.himalaya.supplychain.search.ProductStoreRecordSO;

/**
 * 仓库库存变更记录
 *
 * <AUTHOR>
 */
public interface ProductInventoryChangeRecordPOMapper {

    /**
     * 批量新增仓库库存变更记录
     *
     * @param productInventoryChangeRecordPOList
     */
    void insertProductInventoryChangeRecordBatch(
        @Param("list") List<ProductInventoryChangeRecordPO> productInventoryChangeRecordPOList);

    /**
     * 分页查询仓库库存变更记录
     */
    PageResult<ProductInventoryChangeRecordPO> findProductInventoryChangeRecordPOList(
        @Param("dto") ProductStoreRecordSO dto, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    /**
     * 分页查询仓库库存变更记录
     */
    PageResult<ProductInventoryChangeRecordPO> findProductInventoryChangeRecordPOListBySkuId(
        @Param("dto") ProductStoreRecordSO dto, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    PageResult<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordByOrder(ProductStoreChangeRecordByOrderSO orderSO);

    /**
     * 查询订单库存变更记录
     * 
     * @return
     */
    List<ProductStoreChangeRecordByOrderDTO>
        listProductStoreChangeRecordGroupByOrder(ProductStoreChangeRecordByOrderSO orderSO);

    // /**
    // * 分页查询sku对应城市下仓库库存变更记录
    // */
    // PageResult<CityInventoryRecordDTO> findCityInventoryRecordList(@Param("dto") CityInventoryRecordQueryDTO dto,
    // @Param("pageNum") int pageNum,
    // @Param("pageSize") int pageSize);

    /**
     * 修改仓库库存变更记录
     */
    void updateProductInventoryChangeRecord(ProductInventoryChangeRecordPO productInventoryChangeRecordPO);

    List<InventoryStorageDTO> findInventoryStorage(@Param("orderNos") List<String> orderNos);

    /**
     * 根据规格id、货主id查询仓库库存变更记录
     */
    PageResult<ProductInventoryChangeRecordPO> findInventoryChangeBySpec(@Param("dto") ProductStoreRecordSO dto,
        @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    List<InventoryProductStoreBatchChangeRecordDTO>
        findProductStoreBatchChangeRecordList(ProductStoreChangeRecordByOrderSO orderSO);
}

package com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreReturnDTO;

/**
 * <AUTHOR>
 */
public interface ProductStorePOReturnMapper {

    /**
     * 易经销根据仓库ID，经销商ID,查询仓库库存
     * 
     * @param productStoreQueryDTO
     * @return
     */
    PageResult<ProductStoreReturnDTO> findProductStoreList(ProductStoreQueryDTO productStoreQueryDTO);
}
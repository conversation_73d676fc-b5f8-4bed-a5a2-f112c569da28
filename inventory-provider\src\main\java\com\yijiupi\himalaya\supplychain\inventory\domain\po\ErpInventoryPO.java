package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.io.Serializable;
import java.util.Date;

/**
 * ERP库存PO对象
 * 
 * <AUTHOR>
 */
public class ErpInventoryPO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private String id;
    /**
     * 城市ID
     */
    private Integer cityId;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 库存所属类型(酒批(0), 合作商(1))
     */
    private Integer ownerType;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * ERP显示库存数量
     */
    private Integer erpDisplayCount;
    /**
     * 未审核销售数量
     */
    private Integer saleCount;
    /**
     * 未审核退货数量
     */
    private Integer saleReturnCount;
    /**
     * 未审核采购数量
     */
    private Integer buyCount;
    /**
     * 未审核采购退货数量
     */
    private Integer buyReturnCount;
    /**
     * ERP实际库存数量
     */
    private Integer erpRealCount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 产品信息规格ID
     */
    private Integer productSpecificationId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getErpDisplayCount() {
        return erpDisplayCount;
    }

    public void setErpDisplayCount(Integer erpDisplayCount) {
        this.erpDisplayCount = erpDisplayCount;
    }

    public Integer getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount) {
        this.saleCount = saleCount;
    }

    public Integer getSaleReturnCount() {
        return saleReturnCount;
    }

    public void setSaleReturnCount(Integer saleReturnCount) {
        this.saleReturnCount = saleReturnCount;
    }

    public Integer getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(Integer buyCount) {
        this.buyCount = buyCount;
    }

    public Integer getBuyReturnCount() {
        return buyReturnCount;
    }

    public void setBuyReturnCount(Integer buyReturnCount) {
        this.buyReturnCount = buyReturnCount;
    }

    public Integer getErpRealCount() {
        return erpRealCount;
    }

    public void setErpRealCount(Integer erpRealCount) {
        this.erpRealCount = erpRealCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Integer productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }
}

/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.controller;

import com.yijiupi.himalaya.supplychain.inventory.controller.model.*;
import com.yijiupi.himalaya.supplychain.inventory.controller.service.StoreInfoApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 库存报表信息
 *
 * @author: tangkun
 * @date: 2017年3月30日 下午2:11:39
 */
@RestController
public class StoreInfoController {
    private static final Logger LOG = LoggerFactory.getLogger(StoreInfoController.class);
    @Autowired
    private StoreInfoApiService storeInfoApiService;

    /**
     * 库存报表
     */
    @RequestMapping(value = "/store/report/findStorePage", method = RequestMethod.POST)
    public BaseResult findStorePage(@RequestBody FindStorePageDTO findStorePageDTO) {
        LOG.info("库存查询:迁移到微服务:");
        ROResult<List<FindStoreInfoVO>> result = storeInfoApiService.findStorePage(findStorePageDTO);
        result.setResult(WebConstants.RESULT_SUCCESS);
        return result;
    }

    /**
     * 库存信息查询
     */
    @RequestMapping(value = "/store/report/findStorePageInfo", method = RequestMethod.POST)
    public BaseResult findStorePageInfo(@RequestBody FindStorePageDTO findStorePageDTO) {
        LOG.info("库存查询新:迁移到微服务:");
        ROResult<List<FindStoreInfoVO>> result = storeInfoApiService.findStorePageInfo(findStorePageDTO);
        result.setResult(WebConstants.RESULT_SUCCESS);
        return result;
    }

}

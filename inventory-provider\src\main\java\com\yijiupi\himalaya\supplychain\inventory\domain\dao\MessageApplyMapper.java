package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * 消息消费记录Mapper
 *
 * <AUTHOR>
 */
public interface MessageApplyMapper {

    /**
     * 消息消费记录数, 用于判断是否已经消费过.
     */
    int countMessageApply(@Param("messageId") String messageId);

    /**
     * 根据Id找到已经处理过的Id
     * 
     * @param msgId
     * @return
     */
    List<String> getProcessedMessage(@Param("list") List<String> msgId);

    /**
     * 记录消息消费.
     */
    void insertMessageApply(@Param("messageId") String messageId);

    /**
     * 批量插入消费记录
     */
    void insertMessageApplyBatch(@Param("list") List<String> messageId);

    /**
     * 批量删除消费记录
     */
    void deleteMessagesBatch(@Param("list") List<String> messageId);

}

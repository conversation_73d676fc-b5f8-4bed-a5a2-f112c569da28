package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductSecOwnerServiceBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSecOwnerIdQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductSecOwnerService;

@Service(timeout = 30000)
public class ProductSecSecOwnerServiceImpl implements IProductSecOwnerService {

    @Autowired
    private ProductSecOwnerServiceBL productSecOwnerServiceBL;

    @Override
    public Map<String, List<Long>> getSecOwnerIdMap(ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO) {
        AssertUtils.notNull(productSecOwnerIdQueryDTO, "查询参数不能为空！");
        AssertUtils.notNull(productSecOwnerIdQueryDTO.getWarehouseId(), "查询参数仓库ID不能为空！");
        AssertUtils.notEmpty(productSecOwnerIdQueryDTO.getSpecAndOwnerIds(), "查询参数规格信息不能为空！");
        return productSecOwnerServiceBL.getSecOwnerMap(productSecOwnerIdQueryDTO);
    }
}

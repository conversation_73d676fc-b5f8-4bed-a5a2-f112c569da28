package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.enums.ChargeConfigStatusEnum;
import com.yijiupi.himalaya.supplychain.enums.WarehouseChooseType;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChargeConfigConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.DealerChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductStorePOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.WarehouseChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWareHouseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWarehouseQuery;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 仓库标准费率BL
 *
 * @author: lidengfeng
 * @date 2018/9/15 11:23
 */
@Service
public class WarehouseChargeConfigBL {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseChargeConfigBL.class);

    @Autowired
    private WarehouseChargeConfigPOMapper warehouseChargeConfigPOMapper;

    @Autowired
    private WarehouseChargeConfigConvert chargeConfigConvert;
    @Autowired
    private DealerChargeConfigPOMapper dealerChargeConfigPOMapper;

    @Autowired
    private DealerChargeConfigBL dealerChargeConfigBL;

    @Autowired
    private ProductChargeConfigBL productChargeConfigBL;

    @Autowired
    private ProductStorePOMapper productStorePOMapper;

    /**
     * 新增或修改仓库标准费率
     *
     * @param dto
     * @return: void
     */
    public void saveOrUpdateChargeConfig(WarehouseChargeConfigDTO dto) {
        LOG.info("新增或修改仓库标准费率：" + JSON.toJSONString(dto));
        WarehouseChargeConfigPO po = chargeConfigConvert.reverseConvert(dto);
        int count = warehouseChargeConfigPOMapper.selectCountByWarehouseId(dto.getWarehouseId());
        if (count > 0) {
            warehouseChargeConfigPOMapper.updateWarehouseChargeConfig(po);
        } else {
            po.setId(UUIDGenerator.getUUID(WarehouseChargeConfigPO.class.getName()));
            warehouseChargeConfigPOMapper.insertWarehouseChargeConfig(po);
        }
    }

    /**
     * 仓库标准费率明细查询
     *
     * @param warehouseId
     * @return
     */
    public WarehouseChargeConfigDTO selectWarehouseChargeConfigById(Integer warehouseId) {
        WarehouseChargeConfigDTO warehouseChargeConfigDTO =
            warehouseChargeConfigPOMapper.selectWarehouseChargeConfigById(warehouseId);
        return warehouseChargeConfigDTO;
    }

    /**
     * 启用停用仓库标准费率
     *
     * @param dto
     * @return
     */
    public void updateChargeConfigStatus(WarehouseChargeConfigDTO dto) {
        WarehouseChargeConfigPO warehouseChargeConfigPO = chargeConfigConvert.reverseConvert(dto);
        warehouseChargeConfigPOMapper.updateChargeConfigStatus(warehouseChargeConfigPO);

    }

    /**
     * 根据仓库id集合查询标准费率
     *
     * @param
     * @return
     */
    public Map<Integer, WarehouseChargeConfigDTO> selectWarehouseChargeList(List<Integer> list) {
        Map<Integer, WarehouseChargeConfigDTO> map = warehouseChargeConfigPOMapper.selectWarehouseChargeList(list);
        return map;
    }

    /**
     * 根据服务商id，城市查询仓库信息及费用
     *
     * @param agencyStockWarehouseQuery
     * @return
     */
    public PageList<AgencyStockWareHouseDTO>
        findWarehouseChargeList(AgencyStockWarehouseQuery agencyStockWarehouseQuery) {
        PageList<AgencyStockWareHouseDTO> pageList = new PageList<AgencyStockWareHouseDTO>();
        PageResult<AgencyStockWareHouseDTO> poList =
            warehouseChargeConfigPOMapper.findWarehouseChargeList(agencyStockWarehouseQuery);
        List<Integer> list = poList.stream().map(p -> p.getWarehouseId()).distinct().collect(Collectors.toList());
        Map<Integer, WarehouseChargeConfigDTO> map = warehouseChargeConfigPOMapper.selectWarehouseChargeList(list);
        poList.forEach(warehouseDTO -> {
            if (map.get(warehouseDTO.getWarehouseId()) != null) {
                warehouseDTO.setCustodiancharge(map.get(warehouseDTO.getWarehouseId()).getCustodiancharge());
                warehouseDTO.setTransportcharge(map.get(warehouseDTO.getWarehouseId()).getTransportcharge());

            }

        });
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;
    }

    /**
     * 入库单费用
     *
     * @param shopChargeDTO
     * @return
     */
    public InStockCharge findInStockTotalCharge(ShopChargeDTO shopChargeDTO) {
        InStockCharge inStockCharge = new InStockCharge();
        BigDecimal sortingfee = BigDecimal.ZERO;
        BigDecimal shipGoodsAmount = BigDecimal.ZERO;
        // 得到经销商收费配置
        DealerChargeConfigDTO dealerChargeConfigDTO = getDealerChargeConfigDTO(shopChargeDTO);
        List<ProductChargeDTO> items = shopChargeDTO.getItems();
        // 得到规格参数Id集合
        List<Long> skuIdList =
            items.stream().map(p -> p.getProductSpecificationId()).distinct().collect(Collectors.toList());
        // 产品skuid为key,产品数量为value
        Map<Long, BigDecimal> productNumMap = items.stream()
            .collect(Collectors.toMap(ProductChargeDTO::getProductSpecificationId, ProductChargeDTO::getNum));
        Map<Long, ProductChargeConfigDTO> productChargeConfigMap =
            getIntegerProductChargeConfigDTOMap(shopChargeDTO, skuIdList);

        // 得到仓库的收费配置
        WarehouseChargeConfigDTO warehouseChargeConfigDTO =
            selectWarehouseChargeConfigById(shopChargeDTO.getWarehouseId());

        if (isaBoolean(dealerChargeConfigDTO)) {
            for (Long productSpecificationId : skuIdList) {
                ProductChargeConfigDTO productChargeConfigDTO = productChargeConfigMap.get(productSpecificationId);
                if (isProductBoolean(productChargeConfigDTO)) {
                    // 下车费
                    BigDecimal unloadingCharge =
                        productChargeConfigDTO.getUnloadingCharge().multiply(productNumMap.get(productSpecificationId));
                    shipGoodsAmount = shipGoodsAmount.add(unloadingCharge);
                    // 分拣费
                    BigDecimal sortingCharge =
                        productChargeConfigDTO.getSortingCharge().multiply(productNumMap.get(productSpecificationId));
                    sortingfee = sortingfee.add(sortingCharge);

                } else if (isWarehouseBoolean(warehouseChargeConfigDTO)) {
                    // 下车费
                    BigDecimal unloadingCharge = warehouseChargeConfigDTO.getUnloadingcharge()
                        .multiply(productNumMap.get(productSpecificationId));
                    shipGoodsAmount = shipGoodsAmount.add(unloadingCharge);
                    // 分拣费
                    BigDecimal sortingCharge =
                        warehouseChargeConfigDTO.getSortingcharge().multiply(productNumMap.get(productSpecificationId));
                    sortingfee = sortingfee.add(sortingCharge);

                }
            }

        }
        inStockCharge.setSortingfee(sortingfee);
        inStockCharge.setShipGoodsAmount(shipGoodsAmount);

        return inStockCharge;
    }

    /**
     * 判断 仓库是否收费
     *
     * @param warehouseChargeConfigDTO
     * @return
     */
    private boolean isWarehouseBoolean(WarehouseChargeConfigDTO warehouseChargeConfigDTO) {
        return warehouseChargeConfigDTO != null
            && ChargeConfigStatusEnum.START_USE.getType() == warehouseChargeConfigDTO.getStatus();
    }

    /**
     * 判断 经销商是否收费
     *
     * @param dealerChargeConfigDTO
     * @return
     */
    private boolean isaBoolean(DealerChargeConfigDTO dealerChargeConfigDTO) {
        return (dealerChargeConfigDTO != null
            && ChargeConfigStatusEnum.START_USE.getType() == dealerChargeConfigDTO.getIsGetWarehouseCharge()
            && ChargeConfigStatusEnum.START_USE.getType() == dealerChargeConfigDTO.getStatus())
            || (dealerChargeConfigDTO == null);
    }

    /**
     * 判断产品是否收费
     *
     * @param productChargeConfigDTO
     * @return
     */
    private boolean isProductBoolean(ProductChargeConfigDTO productChargeConfigDTO) {
        return productChargeConfigDTO != null
            && ChargeConfigStatusEnum.START_USE.getType() == productChargeConfigDTO.getStatus();
    }

    /**
     * 获取 经销商收费配置
     *
     * @param shopChargeDTO
     * @return
     */
    private DealerChargeConfigDTO getDealerChargeConfigDTO(ShopChargeDTO shopChargeDTO) {
        DealerCountQuery dealerCountQuery = new DealerCountQuery();
        dealerCountQuery.setDealerId(shopChargeDTO.getShopId());
        dealerCountQuery.setFacilitatorId(shopChargeDTO.getFacilitatorId());
        DealerChargeConfigDTO dealerChargeConfigDTO =
            dealerChargeConfigBL.selectDealerChargeConfigById(dealerCountQuery);
        LOG.info("经销商收费配置: {}", JSON.toJSONString(dealerChargeConfigDTO));
        return dealerChargeConfigDTO;
    }

    /**
     * 获取产品收费
     *
     * @param shopChargeDTO
     * @param idList
     * @return
     */
    private Map<Long, ProductChargeConfigDTO> getIntegerProductChargeConfigDTOMap(ShopChargeDTO shopChargeDTO,
        List<Long> idList) {
        // 得到所有产品的收费配置,产品规格参数id为key
        ProductChargeQuery productChargeQuery = new ProductChargeQuery();
        productChargeQuery.setDealerId(shopChargeDTO.getShopId());
        productChargeQuery.setProductSpecificationIdList(idList);
        productChargeQuery.setFacilitatorId(shopChargeDTO.getFacilitatorId());
        Map<Long, ProductChargeConfigDTO> productChargeConfigDTOMap =
            productChargeConfigBL.selectProductChargeList(productChargeQuery);
        Map<Long, ProductChargeConfigDTO> productChargeConfigMap =
            productChargeConfigBL.selectProductCharge(productChargeQuery);
        productChargeConfigMap.putAll(productChargeConfigDTOMap);
        LOG.info("产品收费Map: {}", JSON.toJSONString(productChargeConfigMap));
        return productChargeConfigMap;
    }

    /**
     * 出库单或委托配送单费用
     *
     * @param shopChargeDTO
     * @return
     */
    public OutStockCharge findOutStockTotalCharge(ShopChargeDTO shopChargeDTO) {

        OutStockCharge outStockCharge = new OutStockCharge();
        BigDecimal deliveryfee = BigDecimal.ZERO;
        BigDecimal depositfee = BigDecimal.ZERO;
        BigDecimal loadingCharge = BigDecimal.ZERO;
        BigDecimal landingCharge = BigDecimal.ZERO;
        // 得到经销商收费配置
        DealerChargeConfigDTO dealerChargeConfigDTO = getDealerChargeConfigDTO(shopChargeDTO);
        List<ProductChargeDTO> items = shopChargeDTO.getItems();
        // 得到skuId集合
        List<Long> skuIdList =
            items.stream().map(p -> p.getProductSpecificationId()).distinct().collect(Collectors.toList());
        // 产品skuid为key,产品数量为value
        Map<Long, BigDecimal> productNumMap = items.stream()
            .collect(Collectors.toMap(ProductChargeDTO::getProductSpecificationId, ProductChargeDTO::getNum));
        // 得到所有产品的收费配置,产品skuid为key
        Map<Long, ProductChargeConfigDTO> productChargeConfigMap =
            getIntegerProductChargeConfigDTOMap(shopChargeDTO, skuIdList);
        // 得到仓库的收费配置
        WarehouseChargeConfigDTO warehouseChargeConfigDTO =
            selectWarehouseChargeConfigById(shopChargeDTO.getWarehouseId());
        if (isaBoolean(dealerChargeConfigDTO)) {
            for (Long productSpecificationId : skuIdList) {
                ProductChargeConfigDTO productChargeConfigDTO = productChargeConfigMap.get(productSpecificationId);
                if (isProductBoolean(productChargeConfigDTO)) {
                    // 托管费
                    BigDecimal custodiancharge =
                        productChargeConfigDTO.getCustodianCharge().multiply(productNumMap.get(productSpecificationId));
                    depositfee = depositfee.add(custodiancharge);
                    // 装车费
                    BigDecimal loadingcharge =
                        productChargeConfigDTO.getLoadingCharge().multiply(productNumMap.get(productSpecificationId));
                    loadingCharge = loadingCharge.add(loadingcharge);
                    // 配送费
                    BigDecimal transportcharge =
                        productChargeConfigDTO.getTransportCharge().multiply(productNumMap.get(productSpecificationId));
                    deliveryfee = deliveryfee.add(transportcharge);
                    // 卸货费
                    BigDecimal landingFee =
                        productChargeConfigDTO.getLandingCharge().multiply(BigDecimal.valueOf(productSpecificationId));
                    landingCharge = landingCharge.add(landingFee);

                } else if (isWarehouseBoolean(warehouseChargeConfigDTO)) {
                    // 托管费
                    BigDecimal custodiancharge = warehouseChargeConfigDTO.getCustodiancharge()
                        .multiply(productNumMap.get(productSpecificationId));
                    depositfee = depositfee.add(custodiancharge);
                    // 装车费
                    BigDecimal loadingcharge =
                        warehouseChargeConfigDTO.getLoadingcharge().multiply(productNumMap.get(productSpecificationId));
                    loadingCharge = loadingCharge.add(loadingcharge);
                    // 配送费
                    BigDecimal transportcharge = warehouseChargeConfigDTO.getTransportcharge()
                        .multiply(productNumMap.get(productSpecificationId));
                    deliveryfee = deliveryfee.add(transportcharge);
                    // 卸货费
                    BigDecimal landingFee =
                        warehouseChargeConfigDTO.getLandingcharge().multiply(productNumMap.get(productSpecificationId));
                    landingCharge = landingCharge.add(landingFee);
                }
            }
        }
        outStockCharge.setDeliveryfee(deliveryfee);
        outStockCharge.setDepositfee(depositfee);
        outStockCharge.setLoadingCharge(loadingCharge);
        outStockCharge.setLandingCharge(landingCharge);

        return outStockCharge;

    }

    /**
     * 查询自有托管仓库
     */
    public PageList<WarehouseChooseReturnDTO> findWarehouseChooseList(WarehouseChooseDTO warehouseChooseDTO) {
        // LOG.info("自有托管仓库：{}", JSON.toJSONString(warehouseChooseDTO));
        warehouseChooseDTO.setShopId(warehouseChooseDTO.getShopId());
        PageList<WarehouseChooseReturnDTO> pageList = new PageList<WarehouseChooseReturnDTO>();
        warehouseChooseDTO.setShopId(warehouseChooseDTO.getShopId());
        // LOG.info("查寻仓库：{}", JSON.toJSONString(warehouseChooseDTO));
        PageResult<WarehouseChooseReturnDTO> warehouseChooseList = new PageResult<>();
        // 查询所有的仓库
        if (WarehouseChooseType.ALL_WAREHOUSE == warehouseChooseDTO.getWarehouseChooseType()) {
            warehouseChooseList = warehouseChargeConfigPOMapper.findWarehouseList(warehouseChooseDTO);

        }
        // 查询自有仓库
        if (WarehouseChooseType.OWNER_WAREHOUSE == warehouseChooseDTO.getWarehouseChooseType()) {
            warehouseChooseList = warehouseChargeConfigPOMapper.findOwnerWarehouseList(warehouseChooseDTO);

        }
        // 查询托管仓库
        if (WarehouseChooseType.DEPOSIT_WAREHOUSE == warehouseChooseDTO.getWarehouseChooseType()) {
            warehouseChooseList = warehouseChargeConfigPOMapper.findDealerWarehouseList(warehouseChooseDTO);

        }
        // 查询申请入库仓库
        if (WarehouseChooseType.DEALEAR_WAREHOUSE == warehouseChooseDTO.getWarehouseChooseType()) {
            warehouseChooseList = warehouseChargeConfigPOMapper.findDepositWarehouseList(warehouseChooseDTO);
        }

        pageList.setPager(warehouseChooseList.getPager());
        pageList.setDataList(warehouseChooseList);
        return pageList;
    }

    /**
     * 城市下服务商的仓库查询
     *
     * @param cityWarehouseQuery
     * @return
     */
    public PageList<WarehouseChargeConfigDTO> findWarehouseChargeDetailList(CityWarehouseQuery cityWarehouseQuery) {
        PageList<WarehouseChargeConfigDTO> pageList = new PageList<WarehouseChargeConfigDTO>();
        PageResult<WarehouseChargeConfigDTO> poList =
            warehouseChargeConfigPOMapper.findWarehouseChargeDetailList(cityWarehouseQuery);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;

    }

    /**
     * 校验校验仓库是否可停用
     *
     * @param productWarehouseDTO
     * @return
     */
    public Boolean warehouseStock(ProductWarehouseDTO productWarehouseDTO) {
        ProductShopStoreReturnDTO productStore = productStorePOMapper.findProductStore(productWarehouseDTO);
        if (productStore != null && productStore.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 查询仓库信息及经销商信息 仓库管理仓库信息
     *
     * @param cityWarehouseQuery
     * @return
     */
    public WarehouseDealerDetailDTO getWarehouseDealerDetail(CityWarehouseQuery cityWarehouseQuery) {
        WarehouseDealerDetailDTO warehouseDealerDetailDTO = new WarehouseDealerDetailDTO();
        // 仓库信息集合
        PageResult<WarehouseChargeConfigDTO> warehouseChargeDetailList =
            warehouseChargeConfigPOMapper.findWarehouseChargeDetailList(cityWarehouseQuery);
        WarehouseChargeConfigDTO warehouseChargeConfigDTO = warehouseChargeDetailList.get(0);
        // 设置经销商信息集合
        PageList<DealerChargeConfigDTO> dealerPageList = new PageList<>();
        PageResult<DealerChargeConfigDTO> dealerChargeConfigDTOList =
            dealerChargeConfigPOMapper.selectDealerInfoList(cityWarehouseQuery);
        dealerPageList.setDataList(dealerChargeConfigDTOList);
        dealerPageList.setPager(dealerChargeConfigDTOList.getPager());

        // 返回数据
        warehouseDealerDetailDTO.setWarehouseChargeConfigDTO(warehouseChargeConfigDTO);
        warehouseDealerDetailDTO.setDealerChargeList(dealerPageList);
        return warehouseDealerDetailDTO;

    }

    /**
     * 仓库管理 仓库库存
     *
     * @param warehouseServiceStoreQuery
     * @return
     */
    public PageList<WarehouseServiceStoreDTO>
        findWarehouseServiceStoreList(WarehouseServiceStoreQuery warehouseServiceStoreQuery) {

        PageList<WarehouseServiceStoreDTO> pageList = new PageList<WarehouseServiceStoreDTO>();
        PageResult<WarehouseServiceStoreDTO> poList =
            warehouseChargeConfigPOMapper.findWarehouseServiceStoreList(warehouseServiceStoreQuery);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;

    }

    /**
     * 根据仓库id查询仓库信息
     */
    public WarehouseReturnDTO findByWarehouseId(CityWarehouseQuery cityWarehouseQuery) {
        LOG.info("根据仓库id查询仓库信息入参：{}", JSON.toJSONString(cityWarehouseQuery));
        return warehouseChargeConfigPOMapper.findByWarehouseId(cityWarehouseQuery);
    }

    /**
     * 根据仓库id集合查询仓库信息
     *
     * @param warehouseIdList
     * @return
     */
    public PageList<WarehouseReturnDTO> findByWarehouseList(List<Integer> warehouseIdList) {
        PageList<WarehouseReturnDTO> pageList = new PageList<WarehouseReturnDTO>();
        PageResult<WarehouseReturnDTO> poList = warehouseChargeConfigPOMapper.findByWarehouseList(warehouseIdList);
        pageList.setPager(poList.getPager());
        pageList.setDataList(poList);
        return pageList;

    }

}

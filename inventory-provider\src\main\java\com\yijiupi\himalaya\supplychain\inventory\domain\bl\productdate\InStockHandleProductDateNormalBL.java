package com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved. 处理销售订单
 * 
 * <AUTHOR>
 * @date 2023/9/20
 */
@Service
public class InStockHandleProductDateNormalBL extends InStockHandleProductDateBaseBL {
    @Override
    protected List<OrderDTO> doSupport(List<OrderDTO> orderList) {
        return orderList.stream()
            .filter(m -> !isNotNumberOrder(m.getRefOrderNo()) && !isReturnOrder(m.getOrderType(), m.getBusinessType()))
            .collect(Collectors.toList());
    }

    @Override
    protected Map<String, String> getOrderNoMap(List<OrderDTO> orderList) {
        LOG.info("匹配订单为：{}", JSON.toJSONString(orderList));
        return orderList.stream().collect(Collectors.toMap(OrderDTO::getRefOrderNo, OrderDTO::getRefOrderNo));
    }
}

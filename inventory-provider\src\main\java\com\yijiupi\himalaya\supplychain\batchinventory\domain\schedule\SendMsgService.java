package com.yijiupi.himalaya.supplychain.batchinventory.domain.schedule;

import com.yijiupi.himalaya.supplychain.dubbop.dto.PushMessageDTO;
import com.yijiupi.himalaya.supplychain.dubbop.dto.SMSMessageDTO;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SendMsgService {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendMessage(SMSMessageDTO smsSingleMessageDTO) {
        if (null != smsSingleMessageDTO && smsSingleMessageDTO.getContent() != null) {
            this.rabbitTemplate.convertAndSend("ex.baseservice.message.yjp_Message_SMS", (String) null,
                    smsSingleMessageDTO);
        }
    }

    public void pushAppMessage(PushMessageDTO pushMessage) {
        if (null != pushMessage) {
            this.rabbitTemplate.convertAndSend("ex.baseservice.message.yjp_Message_Push", (String) null, pushMessage);
        }
    }
}

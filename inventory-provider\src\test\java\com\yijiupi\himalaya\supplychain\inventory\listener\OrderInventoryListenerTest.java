package com.yijiupi.himalaya.supplychain.inventory.listener;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryErpBL;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class OrderInventoryListenerTest {

    @Autowired
    private InventoryErpBL inventoryErpBL;

    @Test
    public void testERPListener() {
        // {"erpType":3,"erpEventType":1,"orderId":"CR99920171120004","cityId":"999","userName":"黄正全","description":"【ERP】采购审核采购入库单","productSkuList":[{"productSkuId":"99900000003779","productSkuVersion":2,"storeCountMaxUnit":10,"storeCountMinUnit":0,"description":"【ERP】采购审核采购入库单","warehouseId":"9991","price":0.0}]}
        // String strTmp =
        // "{\"cityId\":\"898\",\"description\":\"【供应链】新增物料调拨单\",\"erpEventType\":1,\"erpType\":10,\"orderId\":\"TD89820180122012\",\"productSkuList\":[{\"channel\":0,\"description\":\"【供应链】新增物料调拨单\",\"productSkuId\":\"99900057690668\",\"totalStoreCountMinUnit\":1000,\"warehouseId\":\"9991\"}],\"userName\":\"liushuang\"}";

        String json = "[{\n" + "  \"cityId\": \"9991\",\n" + "  \"description\": \"新增入库单\",\n"
            + "  \"erpEventType\": 2,\n" + "  \"erpOrderId\": \"CS201804260051\",\n" + "  \"outInType\": 1,\n"
            + "  \"productSkuList\": [\n" + "    {\n" + "      \"channel\": 0,\n" + "      \"locationId\": 123,\n"
            + "      \"locationName\": \"测试\",\n" + "      \"productSkuId\": \"99900059768906\",\n"
            + "      \"totalStoreCountMinUnit\": 20\n" + "    }\n" + "  ],\n"
            + "  \"stockOrderId\": \"999118050200416\",\n" + "  \"userName\": \"测试\",\n" + "  \"warehouseId\": 9991\n"
            + "}\n]";
        List<StockOrderStoreDTO> list = JSON.parseObject(json, new TypeReference<List<StockOrderStoreDTO>>() {});
        for (StockOrderStoreDTO stockOrderStoreDTO : list) {
            inventoryErpBL.applyErpOrder(stockOrderStoreDTO, false, false, true, true, true);
        }
    }

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Test
    public void deliveryOut() throws Exception {

        rabbitTemplate.convertAndSend("", 1L);
    }

    @Test
    public void deliveryFail() throws Exception {}

    @Test
    public void deliveryComplete() throws Exception {}

    // @Test
    // public void returnOrderDeliveryComplete() throws Exception {
    // rabbitTemplate.convertAndSend("yjp_Inventory_ReturnOrderDeliveryComplete", new InventoryCountDTO());
    // }

}
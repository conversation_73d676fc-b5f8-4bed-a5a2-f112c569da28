package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.Date;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.DealerChargeConfigQuery;
import com.yijiupi.himalaya.supplychain.inventory.dto.DealerWarehouseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.DealerWarehouseQuery;

/**
 * @author: lidengfeng
 * @date 2018/11/29 15:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class IDealerChargeConfigServiceTest {

    @Reference
    private IDealerChargeConfigService iDealerChargeConfigService;

    /**
     * 保存或者更新经销商信息
     */
    @Test
    public void saveOrUpdateDealerChargeConfig() {
        DealerChargeConfigDTO dealerChargeConfigDTO = new DealerChargeConfigDTO();

        dealerChargeConfigDTO.setDealerId("169818873699270071");
        dealerChargeConfigDTO.setDealerName("baby");
        dealerChargeConfigDTO.setIsGetWarehouseCharge((byte)0);
        dealerChargeConfigDTO.setBusinessType((byte)0);
        dealerChargeConfigDTO.setStatus((byte)1);
        dealerChargeConfigDTO.setFacilitatorId(6L);
        dealerChargeConfigDTO.setFacilitatorName("服务商");
        dealerChargeConfigDTO.setCreateUser(169818873699270070L);
        dealerChargeConfigDTO.setLastUpdateUser(169818873699270070L);
        dealerChargeConfigDTO.setMobileNo("***********");
        dealerChargeConfigDTO.setFirstServiceTime(new Date());
        iDealerChargeConfigService.saveOrUpdateDealerChargeConfig(dealerChargeConfigDTO);
    }

    @Test
    public void selectCountByDealerId() {
        // Boolean byDealerId = iDealerChargeConfigService.selectCountByDealerId(169818873699270071L);
        // System.out.println(JSON.toJSONString(byDealerId));
    }

    @Test
    public void selectDealerChargeList() {
        DealerChargeConfigQuery dealerChargeConfigQuery = new DealerChargeConfigQuery();
        dealerChargeConfigQuery.setFacilitatorId(6L);
        // dealerChargeConfigQuery.setGetWarehouseCharge((byte) 1);
        // dealerChargeConfigQuery.setMobileNo("1234");
        dealerChargeConfigQuery.setDealerId(169818873699270072L);
        PageList<DealerChargeConfigDTO> dealerChargeConfigDTOPageList =
            iDealerChargeConfigService.selectDealerChargeList(dealerChargeConfigQuery);
        System.out.println(JSON.toJSONString(dealerChargeConfigDTOPageList));
    }

    @Test
    public void updateDealerConfigStatus() {
        DealerChargeConfigDTO dealerChargeConfigDTO = new DealerChargeConfigDTO();

        dealerChargeConfigDTO.setDealerId("169818873699270071");
        dealerChargeConfigDTO.setStatus((byte)0);
        dealerChargeConfigDTO.setLastUpdateUser(666L);
        iDealerChargeConfigService.updateDealerConfigStatus(dealerChargeConfigDTO);
    }

    @Test
    public void selectDealerWarehouseList() {
        DealerWarehouseQuery dealerWarehouseQuery = new DealerWarehouseQuery();
        dealerWarehouseQuery.setDealerId(169818873699270071L);
        // dealerWarehouseQuery.setCityId(999);
        // dealerWarehouseQuery.setWarehouseId(99916);
        dealerWarehouseQuery.setPageNum(1);
        dealerWarehouseQuery.setPageSize(3);
        PageList<DealerWarehouseDTO> pageList =
            iDealerChargeConfigService.selectDealerWarehouseList(dealerWarehouseQuery);
        System.out.println(JSON.toJSONString(pageList));

    }
}
package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplateRelationPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeTemplateRelationConvert {

    public static BatchAttributeTemplateRelationPO
        batchAttributeTemplateRelationDTO2PO(BatchAttributeTemplateRelationDTO dto) {
        if (dto == null) {
            return null;
        }
        BatchAttributeTemplateRelationPO batchAttributeTemplateRelationPO = new BatchAttributeTemplateRelationPO();
        batchAttributeTemplateRelationPO.setAttributeName(dto.getAttributeName());
        batchAttributeTemplateRelationPO.setAttributeType(dto.getAttributeType());
        batchAttributeTemplateRelationPO.setEffectiveDigit(dto.getEffectiveDigit());
        batchAttributeTemplateRelationPO.setRequired(dto.getRequired());
        batchAttributeTemplateRelationPO.setAttributeId(dto.getDicId());
        batchAttributeTemplateRelationPO.setIsCalculation(dto.getIsCalculation());
        batchAttributeTemplateRelationPO.setIsStoreCheck(dto.getIsStoreCheck());
        return batchAttributeTemplateRelationPO;
    }

    public static List<BatchAttributeTemplateRelationPO>
        batchAttributeTemplateRelationDTOS2POS(List<BatchAttributeTemplateRelationDTO> dtoList) {
        ArrayList<BatchAttributeTemplateRelationPO> poArrayList = new ArrayList<>();
        for (BatchAttributeTemplateRelationDTO batchAttributeTemplateRelationDTO : dtoList) {
            poArrayList.add(batchAttributeTemplateRelationDTO2PO(batchAttributeTemplateRelationDTO));
        }
        return poArrayList;
    }
}

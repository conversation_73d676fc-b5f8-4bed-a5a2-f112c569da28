package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change;

import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO;

/**
 * 库存变更基类 Created by Lifeng on 2017/7/19.
 */
public abstract class BaseInventoryChangeBO {

    private Integer cityId;

    private Integer orderType;

    private String orderId;

    private String orderNo;

    private Integer jiupiEventType;

    private Integer erpEventType;

    private String description;

    private String createUserId;
    private String createUserName;

    private Integer ownType;
    private Long ownId;

    private Long productSpecificationId;

    /**
     * 二级货主Id
     */
    private Long secOwnerId;

    /**
     * 来源渠道
     */
    private Integer channel = 0;

    /**
     * 产品来源（0：酒批，1：微酒）
     */
    private Integer source = 0;

    /**
     * oms订单id
     */
    private Long omsOrderId;

    /**
     * 销售库存变更消息是否发送给OMS
     */
    private boolean sendMsgToOms = true;

    public boolean isSendMsgToOms() {
        return sendMsgToOms;
    }

    public void setSendMsgToOms(boolean sendMsgToOms) {
        this.sendMsgToOms = sendMsgToOms;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public ProductInventoryChangeRecordPO createChangeRecord() {
        ProductInventoryChangeRecordPO re = new ProductInventoryChangeRecordPO();
        re.setCityId(cityId);
        re.setOrderType(orderType);
        re.setOrderId(orderId);
        re.setOrderNo(orderNo);
        re.setJiupiEventType(jiupiEventType);
        re.setErpEventType(erpEventType);
        re.setDescription(description);
        re.setCreateUser(createUserName);
        return re;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getJiupiEventType() {
        return jiupiEventType;
    }

    public void setJiupiEventType(Integer jiupiEventType) {
        this.jiupiEventType = jiupiEventType;
    }

    public Integer getErpEventType() {
        return erpEventType;
    }

    public void setErpEventType(Integer erpEventType) {
        this.erpEventType = erpEventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOwnType() {
        return ownType;
    }

    public void setOwnType(Integer ownType) {
        this.ownType = ownType;
    }

    public Long getOwnId() {
        return ownId;
    }

    public void setOwnId(Long ownId) {
        this.ownId = ownId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Long getOmsOrderId() {
        return omsOrderId;
    }

    public void setOmsOrderId(Long omsOrderId) {
        this.omsOrderId = omsOrderId;
    }
}

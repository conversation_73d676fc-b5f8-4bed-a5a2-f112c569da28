package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.TranseferUtil;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderState;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;

/**
 * 内配单转入库单工具类
 */
public class InternalOrderDeliveryConvert {

    /**
     * 将内配单转化成入库单.
     *
     * @param orderDTOList
     * @return
     */
    public static List<InStockOrderDTO> convertToInStockOrderDTOList(List<OrderDTO> orderDTOList, Integer cityId,
        Integer warehouseId, Integer fromCity) {
        if (CollectionUtils.isEmpty(orderDTOList)) {
            return null;
        }
        List<InStockOrderDTO> inStockOrderDTOList = new ArrayList<InStockOrderDTO>(orderDTOList.size());
        for (OrderDTO orderDTO : orderDTOList) {
            InStockOrderDTO inStockOrderDTO = convertToInStockOrderDTO(orderDTO);
            if (inStockOrderDTO != null) {
                inStockOrderDTOList.add(inStockOrderDTO);
            }
        }
        inStockOrderDTOList.stream().forEach(d -> {
            // d.setId(Long.valueOf(UuidGenerator.generator(cityId, null)));
            d.setOrgId(cityId); // 入库城市ID
            d.setWarehouseId(warehouseId);
            d.setFromCityId(fromCity);
            d.setCreateBatchNo(false);
            List<InStockOrderItemDTO> inStockOrderItemDTOList = d.getInStockOrderItemDTOList();
            if (CollectionUtils.isNotEmpty(inStockOrderDTOList)) {
                // 设置子项
                inStockOrderItemDTOList.stream().forEach(item -> {
                    // item.setId(Long.valueOf(UuidGenerator.generator(d.getOrgId(), null))); // id
                    item.setOrgId(d.getOrgId());
                    item.setInStockorderId(d.getId());
                    List<InStockOrderItemDetailDTO> detailDTOList = item.getItemDetailDTOList();
                    if (CollectionUtils.isNotEmpty(detailDTOList)) {
                        detailDTOList.stream().filter(Objects::nonNull).forEach(detail -> {
                            detail.setOrgId(d.getOrgId());
                            detail.setInStockOrderItemId(item.getId());
                        });
                    }
                });
            }
        });
        return inStockOrderDTOList;
    }

    public static InStockOrderDTO convertToInStockOrderDTO(OrderDTO order) {
        if (order == null) {
            return null;
        }
        InStockOrderDTO inStockOrderDTO = TranseferUtil.transferObjectIgnoreCase(order, InStockOrderDTO.class);
        if (inStockOrderDTO == null) {
            return null;
        }
        inStockOrderDTO.setState(InStockOrderState.待入库.getType());
        inStockOrderDTO.setCrossWareHouse((byte)0); // 出库单没有此属性,内配单生成默认0
        inStockOrderDTO.setRefOrderId(order.getBusinessId());
        // 子项
        List<InStockOrderItemDTO> items = convertToInStockOrderItemDTOList(order.getItems());
        inStockOrderDTO.setCreatetime(new Date());
        inStockOrderDTO.setLastupdatetime(new Date());
        // 设置子项
        inStockOrderDTO.setInStockOrderItemDTOList(items);
        return inStockOrderDTO;

    }

    public static InStockOrderItemDTO convertToInStockOrderItemDTO(OrderItemDTO itemDTO) {
        if (itemDTO == null) {
            return null;
        }
        InStockOrderItemDTO inStockOrderItemDTO =
            TranseferUtil.transferObjectIgnoreCase(itemDTO, InStockOrderItemDTO.class);
        inStockOrderItemDTO.setCreatetime(new Date());
        inStockOrderItemDTO.setLastupdatetime(new Date());
        inStockOrderItemDTO.setSource(itemDTO.getSource() == null ? (byte)0 : itemDTO.getSource().byteValue());
        inStockOrderItemDTO.setChannel(itemDTO.getChannel() == null ? (byte)0 : itemDTO.getChannel().byteValue());
        inStockOrderItemDTO.setSaleModel(itemDTO.getSaleMode());
        inStockOrderItemDTO.setOwnerId(itemDTO.getOwnerId());
        inStockOrderItemDTO.setSecOwnerId(itemDTO.getSecOwnerId());
        // 设置关联单据
        inStockOrderItemDTO.setRefOrderItemId(itemDTO.getBusinessItemId());
        if (CollectionUtils.isNotEmpty(itemDTO.getItemDetailList())) {
            List<InStockOrderItemDetailDTO> detailDTOList =
                itemDTO.getItemDetailList().stream().filter(Objects::nonNull).map(dt -> {
                    InStockOrderItemDetailDTO itemDetailDTO =
                        TranseferUtil.transferObjectIgnoreCase(dt, InStockOrderItemDetailDTO.class);
                    itemDetailDTO.setInStockOrderItemId(inStockOrderItemDTO.getId());
                    return itemDetailDTO;
                }).collect(Collectors.toList());
            inStockOrderItemDTO.setItemDetailDTOList(detailDTOList);
        }
        return inStockOrderItemDTO;
    }

    public static List<InStockOrderItemDTO> convertToInStockOrderItemDTOList(List<OrderItemDTO> orderItemDTOList) {

        if (orderItemDTOList == null) {
            return null;
        }
        List<InStockOrderItemDTO> inStockOrderItemPOList = new ArrayList<InStockOrderItemDTO>(orderItemDTOList.size());
        for (OrderItemDTO itemDTO : orderItemDTOList) {
            InStockOrderItemDTO inStockOrderItemDTO = convertToInStockOrderItemDTO(itemDTO);
            if (inStockOrderItemDTO != null) {
                inStockOrderItemPOList.add(inStockOrderItemDTO);
            }
        }
        return inStockOrderItemPOList;
    }

}

/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.domain.schedule;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * ERP库存定时同步
 */
@Service
public class InventorySnapService {

    @Reference(timeout = 600000)
    private IWarehouseInventoryCheckService iWarehouseInventoryCheckService;

    private static final List<Integer> lstCityId = Collections.singletonList(10000);

    private static final Logger LOGGER = LoggerFactory.getLogger(InventorySnapService.class);

    // 指定每晚1点
    // @Scheduled(cron = "0 0 1 * * ?")
    @XxlJob("wms_syncSnapInventory")
    @Async
    public void syncInventory() {
        LOGGER.info("开始记录库存快照");
        checkStoreInventoryByOwner();

        // 记录指定城市（微酒等）
        for (Integer cityId : lstCityId) {
            try {
                LOGGER.info("开始记录库存快照：{}", cityId);
                updateStoreInventoryByCityId(cityId);
                LOGGER.info("结束记录库存快照：{}", cityId);
            } catch (Exception e) {
                LOGGER.error(String.format("CityId:%s 记录库存快照出错！", cityId), e);
            }
        }
        LOGGER.info("结束记录库存快照");
    }

    /**
     * 记录有货主的产品
     */
    @Async
    private void checkStoreInventoryByOwner() {
        iWarehouseInventoryCheckService.checkStoreInventoryByOwner(1);
    }

    /**
     * 根据城市id,根据ERP库存更新仓库库存
     *
     * @param cityId
     */
    public void updateStoreInventoryByCityId(Integer cityId) {
        iWarehouseInventoryCheckService.checkStoreInventoryByCityId(cityId, 1, true);
    }

}

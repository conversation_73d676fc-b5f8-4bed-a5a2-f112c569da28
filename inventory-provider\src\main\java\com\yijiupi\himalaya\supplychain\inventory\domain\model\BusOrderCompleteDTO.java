package com.yijiupi.himalaya.supplychain.inventory.domain.model;

import java.io.Serializable;

/**
 * Created by 余明 on 2019-01-30.
 */
public class BusOrderCompleteDTO implements Serializable {

    /**
     * 订单Id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单所属城市
     */
    private Integer cityId;

    /**
     * 取货仓库ID.
     */
    private Integer pickupWarehouseId;

    /**
     * 订单类型
     */
    private Integer jiupiOrderType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getPickupWarehouseId() {
        return pickupWarehouseId;
    }

    public void setPickupWarehouseId(Integer pickupWarehouseId) {
        this.pickupWarehouseId = pickupWarehouseId;
    }

    public Integer getJiupiOrderType() {
        return jiupiOrderType;
    }

    public void setJiupiOrderType(Integer jiupiOrderType) {
        this.jiupiOrderType = jiupiOrderType;
    }
}

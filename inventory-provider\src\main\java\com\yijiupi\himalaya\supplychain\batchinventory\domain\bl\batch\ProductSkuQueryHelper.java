package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchLocationInfoQueryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.PageHelperUtils;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-01-23 17:35
 **/
@Service
@SuppressWarnings("NonAsciiCharacters")
public class ProductSkuQueryHelper {

    @Resource
    private BatchInventoryProductSkuMapper batchInventoryProductSkuMapper;

    private static final int LIMIT_COUNT = 400;

    private static final List<Integer> SUB_CATEGORIES = Arrays.asList(
            LocationAreaEnum.存储区.getType(), LocationAreaEnum.拣货区.getType(), LocationAreaEnum.零拣区.getType()
    );

    private static final Logger logger = LoggerFactory.getLogger(ProductSkuQueryHelper.class);

    public List<BatchLocationInfoDTO> listLocationInfoBySkuList(
            Collection<BatchLocationInfoQueryPO> query, Integer warehouseId, Integer subcategory
    ) {
        long start = System.currentTimeMillis();
        List<BatchLocationInfoDTO> pageResult = PageHelperUtils.splitPageQuery(new ArrayList<>(query), LIMIT_COUNT, it -> {
            if (subcategory == null) {
                return batchInventoryProductSkuMapper.findBatchDTOBySkuList(it, warehouseId);
            } else if (subcategory == LocationAreaEnum.存储区.getType().intValue()) {
                return batchInventoryProductSkuMapper.findBatchBySkuListAndCategory(it, SUB_CATEGORIES, warehouseId);
            } else {
                return batchInventoryProductSkuMapper.findBatchDTOBySkuListAndCategory(it, subcategory, warehouseId);
            }
        });
        long costMs = System.currentTimeMillis() - start;
        if (TimeUnit.MILLISECONDS.toMinutes(costMs) >= 1) {
            logger.warn(
                    "通过 skuId 查询批次货位花费时间大于 1 分钟, 入参: {}, warehouseId: {}, subcategory: {}",
                    JSON.toJSONString(query), warehouseId, subcategory
            );
        }
        return pageResult;
    }

}

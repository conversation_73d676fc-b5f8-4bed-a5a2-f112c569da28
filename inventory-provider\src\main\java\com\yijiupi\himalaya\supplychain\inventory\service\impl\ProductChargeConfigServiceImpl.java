package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreQuery;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductChargeConfigBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductChargeConfigService;

/**
 * 产品费用配置
 * 
 * @author: lidengfeng
 * @date 2018/9/27 17:35
 */
@Service
public class ProductChargeConfigServiceImpl implements IProductChargeConfigService {

    @Autowired
    private ProductChargeConfigBL productChargeConfigBL;

    /**
     * 新增或修改产品标准费率
     * 
     * @param dto
     * @return: void
     */
    @Override
    public void saveOrUpdateChargeConfig(ProductChargeConfigDTO dto) {
        productChargeConfigBL.saveOrUpdateChargeConfig(dto);
    }

    /**
     * 批量新增产品标准费率
     * 
     * @param list
     */
    @Override
    public void saveProductChargeConfig(List<ProductChargeConfigDTO> list) {
        productChargeConfigBL.saveProductChargeConfig(list);
    }

    /**
     * 产品标准费率明细查询
     * 
     * @param productCountQuery
     * @return
     */
    @Override
    public ProductChargeConfigDTO selectProductChargeConfigById(ProductCountQuery productCountQuery) {
        AssertUtils.notNull(productCountQuery.getFacilitatorId(), "服务商id不能为空");
        return productChargeConfigBL.selectProductChargeConfigById(productCountQuery);
    }

    /**
     * 根据产品id集合查询标准费率
     * 
     * @param
     * @return
     */
    @Override
    public Map<Long, ProductChargeConfigDTO> selectProductChargeList(ProductChargeQuery productChargeQuery) {
        AssertUtils.notNull(productChargeQuery.getProductSpecificationIdList(), "产品id集合不能为空");
        return productChargeConfigBL.selectProductChargeList(productChargeQuery);
    }

    /**
     * 得到产品费率是否配置
     * 
     * @param
     * @return
     */
    @Override
    public Boolean selectCountByProductId(ProductCountQuery productCountQuery) {
        AssertUtils.notNull(productCountQuery.getProductSpecificationId(), "产品规格参数id不能为空");
        AssertUtils.notNull(productCountQuery.getFacilitatorId(), "服务商id不能为空");
        return productChargeConfigBL.selectCountByProductId(productCountQuery);
    }

    /**
     * 获取 经销商详情的托管商品
     * 
     * @param dealerProductQuery
     * @return
     */
    @Override
    public PageList<DealerProductDTO> selectDealerProductList(DealerProductQuery dealerProductQuery) {
        AssertUtils.notNull(dealerProductQuery.getFacilitatorId(), "服务商id不能为空");
        return productChargeConfigBL.selectDealerProductList(dealerProductQuery);
    }

    /**
     * 查询申请入库商品和经销商的费用配置 经销商仓配
     * 
     * @param productInStockQuery
     * @return
     */
    @Override
    public PageList<ProductStoreStockDTO> selectProductInStockList(ProductInStockQuery productInStockQuery) {
        AssertUtils.notNull(productInStockQuery.getFacilitatorId(), "服务商id不能为空");
        return productChargeConfigBL.selectProductInStockList(productInStockQuery);
    }

    /**
     * 查询申请入库商品和经销商的费用配置明细 经销商仓配申请入库产品
     * 
     * @param dealerProductDetailQuery
     * @return
     */
    @Override
    public ProductStoreStockDTO selectDealerProductDetail(DealerProductDetailQuery dealerProductDetailQuery) {
        AssertUtils.notNull(dealerProductDetailQuery.getProductSpecificationId(), "规格参数id不能为空");
        return productChargeConfigBL.selectDealerProductDetail(dealerProductDetailQuery);
    }

    /**
     * 根据经销商id，仓库id查询仓库商品的库存
     * 
     * @param productStockStoreQuery
     * @return
     */
    @Override
    public PageList<ProductStockStoreDTO> findProductStoreList(ProductStockStoreQuery productStockStoreQuery) {
        AssertUtils.notNull(productStockStoreQuery.getWarehouseId(), "仓库id不能为空");
        return productChargeConfigBL.findProductStoreList(productStockStoreQuery);
    }

    /**
     * 启用停用经销商费用配置
     *
     * @param dto
     * @return
     */
    @Override
    public void updateProductConfigStatus(ProductChargeConfigDTO dto) {
        AssertUtils.notNull(dto.getFacilitatorId(), "服务商id不能为空");
        productChargeConfigBL.updateProductConfigStatus(dto);
    }

    /**
     * 查询产品
     * 
     * @param productProDetailQuery
     * @return
     */
    @Override
    public PageList<ProductProDetailDTO> findProductProList(ProductProDetailQuery productProDetailQuery) {
        return productChargeConfigBL.findProductProList(productProDetailQuery);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductStoreChangeRecordBL;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductInventoryRecordManagerService;

@Service(timeout = 30000)
public class ProductInventoryRecordManagerServiceImpl implements IProductInventoryRecordManagerService {

    @Autowired
    private ProductStoreChangeRecordBL productStoreChangeRecordBL;

    /**
     * 批量新增仓库库存变更记录
     * 
     * @param productInventoryChangeRecordDTOList
     */
    @Override
    public void
        saveProductStoreChangeRecord(List<ProductInventoryChangeRecordDTO> productInventoryChangeRecordDTOList) {
        productStoreChangeRecordBL.saveProductInventoryChangeRecordBatch(productInventoryChangeRecordDTOList);
    }

    /**
     * 更新仓库库存变更记录
     */
    @Override
    public void updateProductInventoryChangeRecord(ProductInventoryChangeRecordDTO dto) {
        productStoreChangeRecordBL.updateProductInventoryChangeRecord(dto);
    }
}

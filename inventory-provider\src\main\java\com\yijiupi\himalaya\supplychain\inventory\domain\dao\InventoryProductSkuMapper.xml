<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventoryProductSkuMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="Name" property="name" jdbcType="VARCHAR"/>
        <result column="City_id" property="cityId" jdbcType="INTEGER"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="ProductSku_Id" property="productSkuId" jdbcType="BIGINT"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="INTEGER"/>
        <result column="Company_Id" property="companyId" jdbcType="BIGINT"/>
        <result column="OwnerType" property="ownerType" jdbcType="INTEGER"/>
        <result column="OwnerName" property="ownerName" jdbcType="VARCHAR"/>
        <result column="secOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
    </resultMap>
    <select id="getProductSkuBySkuId" resultMap="BaseResultMap">
        SELECT
        id,
        Name,
        City_id,
        ProductSpecification_Id,
        ProductSku_Id,
        specificationName,
        packageName,
        unitName,
        packageQuantity,
        Source,
        Company_Id,
        OwnerName,
        secOwner_Id
        FROM
        productsku
        WHERE
        ProductSku_Id = #{productSkuId,jdbcType=INTEGER}
    </select>
    <select id="getAnotherCityProductSkuId" resultType="java.lang.Long">
        SELECT
        a.ProductSku_Id
        from productsku a
        inner join productsku b on a.productspecification_id = b.productspecification_id
        WHERE
        b.ProductSku_Id = #{productSkuId,jdbcType=INTEGER}
        and
        a.city_id = #{cityId,jdbcType=INTEGER}
    </select>

    <select id="getActualDeliverySkuIdS" resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ActualSkuPO">
        SELECT
        a.ProductSku_Id AS actualDeliverySkuId,
        b.ProductSku_Id AS orderSkuId,
        a.productState
        from productsku a
        RIGHT join productsku b on a.productspecification_id = b.productspecification_id
        AND ((a.Company_Id is null and b.Company_Id is null) or (a.Company_Id = b.Company_Id))
        AND ((a.secOwner_Id is null and b.secOwner_Id is null) or (a.secOwner_Id = b.secOwner_Id))
        and a.city_id = #{cityId,jdbcType=INTEGER}
        WHERE
        b.ProductSku_Id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by a.productState desc, a.ProductSku_Id
    </select>

    <select id="getNormalProductStateSkuIdS"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.ActualSkuPO">
        SELECT
        ifnull(a.ProductSku_Id,b.ProductSku_Id) AS actualDeliverySkuId,
        b.ProductSku_Id AS orderSkuId
        from productsku a
        RIGHT join productsku b on a.productspecification_id = b.productspecification_id
        and a.city_id = b.city_id
        AND ((a.Company_Id is null and b.Company_Id is null) or (a.Company_Id = b.Company_Id))
        AND ((a.secOwner_Id is null and b.secOwner_Id is null) or (a.secOwner_Id = b.secOwner_Id))
        AND a.productstate != 1 and a.IsDelete = 0
        WHERE
        b.ProductSku_Id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and b.ProductState != 2
    </select>
    <select id="getExitsProductSkuIds" resultType="java.lang.Long">
        SELECT
        ProductSku_Id
        from productsku
        WHERE
        ProductSku_Id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getProductSkuListByIds" resultMap="BaseResultMap">
        SELECT
        sku.id,
        sku.Name,
        sku.City_id,
        sku.ProductSpecification_Id,
        sku.ProductSku_Id,
        sku.specificationName,
        sku.packageName,
        sku.unitName,
        sku.packageQuantity,
        sku.Source,
        sku.Company_Id,
        IFNULL(own.ownerType,0) as OwnerType,
        sku.OwnerName,
        sku.secOwner_Id
        FROM
        productsku sku
        LEFT JOIN owner own on sku.company_id is not null and sku.company_Id = own.id
        WHERE
        ProductSku_Id in
        <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--    &lt;!&ndash;批量查询库存&ndash;&gt;-->
    <!--    <select id="getInventoryProductSkuMap" resultType="java.util.Map">-->
    <!--        SELECT-->
    <!--        psku.ProductSku_Id as productSkuId,-->
    <!--        sum(ps.TotalCount_MinUnit) as sumInventory-->
    <!--        FROM-->
    <!--        productsku psku-->
    <!--        INNER JOIN productstore ps ON psku.ProductSpecification_Id = ps.ProductSpecification_Id-->
    <!--        AND psku.City_Id = ps.City_Id-->
    <!--        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))-->
    <!--        AND ((psku.secOwner_Id is null and ps.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))-->
    <!--        WHERE-->
    <!--        ps.Channel=#{channel,jdbcType=INTEGER}-->
    <!--        AND psku.productsku_id in-->
    <!--        <foreach collection="set" index="index" item="item" open="(" separator="," close=")">-->
    <!--            #{item}-->
    <!--        </foreach>-->
    <!--        <if test="secOwnerId!=null">-->
    <!--            and ps.SecOwner_Id = #{secOwnerId,jdbcType=BIGINT}-->
    <!--        </if>-->
    <!--        group by psku.ProductSku_Id-->
    <!--    </select>-->
    <select id="getSpecificationInfo"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.SpecificationInfoPO">
        SELECT packageName,unitName,packageQuantity
        from productsku
        where ProductSpecification_Id = #{prodcutInfoSpecId,jdbcType=BIGINT}
        LIMIT 1
    </select>
    <select id="getSpecificationInfoByStoreId"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.SpecificationInfoPO">
        SELECT psku.packageName,psku.unitName,psku.packageQuantity from productstore ps
        INNER JOIN productsku psku on ps.ProductSpecification_Id = psku.ProductSpecification_Id
        and ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where ps.id = #{productStoreId} LIMIT 1
    </select>

    <!-- 产品查询 -->
    <select id="listProductSkuInfo" resultType="com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoDTO">
        SELECT
        sku.ProductSku_Id AS productSkuId,
        sku.Name AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageName,
        sku.unitName,
        sku.packageQuantity,
        sku.Company_Id as ownerId,
        sku.OwnerName as ownerName,
        sku.MonthOfShelfLife as monthOfShelfLife,
        sku.ShelfLifeUnit as shelfLifeUnit,
        sku.ProductState as productState,
        ps.TotalCount_MinUnit as unitTotolCount
        FROM productsku sku

        INNER JOIN productskuconfig pc on pc.ProductSku_Id = sku.ProductSku_Id and pc.State = 1
            and pc.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}

        LEFT JOIN productstore ps ON ps.productspecification_id = sku.productspecification_id
        <if test="eraseOwnerId == null or eraseOwnerId == 0">
            AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
            AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        </if>
        AND ps.Channel = 0
        AND ps.city_id = sku.city_id
        <if test="warehouseId != null">
            AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <where>
            <if test="cityId != null">
                sku.city_id in (#{cityId,jdbcType=INTEGER}, 10000)
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and sku.name like concat('%',#{productSkuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productSkuIdList != null">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="withoutLocation != null and withoutLocation">
                and not exists ( select pl.ProductSku_Id from productlocation pl where pl.ProductSku_Id =
                sku.ProductSku_Id )
            </if>
            <if test="storeType != null and storeType == 1">
                and ps.TotalCount_MinUnit <![CDATA[ > ]]> 0
            </if>
            <if test="storeType != null and storeType == 2">
                and ps.TotalCount_MinUnit <![CDATA[ <= ]]> 0
            </if>
            <if test="storeType != null and storeType == 2">
                and ps.TotalCount_MinUnit <![CDATA[ <= ]]> 0
            </if>
            <if test="deleted != null and deleted.size > 0">
                and sku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 产品SKU基础信息查询 -->
    <select id="findProductBaseInfoByStoreCheck"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoDTO">
        SELECT
        sku.ProductSku_Id AS productSkuId,
        sku.Name AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageName,
        sku.unitName,
        sku.packageQuantity,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.Company_Id as ownerId,
        sku.secOwner_Id as secOwnerId,
        sku.OwnerName as ownerName,
        sku.ProductState as productState,
        sku.Source as source,
        cfg.ProductFeature as productFeature,
        IFNULL(info.MonthOfShelfLife, sku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit, sku.ShelfLifeUnit) as shelfLifeUnit,
        info.ShelfLifeLongTime
        from productsku sku
        INNER JOIN productinfo info on sku.ProductInfo_Id = info.Id
        <if test="storeType != null or ownerType != null">
            INNER
        </if>
        <if test="storeType == null and ownerType == null">
            LEFT
        </if>
        JOIN productstore ps ON ps.productspecification_id = sku.productspecification_id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        AND ps.city_id = sku.city_id
        AND ps.Channel = 0
        <if test="warehouseId != null">
            AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        </if>
        <if test="ownerType != null">
            and ps.OwnerType = #{ownerType,jdbcType=INTEGER}
        </if>
        <if test="warehouseId != null">
            INNER JOIN productskuconfig cfg ON cfg.ProductSku_Id = sku.ProductSku_Id
                AND cfg.Warehouse_Id = #{warehouseId,jdbcType=INTEGER} and cfg.State = 1
            <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
                and cfg.storageAttribute = #{warehouseAllocationType, jdbcType=INTEGER}
            </if>
        </if>
        <where>
            <if test="cityId != null">
                and sku.city_id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and sku.name like concat('%',#{productSkuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productSkuIdList != null">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="storeType != null and storeType == 1">
                and ps.TotalCount_MinUnit > 0
            </if>
            <if test="storeType != null and storeType == 2">
                and ps.TotalCount_MinUnit = 0
            </if>
            <if test="deleted != null and deleted.size > 0">
                and sku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
        ORDER BY ps.TotalCount_MinUnit DESC, sku.ProductSku_Id
    </select>

    <!-- 产品SKU基础信息查询 -->
    <select id="findProductBaseInfo" resultType="com.yijiupi.himalaya.supplychain.dto.product.ProductSkuInfoDTO">
        SELECT
        sku.ProductSku_Id AS productSkuId,
        sku.Name AS productName,
        sku.specificationName,
        sku.SaleModel AS saleModel,
        sku.packageName,
        sku.unitName,
        sku.packageQuantity,
        sku.ProductSpecification_Id as productSpecificationId,
        sku.Company_Id as ownerId,
        sku.secOwner_Id as secOwnerId,
        sku.OwnerName as ownerName,
        sku.ProductState as productState,
        sku.Source as source,
        IFNULL(info.MonthOfShelfLife, sku.MonthOfShelfLife) as monthOfShelfLife,
        IFNULL(info.ShelfLifeUnit, sku.ShelfLifeUnit) as shelfLifeUnit,
        info.ShelfLifeLongTime
        <if test="warehouseId != null">
            , cfg.ProductFeature as productFeature
        </if>
        from productsku sku
        INNER JOIN productinfo info on sku.ProductInfo_Id = info.Id
        <if test="storeType != null or ownerType != null">
            INNER JOIN
        </if>
        <if test="storeType == null and ownerType == null">
            LEFT JOIN
        </if>
        (
        SELECT
        psku.ProductSku_Id, psku.City_Id, SUM(ps.TotalCount_MinUnit) AS TotalCount_MinUnit
        FROM productsku psku
        INNER JOIN productstore ps ON ps.productspecification_id = psku.productspecification_id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND ps.city_id = psku.city_id
        AND ps.Channel = 0
        <where>
            <if test="warehouseId != null">
                AND ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and psku.city_id in (#{cityId,jdbcType=INTEGER}, 10000)
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and psku.name like concat('%',#{productSkuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productSkuIdList != null">
                AND psku.ProductSku_Id in
                <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="source != null">
                and psku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="storeType != null and storeType == 1">
                and ps.TotalCount_MinUnit > 0
            </if>
            <if test="storeType != null and storeType == 2">
                and ps.TotalCount_MinUnit = 0
            </if>
            <if test="ownerType != null">
                and ps.OwnerType = #{ownerType,jdbcType=INTEGER}
            </if>
        </where>
        GROUP BY psku.ProductSku_Id, psku.City_Id
        ) tmp on sku.City_Id = tmp.City_Id and sku.ProductSku_Id = tmp.ProductSku_Id
        <if test="warehouseId != null">
            <if test="warehouseAllocationType == null or warehouseAllocationType == ''">
                LEFT JOIN productskuconfig cfg ON cfg.ProductSku_Id = sku.ProductSku_Id
                    AND cfg.Warehouse_Id = #{warehouseId,jdbcType=INTEGER} and cfg.State = 1
            </if>

            <if test="warehouseAllocationType != null and warehouseAllocationType != ''">
                inner JOIN productskuconfig cfg ON cfg.ProductSku_Id = sku.ProductSku_Id
                    AND cfg.Warehouse_Id = #{warehouseId,jdbcType=INTEGER} and cfg.State = 1
                and cfg.storageAttribute = #{warehouseAllocationType, jdbcType=INTEGER}
            </if>
        </if>
        <where>
            <if test="cityId != null">
                and sku.city_id in (#{cityId,jdbcType=INTEGER}, 10000)
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and sku.name like concat('%',#{productSkuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productSkuIdList != null">
                AND sku.ProductSku_Id in
                <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="source != null">
                and sku.Source = #{source,jdbcType=INTEGER}
            </if>
            <if test="deleted != null and deleted.size > 0">
                and sku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
        ORDER BY tmp.TotalCount_MinUnit DESC, sku.ProductSku_Id
    </select>
    <select id="queryInventoryProperty"
            resultType="com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO">
        select ProductSku_Id as productSkuId,inventoryPinProperty as inventoryRatio
        from sc_product.productskuconfig where ProductSku_Id in
        <foreach collection="productSkuIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and Warehouse_Id =#{warehouseId};
    </select>
</mapper>
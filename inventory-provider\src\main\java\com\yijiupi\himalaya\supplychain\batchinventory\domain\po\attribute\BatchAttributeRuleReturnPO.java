package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationReturnPO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeRuleReturnPO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 适用仓库仓库
     */
    private List<BatchAttributeRuleRelationReturnPO> relationList;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 模板id
     */
    public Long getTemplateId() {
        return this.templateId;
    }

    /**
     * 设置 模板id
     */
    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    /**
     * 获取 模板名称
     */
    public String getTemplateName() {
        return this.templateName;
    }

    /**
     * 设置 模板名称
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * 获取 备注
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * 设置 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 创建时间
     */
    public String getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 适用仓库仓库
     */
    public List<BatchAttributeRuleRelationReturnPO> getRelationList() {
        return this.relationList;
    }

    /**
     * 设置 适用仓库仓库
     */
    public void setRelationList(List<BatchAttributeRuleRelationReturnPO> relationList) {
        this.relationList = relationList;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStoreChangeRecordPOMapper">

    <select id="findProductStoreRecordList"
            resultType="com.yijiupi.himalaya.supplychain.dto.easysell.ProductStoreChangeDetailDTO">
        select
        distinct
        psku.Name as name,
        psku.specificationName as specificationname,
        psku.packageName as packagename ,
        psku.unitName as unitName,
        psku.packageQuantity as packagequantity,
        pscr.OrderType as ordertype,
        pscr.TotalCount as totalcount,
        pscr.SourceTotalCount as sourcetotalcount,
        pscr.CreateTime as createtime
        from productstore ps
        inner join productsku psku on ps.ProductSpecification_Id = psku.ProductSpecification_Id
        inner join productstorechangerecord pscr on ps.id = pscr.ProductStore_Id
        where
        ps.Warehouse_Id =#{warehouseId,jdbcType=INTEGER}
        and ps.Owner_Id = #{shopId,jdbcType=BIGINT}
        and psku.ProductSpecification_Id= #{productSpecificationId}
        <if test="productSkuId != null">
            and psku.ProductSku_Id = #{productSkuId}
        </if>

        order by pscr.CreateTime desc
    </select>

    <select id="findStoreChangeOrderNos" resultType="java.lang.String">
        select OrderNo
        from productstorechangerecord
        where OrderNo in
        <foreach collection="orderNos" item="orderNo" separator="," open="(" close=")">
            #{orderNo}
        </foreach>
        <if test="excludeUser != null">
            and (CreateUser <![CDATA[ != ]]> #{excludeUser,jdbcType=VARCHAR} OR CreateUser IS NULL)
        </if>
    </select>
    <select id="findBatchInventoryByOrderNos"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.batch.BatchProductStoreChangeRecordPO">
        select psr.City_Id as cityId, ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId, ps.SecOwner_Id as secOwnerId, ps.ProductSpecification_Id as specId,
        psr.JiupiEventType as jiupiEventType, psr.ERPEventType as erpEventType, psr.OrderType as orderType,
        psr.Order_Id as orderId, psr.OrderNo as orderNo, psbr.totalcount_minunit as changeCount,
        psr.CreateUser as createUser, ps.OwnerType as ownerType, psr.source,
        psb.location_id as locationId, psb.location_name as locationName,
        psb.batchtime as batchTime, psb.productiondate as productionDate, psb.expiretime as expireTime,
        psb.id as productStoreBatchId, psb.BatchAttributeInfoNo,psb.createtime as batchCreatetime,
        psku.ProductSku_Id as productSkuId
        from productstorechangerecord psr
        inner join productstorebatchchangerecord psbr on psr.id = psbr.changerecord_id
        inner join productstorebatch psb on psb.id = psbr.batch_id
        inner join productstore ps on ps.Id = psb.productstore_id
        left join productsku psku on psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where psr.OrderNo in
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findBatchInventory"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.batch.BatchProductStoreChangeRecordPO">
        select psr.City_Id as cityId, ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId, ps.SecOwner_Id as secOwnerId, ps.ProductSpecification_Id as specId,
        psr.JiupiEventType as jiupiEventType, psr.ERPEventType as erpEventType, psr.OrderType as orderType,
        psr.Order_Id as orderId, psr.OrderNo as orderNo, psbr.totalcount_minunit as changeCount,
        psr.CreateUser as createUser, ps.OwnerType as ownerType, psr.source,
        psb.location_id as locationId, psb.location_name as locationName,
        psb.batchtime as batchTime, psb.productiondate as productionDate, psb.expiretime as expireTime,
        psb.id as productStoreBatchId, psb.BatchAttributeInfoNo,psb.createtime as batchCreatetime,
        psku.ProductSku_Id as productSkuId
        from productstorechangerecord psr force index (idx_OrderNo)
        inner join productstorebatchchangerecord psbr on psr.id = psbr.changerecord_id
        inner join productstorebatch psb on psb.id = psbr.batch_id
        inner join productstore ps on ps.Id = psb.productstore_id
        left join productsku psku on psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where psr.OrderNo in
        <foreach collection="dto.orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        <if test="dto.specificationIds != null and dto.specificationIds.size() > 0">
            and ps.ProductSpecification_Id in
            <foreach collection="dto.specificationIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>
    <select id="queryCountBatchInventoryByOrderNo" resultType="java.lang.Long">
        select count(1) from (
        select psr.City_Id as cityId, ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId, ps.SecOwner_Id as secOwnerId, ps.ProductSpecification_Id as specId,
        psr.JiupiEventType as jiupiEventType, psr.ERPEventType as erpEventType, psr.OrderType as orderType,
        psr.Order_Id as orderId, psr.OrderNo as orderNo, psbr.totalcount_minunit as changeCount,
        psr.CreateUser as createUser, ps.OwnerType as ownerType, psr.source,
        psb.location_id as locationId, psb.location_name as locationName,
        psb.batchtime as batchTime, psb.productiondate as productionDate, psb.expiretime as expireTime,
        psb.id as productStoreBatchId, psb.BatchAttributeInfoNo,psb.createtime as batchCreatetime,
        psku.ProductSku_Id as productSkuId
        from productstorechangerecord psr
        inner join productstorebatchchangerecord psbr on psr.id = psbr.changerecord_id
        inner join productstorebatch psb on psb.id = psbr.batch_id
        inner join productstore ps on ps.Id = psb.productstore_id
        left join productsku psku on psku.ProductSpecification_Id = ps.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where psr.OrderNo in
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        )as A ;
    </select>

    <select id="findBatchInventoryProductionDate"
            resultType="com.yijiupi.himalaya.supplychain.inventory.domain.po.batch.BatchProductStoreChangeRecordPO">
        select psr.City_Id as cityId, ps.Warehouse_Id as warehouseId,
        ps.Owner_Id as ownerId, ps.SecOwner_Id as secOwnerId, ps.ProductSpecification_Id as specId,
        psr.JiupiEventType as jiupiEventType, psr.ERPEventType as erpEventType, psr.OrderType as orderType,
        psr.Order_Id as orderId, psr.OrderNo as orderNo, psbr.totalcount_minunit as changeCount,
        psr.CreateUser as createUser, ps.OwnerType as ownerType, psr.source,
        psb.location_id as locationId, psb.location_name as locationName,
        psb.batchtime as batchTime, psb.productiondate as productionDate, psb.expiretime as expireTime,
        psb.id as productStoreBatchId, psb.BatchAttributeInfoNo,psb.createtime as batchCreatetime
        from productstorechangerecord psr
        inner join productstorebatchchangerecord psbr on psr.id = psbr.changerecord_id
        inner join productstorebatch psb on psb.id = psbr.batch_id
        inner join productstore ps on ps.Id = psb.productstore_id
        where psr.OrderNo in
        <foreach collection="dto.orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        <if test="dto.specificationIds != null and dto.specificationIds.size() > 0">
            and ps.ProductSpecification_Id in
            <foreach collection="dto.specificationIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>
</mapper>
package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.easysell.*;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.easysell.EasySellProductStockQueryBL;

/**
 * @author: lidengfeng
 * @date 2018/9/10 9:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EasySellProductStockQueryBLTest {

    @Autowired
    private EasySellProductStockQueryBL easySellProductStockQueryBL;

    /**
     * 易经销根据仓库id,经销商id查询库存
     */
    @Test
    public void findProductStoreList() {
        ProductStoreQueryDTO productStoreQueryDTO = new ProductStoreQueryDTO();
        productStoreQueryDTO.setWarehouseId(7501);
        // productStoreQueryDTO.setOwnerId(Long.valueOf(146));
        PageList<ProductStoreReturnDTO> storeList =
            easySellProductStockQueryBL.findProductStoreList(productStoreQueryDTO);
        System.out.println(JSON.toJSONString(storeList));

    }

    /**
     * 根据商品id，经销商Id查询库存
     */
    @Test
    public void findProductInfoStoreList() {
        ProductInfoStoreQueryDTO productInfoStoreQueryDTO = new ProductInfoStoreQueryDTO();
        // productInfoStoreQueryDTO.setOwnerId(Long.valueOf(146));
        // productInfoStoreQueryDTO.setProductskuId(70400016764270L);
        PageList<ProductInfoStoreDTO> productInfoStoreList =
            easySellProductStockQueryBL.findProductInfoStoreList(productInfoStoreQueryDTO);
        System.out.println(JSON.toJSONString(productInfoStoreList));

    }

    /**
     * 根据商品skuId、经销商、仓库编号查询库存出入库历史记录明细
     */
    @Test
    public void findProductStoreRecordList() {
        ProductStoreChangeDetailQueryDTO queryDTO = new ProductStoreChangeDetailQueryDTO();
        // queryDTO.setOwnerId(146);
        queryDTO.setWarehouseId(7501);
        // queryDTO.setProductSkuId(70400016764270L);
        // PageList<ProductStoreChangeDetailDTO> productStoreRecordList =
        // easySellProductStockQueryBL.findProductStoreRecordList(queryDTO);
        // System.out.println(JSON.toJSONString(productStoreRecordList));
    }
}
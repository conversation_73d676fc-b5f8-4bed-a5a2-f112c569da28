package com.yijiupi.himalaya.supplychain.inventory.domain.bl.fee;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.wmsdubbop.adapter.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.wmsdubbop.config.ServerPath;
import com.yijiupi.himalaya.supplychain.wmsdubbop.dto.ProductSku;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.gateway.client.annotation.ReferGateway;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductSkuQueryBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryChangeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryStorageDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;

/**
 * 库存费用查询BL
 *
 * <AUTHOR> 2018/2/2
 */
@Service
public class InventoryFeeBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryFeeBL.class);

    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;
    @Autowired
    private InventoryProductStoreBatchMapper inventoryProductStoreBatchMapper;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    @ReferGateway(path = ServerPath.BASE_SERVICE)
    private IProductSkuQueryService iProductSkuQueryService;

    @Value("${deliveryFeeDefault}")
    private BigDecimal deliveryFeeDefault;
    @Value("${warehouseCustodyFeeDefault}")
    private BigDecimal warehouseCustodyFeeDefault;

    @Autowired
    private ProductInventoryChangeRecordPOMapper productInventoryChangeRecordPOMapper;

    /**
     * 查询配送费,托管费(该订单的总数)
     *
     * @param inventoryFeeQueryDTOS
     * @return
     */
    public InventoryFeeDTO findInventoryFeeSum(List<InventoryFeeQueryDTO> inventoryFeeQueryDTOS) {
        InventoryFeeDTO inventoryFeeDTO = new InventoryFeeDTO();
        BigDecimal deliveryFeeSum = BigDecimal.valueOf(0);
        BigDecimal warehouseCustodyFeeSum = BigDecimal.valueOf(0);
        List<InventoryFeeDTO> inventoryFeeS = findInventoryFee(inventoryFeeQueryDTOS);
        for (InventoryFeeDTO inventoryFee : inventoryFeeS) {
            deliveryFeeSum = deliveryFeeSum.add(inventoryFee.getDeliveryFeeSum());
            warehouseCustodyFeeSum = warehouseCustodyFeeSum.add(inventoryFee.getWarehouseCustodyFeeSum());
        }
        inventoryFeeDTO.setDeliveryFeeSum(deliveryFeeSum);
        inventoryFeeDTO.setWarehouseCustodyFeeSum(warehouseCustodyFeeSum);
        return inventoryFeeDTO;
    }

    /**
     * 查询配送费,托管费
     *
     * @param inventoryFeeQueryDTOS
     * @return
     */
    public List<InventoryFeeDTO> findInventoryFee(List<InventoryFeeQueryDTO> inventoryFeeQueryDTOS) {
        List<InventoryFeeDTO> inventoryFeeDTOS = new ArrayList<>();
        // 按照产品来源进行分组
        Map<Integer, List<InventoryFeeQueryDTO>> map =
            inventoryFeeQueryDTOS.stream().collect(Collectors.groupingBy(InventoryFeeQueryDTO::getSource));
        for (Integer source : map.keySet()) {

            List<InventoryFeeQueryDTO> inventoryFeeQueryDTOList = map.get(source);
            Map<Long, InventoryFeeQueryDTO> inventoryFeeQueryDTOMap = inventoryFeeQueryDTOList.stream()
                .collect(Collectors.toMap(InventoryFeeQueryDTO::getProductSkuId, Function.identity()));

            InventoryFeeQueryDTO dto = inventoryFeeQueryDTOList.get(0);

            // 查询对应po
            List<Long> productSkuList = inventoryFeeQueryDTOList.stream().map(InventoryFeeQueryDTO::getProductSkuId)
                .collect(Collectors.toList());
            List<ProductInventoryPO> productInventoryPOS =
                productInventoryPOMapper.findProductInventoryByProductSkuIdWarehouseId(productSkuList,
                    dto.getWarehouseId(), dto.getChannel(), dto.getSecOwnerId(), null);

            List<Long> skuList =
                inventoryFeeQueryDTOS.stream().map(InventoryFeeQueryDTO::getProductSkuId).collect(Collectors.toList());
            Map<Long, ProductSku> productSkuListMap = iProductSkuQueryService.getProductSkuListMap(skuList);

            // 循环计算每一个产品的配送费,托管费
            for (ProductInventoryPO productInventoryPO : productInventoryPOS) {
                InventoryFeeQueryDTO inventoryFeeQueryDTO =
                    inventoryFeeQueryDTOMap.get(productInventoryPO.getProductSkuId());// 通过skuId将dto与po关联上
                BigDecimal deliveryFeeSum =
                    computeDeliveryFeeSum(productInventoryPO, inventoryFeeQueryDTO, productSkuListMap);
                BigDecimal warehouseCustodyFeeSum =
                    computeWarehouseCustodyFeeSum(productInventoryPO, inventoryFeeQueryDTO);

                InventoryFeeDTO inventoryFeeDTO = new InventoryFeeDTO();
                inventoryFeeDTO.setDeliveryFeeSum(deliveryFeeSum);// 配送费总数
                inventoryFeeDTO.setWarehouseCustodyFeeSum(warehouseCustodyFeeSum);// 托管费
                inventoryFeeDTOS.add(inventoryFeeDTO);
            }
        }
        return inventoryFeeDTOS;
    }

    /**
     * 计算天数差
     *
     * @return
     */
    private int differentDaysByMillisecond(Date start, Date end) {
        long difference = (start.getTime() - end.getTime()) / 86400000;
        return (int)Math.ceil(Math.abs(difference));
    }

    /**
     * 计算配送费用
     */
    private BigDecimal computeDeliveryFeeSum(ProductInventoryPO po, InventoryFeeQueryDTO inventoryFeeQueryDTO,
        Map<Long, ProductSku> productSkuListMap) {
        BigDecimal packageQuantity = po.getPackageQuantity();// 转化系数
        Integer deliveryPayType = po.getDeliveryPayType();
        BigDecimal deliveryFee = po.getDeliveryFee();
        BigDecimal deliveryFeeSum = null;// 配送费总数
        if (deliveryFee == null || BigDecimal.valueOf(0).equals(deliveryFee)) {
            deliveryFee = deliveryFeeDefault;
        }
        BigDecimal deliveryPackageCount =
            inventoryFeeQueryDTO.getCount().divide(packageQuantity, 6, BigDecimal.ROUND_UP);
        // (int) Math.ceil((double) inventoryFeeQueryDTO.getCount() / (double) packageQuantity);//配送数量(大单位),向上取整.
        if (Integer.valueOf(0).equals(deliveryPayType)) {// 正常
            deliveryFeeSum =
                deliveryFee.divide(BigDecimal.valueOf(30), 2, BigDecimal.ROUND_HALF_UP).multiply(deliveryPackageCount);
        } else {
            // 如果是百分比 去交易平台查价格 进行计算
            ProductSku productSku = productSkuListMap.get(inventoryFeeQueryDTO.getProductSkuId());
            BigDecimal sellingPrice = productSku.getSellingPrice();
            String sellingPriceUnit = productSku.getSellingPriceUnit();
            String packageName = po.getPackageName();
            String unitName = po.getUnitName();
            if (sellingPriceUnit.equals(packageName)) {
                // 价格(小单位)*配送数量(小单位)
                deliveryFeeSum = deliveryFee.multiply(sellingPrice).multiply(inventoryFeeQueryDTO.getCount());
            } else if (sellingPriceUnit.equals(unitName)) {
                deliveryFeeSum = deliveryFee.multiply(sellingPrice).multiply(deliveryPackageCount);// 价格(大单位)*配送数量(大单位)
            }
        }
        return deliveryFeeSum;
    }

    /**
     * 计算托管费
     */
    private BigDecimal computeWarehouseCustodyFeeSum(ProductInventoryPO po, InventoryFeeQueryDTO inventoryFeeQueryDTO) {
        BigDecimal packageQuantity = po.getPackageQuantity();
        BigDecimal deliveryPackageCount =
            inventoryFeeQueryDTO.getCount().divide(packageQuantity, 6, BigDecimal.ROUND_UP);
        // (int) Math.ceil((double) inventoryFeeQueryDTO.getCount() / (double) packageQuantity);
        BigDecimal warehouseCustodyFeeSum = BigDecimal.valueOf(0);// 托管费总量
        List<ProductStoreBatchPO> productStoreBatch = inventoryProductStoreBatchMapper.findProductStoreBatch(po.getId());// 查询批次库存
        BigDecimal warehouseCustodyFee = po.getWarehouseCustodyFee();// 仓库托管费（每件每月，单位为元）
        if (warehouseCustodyFee == null || BigDecimal.valueOf(0).equals(warehouseCustodyFee)) {
            warehouseCustodyFee = warehouseCustodyFeeDefault;
        }
        for (ProductStoreBatchPO storeBatch : productStoreBatch) {
            if (deliveryPackageCount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal batchTotalCount = storeBatch.getTotalCount();// 批次数量
            int CustodyDays =
                differentDaysByMillisecond(storeBatch.getBatchTime(), inventoryFeeQueryDTO.getGetGoodsTime());// 托管天数

            if (deliveryPackageCount.compareTo(batchTotalCount) > 0) {// 如果第一个批次不够扣
                deliveryPackageCount = deliveryPackageCount.subtract(batchTotalCount);
                // 配送费 += 仓库托管费*配送数量*天数*/30
                warehouseCustodyFeeSum = warehouseCustodyFeeSum
                    .add(warehouseCustodyFee.multiply(batchTotalCount).multiply(BigDecimal.valueOf(CustodyDays))
                        .divide(BigDecimal.valueOf(30), 2, BigDecimal.ROUND_HALF_UP));
                continue;
            }
            if (deliveryPackageCount.compareTo(batchTotalCount) < 0) {
                warehouseCustodyFeeSum = warehouseCustodyFeeSum
                    .add(warehouseCustodyFee.multiply(deliveryPackageCount).multiply(BigDecimal.valueOf(CustodyDays))
                        .divide(BigDecimal.valueOf(30), 2, BigDecimal.ROUND_HALF_UP));
                break;
            }
        }
        return warehouseCustodyFeeSum;
    }

    /**
     * 通过订单号查询存储天数
     */
    public Map<String, List<InventoryStorageDTO>> findInventoryStorage(List<String> orderNos) {
        List<InventoryStorageDTO> inventoryStorages =
            productInventoryChangeRecordPOMapper.findInventoryStorage(orderNos);
        inventoryStorages =
            inventoryStorages.stream().filter(StreamUtils.distinctByKey(InventoryStorageDTO::getBatchChangeRecordId))
                .collect(Collectors.toList());
        // LOG.info("存储天数查询结果:{}", JSON.toJSONString(inventoryStorages));
        return inventoryStorages.stream().collect(Collectors.groupingBy(InventoryStorageDTO::getOrderNo));
    }
}

package com.yijiupi.himalaya.supplychain.inventory.listener;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;

@Component
public class OrderDirectProcessingListener {

    private static final Logger LOG = LoggerFactory.getLogger(OrderDirectProcessingListener.class);

    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;

    @RabbitListener(queues = "${mq.supplychain.outStockOrder.directProcessing}")
    public void directProcessingOrderSync(List<OrderDTO> orders, Message message) {
        LOG.info("接收直接处理的订单消息:{}", JSON.toJSONString(orders));
        // POS单项id就是OMS项id
        orders.stream().flatMap(order -> order.getItems().stream())
            .forEach(item -> item.setBusinessItemId(item.getId().toString()));
        // 查询仓库抹去货主设置
        // SCM2-8596 店仓多货主调整: 店仓不抹货主
        // List<Integer> warehouseIdList = orders.stream().filter(e -> e != null && e.getWarehouseId() != null)
        // .map(e -> e.getWarehouseId()).distinct().collect(Collectors.toList());
        // Map<Integer, Boolean> eraseWarehouseConfig =
        // iInStockQueryService.findOwnerInfoEraseWarehouseConfig(warehouseIdList);
        //
        // orders.stream().collect(Collectors.groupingBy(OrderDTO::getWarehouseId)).forEach((warehouseId, orderDTOS) ->
        // {
        // if (eraseWarehouseConfig.get(warehouseId) != null && eraseWarehouseConfig.get(warehouseId)) {
        // orderDTOS.stream().flatMap(order -> order.getItems().stream()).forEach(item -> item.setOwnerId(null));
        // }
        // });

        List<OrderDTO> outStockOrders = orders.stream()
            .filter(order -> JiupiOrderTypeEnum.ORDER_TYPE_OEFLINE == order.getOrderType().intValue()
                || (order.getRegistrationPromotion() != null && order.getRegistrationPromotion()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outStockOrders)) {
            inventoryOrderBizBL.directCompleteOutStockOrder(outStockOrders);
        }

        List<OrderDTO> returnOrders = orders.stream()
            .filter(order -> JiupiOrderTypeEnum.ORDER_TYPE_RTURNOFFLINE == order.getOrderType().intValue())
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(returnOrders)) {
            inventoryOrderBizBL.directCompleteInStockOrder(returnOrders);
        }
    }
}

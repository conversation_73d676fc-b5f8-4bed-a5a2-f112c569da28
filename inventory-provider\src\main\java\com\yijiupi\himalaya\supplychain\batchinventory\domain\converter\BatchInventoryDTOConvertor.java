package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.dubbop.constant.ProductSaleMode;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationWithAreaDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.productinfo.WarehouseProductInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductInfoQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@Component
public class BatchInventoryDTOConvertor {

    @Reference
    private ILocationService iLocationService;
    @Reference
    private OwnerService ownerService;
    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IProductInfoQueryService iProductInfoQueryService;

    public List<BatchInventoryDTO> convertToLocationBatchInventory(List<BatchInventoryPO> batchInventoryPOS) {
        if (CollectionUtils.isEmpty(batchInventoryPOS)) {
            return Collections.emptyList();
        }
        List<Long> productInfoIds = batchInventoryPOS.stream()
            .filter(m -> Objects.isNull(m.getMonthOfShelfLife()) || Objects.isNull(m.getShelfLifeUnit()))
            .map(BatchInventoryPO::getProductInfoId).distinct().collect(Collectors.toList());

        List<WarehouseProductInfoDTO> productInfoDTOList = iProductInfoQueryService.findByIds(productInfoIds);
        List<Long> locationIds =
            batchInventoryPOS.stream().map(BatchInventoryPO::getLocationId).distinct().collect(Collectors.toList());
        List<LocationWithAreaDTO> locationWithAreaList = iLocationService.findLocationAndAreaByIds(locationIds);
        Map<Long, LocationWithAreaDTO> locationWithAreaMap =
            locationWithAreaList.stream().collect(Collectors.toMap(LocationWithAreaDTO::getId, v -> v));

        Map<Long, WarehouseProductInfoDTO> productInfoDTOMap =
            productInfoDTOList.stream().collect(Collectors.toMap(WarehouseProductInfoDTO::getId, v -> v));

        List<BatchInventoryDTO> batchInventoryDTOS = new ArrayList<>();
        for (BatchInventoryPO batchInventoryPO : batchInventoryPOS) {
            BatchInventoryDTO batchInventoryDTO = new BatchInventoryDTO();
            BeanUtils.copyProperties(batchInventoryPO, batchInventoryDTO);
            LocationWithAreaDTO locationWithAreaDTO = locationWithAreaMap.get(batchInventoryPO.getLocationId());
            initLocationInfo(batchInventoryDTO, locationWithAreaDTO);
            WarehouseProductInfoDTO productInfoDTO = productInfoDTOMap.get(batchInventoryPO.getProductInfoId());
            initProductInfo(batchInventoryDTO, productInfoDTO);

            // 获取销售模式名称
            if (null != batchInventoryPO.getSaleModel()) {
                batchInventoryDTO
                    .setSaleModelName(ProductSaleMode.getEnumName(Integer.valueOf(batchInventoryPO.getSaleModel())));
            }
            batchInventoryDTOS.add(batchInventoryDTO);
        }

        return batchInventoryDTOS;
    }

    private void initProductInfo(BatchInventoryDTO batchInventoryDTO, WarehouseProductInfoDTO productInfoDTO) {
        if (Objects.nonNull(productInfoDTO.getMonthOfShelfLife())) {
            batchInventoryDTO.setMonthOfShelfLife(productInfoDTO.getMonthOfShelfLife());
        }
        if (Objects.nonNull(productInfoDTO.getShelfLifeUnit())) {
            batchInventoryDTO.setShelfLifeUnit(productInfoDTO.getShelfLifeUnit().intValue());
        }
    }

    private void initLocationInfo(BatchInventoryDTO batchInventoryDTO, LocationWithAreaDTO locationWithAreaDTO) {
        batchInventoryDTO.setLocationId(locationWithAreaDTO.getId());
        batchInventoryDTO.setLocationName(locationWithAreaDTO.getName());
        batchInventoryDTO.setLocationCategory(locationWithAreaDTO.getCategory().intValue());
        batchInventoryDTO.setLocationSubcategory(locationWithAreaDTO.getSubcategory().intValue());
        batchInventoryDTO.setLocationSequence(locationWithAreaDTO.getSequence());
        batchInventoryDTO.setArea(locationWithAreaDTO.getArea());
        LocationWithAreaDTO areaDTO = locationWithAreaDTO.getAreaDTO();
        if (Objects.nonNull(areaDTO)) {
            batchInventoryDTO.setAreaSubcategory(areaDTO.getSubcategory());
        }
    }

    public void initShopInfo(List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }
        List<Long> secondOwnerIds = batchInventoryDTOS.stream().map(BatchInventoryDTO::getSecOwnerId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(secondOwnerIds)) {
            return;
        }
        Map<Long, String> map = ownerService.getOwnerNameMap(secondOwnerIds);
        if (org.springframework.util.CollectionUtils.isEmpty(map)) {
            return;
        }

        batchInventoryDTOS.forEach(dto -> {
            dto.setSecOwnerName(map.getOrDefault(dto.getSecOwnerId(), ""));
        });
    }

}

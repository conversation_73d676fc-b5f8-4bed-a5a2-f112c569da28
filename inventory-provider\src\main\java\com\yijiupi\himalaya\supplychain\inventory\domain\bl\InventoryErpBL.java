package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.constants.InStockStrategyConstants;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.InventoryChangeFactory;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.TrainsImportInStockBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.TrainsImportOutStockBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.configuration.ERPStoreClient;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryChangeRecordPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStockOrderRecordDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IErpStoreOrderService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 库存与ERP相关业务 Created by Lifeng on 2017/7/17.
 */
@Service
public class InventoryErpBL {

    @Autowired
    private InventoryChangeFactory inventoryChangeFactory;
    @Autowired
    private ERPStoreClient erpStoreClient;
    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;
    @Reference
    private IErpStoreOrderService erpStoreOrderService;
    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;
    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;
    @Autowired
    private InventoryProductSkuMapper inventoryProductSkuMapper;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;
    // @Autowired
    // private WarehouseInventoryCheckBL warehouseInventoryCheckBL;
    @Autowired
    private ProductStoreChangeRecordBL productStoreChangeRecordBL;
    @Reference
    private IOrgService iOrgService;

    @Autowired
    private ProductInventoryChangeRecordPOMapper productInventoryChangeRecordPOMapper;
    @Autowired
    private TrainsImportOutStockBL trainsImportOutStockBL;
    @Autowired
    private TrainsImportInStockBL trainsImportInStockBL;

    @Reference
    private ILocationService iLocationService;

    private static final Logger LOG = LoggerFactory.getLogger(InventoryErpBL.class);

    /**
     * 处理ERP出/入库单.
     */
    @Transactional(rollbackFor = Exception.class)
    public void applyErpOrder(StockOrderStoreDTO stockOrderStoreDTO, boolean checkWarehouseInventory,
                              boolean isUpdateDeliveryCount, boolean isUpdateProductStore, boolean isUpdateProductBatchStore,
                              boolean isUpdateSellProductStore) {
        List<StockOrderStoreItemDTO> productSkuList = stockOrderStoreDTO.getProductSkuList();
        Integer warehouseId = stockOrderStoreDTO.getWarehouseId();
        AssertUtils.notNull(warehouseId, "warehouseId不能为空");
        Map<Long, LoactionDTO> storeItemLocationMap = findStoreItemLocationType(productSkuList);
        // 是否需要计算
        Boolean allocationCalculation = stockOrderStoreDTO.getAllocationCalculation();
        final List<WarehouseInventoryChangeBO> list = new ArrayList<>();
        for (StockOrderStoreItemDTO storeItemDTO : productSkuList) {
            Long productSkuId = Long.valueOf(storeItemDTO.getProductSkuId());
            AssertUtils.notNull(productSkuId, "SKUID不能为空");

            BigDecimal changeCount = storeItemDTO.getTotalStoreCountMinUnit();

            WarehouseInventoryChangeBO warehouseInventoryChangeBO =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, changeCount,
                            storeItemDTO.getChannel(), storeItemDTO.getOwnerId(), storeItemDTO.getSecOwnerId());

            warehouseInventoryChangeBO.setCityId(Integer.valueOf(stockOrderStoreDTO.getCityId()));
            warehouseInventoryChangeBO.setOrderId(stockOrderStoreDTO.getStockOrderId());
            warehouseInventoryChangeBO.setOrderNo(stockOrderStoreDTO.getErpOrderId());
            // 设置关联出入库单据子项ID
            warehouseInventoryChangeBO.setOrderItemId(storeItemDTO.getStockOrderItemId());
            warehouseInventoryChangeBO.setOrderType(stockOrderStoreDTO.getErpType());
            warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.erp库存同步.getType());
            warehouseInventoryChangeBO.setErpEventType(stockOrderStoreDTO.getErpEventType());
            warehouseInventoryChangeBO.setCreateUserName(stockOrderStoreDTO.getUserName());
            warehouseInventoryChangeBO.setDescription(stockOrderStoreDTO.getDescription());
            warehouseInventoryChangeBO.setValidateSelf(false);
            if (storeItemDTO.getLocationId() != null && storeItemDTO.getLocationSubcategory() == null) {
                LoactionDTO loactionDTO = storeItemLocationMap.get(storeItemDTO.getLocationId());
                if (loactionDTO != null) {
                    storeItemDTO.setLocationSubcategory(loactionDTO.getSubcategory());
                }
            }
            warehouseInventoryChangeBO.setLocationId(storeItemDTO.getLocationId());
            warehouseInventoryChangeBO.setLocationName(storeItemDTO.getLocationName());
            warehouseInventoryChangeBO.setLocationSubcategory(storeItemDTO.getLocationSubcategory());
            // 出入库类型
            warehouseInventoryChangeBO.setOutInType(stockOrderStoreDTO.getOutInType());
            // 计算批属性编号用
            warehouseInventoryChangeBO.setProductionDate(storeItemDTO.getProductionDate());
            warehouseInventoryChangeBO.setBatchTime(storeItemDTO.getBatchTime());
            warehouseInventoryChangeBO.setExpireTime(storeItemDTO.getExpireTime());
            // 产品规格id
            warehouseInventoryChangeBO.setProductSpecificationId(storeItemDTO.getProductSpecificationId());
            if (allocationCalculation != null) {
                warehouseInventoryChangeBO.setAllocationCalculation(allocationCalculation);
            }
            // 是否在上架任务完成后处理销售库存
            warehouseInventoryChangeBO
                    .setIsProcessSalesStockAfterPutAway(storeItemDTO.getIsProcessSalesStockAfterPutAway());
            // SCM2-9937 批次编号
            warehouseInventoryChangeBO.setBatchNo(storeItemDTO.getBatchNo());
            // 残次品位与残次品区不处理销售库存,只加仓库库存你
            if (Objects.equals(storeItemDTO.getChannel(), ProductChannelType.LARGE)
                    || Objects.equals(LocationEnum.残次品位.getType().byteValue(),
                    warehouseInventoryChangeBO.getLocationSubcategory())
                    || Objects.equals(LocationAreaEnum.残次品区.getType().byteValue(),
                    warehouseInventoryChangeBO.getLocationSubcategory())) {
                warehouseInventoryChangeBO.setHasUpdateOPInventory(false);
            }
            list.add(warehouseInventoryChangeBO);

        }
        LOG.info("ERP -> 处理ERP出/入库单库存 WarehouseInventoryChangeBO 集合：{}", JSON.toJSONString(list));
        // 处理库存变更.库存记录
        warehouseInventoryManageBL.validateAndProcessProductStore(list, checkWarehouseInventory, isUpdateDeliveryCount,
                isUpdateProductStore, isUpdateProductBatchStore, true);
        LOG.info("ERP -> 处理ERP出/入库单库存变更完成 erpOrderId:[{}]", stockOrderStoreDTO.getErpOrderId());
        if (isUpdateProductStore && isUpdateSellProductStore) {
            // 是否在上架任务完成后处理销售库存：过滤出在上架完成才能变更销售库存的数据
            List<WarehouseInventoryChangeBO> sellInventoryList = list.stream()
                    .filter(e -> e != null && (e.getIsProcessSalesStockAfterPutAway() == null
                            || Objects.equals(e.getIsProcessSalesStockAfterPutAway(),
                            InStockStrategyConstants.PROCESS_SALESSTOCK_AFTERPUTAWAY_NO)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sellInventoryList)) {
                LOG.info("ERP -> 处理ERP出/入库单,发送销售库存变更消息 erpOrderId:[{}]", stockOrderStoreDTO.getErpOrderId());
                // 发送销售库存变更消息
                warehouseInventoryManageBL.processSellInventory(sellInventoryList, null);
            }
        }
        // 该出入库单的状态
        // 入库单改为已入库,出库单改成已出库.
        LOG.info("ERP -> 处理ERP出/入库单完成 erpOrderId:[{}]", stockOrderStoreDTO.getErpOrderId());
    }

    /**
     * 处理ERP盘点单
     */
    @Transactional(rollbackFor = Exception.class)
    public void processStockOrder(String orderId, Map<Long, List<Long>> skuAndSecOwnerMap, Boolean isDif,
                                  Boolean isReverse, String orderNO, List<String> specAndOwnerAndSecOwnerList) {
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            LOG.info("Interrupted!", e);
            Thread.currentThread().interrupt();
        }
        List<ErpStockOrderRecordDTO> erpStoreOrderRecord =
                erpStoreOrderService.findErpStoreOrderRecord(orderId, isDif, null);
        if (erpStoreOrderRecord.size() <= 0) {
            String errorMsg = String.format("没有找到符合条件的盘单点项！ID：%s,是否差异项:%s", orderId, isDif);
            if (isDif) {
                throw new BusinessException(errorMsg);
            }
            LOG.info(errorMsg);
            return;
        }
        // 盘盈盘亏是一个产品一个产品审核的
        if (isDif) {
            // 移除其他不相干的SKU：支持供应商后需要带上二级货主
            List<String> secOwnerKeys = new ArrayList<>();
            skuAndSecOwnerMap.forEach((skuId, secOwnerIds) -> {
                if (CollectionUtils.isEmpty(secOwnerIds)) {
                    secOwnerKeys.add(String.format("%s-%s", skuId, null));
                } else {
                    secOwnerIds.stream().forEach(secOwnerId -> {
                        secOwnerKeys.add(String.format("%s-%s", skuId, secOwnerId));
                    });
                }
            });

            if (CollectionUtils.isNotEmpty(specAndOwnerAndSecOwnerList)) {
                erpStoreOrderRecord.removeIf(p -> !specAndOwnerAndSecOwnerList.contains(String.format("%s-%s-%s-%s",
                        p.getWarehouseId(), p.getProductSpecificationId(), p.getOwnerId(), p.getSecOwnerId())));
            } else {
                erpStoreOrderRecord.removeIf(
                        p -> !secOwnerKeys.contains(String.format("%s-%s", p.getProductskuId(), p.getSecOwnerId())));
            }
        }
        // 反审核，数量改成负的
        if (isReverse) {
            erpStoreOrderRecord.forEach(p -> {
                // 批次库存数量改成负的
                p.setUnittotalcount(p.getUnittotalcount().multiply(new BigDecimal(-1)));
                // 总库存数量改成负的
                p.setDiffLargeCount(p.getDiffLargeCount().multiply(new BigDecimal(-1)));
                p.setDiffNormalCount(p.getDiffNormalCount().multiply(new BigDecimal(-1)));
            });
        }

        if (CollectionUtils.isEmpty(erpStoreOrderRecord)) {
            LOG.info(String.format("第一层过滤之后没有符合条件的盘单点项！ID：%s,是否差异项:%s", orderId, isDif));
            return;
        }
        Integer warehouseId = erpStoreOrderRecord.get(0).getWarehouseId();
        AssertUtils.notNull(warehouseId, "warehouseId不能为空");

        LOG.info(String.format("库存盘点：%s", JSON.toJSONString(erpStoreOrderRecord)));

        List<Long> lstSkuIds =
                erpStoreOrderRecord.stream().map(p -> p.getProductskuId()).distinct().collect(Collectors.toList());

        // 移除SKU不存在的项
        List<Long> lstNewSkuIds = inventoryProductSkuMapper.getExitsProductSkuIds(lstSkuIds);
        if (lstNewSkuIds.size() != lstSkuIds.size()) {
            LOG.info(String.format("库存盘点时，部分SKU不存在，入参数量：%s,实际数量：%s", lstSkuIds.size(), lstNewSkuIds.size()));
        }
        erpStoreOrderRecord.removeIf(p -> !lstNewSkuIds.contains(p.getProductskuId()));
        lstSkuIds = lstNewSkuIds;

        if (CollectionUtils.isEmpty(erpStoreOrderRecord)) {
            LOG.info(String.format("第二层过滤之后没有符合条件的盘单点项！ID：%s,是否差异项:%s", orderId, isDif));
            return;
        }
        if (StringUtils.isNotBlank(orderNO)) {
            // 如果有盘盈盘亏单号则用盘盈盘亏单号记录，没有则保持原样
            erpStoreOrderRecord.stream().filter(Objects::nonNull)
                    .forEach(recordDTO -> recordDTO.setReforderid(orderNO));
        }
        Integer orderType;
        Integer erpEventType;
        String description;
        if (isDif == true) {
            orderType = ERPType.库存盘点单.getType();
            if (isReverse) {
                description = "盘盈盘亏单反审核同步库存";
                erpEventType = ERPEventType.单据反审核.getType();
            } else {
                description = "盘盈盘亏单审核同步库存";
                erpEventType = ERPEventType.单据审核.getType();
            }
        } else {
            orderType = ERPType.盘点单.getType();
            description = "库存盘点单审核通过同步库存";
            erpEventType = ERPEventType.单据审核.getType();
        }

        LOG.info(String.format("开始更新总库存：%s", orderId));
        boolean checkWarehouseInventory = false;
        boolean isUpdateDeliveryCount = false;
        boolean isUpdateProductStore = true;
        boolean isUpdateProductBatchStore = false;
        boolean isSkipNotExitsSku = true;
        processStoreInventory(checkWarehouseInventory, isUpdateDeliveryCount, isUpdateProductStore,
                isUpdateProductBatchStore, isSkipNotExitsSku, erpStoreOrderRecord, lstSkuIds, warehouseId, orderType,
                erpEventType, description);
        LOG.info(String.format("结束更新总库存：%s", orderId));

        erpStoreOrderRecord =
                erpStoreOrderRecord.stream().filter(p -> p.getType().intValue() == 1).collect(Collectors.toList());
        LOG.info(String.format("开始更新批次库存：%s", orderId));
        processBatchInventory(erpStoreOrderRecord, orderType, erpEventType, description, warehouseId, lstSkuIds);
        LOG.info(String.format("盘盈盘亏通知中台入参：%s", JSON.toJSONString(erpStoreOrderRecord)));
        trainsImportOutStockBL.notifyOrderCenterOut(erpStoreOrderRecord, erpEventType);
        trainsImportInStockBL.notifyOrderCenterIn(erpStoreOrderRecord, erpEventType);
        LOG.info(String.format("结束更新ERP批次库存：%s", orderId));
    }

    /**
     * 更新总库存差异
     */
    private void processStoreInventory(boolean checkWarehouseInventory, boolean isUpdateDeliveryCount,
                                       boolean isUpdateProductStore, boolean isUpdateProductBatchStore, boolean isSkipNotExitsSku,
                                       List<ErpStockOrderRecordDTO> erpStoreOrderRecord, List<Long> lstSkuIds, Integer warehouseId, Integer orderType,
                                       Integer erpEventType, String description) {

        final List<WarehouseInventoryChangeBO> list = new ArrayList<>();
        // 更新总库存
        // 总库存差异项
        lstSkuIds.forEach(sku -> {
            List<ErpStockOrderRecordDTO> lstERP =
                    erpStoreOrderRecord.stream().filter(p -> p.getProductskuId().equals(sku)).collect(Collectors.toList());
            if (lstERP.size() > 0) {
                ErpStockOrderRecordDTO erp = lstERP.get(0);
                if (erp.getDiffLargeCount().compareTo(BigDecimal.ZERO) != 0) {
                    WarehouseInventoryChangeBO warehouseInventoryChangeBO = getWarehouseInventoryChangeBO(warehouseId,
                            orderType, erpEventType, description, erp, erp.getProductskuId(), erp.getDiffLargeCount(), 1);
                    warehouseInventoryChangeBO.setCityId(erp.getOrgId());
                    warehouseInventoryChangeBO.setSource(ProductSourceType.易酒批);
                    list.add(warehouseInventoryChangeBO);
                }
                if (erp.getDiffNormalCount().compareTo(BigDecimal.ZERO) != 0) {
                    WarehouseInventoryChangeBO warehouseInventoryChangeBO = getWarehouseInventoryChangeBO(warehouseId,
                            orderType, erpEventType, description, erp, erp.getProductskuId(), erp.getDiffNormalCount(), 0);
                    warehouseInventoryChangeBO.setCityId(erp.getOrgId());
                    warehouseInventoryChangeBO.setSource(ProductSourceType.易酒批);
                    list.add(warehouseInventoryChangeBO);
                }
            }
        });

        LOG.info(String.format("总库存差异差异项：%s", JSON.toJSONString(list)));
        if (list.size() > 0) {
            // 盘盈盘亏单不需要计算
            list.stream().filter(Objects::nonNull).forEach(change -> change.setAllocationCalculation(false));
            // 处理库存变更.库存记录
            warehouseInventoryManageBL.validateAndProcessProductStore(list, checkWarehouseInventory,
                    isUpdateDeliveryCount, isUpdateProductStore, isUpdateProductBatchStore, isSkipNotExitsSku);
            // Integer cityId = list.get(0).getCityId();
            // // 全量更新销售库存
            // List<Long> productSkuIds = list.stream().map(p -> p.getProductSkuId()).collect(Collectors.toList());
            // if (canProcessByApi(cityId)) {
            // processSellInventoryMsgByApi(cityId, warehouseId, productSkuIds);
            // } else {
            // 发送销售库存变更消息
            // 开启货位库存销售库存变更改由货位库存差异发送
            // warehouseInventoryManageBL.processSellInventory(list, null);
            // }
        }
    }

    public Boolean canProcessByApi(Integer cityId) {
        Byte fromOrgType = OrgConstant.ORG_TYPE_JIUPI;
        if (cityId != null) {
            OrgDTO org = iOrgService.getOrg(cityId);
            if (org != null) {
                fromOrgType = org.getFromOrgType();
            }
        }
        // 酒批产品，调交易API校正销售库存
        // 非交易产品，通过MQ增量更新
        boolean isUpdateSellStoreByAPI = Objects.equals(fromOrgType, OrgConstant.ORG_TYPE_JIUPI);
        return isUpdateSellStoreByAPI;
    }
    //
    // public void processSellInventoryMsgByApi(Integer cityId, Integer warehouseId, List<Long> productSkuIds) {
    // //酒批产品，调交易API校正销售库存
    // try {
    // warehouseInventoryCheckBL.checkSellInventory(productSkuIds, warehouseId, cityId, null);
    // } catch (Exception e) {
    // LOG.error("审核盘盈盘亏单更新销售库存失败：", e);
    // }
    // }

    private WarehouseInventoryChangeBO getWarehouseInventoryChangeBO(Integer warehouseId, Integer orderType,
                                                                     Integer erpEventType, String description, ErpStockOrderRecordDTO stockOrderStoreDTO, Long productSkuId,
                                                                     BigDecimal changeCount, Integer channel) {
        WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(
                productSkuId, warehouseId, changeCount, stockOrderStoreDTO.getChannel().intValue(),
                stockOrderStoreDTO.getOwnerId(), stockOrderStoreDTO.getSecOwnerId());

        warehouseInventoryChangeBO.setCityId(stockOrderStoreDTO.getOrgId());
        warehouseInventoryChangeBO.setOrderId(stockOrderStoreDTO.getReforderid());
        warehouseInventoryChangeBO.setOrderNo(stockOrderStoreDTO.getReforderid());
        warehouseInventoryChangeBO.setOrderType(orderType);
        warehouseInventoryChangeBO.setDescription(description);
        warehouseInventoryChangeBO.setErpEventType(erpEventType);
        warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.erp库存同步.getType());
        warehouseInventoryChangeBO.setCreateUserName(stockOrderStoreDTO.getCreateuser());
        warehouseInventoryChangeBO.setValidateSelf(false);
        warehouseInventoryChangeBO.setChannel(channel);
        warehouseInventoryChangeBO.setProductSpecificationId(stockOrderStoreDTO.getProductSpecificationId());
        // 批次库存ID
        warehouseInventoryChangeBO.setProductStoreBatchId(stockOrderStoreDTO.getProductStoreBatchId());

        if (channel == ProductChannelType.LARGE) {// 如果是大宗商品,就设置为不需要修改销售库存
            warehouseInventoryChangeBO.setHasUpdateOPInventory(false);
        }
        return warehouseInventoryChangeBO;
    }

    /**
     * 批次库存差异
     */
    public void processBatchInventory(List<ErpStockOrderRecordDTO> erpStoreOrderRecord, Integer orderType,
                                      Integer erpEventType, String description, Integer warehouseId, List<Long> lstSkuIds) {

        // 查询出总库存不分渠道
        List<ProductInventoryPO> lstProductStores = findErpStockOrderInventory(erpStoreOrderRecord);
        // 批次库存变更记录
        List<ProductInventoryChangeRecordPO> lstProductStoreBatchPO = new ArrayList<>();
        // 销售库存变更记录
        List<WarehouseInventoryChangeBO> saleInventoryChangeList = new ArrayList<>();
        List<Long> locationIds = erpStoreOrderRecord.stream().filter(e -> e != null && e.getLocationid() != null)
                .map(e -> e.getLocationid()).distinct().collect(Collectors.toList());
        Map<Long, Byte> locationSubcategoryMap = new HashMap<>(16);
        if (!locationIds.isEmpty()) {
            // 判断货位类型是否为：残次品位或者残次品区
            List<LoactionDTO> locationInfoList = iLocationService.findLocationByIds(locationIds);
            if (CollectionUtils.isNotEmpty(locationInfoList)) {
                Map<Long, Byte> locationMap = locationInfoList.stream().filter(e -> e != null).collect(
                        Collectors.toMap(e -> e.getId(), e -> e.getSubcategory(), (v1, v2) -> v1 != null ? v1 : v2));
                locationSubcategoryMap.putAll(locationMap);
            }
        }
        erpStoreOrderRecord.forEach(erp -> {
            ProductInventoryPO productStorePO = getProductStoreId(lstProductStores, erp);
            if (productStorePO == null) {
                LOG.warn("处理盘点单库存差异项时,库存不存在！" + JSON.toJSONString(erp));
                return;
            }
            ProductInventoryChangeRecordPO changeRecordPO = getProductInventoryChangeRecordPO(orderType, erpEventType,
                    description, erp, productStorePO.getId(), productStorePO.getOwnerId(), productStorePO.getOwnerType(),
                    productStorePO.getSecOwnerId(), erp.getUnittotalcount());
            Byte locationSubcategory = locationSubcategoryMap.get(erp.getLocationid());
            // 发送销售库存：排除残次品/残次品区货位变更
            boolean isProcessSaleStore =
                    ObjectUtils.defaultIfNull(erp.getUnittotalcount(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0
                            && !(Objects.equals(locationSubcategory, LocationEnum.残次品位.getType().byteValue())
                            || Objects.equals(locationSubcategory, LocationAreaEnum.残次品区.getType().byteValue()));
            if (isProcessSaleStore) {
                // 同 skuId 产品进行合并
                Optional<WarehouseInventoryChangeBO> existChangeOpt = saleInventoryChangeList.stream()
                        .filter(sale -> sale != null && Objects.equals(sale.getProductSkuId(), erp.getProductskuId())
                                && Objects.equals(sale.getSecOwnerId(), erp.getSecOwnerId()))
                        .findAny();
                if (existChangeOpt.isPresent()) {
                    // 存在则进行数量合并
                    WarehouseInventoryChangeBO changeBO = existChangeOpt.get();
                    changeBO.setCount(ObjectUtils.defaultIfNull(changeBO.getCount(), BigDecimal.ZERO)
                            .add(ObjectUtils.defaultIfNull(erp.getUnittotalcount(), BigDecimal.ZERO)));
                } else {
                    // 不存在则添加至集合
                    WarehouseInventoryChangeBO warehouseInventoryChangeBO = getWarehouseInventoryChangeBO(warehouseId,
                            orderType, erpEventType, description, erp, erp.getProductskuId(), erp.getUnittotalcount(),
                            ObjectUtils.defaultIfNull(erp.getChannel(), ProductChannelType.JIUPI).intValue());
                    warehouseInventoryChangeBO.setCityId(erp.getOrgId());
                    warehouseInventoryChangeBO.setSource(ProductSourceType.易酒批);
                    saleInventoryChangeList.add(warehouseInventoryChangeBO);
                }

            }
            // 增加SKUID传递
            changeRecordPO.setProductSkuId(productStorePO.getProductSkuId());
            lstProductStoreBatchPO.add(changeRecordPO);
        });
        LOG.info(String.format("批次库存差异差异项：%s", JSON.toJSONString(lstProductStoreBatchPO)));
        if (lstProductStoreBatchPO.size() > 0) {
            // 为了防止盘平或总库存不变更导致批次库存差异无法查询，发送批次库存记录前增加一条“假”库存变更记录
            ProductInventoryChangeRecordPO inventoryChangeRecordPO =
                    productStoreChangeRecordBL.buildEmptyInventoryChangeRecord(lstProductStoreBatchPO.get(0));
            // 设置批次库存变更记录与总库存变更记录关联关系
            if (inventoryChangeRecordPO != null) {
                lstProductStoreBatchPO.stream().filter(d -> d != null)
                        .forEach(d -> d.setId(inventoryChangeRecordPO.getId()));
            }
            inventoryChangeEventFireBL.storeInventoryChangeEvent(lstProductStoreBatchPO);
        }
        // 发送消息前去掉 Count 为 0 的数据
        saleInventoryChangeList.removeIf(
                e -> e != null && ObjectUtils.defaultIfNull(e.getCount(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0);
        if (saleInventoryChangeList.size() > 0) {
            // 发送销售库存：排除残次品/残次品区货位变更
            warehouseInventoryManageBL.processSellInventory(saleInventoryChangeList, null);
        }
    }

    private ProductInventoryPO getProductStoreId(List<ProductInventoryPO> lstProductStores,
                                                 ErpStockOrderRecordDTO recordDTO) {
        if (CollectionUtils.isEmpty(lstProductStores) || recordDTO == null) {
            return null;
        }
        return lstProductStores.stream()
                .filter(store -> store != null && Objects.equals(store.getWarehouseId(), recordDTO.getWarehouseId())
                        && Objects.equals(store.getProductSpecificationId(), recordDTO.getProductSpecificationId())
                        && Objects.equals(store.getOwnerId(), recordDTO.getOwnerId())
                        && Objects.equals(store.getSecOwnerId(), recordDTO.getSecOwnerId()))
                .findAny().orElse(null);
    }

    private ProductInventoryChangeRecordPO getProductInventoryChangeRecordPO(Integer orderType, Integer erpEventType,
                                                                             String description, ErpStockOrderRecordDTO erp, String productStoreId, Long ownerId, Integer ownerType,
                                                                             Long secOwnerId, BigDecimal changeCount) {
        ProductInventoryChangeRecordPO changeRecordPO = new ProductInventoryChangeRecordPO();
        changeRecordPO.setOrderType(orderType);
        changeRecordPO.setErpEventType(erpEventType);
        changeRecordPO.setDescription(description);
        changeRecordPO.setProductionDate(erp.getProductionDate());
        changeRecordPO.setJiupiEventType(JiupiEventType.erp库存同步.getType());
        changeRecordPO.setProductStoreId(productStoreId);
        changeRecordPO.setOrderId(erp.getReforderid());
        changeRecordPO.setCityId(erp.getOrgId());
        changeRecordPO.setCreateUser(erp.getCreateuser());
        changeRecordPO.setLocationId(erp.getLocationid());
        changeRecordPO.setLocationName(erp.getLocationname());
        changeRecordPO.setOrderNo(erp.getReforderid());
        changeRecordPO.setWarehouseId(erp.getWarehouseId());
        // changeRecordPO.setSourceTotalCount(changeCount);
        changeRecordPO.setTotalCount(changeCount);
        changeRecordPO.setStoreType(1);
        changeRecordPO.setCreateTime(new Date());
        changeRecordPO.setOwnerId(ownerId);
        changeRecordPO.setOwnerType(ownerType);
        changeRecordPO.setSecOwnerId(secOwnerId);
        // 批次库存ID
        changeRecordPO.setProductStoreBatchId(erp.getProductStoreBatchId());
        // 库龄管控入库时间
        changeRecordPO.setBatchTime(erp.getBatchInStockTime());
        return changeRecordPO;
    }

    /**
     * 根据盘盈盘亏单查询产品库存
     */
    public List<ProductInventoryPO> findErpStockOrderInventory(List<ErpStockOrderRecordDTO> erpStoreOrderRecord) {
        if (CollectionUtils.isEmpty(erpStoreOrderRecord)) {
            return Collections.emptyList();
        }
        Map<String, List<ErpStockOrderRecordDTO>> recordMap = erpStoreOrderRecord.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ErpStockOrderRecordDTO::getWarehouseIdAndChannelAndOwnerId));
        List<ProductInventoryPO> inventoryPOList = new ArrayList<>(erpStoreOrderRecord.size());
        recordMap.forEach((key, records) -> {
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            ErpStockOrderRecordDTO recordDTO = records.get(0);
            List<Long> specIdList = records.stream().filter(Objects::nonNull)
                    .map(ErpStockOrderRecordDTO::getProductSpecificationId).distinct().collect(Collectors.toList());
            List<ProductInventoryPO> productInventories =
                    productInventoryPOMapper.findProductInventoryByProductSpecIdWarehouseId(specIdList,
                            recordDTO.getOwnerId(), recordDTO.getWarehouseId(), null, recordDTO.getSecOwnerId());
            if (CollectionUtils.isNotEmpty(productInventories)) {
                inventoryPOList.addAll(productInventories);
            }
        });
        return inventoryPOList;
    }

    /**
     * 查询货位类型
     */
    private Map<Long, LoactionDTO> findStoreItemLocationType(List<StockOrderStoreItemDTO> storeItemDTOS) {
        List<StockOrderStoreItemDTO> needFillItems = storeItemDTOS.stream()
                .filter(item -> item != null && item.getLocationId() != null && item.getLocationSubcategory() == null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needFillItems)) {
            return Collections.emptyMap();
        }
        List<Long> locationIds =
                needFillItems.stream().map(StockOrderStoreItemDTO::getLocationId).distinct().collect(Collectors.toList());
        List<LoactionDTO> locationInfoList = iLocationService.findLocationByIds(locationIds);
        if (CollectionUtils.isEmpty(locationInfoList)) {
            return Collections.emptyMap();
        }
        return locationInfoList.stream().collect(Collectors.toMap(LoactionDTO::getId, Function.identity()));
    }
}

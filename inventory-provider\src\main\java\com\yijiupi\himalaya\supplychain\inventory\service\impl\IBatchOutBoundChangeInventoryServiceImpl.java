package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.BatchOutBoundChangeInventoryBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.BatchOutBoundConvert;
import com.yijiupi.himalaya.supplychain.inventory.dto.DeliveryProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.service.IBatchOutBoundChangeInventoryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;

/**
 * <AUTHOR> 批次出库 变更库存
 */
@Service(timeout = 30000)
public class IBatchOutBoundChangeInventoryServiceImpl implements IBatchOutBoundChangeInventoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IBatchOutBoundChangeInventoryServiceImpl.class);

    @Autowired
    private BatchOutBoundChangeInventoryBL batchOutBoundChangeInventoryBL;

    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;

    /**
     * 配送单批量发货
     * 
     * @return
     */
    @Override
    public List<InventoryDeliveryJiupiOrder> affirmOutStock(List<OrderDTO> orders) {
        AssertUtils.notEmpty(orders, "批次出库单信息为空");

        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            BatchOutBoundConvert.orderDTOS2InventoryDeliveryJiupiOrderS(orders);
        String syncData = JSON.toJSONString(deliveryOrders);
        LOGGER.info("按批次确认出库-库存处理入参：{}", syncData);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");

            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
            }
        }
        // return batchOutBoundChangeInventoryBL.affirmOutStock(deliveryOrders, true, true);TODO 临时
        return batchOutBoundChangeInventoryBL.affirmOutStock(deliveryOrders, true, false);

    }

    /**
     * 校验按批次确认出库库存数量
     */
    @Override
    public List<DeliveryProductInventoryDTO> validateOrderDeliveryProductInventory(List<OrderDTO> orders,
        String orderDeliveryOpType) {
        LOGGER.info("按批次确认出库校验库存数量参数:{},orderDeliveryOpType:{}", JSON.toJSONString(orders), orderDeliveryOpType);
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            BatchOutBoundConvert.orderDTOS2InventoryDeliveryJiupiOrderS(orders);
        for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
            AssertUtils.notNull(deliveryOrder.getJiupiOrderType(), "JiuPiOrderType不能为空");
            AssertUtils.notNull(deliveryOrder.getCityId(), "发货城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getFromCityId(), "下单城市Id不能为空");
            AssertUtils.notNull(deliveryOrder.getDeliveryMode(), "配送方式不能为空");

            for (InventoryDeliveryJiupiOrderItem item : deliveryOrder.getItems()) {
                AssertUtils.notNull(item.getProductSpecification_Id(), "产品规格Id不能为空");
                AssertUtils.notNull(item.getSaleSpecQuantity(), "销售规格系数不能为空");
            }
        }
        return inventoryOrderBizBL.validateOrderDeliveryProductInventoryNew(deliveryOrders, orderDeliveryOpType);
    }
}

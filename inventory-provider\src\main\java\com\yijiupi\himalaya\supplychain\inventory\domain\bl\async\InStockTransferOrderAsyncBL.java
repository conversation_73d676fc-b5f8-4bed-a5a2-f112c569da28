package com.yijiupi.himalaya.supplychain.inventory.domain.bl.async;

import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.service.IBatchInventoryQueryService;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.InBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.instockorder.utils.constant.AllotOrderTypeConstans;
import com.yijiupi.himalaya.supplychain.inventory.domain.aspect.InventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.InternalOrderDeliveryConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.dto.InternalDistributionOrderDeliveryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.util.TranseferUtil;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IInStockOrderService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommQueryService;
import com.yijiupi.himalaya.supplychain.packageorder.domain.dto.PackageOrderItemDTO;
import com.yijiupi.himalaya.supplychain.packageorder.domain.dto.PackageOrderItemQueryDTO;
import com.yijiupi.himalaya.supplychain.packageorder.domain.service.IPackageOrderItemService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDeliveryItemSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDeliverySO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.himalaya.supplychain.waves.enums.PackageTypeEnum;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.company.CompanyCode;

@Service
public class InStockTransferOrderAsyncBL {

    private static final Logger LOG = LoggerFactory.getLogger(InStockTransferOrderAsyncBL.class);

    @Reference(timeout = 60000)
    private IInStockOrderService iInStockOrderService;

    @Reference
    private IOutStockCommQueryService outStockCommQueryService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    @Autowired
    private InventorySendFaildMQ inventorySendFaildMQ;

    @Reference
    private IBatchInventoryQueryService iBatchInventoryQueryService;

    @Reference
    private IPackageOrderItemService iPackageOrderItemService;

    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Lazy
    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;

    /**
     * 异步生成内配入库单据
     */
    @Async
    public void batchAddInStockTransferOrder(InternalDistributionOrderDeliveryDTO deliveryOrders,
        List<WarehouseInventoryChangeBO> intercityWarehouseChangeList) {
        try {
            Thread.sleep(10000);
            if (deliveryOrders == null || CollectionUtils.isEmpty(deliveryOrders.getDeliveryJiupiOrders())) {
                return;
            }
            List<String> newOrderNos = deliveryOrders.getDeliveryJiupiOrders().stream()
                .filter(order -> order != null && !OrderConstant.ALLOT_TYPE_DELIVERY.equals(order.getAllotType())
                    && !OrderConstant.ALLOT_TYPE_DELIVERY_RETURN.equals(order.getAllotType())
                    && !Objects.equals(order.getOrderType().byteValue(), OrderConstant.ORDER_TYPE_ALLOT))
                .map(e -> e.getOrderNo()).distinct().collect(Collectors.toList());
            // 根据出库库存处理结果组装二级货主信息
            List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(intercityWarehouseChangeList)) {
                List<WarehouseInventoryChangeBO> warehouseChangeList = intercityWarehouseChangeList.stream()
                    .filter(changeBO -> newOrderNos.contains(changeBO.getOrderNo())).collect(Collectors.toList());
                outStockOrderItemDetailDTOS =
                    warehouseChangListBOConverter.processChangeBOSToOutStockOrderItemDetailDTOS(warehouseChangeList);
            }
            List<InStockOrderDTO> inStockOrderDTOList =
                createInStockOrderByOrderNo(deliveryOrders, newOrderNos, true, outStockOrderItemDetailDTOS);
            if (CollectionUtils.isNotEmpty(inStockOrderDTOList)) {
                LOG.info("内配单-发货入库信息: {}", JSON.toJSONString(inStockOrderDTOList));
                iInStockOrderService.saveInStockOrderList(inStockOrderDTOList);

                // 内配入新增装箱明细
                addPackageOrderItem(deliveryOrders, inStockOrderDTOList);
            }
        } catch (Exception e) {
            LOG.error("内配单-发货生成入库信息失败 : {}", JSON.toJSONString(deliveryOrders), e);
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(deliveryOrders), "batchAddInStockTransferOrder", e);
        }
    }

    /**
     * 内配单根据出库分包明细生成入库分包明细数据
     *
     * @param inStockOrderDTOList
     * @return
     */
    public void addPackageOrderItem(InternalDistributionOrderDeliveryDTO deliveryOrders,
        List<InStockOrderDTO> inStockOrderDTOList) {
        Integer deliveryCity = deliveryOrders.getDeliveryCity();
        Integer deliveryWarehouseId = deliveryOrders.getDeliveryWarehouseId();
        Integer receiptCity = deliveryOrders.getReceiptCity();
        Integer receiptWarehouseId = deliveryOrders.getReceiptWarehouseId();

        List<String> orderNoList = deliveryOrders.getDeliveryJiupiOrders().stream()
            .filter(order -> order != null && !OrderConstant.ALLOT_TYPE_DELIVERY.equals(order.getAllotType())
                && !OrderConstant.ALLOT_TYPE_DELIVERY_RETURN.equals(order.getAllotType())
                && !Objects.equals(order.getOrderType().byteValue(), OrderConstant.ORDER_TYPE_ALLOT)
                && !OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN.equals(order.getAllotType()))
            .map(e -> e.getOrderNo()).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderNoList)) {
            LOG.info("内配单[城市-{}-仓库{}]-查询出库数据订单号为空！", deliveryCity, deliveryWarehouseId);
            return;
        }
        // 获取订单是否生成内配单
        List<OrderDTO> outOrderDTOS =
            outStockCommQueryService.findOrderByNos(deliveryCity, deliveryWarehouseId, orderNoList);
        if (CollectionUtils.isEmpty(outOrderDTOS)) {
            LOG.info("内配单[城市-{}]-查询出库数据不存在！查询参数：{}", deliveryCity, JSON.toJSONString(orderNoList));
            return;
        }

        // 获取内配单号
        List<String> refOrderNoList = inStockOrderDTOList.stream()
            .filter(p -> StringUtils.isNotEmpty(p.getRefOrderNo()) && StringUtils.isEmpty(p.getRelatedNoteNO()))
            .map(InStockOrderDTO::getRefOrderNo).distinct().collect(Collectors.toList());

        // 内配前置仓入库单关联订单号
        List<String> relatedNoteNOList =
            inStockOrderDTOList.stream().filter(p -> StringUtils.isNotEmpty(p.getRelatedNoteNO()))
                .map(InStockOrderDTO::getRelatedNoteNO).distinct().collect(Collectors.toList());
        refOrderNoList.addAll(relatedNoteNOList);

        // 获取内配入订单项
        List<InStockOrderItemDTO> orderItems =
            inStockOrderDTOList.stream().filter(p -> CollectionUtils.isNotEmpty(p.getInStockOrderItemDTOList()))
                .flatMap(orderDTO -> orderDTO.getInStockOrderItemDTOList().stream()).collect(Collectors.toList());
        // 获取内配入Map<refOrderId,id>
        Map<Long, Long> orderIdMap = inStockOrderDTOList.stream()
            .collect(Collectors.toMap(p -> Long.valueOf(p.getRefOrderId()), p -> p.getId(), (v1, v2) -> v1));
        // 获取内配入Map<refOrderItemId,itemId>
        Map<Long, Long> orderItemIdMap = orderItems.stream()
            .collect(Collectors.toMap(p -> Long.valueOf(p.getRefOrderItemId()), p -> p.getId(), (v1, v2) -> v1));
        // 获取内配入Map<refOrderItemId,skuId>
        Map<Long, Long> orderItemSkuIdMap = orderItems.stream()
            .collect(Collectors.toMap(p -> Long.valueOf(p.getRefOrderItemId()), p -> p.getSkuId(), (v1, v2) -> v1));

        if (CollectionUtils.isNotEmpty(outOrderDTOS)) {
            // 获取内配出订单项
            List<OrderItemDTO> outOrderItems =
                outOrderDTOS.stream().filter(p -> CollectionUtils.isNotEmpty(p.getItems()))
                    .flatMap(orderDTO -> orderDTO.getItems().stream()).collect(Collectors.toList());
            // 获取内配出Map<id,businessId>
            Map<Long, Long> outOrderIdMap = outOrderDTOS.stream()
                .collect(Collectors.toMap(p -> p.getId(), p -> Long.valueOf(p.getBusinessId()), (v1, v2) -> v1));
            // 获取内配出Map<itemId,businessItemId>
            Map<Long, Long> outOrderItemIdMap = outOrderItems.stream()
                .collect(Collectors.toMap(p -> p.getId(), p -> Long.valueOf(p.getBusinessItemId()), (v1, v2) -> v1));

            // 查询内配出装箱明细数据
            PackageOrderItemQueryDTO queryDTO = new PackageOrderItemQueryDTO();
            queryDTO.setOrgId(deliveryCity);
            queryDTO.setWarehouseId(deliveryWarehouseId);
            queryDTO.setRefOrderNoList(refOrderNoList);
            queryDTO.setPackageType(PackageTypeEnum.包装箱.getType());
            List<PackageOrderItemDTO> packageOrderItemDTOS = iPackageOrderItemService.listPackageOrderItem(queryDTO);
            LOG.info("原内配出装箱明细查询结果：{}", JSON.toJSONString(packageOrderItemDTOS));
            if (CollectionUtils.isNotEmpty(packageOrderItemDTOS)) {
                List<PackageOrderItemDTO> newPackageOrderItemDTOS = new ArrayList<>();
                packageOrderItemDTOS.stream().forEach(p -> {
                    Long outBusinessId = outOrderIdMap.get(p.getRefOrderId());
                    Long outBusinessItemId = outOrderItemIdMap.get(p.getRefOrderItemId());
                    if (null != outBusinessId && null != outBusinessItemId && null != orderIdMap.get(outBusinessId)
                        && null != orderItemIdMap.get(outBusinessItemId)) {
                        p.setRefOrderId(orderIdMap.get(outBusinessId));
                        p.setRefOrderItemId(orderItemIdMap.get(outBusinessItemId));
                        p.setSkuId(orderItemSkuIdMap.get(outBusinessItemId));
                        p.setWarehouseId(receiptWarehouseId);
                        p.setOrgId(receiptCity);

                        newPackageOrderItemDTOS.add(p);
                    }
                });

                LOG.info("内配入装箱明细批量新增，入参：{}", JSON.toJSONString(newPackageOrderItemDTOS));
                if (!CollectionUtils.isEmpty(newPackageOrderItemDTOS)) {
                    iPackageOrderItemService.savePackageOrderItemBatch(newPackageOrderItemDTOS);
                }
            }
        }
    }

    /**
     * 根据出库单生成入库单
     *
     * @param deliveryOrders
     * @param orderNoList
     * @param outStockOrderItemDetailDTOS
     * @return
     */
    public List<InStockOrderDTO> createInStockOrderByOrderNo(InternalDistributionOrderDeliveryDTO deliveryOrders,
        List<String> orderNoList, boolean useDeliver, List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS) {
        Integer deliveryCity = deliveryOrders.getDeliveryCity();
        Integer deliveryWarehouseId = deliveryOrders.getDeliveryWarehouseId();
        Integer receiptCity = deliveryOrders.getReceiptCity();
        Integer receiptWarehouseId = deliveryOrders.getReceiptWarehouseId();
        if (CollectionUtils.isEmpty(orderNoList)) {
            LOG.info("内配单[城市-{}-仓库{}]-查询出库数据订单号为空！", deliveryCity, deliveryWarehouseId);
            return null;
        }
        // 获取订单是否生成内配单
        List<OrderDTO> outStockOrders =
            outStockCommQueryService.findOrderByNos(deliveryCity, deliveryWarehouseId, orderNoList);
        if (CollectionUtils.isEmpty(outStockOrders)) {
            LOG.info("内配单[城市-{}]-查询出库数据不存在！查询参数：{}", deliveryCity, JSON.toJSONString(orderNoList));
            return null;
        }

        // 根据实际出库结果处理detail数据
        processItemDetails(outStockOrders, outStockOrderItemDetailDTOS);

        // 将内配单转化成入库单
        List<InStockOrderDTO> inStockOrderDTOS = InternalOrderDeliveryConvert
            .convertToInStockOrderDTOList(outStockOrders, receiptCity, receiptWarehouseId, deliveryCity);

        // 处理普通内配前置仓入库单数据
        processInStockOrderData(inStockOrderDTOS, deliveryOrders);

        if (CollectionUtils.isNotEmpty(inStockOrderDTOS)
            && CollectionUtils.isNotEmpty(deliveryOrders.getDeliveryJiupiOrders()) && useDeliver) {
            Map<Long,
                List<InventoryDeliveryJiupiOrderItem>> jiupiOrderItemMap = deliveryOrders.getDeliveryJiupiOrders()
                    .stream().filter(d -> d != null && CollectionUtils.isNotEmpty(d.getItems()))
                    .flatMap(d -> d.getItems().stream()).filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getOrderItem_Id));
            if (!jiupiOrderItemMap.isEmpty()) {
                // 没有 detail 且 useDeliver 属性则用发车数据生成 detail
                inStockOrderDTOS.stream().filter(Objects::nonNull).forEach(order -> {
                    List<InStockOrderItemDTO> itemList = order.getInStockOrderItemDTOList();
                    if (CollectionUtils.isNotEmpty(itemList)) {
                        itemList.stream().filter(Objects::nonNull).forEach(item -> {
                            Long refOrderItemId =
                                org.apache.commons.lang3.StringUtils.isNumeric(item.getRefOrderItemId())
                                    ? Long.valueOf(item.getRefOrderItemId()) : null;
                            List<InventoryDeliveryJiupiOrderItem> jiupiOrderItems =
                                jiupiOrderItemMap.get(refOrderItemId);
                            if (CollectionUtils.isEmpty(item.getItemDetailDTOList())
                                && CollectionUtils.isNotEmpty(jiupiOrderItems)) {
                                List<InStockOrderItemDetailDTO> detailDTOList =
                                    jiupiOrderItems.stream().filter(Objects::nonNull).map(jiupiItem -> {
                                        InStockOrderItemDetailDTO detailDTO = TranseferUtil
                                            .transferObjectIgnoreCase(jiupiItem, InStockOrderItemDetailDTO.class);
                                        detailDTO.setUnitTotalCount(jiupiItem.getDeliverCount());
                                        detailDTO
                                            .setProductSpecificationId(jiupiItem.getProductSpecification_Id() == null
                                                ? item.getProductSpecificationId()
                                                : jiupiItem.getProductSpecification_Id());
                                        detailDTO.setOrgId(item.getOrgId());
                                        detailDTO.setInStockOrderItemId(item.getId());
                                        return detailDTO;
                                    }).collect(Collectors.toList());
                                item.setItemDetailDTOList(detailDTOList);
                            }
                        });
                    }
                });
            }
        }
        // 需要将发货城市sku转化成收货城市sku
        transformToReceiptSku(receiptCity, receiptWarehouseId, deliveryCity, inStockOrderDTOS);
        // 根据出库信息回填入库单生产日期
        processProductDate(deliveryCity, deliveryWarehouseId, outStockOrders, inStockOrderDTOS);

        return inStockOrderDTOS;
    }

    /**
     * 根据收货城市仓库信息将发货城市sku转化成收货城市sku
     */
    private void transformToReceiptSku(Integer receiptCity, Integer receiptWarehouseId, Integer deliveryCity,
        List<InStockOrderDTO> inStockOrderDTOS) {
        if (CollectionUtils.isEmpty(inStockOrderDTOS)) {
            return;
        }
        String companyCode = inStockOrderDTOS.get(0).getCompanyCode();
        List<InStockOrderItemDTO> inStockOrderItemDTOS = inStockOrderDTOS.stream()
            .filter(e -> e != null && CollectionUtils.isNotEmpty(e.getInStockOrderItemDTOList()))
            .flatMap(e -> e.getInStockOrderItemDTOList().stream()).filter(Objects::nonNull)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inStockOrderItemDTOS)) {
            return;
        }
        List<ProductSkuDeliveryItemSO> deliveryItems = inStockOrderItemDTOS.stream().map(e -> {
            ProductSkuDeliveryItemSO deliveryItemSO = new ProductSkuDeliveryItemSO();
            deliveryItemSO.setSkuId(e.getSkuId());
            deliveryItemSO.setSpecId(e.getProductSpecificationId());
            deliveryItemSO.setOwnerId(e.getOwnerId());
            deliveryItemSO.setSaleModel(e.getSaleModel());
            return deliveryItemSO;
        }).collect(Collectors.toList());

        ProductSkuDeliverySO deliverySO = new ProductSkuDeliverySO();
        deliverySO.setDeliveryCityId(receiptCity);
        deliverySO.setDeliveryWarehouseId(receiptWarehouseId);
        deliverySO.setCityId(deliveryCity);
        deliverySO.setCompanyCode(StringUtils.isBlank(companyCode) ? CompanyCode.YJP : companyCode);
        deliverySO.setSkuList(deliveryItems);
        Map<Long, ProductSkuDTO> productSkuDeliveryMap = iProductSkuQueryService.getProductSkuDeliveryMap(deliverySO);
        if (productSkuDeliveryMap == null || productSkuDeliveryMap.isEmpty()) {
            return;
        }
        inStockOrderItemDTOS.forEach(itemDTO -> {
            ProductSkuDTO skuDTO = productSkuDeliveryMap.get(itemDTO.getSkuId());
            if (skuDTO != null) {
                itemDTO.setSkuId(skuDTO.getProductSkuId());
            }
        });
    }

    /**
     * 根据出库信息回填入库单生产日期
     */
    private void processProductDate(Integer deliveryCity, Integer deliveryWarehouseId, List<OrderDTO> outStockOrders,
        List<InStockOrderDTO> inStockOrderDTOS) {
        if (CollectionUtils.isEmpty(outStockOrders) || CollectionUtils.isEmpty(inStockOrderDTOS)) {
            return;
        }

        LOG.info("内配单保存生产日期，入参-原出库单：{},组装后入库单：{}", JSON.toJSONString(outStockOrders),
            JSON.toJSONString(inStockOrderDTOS));

        // 获取出库单号
        List<String> outOrderNos =
            outStockOrders.stream().filter(o -> o != null && !StringUtils.isBlank(o.getRefOrderNo()))
                .map(o -> o.getRefOrderNo()).distinct().collect(Collectors.toList());
        // 获取组装后的入库单详细数据
        List<InStockOrderItemDetailDTO> itemDetailDTOList = inStockOrderDTOS.stream()
            .filter(d -> d != null && CollectionUtils.isNotEmpty(d.getInStockOrderItemDTOList()))
            .flatMap(d -> d.getInStockOrderItemDTOList().stream())
            .filter(d -> d != null && CollectionUtils.isNotEmpty(d.getItemDetailDTOList()))
            .flatMap(d -> d.getItemDetailDTOList().stream()).collect(Collectors.toList());

        List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOS = new ArrayList<>();
        ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO =
            new ProductStoreBatchChangeRecordQueryDTO();
        productStoreBatchChangeRecordQueryDTO.setCityId(deliveryCity);
        productStoreBatchChangeRecordQueryDTO.setWarehouseId(deliveryWarehouseId);
        productStoreBatchChangeRecordQueryDTO.setOrderNos(outOrderNos);
        LOG.info("内配单保存生产日期，查询批次库存变更记录-入参：{}", JSON.toJSONString(productStoreBatchChangeRecordQueryDTO));
        productStoreBatchChangeRecordDTOS.addAll(
            iBatchInventoryQueryService.selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO));
        LOG.info("内配单保存生产日期，查询批次库存变更记录-结果：{}", JSON.toJSONString(productStoreBatchChangeRecordDTOS));

        if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOS)) {
            // 按规格id + 一级货主id + 二级货主id 进行分组
            Map<String,
                List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdMap = productStoreBatchChangeRecordDTOS
                    .stream().filter(p -> null != p && null != p.getProductionDate()).collect(Collectors.groupingBy(
                        it -> it.getProductSpecificationId() + "_" + it.getOwnerId() + "_" + it.getSecOwnerId()));
            if (ownerIdAndSecOwnerIdMap != null && ownerIdAndSecOwnerIdMap.size() > 0) {
                itemDetailDTOList.stream().filter(d -> d != null).forEach(d -> {
                    List<ProductStoreBatchChangeRecordDTO> productStoreBatchChangeRecordDTOList =
                        ownerIdAndSecOwnerIdMap
                            .get(d.getProductSpecificationId() + "_" + d.getOwnerId() + "_" + d.getSecOwnerId());
                    LOG.info("内配单保存生产日期，获取map：{}", JSON.toJSONString(productStoreBatchChangeRecordDTOList));
                    if (CollectionUtils.isNotEmpty(productStoreBatchChangeRecordDTOList)) {
                        d.setProductionDate(productStoreBatchChangeRecordDTOList.get(0).getProductionDate());
                    }
                });
            }
        }
    }

    /**
     * 内配自动生成前置仓入库单数据处理 前置仓入库单号重新生成，并记录关联订单数据
     */
    public void processInStockOrderData(List<InStockOrderDTO> inStockOrderDTOS,
        InternalDistributionOrderDeliveryDTO deliveryOrders) {
        LOG.info("InStockTransferOrderAsyncBL.processInStockOrderData-入参inStockOrderDTOS：{}, deliveryOrders：{}",
            JSON.toJSONString(inStockOrderDTOS), JSON.toJSONString(deliveryOrders));
        if (CollectionUtils.isEmpty(inStockOrderDTOS) || Objects.isNull(deliveryOrders)) {
            return;
        }

        List<String> internalInOrderNos = deliveryOrders.getDeliveryJiupiOrders().stream()
            .filter(order -> order != null && OrderConstant.ALLOT_TYPE_ALLOCATION.equals(order.getAllotType()))
            .map(e -> e.getOrderNo()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalInOrderNos)) {
            return;
        }

        List<InStockOrderDTO> newInStockOrderDTOS = inStockOrderDTOS.stream()
            .filter(p -> internalInOrderNos.contains(p.getRefOrderNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newInStockOrderDTOS)) {
            return;
        }

        // boolean isOpenOrderCenter =
        // iWarehouseQueryService.isOpenOrderCenter(newInStockOrderDTOS.get(0).getWarehouseId());

        LOG.info("InStockTransferOrderAsyncBL.processInStockOrderData-仓库灰度：， 待处理数据newInStockOrderDTOS：{}",
            JSON.toJSONString(newInStockOrderDTOS));
        newInStockOrderDTOS.forEach(order -> {
            order.setRelatedNoteNO(order.getRefOrderNo());
            order.setRelatedNoteId(order.getBusinessId());
            order.setRefOrderNo(AllotOrderTypeConstans.NPQZ_NOTENO + order.getRefOrderNo());
            order.setBusinessId(order.getId() != null ? order.getId().toString() : null);
            order.setInBoundType(InBoundTypeEnum.DELIVERY_FAILURE_PART_DELIVERY.getCode());
            if (CollectionUtils.isNotEmpty(order.getInStockOrderItemDTOList())) {
                order.getInStockOrderItemDTOList().forEach(item -> {
                    item.setRelatedItemId(item.getBusinessItemId());
                    item.setBusinessItemId(order.getId() != null ? order.getId().toString() : null);
                });
            }
        });
        LOG.info("InStockTransferOrderAsyncBL.processInStockOrderData-数据处理结果newInStockOrderDTOS：{}",
            JSON.toJSONString(newInStockOrderDTOS));
    }

    /**
     * 根据实际出库结果处理detail数据
     */
    public void processItemDetails(List<OrderDTO> orderDTOList,
        List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS) {
        LOG.info("根据实际出库结果处理detail数据 processInStockOrderData-入参orderDTOList：{}, outStockOrderItemDetailDTOS：{}",
            JSON.toJSONString(orderDTOList), JSON.toJSONString(outStockOrderItemDetailDTOS));
        if (CollectionUtils.isEmpty(orderDTOList) || Objects.isNull(outStockOrderItemDetailDTOS)) {
            return;
        }

        // 出库二级货主信息
        Map<Long, List<OutStockOrderItemDetailDTO>> detailDTOMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(outStockOrderItemDetailDTOS)) {
            detailDTOMap = outStockOrderItemDetailDTOS.stream().filter(item -> item.getOutStockOrderItemId() != null)
                .collect(Collectors.groupingBy(OutStockOrderItemDetailDTO::getOutStockOrderItemId));
        }

        // 出库单项数据
        List<OrderItemDTO> orderItemDTOList = orderDTOList.stream().filter(p -> !CollectionUtils.isEmpty(p.getItems()))
            .flatMap(list -> list.getItems().stream()).filter(p -> !CollectionUtils.isEmpty(p.getItemDetailList()))
            .collect(Collectors.toList());

        Map<Long, List<OutStockOrderItemDetailDTO>> finalDetailDTOMap = detailDTOMap;
        orderItemDTOList.forEach(item -> {
            List<OutStockOrderItemDetailDTO> detailDTOS = finalDetailDTOMap.get(item.getId());
            if (CollectionUtils.isEmpty(detailDTOS)) {
                return;
            }

            List<OrderItemDetailDTO> newDetailDTOS = new ArrayList<>();
            detailDTOS.stream().forEach(detail -> {
                OrderItemDetailDTO newDetailDTO = new OrderItemDetailDTO();
                BeanUtils.copyProperties(detail, newDetailDTO);
                newDetailDTO.setDefective(null);
                newDetailDTOS.add(newDetailDTO);
            });

            if (CollectionUtils.isEmpty(newDetailDTOS)) {
                return;
            }

            item.setItemDetailList(newDetailDTOS);
        });

        LOG.info("根据实际出库结果处理detail数据 processInStockOrderData 结果orderDTOList：{}", JSON.toJSONString(orderDTOList));
    }

}

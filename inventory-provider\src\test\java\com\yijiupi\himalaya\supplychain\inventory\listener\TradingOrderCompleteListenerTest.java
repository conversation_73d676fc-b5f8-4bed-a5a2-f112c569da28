package com.yijiupi.himalaya.supplychain.inventory.listener;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.TradingJiupiOrderDTO;

/**
 * <AUTHOR> 2018/3/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class TradingOrderCompleteListenerTest {

    @Autowired
    private TradingOrderCompleteListener orderCompleteListener;

    @Test
    public void syncApply() {
        String json =
            "{\"createTime\":1528526522000,\"createUserId\":439334,\"id\":898118060915002648,\"lastUpdateTime\":1528532576254,\"lastUpdateUserId\":439334,\"cityId\":898,\"verison\":\"1.0.0\",\"orderNo\":\"898816000010\",\"orderType\":0,\"orderSource\":2,\"userState\":9,\"giveCouponAmount\":0.00,\"giveBonusAmount\":0.00,\"userRemark\":null,\"userCancelReason\":null,\"serviceRemark\":null,\"payType\":11,\"giveWineScore\":8.00,\"costWineScore\":0.00,\"orderAmount\":1320.00,\"payableAmount\":1320.00,\"exactPayAmount\":0.00,\"selfPickUpReduceAmout\":4.00,\"reduceAmount\":0.00,\"productReduceAmount\":0.00,\"orderAttachedGiftRuleId\":null,\"orderReducationRuleId\":null,\"useCouponAmount\":0.00,\"useCouponCodeAmount\":0.00,\"useBonusAmount\":0.00,\"useRewardBonusAmount\":0.00,\"hasPayment\":true,\"isTestOrder\":true,\"user_Id\":439334,\"userName\":\"孙亚琴\",\"userMobileNo\":\"15671562727\",\"userCompanyName\":\"孙亚琴招商2\",\"salesman_Id\":8156,\"salesmanDeptId\":null,\"onlineSalesmanId\":null,\"onlineSalesmanDeptId\":null,\"province\":\"西藏自治区\",\"city\":\"拉萨市\",\"county\":\"堆龙德庆区\",\"street\":\"德庆乡\",\"detailaddress\":\"普罗旺斯\",\"contact\":\"孙亚琴\",\"phone\":\"15671562727\",\"zipcode\":null,\"address_Id\":1327334,\"itemList\":[{\"id\":1466796265953034660,\"orderId\":898118060915002648,\"remark\":null,\"sourceType\":0,\"sourceId\":\"89800000427693\",\"oldId\":null,\"productType\":0,\"createTime\":null,\"itemPrice\":{\"originalPrice\":330.00,\"originalPriceUnit\":\"件\",\"costPrice\":390.0000,\"costPriceUnit\":\"件\",\"sellPrice\":330.00,\"sellUnit\":\"件\",\"minUnitPrice\":55.000000,\"minUnitName\":\"瓶\",\"depositPrice\":0,\"depositUnit\":null,\"reduceProductPrice\":0.00,\"selfPickUpReducePrice\":1.00,\"reduceProductPriceUnit\":\"件\",\"sellToMinUnitQuantity\":6,\"maxToMinUnitQuantity\":6,\"wineScore\":8,\"defaultSpecPrice\":null},\"itemAmount\":{\"reduceProductAmount\":0.00,\"selfPickUpReduceAmount\":4.00,\"reduceCouponAmount\":0.00,\"reduceCouponCodeAmount\":0.00,\"reduceBonusAmount\":0.00,\"reduceRewardBonusAmount\":0.00,\"reduceOrderAmount\":0.00,\"discount\":0.00,\"totalAmount\":1320.00,\"payAmount\":1320.00,\"wineScore\":8,\"giveBonusAmount\":null,\"deliveryAmount\":null,\"depositAmount\":0,\"saleCount\":4,\"realCount\":null,\"minUnitTotalCount\":24,\"sourceCount\":4,\"sysRemark\":null},\"itemProduct\":{\"productId\":89800000427693,\"productVersion\":null,\"productSaleSpecId\":89800000427693,\"productName\":\"五粮液股份VVV珍品级52度500ml\",\"productSaleSpec\":\"6瓶/件\",\"saleSpecQuantity\":6,\"sellUnit\":\"件\",\"productSpec\":\"6瓶/件\",\"specQuantity\":6,\"packageName\":\"件\",\"unitName\":\"瓶\",\"saleMode\":1,\"isUseBonus\":true,\"isUseCoupon\":true,\"isCumulative\":false,\"isInOrderGive\":true,\"isAdditionalPurchase\":false,\"hiddenProduct\":false,\"supplierId\":null,\"lastUpdateTime\":null,\"isRegionPurchaseProduct\":true,\"productBusinessClass\":0},\"itemProperty\":null}],\"traceList\":[{\"createTime\":1528530074000,\"createUserId\":439334,\"id\":1466796265953034659,\"lastUpdateTime\":null,\"lastUpdateUserId\":null,\"traceType\":101,\"orderDescription\":\"下单成功\",\"orderMessage\":\"订单号：898816000010 下单成功.\",\"prevStatus\":null,\"currentStaus\":null}],\"classify\":0,\"orderTime\":{\"createTime\":1528526522000,\"auditTime\":1528526522000,\"deliverTime\":null,\"cancelTime\":null,\"completeTime\":1528532576254,\"syncTime\":null,\"printTime\":null,\"confirmTime\":1528526522000},\"deliveryMode\":5,\"sysRemark\":\"\",\"paymentState\":10,\"orderBatchId\":898418060914002531,\"payMode\":null,\"mallAppType\":null,\"shopId\":null,\"pickupWarehouseId\":8986,\"orderOddAmount\":{\"lastOddBalanceAmount\":0.00,\"newOddBalanceAmount\":0.00,\"firstOddAmount\":0.00,\"twiceOddAmount\":null,\"twiceOddCorrectAmount\":null,\"oddAmount\":0.00},\"orderItemCount\":null,\"supplierId\":null,\"supplierName\":null,\"bigGoodsMode\":null,\"channelType\":0,\"userSource\":null,\"shippingFeePayer\":null,\"shippingFee\":null,\"logisticsName\":null,\"logisticsId\":null,\"shippingOrderNO\":null,\"warehouse_Id\":8986,\"delivery_Id\":null,\"stevedore_Id\":null,\"relatedOrder_Id\":null,\"state\":7,\"deliveryTask_Id\":null,\"deliveryCollectionAmount\":null,\"deliveryLossAmount\":null,\"deliveryState\":null,\"taskSequence\":null,\"isPreSale\":false,\"hasAssignCar\":null,\"carId\":null,\"storeAdminId\":null,\"payee\":0,\"pickupType\":0,\"jiupiOrderSource\":1,\"partnerPayState\":null,\"expectDeliveryDays\":0,\"orderCompleteTime\":1528532576254,\"orderCancleTime\":null,\"orderDeliverTime\":null,\"jiupiOrderType\":1,\"orderAssignedTime\":null,\"orderAssigned_Id\":null,\"printTimes\":null}";
        TradingJiupiOrderDTO orderDTO = JSON.parseObject(json, TradingJiupiOrderDTO.class);
        // orderCompleteListener.syncApply(orderDTO,null);
    }

}

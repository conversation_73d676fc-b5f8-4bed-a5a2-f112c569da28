package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.framework.rabbit.delay.DelayMessageTemplate;
import com.yijiupi.himalaya.assignment.dto.push.MessagePushParam;
import com.yijiupi.himalaya.assignment.dto.push.MessagePushTarget;
import com.yijiupi.himalaya.assignment.service.IMessagePushService;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.variable.VariableManager;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import com.yijiupi.supplychain.serviceutils.constant.AdminUserAuthRoleConstant;

/**
 * 促销库存消息发送
 */
@Service
public class PromotionStoreBatchEventBL {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionStoreBatchEventBL.class);

    @Autowired
    private DelayMessageTemplate delayMessageTemplate;

    @Resource
    private VariableManager variableManager;

    @Reference
    private IMessagePushService messagePushService;

    @Reference
    private IProductCategoryService iProductCategoryService;

    private static final String TITLE = "临期滞销强制处理";

    private static final String TEMPLATE =
        "产品【%s】的生产日期为%s的批次已临过期，该批次库存已自动转入残次品区，不再对外销售，请及时将实物移动到残次品区，如客户投诉收到临过期产品，每个投诉将导致仓库主管不享受绩效50元！";

    private static final String UNSALABLE_PERIOD_TEMPLATE = "产品【%s】超%s天无动销，标记为绝对滞销，所有批次库存已自动转入残次品区，请及时将实物移动到残次品区！";

    /**
     * 自动转残次品通知仓管
     */
    @Async
    public void transferToDefectiveNotifyEvent(boolean autoMove, List<BatchInventoryDTO> batchInventoryDTOS) {
        if (CollectionUtils.isEmpty(batchInventoryDTOS)) {
            return;
        }

        List<Long> skuIds =
            batchInventoryDTOS.stream().map(BatchInventoryDTO::getProductSkuId).distinct().collect(Collectors.toList());
        Map<Long,
            Integer> categoryDTOMap = iProductCategoryService.getCategoryPeriodBySkuIds(skuIds).stream()
                .filter(p -> p != null && StringUtils.hasText(p.getUnsalablePeriod())).collect(Collectors
                    .toMap(p -> p.getProductSkuId(), p -> Integer.valueOf(p.getUnsalablePeriod()), (v1, v2) -> v1));

        List<MessagePushParam> messageManagerDTOS = batchInventoryDTOS.stream().map(dto -> {
            MessagePushParam message = new MessagePushParam();
            message.setWarehouseId(dto.getWarehouseId());
            message.setTarget(MessagePushTarget.PDA.getAppCode());
            message.setRoleCodes(Arrays.asList(AdminUserAuthRoleConstant.ROLE_CODE_仓库管理员新流程));
            message.setTaskProperty(dto.getStorageAttribute() != null ? dto.getStorageAttribute().intValue() : 0);
            message.setTitle(TITLE);
            message.setContent(getContent(autoMove, dto, categoryDTOMap));
            message.setSaveMessage(true);
            return message;
        }).collect(Collectors.toList());
        LOG.info("发送促销批次转残次品通知消息：{}", JSON.toJSONString(messageManagerDTOS));
        messagePushService.pushMessageBatchByTaskProperty(messageManagerDTOS);
    }

    private String getContent(boolean autoMove, BatchInventoryDTO dto, Map<Long, Integer> categoryDTOMap) {
        if (autoMove) {
            return String.format(UNSALABLE_PERIOD_TEMPLATE, dto.getProductSkuName(),
                categoryDTOMap.get(dto.getProductSkuId()));
        }
        return String.format(TEMPLATE, dto.getProductSkuName(), DateUtils.getDateFormat(dto.getProductionDate()));
    }

}

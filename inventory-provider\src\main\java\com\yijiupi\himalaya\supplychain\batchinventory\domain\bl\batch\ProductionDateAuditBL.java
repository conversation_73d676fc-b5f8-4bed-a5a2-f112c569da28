package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import static com.yijiupi.himalaya.supplychain.batchinventory.enums.ProductionDateAuditState.HAS_AUDITED;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskDTO;
import com.yijiupi.himalaya.assignment.dto.todo.TodoTaskOperateParam;
import com.yijiupi.himalaya.assignment.service.IProductionDateTaskService;
import com.yijiupi.himalaya.assignment.service.ITodoTaskService;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.ProductionDateAuditConverter;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductSkuMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductionDateAuditPOMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.ProductionDateAuditPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.productSku.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateAuditQuery;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.productiondate.ProductionDateTodoTaskDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.ProductPromotionStoreBatchDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.enums.ProductionDateAuditState;
import com.yijiupi.himalaya.supplychain.dto.VariableDefAndValueDTO;
import com.yijiupi.himalaya.supplychain.dto.VariableValueQueryDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductCategoryGroupDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryDTO;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductInfoCategoryQueryDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductCategoryGroupService;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductInfoCategoryService;
import com.yijiupi.himalaya.supplychain.service.IVariableValueService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.ShelfLifeUnitEnum;

/**
 * <AUTHOR>
 * @since 2025-03-14 11:57
 **/
@Service
public class ProductionDateAuditBL {

    @Resource
    private ProductionDateAuditPOMapper productionDateAuditPOMapper;

    @Resource
    private ProductionDateAuditConverter productionDateAuditConverter;

    @Resource
    private BatchInventoryProductSkuMapper batchInventoryProductSkuMapper;

    @Reference
    private IProductionDateTaskService productionDateTaskService;

    @Reference
    private IProductCategoryGroupService productCategoryGroupService;

    @Reference
    private IProductInfoCategoryService productInfoCategoryService;

    @Reference
    private ITodoTaskService todoTaskService;

    @Reference
    private IVariableValueService variableValueService;

    @Autowired
    private PromotionStoreBatchBL promotionStoreBatchBL;

    private static final int SYSTEM_ADMIN = 1;

    private static final Byte BUSINESS_TYPE_LIN_QI = Byte.valueOf("1");

    private static final Logger logger = LoggerFactory.getLogger(ProductionDateAuditBL.class);

    public ProductionDateTodoTaskDTO queryTodoTaskByProductionDate(ProductionDateAuditQuery query) {
        Integer warehouseId = query.getWarehouseId();
        String productionDate = query.getProductionDate() + " 00:00:00";
        Long skuId = query.getSkuId();
        String productName = query.getProductName();
        String businessNo = String.format("%s,%s,%s,%s", warehouseId, productionDate, skuId, productName);
        TodoTaskDTO todoTask = productionDateTaskService.queryProductionDateTask(businessNo);
        return todoTask == null ? null : productionDateAuditConverter.toDTO(todoTask);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void applyProductionDateAudit(ProductionDateAuditDTO dto) {
        AssertUtils.notNull(dto, "参数不能为空！");
        AssertUtils.notNull(dto.getWarehouseId(), "仓库ID不能为空！");
        AssertUtils.notNull(dto.getSkuId(), "SkuId不能为空！");
        AssertUtils.notNull(dto.getProductionDate(), "生产日期不能为空！");
        // 如果本批次已经存在审批, 则不允许重新发起
        // 大于临期，但还未到过期时间，产品继续销售，可以提交审核
        checkIfCanApply(dto);
        ProductionDateAuditPO po = productionDateAuditConverter.toPO(dto);
        productionDateAuditPOMapper.insertSelective(po);
        // 审核中或者审核通过的产品, 该批次日期不再产生新的生产日期任务, 也不会产生逾期罚款
        todoTaskService.markTodoTaskAsNotRequiredToHandle(TodoTaskOperateParam.of(po.getRefTodoTaskId(), SYSTEM_ADMIN));
        // 若上传图片的生产日期与系统批次的生产日期相同 (前端判断这个), 则直接完成这个任务
        if (Boolean.TRUE.equals(dto.getApplyPassed())) {
            logger.info("符合条件, 自动审核通过");
            ProductionDateAuditPO update = new ProductionDateAuditPO();
            update.setId(po.getId());
            update.setState(ProductionDateAuditState.AUDIT_PASSED.getValue());
            update.setAuditRemark("系统自动审核通过");
            productionDateAuditPOMapper.updateByPrimaryKeySelective(update);
        }
    }

    public ProductionDateAuditDTO queryAuditInfo(ProductionDateAuditQuery query) {
        List<ProductionDateAuditPO> result = productionDateAuditPOMapper.pageList(query);
        if (result.isEmpty()) {
            return null;
        }
        // 同一个仓库、同一个 sku、同一个生产日期的审核只会存在一条
        return productionDateAuditConverter.toDTO(result).get(0);
    }

    public PageList<ProductionDateAuditDTO> pageListProductionDateAudit(ProductionDateAuditQuery query) {
        PageHelper.startPage(query.getCurrentPage(), query.getPageSize());
        return productionDateAuditPOMapper.pageList(query).toPageList(productionDateAuditConverter::toDTO);
    }

    public List<ProductionDateAuditDTO> listProductionDateAudit(ProductionDateAuditQuery query) {
        return productionDateAuditConverter.toDTO(productionDateAuditPOMapper.pageList(query));
    }

    @Transactional(rollbackFor = Throwable.class)
    public void auditProductionDateAudit(ProductionDateAuditDTO dto) {
        ProductionDateAuditPO po = productionDateAuditPOMapper.selectByPrimaryKey(dto.getId());
        AssertUtils.notNull(po, "生产日期审核不存在");
        AssertUtils.isTrue(!HAS_AUDITED.contains(po.getState()), "该审批已经审核过一次, 无法继续审核");
        productionDateAuditPOMapper.updateByPrimaryKeySelective(productionDateAuditConverter.toUpdatePO(dto));
        TodoTaskOperateParam param = TodoTaskOperateParam.of(po.getRefTodoTaskId(), SYSTEM_ADMIN);
        if (Boolean.TRUE.equals(dto.getApplyPassed())) {
            // 审核中或者审核通过的产品, 该批次日期不再产生新的生产日期任务, 也不会产生逾期罚款
            todoTaskService.markTodoTaskAsNotRequiredToHandle(param);
            if (dto.getBusinessType() != null) {
                createPromotionStoreBatch(param);
            }
        } else {
            // 若审核拒绝, 还原上边的操作
            todoTaskService.restartTodoTask(param);
        }
    }

    /**
     * 查询已拒绝申请单能否重新发起
     */
    private Boolean queryRefuseCanApplayConfig(Integer warehouseId) {
        VariableValueQueryDTO queryDTO = new VariableValueQueryDTO();
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setVariableKey("ENABLE_PRODUCTION_DATE_REFUSE_APPLY");
        VariableDefAndValueDTO variableDefAndValueDTO = variableValueService.detailVariable(queryDTO);
        if (variableDefAndValueDTO == null || variableDefAndValueDTO.getVariableData() == null) {
            return false;
        }
        return Boolean.valueOf(variableDefAndValueDTO.getVariableData());
    }

    private void checkIfCanApply(ProductionDateAuditDTO dto) {
        ProductionDateAuditQuery query = new ProductionDateAuditQuery();
        query.setWarehouseId(dto.getWarehouseId());
        query.setSkuId(dto.getSkuId());
        query.setProductionDate(dto.getProductionDate());
        // 如果有已审核和审核中的，禁止重新发起
        List<Integer> lstState = new ArrayList<>();
        lstState.add(ProductionDateAuditState.AUDITING.getValue());
        lstState.add(ProductionDateAuditState.AUDIT_PASSED.getValue());
        // 如果开了配置，审核拒绝也可以重新发起，否则审核拒绝不允许重新发起
        if (!queryRefuseCanApplayConfig(dto.getWarehouseId())) {
            lstState.add(ProductionDateAuditState.AUDIT_REJECTED.getValue());
        }
        query.setState(lstState);
        PageResult<ProductionDateAuditPO> list = productionDateAuditPOMapper.pageList(query);
        AssertUtils.isTrue(list.isEmpty(), "该批次已经发起过审批, 无法重新发起");
        ProductSkuPO sku =
            batchInventoryProductSkuMapper.getProductSkuListByIds(Collections.singletonList(dto.getSkuId())).get(0);
        AssertUtils.notNull(sku, "skuId不存在");
        if (sku.getShelfLifeLongTime() != null && sku.getShelfLifeLongTime()) {
            return;
        }
        TemporalUnit field = getShelfLifeUnit(sku);
        Integer life = sku.getMonthOfShelfLife();
        if (life == null || life == 0) {
            return;
        }
        LocalDate productionDate = DateUtils.getLocalDateByDate(DateUtils.getDateByDateString(dto.getProductionDate()));
        LocalDate now = LocalDate.now();
        // 过期日期 = 生产日期 + 保质期
        LocalDate expiryDate = productionDate.plus(life, field);
        // 当前日期 >= 过期日期 就不允许发起审核
        AssertUtils.isTrue(now.isBefore(expiryDate), "当前批次已经过期, 无法发起审核");
    }

    @Nullable
    private Integer queryCategoryForbidSalesPeriod(Long skuId) {
        ProductInfoCategoryQueryDTO piq = new ProductInfoCategoryQueryDTO();
        piq.setSkuIds(Collections.singletonList(skuId));
        List<ProductInfoCategoryDTO> infoCategories = productInfoCategoryService.findProductCategoryBySkuIds(piq);
        if (CollectionUtils.isEmpty(infoCategories)) {
            return null;
        }
        ProductInfoCategoryDTO infoCategory = infoCategories.get(0);
        List<Long> categoryIds =
            Arrays.asList(infoCategory.getStatisticsClass(), infoCategory.getSecondStatisticsClass());
        // parentId 为 null 的是二级类目, 优先取二级类目配置了 禁止销售日期 的数据
        return productCategoryGroupService.listByIds(categoryIds).stream()
            .sorted(Comparator.comparing(ProductCategoryGroupDTO::getParentId,
                Comparator.nullsFirst(Comparator.naturalOrder())))
            .map(ProductCategoryGroupDTO::getForbidSalesPeriod).filter(StringUtils::hasText).findFirst()
            .map(Integer::parseInt).orElse(null);
    }

    private TemporalUnit getShelfLifeUnit(ProductSkuPO sku) {
        ShelfLifeUnitEnum shelfLifeUnit = ShelfLifeUnitEnum.getEnum(sku.getShelfLifeUnit());
        // 没设置保质期单位，默认为月（兼容老数据为0的场景）
        if (shelfLifeUnit == null) {
            return ChronoUnit.MONTHS;
        }
        switch (shelfLifeUnit) {
            case 年:
                return ChronoUnit.YEARS;
            case 月:
                return ChronoUnit.MONTHS;
            case 日:
                return ChronoUnit.DAYS;
            default:
                throw new BusinessValidateException("不支持的保质期单位: " + sku.getShelfLifeUnit());
        }
    }

    private void createPromotionStoreBatch(TodoTaskOperateParam param) {
        TodoTaskOperateParam queryParam = new TodoTaskOperateParam();
        queryParam.setIsDeleted(YesOrNoEnum.NO.getValue());
        queryParam.setTaskId(param.getTaskId());
        TodoTaskDTO taskDTO = todoTaskService.getTodoTaskDetail(queryParam);
        logger.info("生产日期治理任务查询结果：{}", JSON.toJSONString(taskDTO));
        if (Objects.isNull(taskDTO)) {
            logger.info("待生成促销的生产日期治理任务不存在 任务id：{}", param.getTaskId());
            return;
        }

        // 获取批次库存id, BusinessNo 值为 9981,9bacf18c464e46cb9dbe365bf01ee198,4863512194543254020,补货测试大
        String storeBatchId = taskDTO.getBusinessNo().split(",")[1];
        ProductPromotionStoreBatchDTO addDTO = new ProductPromotionStoreBatchDTO();
        addDTO.setStoreBatchIds(Arrays.asList(storeBatchId));
        addDTO.setCreateUser(String.valueOf(param.getUserId()));
        promotionStoreBatchBL.batchInsertByStoreBatchIds(addDTO);
    }

}

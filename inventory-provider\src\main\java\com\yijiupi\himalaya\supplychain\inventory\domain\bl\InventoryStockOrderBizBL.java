package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.enums.StoreOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockFetchUpdateDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.InStockFetchMethodEnum;
import com.yijiupi.himalaya.supplychain.instockorder.query.InStockOrderQueryDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockCommService;
import com.yijiupi.himalaya.supplychain.inventory.domain.aspect.InventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.InventoryChangeFactory;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.SellInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.StockOrderInventoryDTOConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.dto.StockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryChangeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderState;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.PushStateEnum;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.service.IVirtualWarehouseManageService;
import com.yijiupi.himalaya.supplychain.virtualwarehouse.service.IVirtualWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuBySpecificationSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class InventoryStockOrderBizBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryStockOrderBizBL.class);

    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;

    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    @Autowired
    private InventorySendFaildMQ inventorySendFaildMQ;

    @Autowired
    private InventoryChangeFactory inventoryChangeFactory;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;
    @Reference
    private IVirtualWarehouseQueryService iVirtualWarehouseQueryService;
    @Reference
    private IInStockCommService iInStockCommService;
    @Reference
    private IVirtualWarehouseManageService iVirtualWarehouseManageService;
    @Reference
    private IWarehouseQueryService iWarehouseQueryService;

    @Autowired
    private IdempotenceConsumer idempotenceConsumer;

    /**
     * 代运营-退货批次
     */
    private static final Byte FETCH_TYPE_DYY_RETURN = 4;

    /**
     * 出库单据库存处理
     *
     * @param processDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public List<WarehouseInventoryChangeDTO>
        processOutStockOrderInventory(ProcessOutStockOrderInventoryDTO processDTO) {
        LOG.info("出库单据参数:{}", JSON.toJSONString(processDTO));
        // 单据转换
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS =
                warehouseChangListBOConverter.processOutStockOrderInventoryDTOToWarehouseInventoryChangeBOS(processDTO);

        LOG.info("出库单据库存处理BO:{}", JSON.toJSONString(warehouseInventoryChangeBOS));
        // 仓库库存处理
        boolean isProcessStore = CollectionUtils.isNotEmpty(warehouseInventoryChangeBOS)
                && (processDTO.getNeedToChangeStore() || processDTO.getNeedToChangeBatchStore());
        if (isProcessStore) {
            // 处理库存变更.库存记录
            warehouseInventoryChangeBOS.stream()
                    .collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getValidateSelf))
                    .forEach((validateSelf, inventoryChangeBOS) -> {
                        warehouseInventoryManageBL.validateAndProcessProductStore(warehouseInventoryChangeBOS, validateSelf,
                                processDTO.getNeedUpdateDeliveryCount(), processDTO.getNeedToChangeStore(),
                                processDTO.getNeedToChangeBatchStore(), processDTO.getNeedSkipNotExitsSku());
                    });
        }

        // todo
        // 销售库存处理
        List<WarehouseInventoryChangeBO> sellInventoryList = warehouseInventoryChangeBOS.stream()
                .filter(WarehouseInventoryChangeBO::getHasUpdateOPInventory).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sellInventoryList)) {
            warehouseInventoryManageBL.processSellInventory(sellInventoryList, null);
        }

        return warehouseChangListBOConverter
            .warehouseChangListBO2WarehouseInventoryChangeDTOS(warehouseInventoryChangeBOS);
    }

    /**
     * 处理出库单库存(异常消息重试)
     *
     * @param message
     */
    @Transactional(rollbackFor = Exception.class)
    public void retriesOutStockOrderInventoryByMessage(String message) {
        LOG.info("处理出库单库存重试:{}", message);
        ProcessOutStockOrderInventoryDTO processOutStockOrderInventoryDTO =
                JSON.parseObject(message, ProcessOutStockOrderInventoryDTO.class);
        processOutStockOrderInventory(processOutStockOrderInventoryDTO);
    }

    /**
     * 入库单据库存处理
     *
     * @param processDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void processInStockOrderInventory(ProcessInStockOrderInventoryDTO processDTO) {
        try {
            // 单据转换
            List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS =
                    warehouseChangListBOConverter.processInStockOrderInventoryDTOToWarehouseInventoryChangeBOS(processDTO);

            LOG.info("入库单据库存处理BO:{}", JSON.toJSONString(warehouseInventoryChangeBOS));
            // 仓库库存处理
            boolean isProcessStore = CollectionUtils.isNotEmpty(warehouseInventoryChangeBOS)
                    && (processDTO.getNeedToChangeStore() || processDTO.getNeedToChangeBatchStore());
            if (isProcessStore) {
                // 处理库存变更.库存记录
                warehouseInventoryChangeBOS.stream().filter(e -> e != null)
                        .collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getValidateSelf))
                        .forEach((validateSelf, inventoryChangeBOS) -> {
                            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseInventoryChangeBOS,
                                    validateSelf, processDTO.getNeedUpdateDeliveryCount(), processDTO.getNeedToChangeStore(),
                                    processDTO.getNeedToChangeBatchStore(), processDTO.getNeedSkipNotExitsSku());
                        });
            }

            // 销售库存处理：1、上架后才处理销售库存此处不处理 2、HasUpdateOPInventory = false 的不处理
            List<WarehouseInventoryChangeBO> sellInventoryList =
                    warehouseInventoryChangeBOS.stream()
                            .filter(e -> e != null
                                    && ObjectUtils.defaultIfNull(e.getIsProcessSalesStockAfterPutAway(), (byte) 0) == 0
                                    && e.getHasUpdateOPInventory())
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sellInventoryList)) {
                warehouseInventoryManageBL.processSellInventory(sellInventoryList, null);
            }
        } catch (Exception e) {
            LOG.error(String.format("入库单据库存操作失败,参数:%s", JSON.toJSONString(processDTO)), e);
            if (processDTO.getAllowThrowException() != null && processDTO.getAllowThrowException()) {
                throw e;
            }
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(processDTO), "processInventoryByInStock", e);
        }
    }

    /**
     * 处理出库单库存(异常消息重试)
     *
     * @param message
     */
    @Transactional(rollbackFor = Exception.class)
    public void retriesInStockOrderInventoryByMessage(String message) {
        ProcessInStockOrderInventoryDTO processInStockOrderInventoryDTO = new ProcessInStockOrderInventoryDTO();
        try {
            processInStockOrderInventoryDTO = JSON.parseObject(message, ProcessInStockOrderInventoryDTO.class);
            processInStockOrderInventory(processInStockOrderInventoryDTO);
        } catch (Exception e) {
            LOG.error("处理入库单库存重试失败:{}", message, e);
            // 库存处理会做异常拦截，进此异常就只有json转换出错
            if (processInStockOrderInventoryDTO.getAllowThrowException() != null
                    && processInStockOrderInventoryDTO.getAllowThrowException()) {
                throw e;
            }
            inventorySendFaildMQ.mqSendFaild(message, "processInventoryByInStock", e);
        }
    }

    /**
     * 处理 WMS 不存在单据库存：ERP某些单据不存在WMS但是删除的时候需要处理库存
     */
    @Transactional(rollbackFor = Exception.class)
    public void processWmsNotExitsOrderInventory(List<WarehouseInventoryChangeDTO> changeDTOList,
                                                 boolean checkWarehouseInventory, List<SellInventoryChangeBO> sellChangeList) {
        if (CollectionUtils.isEmpty(changeDTOList)) {
            return;
        }
        changeDTOList.stream().filter(Objects::nonNull).forEach(change -> change
                .setAllocationCalculation(ObjectUtils.defaultIfNull(change.getAllocationCalculation(), false)));
        // todo 需要明确是出库单还是入库单
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS =
                warehouseChangListBOConverter.convertInventoryDTO2InventoryChangeBO(changeDTOList);
        // 处理库存
        warehouseInventoryManageBL.validateAndProcessProductStore(warehouseInventoryChangeBOS, checkWarehouseInventory,
                false, true, true, false);
        // 发送销售库存变更消息
        warehouseInventoryManageBL.processSellInventory(warehouseInventoryChangeBOS, sellChangeList);
    }

    /**
     * 虚仓二次分拣时，经销商取货确认无误 1、司机取货 经销商确认无误（待入库） 2、经销商取货 确认收货（待核验）-> 经销商确认无误
     *
     * @param stockOrderInventoryDTO
     */
    @Deprecated
    public void processOutStockOrderInventoryDYY(StockOrderInventoryDTO stockOrderInventoryDTO) {
        LOG.info("虚仓实配库存管理（取货 退货）请求参数：{}", JSON.toJSONString(stockOrderInventoryDTO));

        List<String> noProcessOrderNos =
                idempotenceConsumer.getNoProcessStockOrderInventoryOrderNo(stockOrderInventoryDTO.getWarehouseId(),
                        stockOrderInventoryDTO, OrderDeliveryOpType.ORDER_INVENTORY_DYY_MESSAGE_TYPE, "取货确认无误");
        if (CollectionUtils.isEmpty(noProcessOrderNos)) {
            LOG.info(String.format("取货确认无误-订单已经全部处理过！%s", JSON.toJSONString(stockOrderInventoryDTO)));
            return;
        }

        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            InStockOrderQueryDTO inOrderQuery = new InStockOrderQueryDTO();
            inOrderQuery.setOrderType((int) InStockOrderTypeEnum.虚仓分拣单.getType());
            inOrderQuery.setRelatedNoteId(stockOrderInventoryDTO.getFetchTaskId().toString());
            List<InStockOrderDTO> inOrderList = iInStockCommService.listInStockOrderInfo(inOrderQuery).getDataList();
            if (CollectionUtils.isNotEmpty(inOrderList)
                    && !inOrderList.stream().allMatch(p -> Objects.equals(p.getState(), InStockOrderState.待审核.getType()))
                    && inOrderList.stream().allMatch(p -> Objects.equals(p.getPushState(), PushStateEnum.已下推.getType()))) {
                throw new BusinessValidateException("取货批次处于非待核验状态不能操作,请让重新出示二维码！");
            }

            this.productInventoryChange(stockOrderInventoryDTO);
            // this.fetchTaskToChangeOms(stockOrderInventoryDTO, stockOrderInventoryDTO.getWarehouseId());

            // 为虚仓二次分拣、入库时，则无需更改库存
            if (CollectionUtils.isNotEmpty(inOrderList)) {
                LOG.info("[虚仓二次分拣]经销商取货确认无误");

                // 根据取货任务id更新虚仓二次分拣入库单remark来记录取货方式
                if (null != stockOrderInventoryDTO.getFetchTaskId()) {
                    stockOrderInventoryDTO.setOrgId(inOrderList.get(0).getOrgId());
                    stockOrderInventoryDTO.setWarehouseId(inOrderList.get(0).getWarehouseId());
                    iVirtualWarehouseManageService.updateInAndOutStockOrder(StockOrderInventoryDTOConvert.convertToVirtualStockOrderInventoryDTO(stockOrderInventoryDTO));
                    LOG.info("[虚仓二次分拣]收货任务更新成功");
                }

                LOG.info("[虚仓二次分拣]收货任务：{}", JSON.toJSONString(inOrderList));
                // 取货任务待核验时，进行入库，并核验取货
                if (Objects.equals(inOrderList.get(0).getState(), InStockOrderState.待审核.getType())
                        && stockOrderInventoryDTO.getOrderType() == 2) {
                    InStockFetchUpdateDTO inStockFetchUpdateDTO = new InStockFetchUpdateDTO();
                    inStockFetchUpdateDTO.setOrgId(inOrderList.get(0).getOrgId());
                    inStockFetchUpdateDTO.setState(InStockOrderState.已入库.getType());
                    inStockFetchUpdateDTO.setOptUserId(stockOrderInventoryDTO.getOperateUserId());
                    inStockFetchUpdateDTO.setOptUserName(stockOrderInventoryDTO.getOperateUserName());
                    inStockFetchUpdateDTO.setFetchTaskId(stockOrderInventoryDTO.getFetchTaskId().toString());
                    inStockFetchUpdateDTO.setFetchMethod(InStockFetchMethodEnum.经销商收货.getType());
                    LOG.info("[虚仓二次分拣]收货任务入库：{}", JSON.toJSONString(inStockFetchUpdateDTO));
                    iInStockCommService.updateStateByFetchTask(inStockFetchUpdateDTO);
                }
            }
        });
    }

    /**
     * tms取货任务确认无误
     */
    // private void fetchTaskToChangeOms(StockOrderInventoryDTO stockOrderInventoryDTO, Integer warehouseId) {
    // if (!stockOrderInventoryDTO.getNeedToChangeTms()) {
    // return;
    // }
    // RealWarehouseInOutStockDTO realWarehouseInOutStockDTO = new RealWarehouseInOutStockDTO();
    // realWarehouseInOutStockDTO.setFetchTaskId(stockOrderInventoryDTO.getFetchTaskId());
    // realWarehouseInOutStockDTO.setFetchTaskType(stockOrderInventoryDTO.getFetchTaskType());
    // realWarehouseInOutStockDTO.setWarehouseId(warehouseId);
    // realWarehouseInOutStockDTO.setUpdateUserId(Objects.equals(stockOrderInventoryDTO.getOperateUserId(), null) ? -1
    // : stockOrderInventoryDTO.getOperateUserId());
    // realWarehouseInOutStockDTO.setUpdateUserName(stockOrderInventoryDTO.getOperateUserName());
    // LOG.info("虚仓实配库存管理（取货 退货）调用OMS-shopWarehouseInOutStock请求参数：{}", JSON.toJSONString(realWarehouseInOutStockDTO));
    // inoutWarehouseService.shopWarehouseInOutStock(realWarehouseInOutStockDTO);
    // }

    /**
     * 产品的库存变化
     */
    private void productInventoryChange(StockOrderInventoryDTO stockOrderInventoryDTO) {
        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        // 转化成库存处理DTO
        List<WarehouseInventoryChangeDTO> changeDTOList = new ArrayList<>();
        // 根据规格查询产品
        ProductSkuBySpecificationSO productSkuBySpecSO = new ProductSkuBySpecificationSO();
        // 规格集合
        List<ProductSkuBySpecificationQueryDTO> specList = new ArrayList<>();
        Integer warehouseId = stockOrderInventoryDTO.getWarehouseId();
        Integer orderType = stockOrderInventoryDTO.getOrderType();
        String refOrderNo = stockOrderInventoryDTO.getRefOrderNo();
        Integer orgId = stockOrderInventoryDTO.getOrgId();
        // 仓库订单类型
        Integer storeOrderType = stockOrderInventoryDTO.getStoreOrderType();

        stockOrderInventoryDTO.getStockOrderItemDTOS().stream().filter(Objects::nonNull).forEach(itemDTO -> {
            WarehouseInventoryChangeDTO changeDTO = new WarehouseInventoryChangeDTO();
            Long productSpecificationId = itemDTO.getProductSpecificationId();
            Long ownerId = itemDTO.getOwnerId();
            Long secOwnerId = itemDTO.getSecOwnerId();

            changeDTO.setCityId(orgId);
            changeDTO.setWarehouseId(warehouseId);
            changeDTO.setProductSpecificationId(productSpecificationId);
            changeDTO.setOwnId(ownerId);
            changeDTO.setSecOwnerId(secOwnerId);
            changeDTO.setCount(
                    Objects.equals(orderType, 1) ? itemDTO.getUnitTotalCount() : itemDTO.getUnitTotalCount().negate());
            changeDTO.setOrderNo(refOrderNo);
            changeDTO.setOrderId(refOrderNo);
            changeDTO.setProductionDate(itemDTO.getProductionDate());
            changeDTO.setBatchTime(itemDTO.getBatchTime());
            changeDTO.setOrderType(null != storeOrderType ? storeOrderType : StoreOrderTypeEnum.订单.getType());
            changeDTO.setJiupiEventType(JiupiEventType.erp库存同步.getType());
            changeDTO.setErpEventType(ERPEventType.单据录入.getType());

            boolean inByReturnOrder = orderType == 1;
            String description = inByReturnOrder ? "按退货单入库" : "按取货单出库";
            changeDTO.setDescription(description);
            changeDTO.setAllocationCalculation(false);
            // 入库更新交易平台库存，出库不更新
            changeDTO.setHasUpdateOPInventory(inByReturnOrder);
            changeDTOList.add(changeDTO);

            ProductSkuBySpecificationQueryDTO productSkuBySpecificationQueryDTO =
                    new ProductSkuBySpecificationQueryDTO();
            productSkuBySpecificationQueryDTO.setProductSpecificationId(productSpecificationId);
            productSkuBySpecificationQueryDTO.setOwnerId(ownerId);
            productSkuBySpecificationQueryDTO.setSecOwnerId(secOwnerId);
            specList.add(productSkuBySpecificationQueryDTO);
        });

        productSkuBySpecSO.setCityId(orgId);
        productSkuBySpecSO.setWarehouseId(warehouseId);
        productSkuBySpecSO.setSpecList(specList);
        List<ProductSkuDTO> productSkuDTOS = iProductSkuQueryService.findBySpec(productSkuBySpecSO);
        if (CollectionUtils.isNotEmpty(productSkuDTOS)) {
            Map<Long, List<ProductSkuDTO>> productSkuMap =
                    productSkuDTOS.stream().filter(p -> p.getProductSpecificationId() != null)
                            .collect(Collectors.groupingBy(ProductSkuDTO::getProductSpecificationId));
            changeDTOList.forEach(c -> {
                List<ProductSkuDTO> productSkuDTOList = productSkuMap.get(c.getProductSpecificationId());
                if (CollectionUtils.isEmpty(productSkuDTOList)) {
                    throw new BusinessException("产品档案在供应链不存在！规格ID：" + c.getProductSpecificationId());
                }
                c.setProductSkuId(productSkuDTOList.get(0).getProductSkuId());
            });
        }
        LOG.info("虚仓实配库存管理（取货 退货）changeDTOList：{}, sellChangeList:{}", JSON.toJSONString(changeDTOList),
                JSON.toJSONString(sellChangeList));
        processWmsNotExitsOrderInventory(changeDTOList, true, sellChangeList);
    }

    /**
     * 易经销出入库处理
     *
     * @param stockOrderInventoryDTO
     */
    public void processOutStockOrderInventoryWithEasySell(StockOrderInventoryDTO stockOrderInventoryDTO) {
        LOG.info("易经销出入库处理，请求参数：{}", JSON.toJSONString(stockOrderInventoryDTO));

        List<String> noProcessOrderNos =
                idempotenceConsumer.getNoProcessStockOrderInventoryOrderNo(stockOrderInventoryDTO.getWarehouseId(),
                        stockOrderInventoryDTO, OrderDeliveryOpType.ORDER_INVENTORY_DYY_MESSAGE_TYPE, "取货确认无误");
        if (CollectionUtils.isEmpty(noProcessOrderNos)) {
            LOG.info(String.format("易经销出入库处理-取货确认无误-订单已经全部处理过！%s", JSON.toJSONString(stockOrderInventoryDTO)));
            return;
        }

        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            InStockOrderQueryDTO inOrderQuery = new InStockOrderQueryDTO();
            inOrderQuery.setOrderType((int) InStockOrderTypeEnum.虚仓分拣单.getType());
            inOrderQuery.setRelatedNoteId(stockOrderInventoryDTO.getFetchTaskId().toString());
            List<InStockOrderDTO> inOrderList = iInStockCommService.listInStockOrderInfo(inOrderQuery).getDataList();
            if (CollectionUtils.isNotEmpty(inOrderList)
                    && !inOrderList.stream().allMatch(p -> Objects.equals(p.getState(), InStockOrderState.待审核.getType()))
                    && inOrderList.stream().allMatch(p -> Objects.equals(p.getPushState(), PushStateEnum.已下推.getType()))) {
                throw new BusinessValidateException("易经销出入库处理-取货批次处于非待核验状态不能操作,请让重新出示二维码！");
            }

            if (Objects.equals(stockOrderInventoryDTO.getFetchTaskType(), FETCH_TYPE_DYY_RETURN)) {
                stockOrderInventoryDTO.setStoreOrderType(StoreOrderTypeEnum.退货单.getType());
            }
            this.productInventoryChange(stockOrderInventoryDTO);
        });

        notifySecPickVirtualPickComplete(stockOrderInventoryDTO);
    }

    /**
     * 重构处理虚仓二次分拣单
     *
     * @param stockOrderInventoryDTO
     */
    private void notifySecPickVirtualPickComplete(StockOrderInventoryDTO stockOrderInventoryDTO) {
        if (Objects.equals(stockOrderInventoryDTO.getFetchTaskType(), FETCH_TYPE_DYY_RETURN)) {
            return;
        }
        InStockOrderQueryDTO inOrderQuery = new InStockOrderQueryDTO();
        inOrderQuery.setOrderType((int) InStockOrderTypeEnum.虚仓分拣单.getType());
        inOrderQuery.setRelatedNoteId(stockOrderInventoryDTO.getFetchTaskId().toString());
        List<InStockOrderDTO> inOrderList = iInStockCommService.listInStockOrderInfo(inOrderQuery).getDataList();
        if (CollectionUtils.isEmpty(inOrderList)) {
            LOG.info("易经销出入库处理，请求参数：{}, 没找到单据", JSON.toJSONString(stockOrderInventoryDTO));
            return;
        }

        List<InStockOrderDTO> virtualOrderList = inOrderList.stream()
                .filter(m -> InStockOrderTypeEnum.虚仓分拣单.getType().equals(m.getOrderType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(virtualOrderList)) {
            return;
        }
        InStockOrderDTO inStockOrderDTO = virtualOrderList.get(0);
        StockOrderInventoryDTO newInventoryDTO = new StockOrderInventoryDTO();
        BeanUtils.copyProperties(stockOrderInventoryDTO, newInventoryDTO);
        newInventoryDTO.setOrgId(inStockOrderDTO.getOrgId());
        newInventoryDTO.setWarehouseId(inStockOrderDTO.getWarehouseId());
        iVirtualWarehouseManageService.updateInAndOutStockOrder(StockOrderInventoryDTOConvert.convertToVirtualStockOrderInventoryDTO(stockOrderInventoryDTO));

        if (Objects.equals(inOrderList.get(0).getState(), InStockOrderState.待审核.getType())
                && stockOrderInventoryDTO.getOrderType() == 2) {
            InStockFetchUpdateDTO inStockFetchUpdateDTO = new InStockFetchUpdateDTO();
            inStockFetchUpdateDTO.setOrgId(inOrderList.get(0).getOrgId());
            inStockFetchUpdateDTO.setState(InStockOrderState.已入库.getType());
            inStockFetchUpdateDTO.setOptUserId(stockOrderInventoryDTO.getOperateUserId());
            inStockFetchUpdateDTO.setOptUserName(stockOrderInventoryDTO.getOperateUserName());
            inStockFetchUpdateDTO.setFetchTaskId(stockOrderInventoryDTO.getFetchTaskId().toString());
            inStockFetchUpdateDTO.setFetchMethod(InStockFetchMethodEnum.经销商收货.getType());
            LOG.info("[虚仓二次分拣]收货任务入库：{}", JSON.toJSONString(inStockFetchUpdateDTO));
            iInStockCommService.updateStateByFetchTask(inStockFetchUpdateDTO);
        }
    }

    private boolean couldIn(boolean isOpenCenter, List<InStockOrderDTO> inOrderList) {
        if (CollectionUtils.isEmpty(inOrderList)) {
            return Boolean.TRUE;
        }
        if (!isOpenCenter) {
            if (CollectionUtils.isNotEmpty(inOrderList)
                    && !inOrderList.stream().allMatch(p -> Objects.equals(p.getState(), InStockOrderState.待审核.getType()))
                    && inOrderList.stream().allMatch(p -> Objects.equals(p.getPushState(), PushStateEnum.已下推.getType()))) {
                return Boolean.FALSE;
            }
        }

        if (isOpenCenter) {
            List<InStockOrderDTO> virtualOrderList =
                    inOrderList.stream().filter(m -> InStockOrderTypeEnum.虚仓分拣单.getType().equals(m.getOrderType()))
                            .collect(Collectors.toList());
            List<InStockOrderDTO> notVirtualOrderList =
                    inOrderList.stream().filter(m -> !InStockOrderTypeEnum.虚仓分拣单.getType().equals(m.getOrderType()))
                            .collect(Collectors.toList());
            // 根据虚仓分拣单区分
            if (CollectionUtils.isNotEmpty(virtualOrderList)
                    && !virtualOrderList.stream()
                    .allMatch(p -> Objects.equals(p.getState(), InStockOrderState.待入库.getType()))
                    && virtualOrderList.stream()
                    .allMatch(p -> Objects.equals(p.getPushState(), PushStateEnum.已下推.getType()))) {
                return Boolean.FALSE;
            }
            if (CollectionUtils.isNotEmpty(notVirtualOrderList)
                    && !notVirtualOrderList.stream()
                    .allMatch(p -> Objects.equals(p.getState(), InStockOrderState.待审核.getType()))
                    && notVirtualOrderList.stream()
                    .allMatch(p -> Objects.equals(p.getPushState(), PushStateEnum.已下推.getType()))) {
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }
}

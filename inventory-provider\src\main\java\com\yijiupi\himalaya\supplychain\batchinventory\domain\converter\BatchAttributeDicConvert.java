package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeDicPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicDTO;

/**
 * 转换类
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeDicConvert {

    public static BatchAttributeDicPO BatchAttributeDicDTO2PO(BatchAttributeDicDTO batchAttributeDicDTO) {
        if (batchAttributeDicDTO == null) {
            return null;
        }
        BatchAttributeDicPO batchAttributeDicPO = new BatchAttributeDicPO();
        batchAttributeDicPO.setAttributeName(batchAttributeDicDTO.getAttributeName());
        batchAttributeDicPO.setAttributeType(batchAttributeDicDTO.getAttributeType());
        batchAttributeDicPO.setAttributeValue(batchAttributeDicDTO.getAttributeValue());
        batchAttributeDicPO.setRemark(batchAttributeDicDTO.getRemark());
        batchAttributeDicPO.setEnable(batchAttributeDicDTO.getEnable());
        batchAttributeDicPO.setCreateUser(batchAttributeDicDTO.getCreateUser());
        return batchAttributeDicPO;
    }
}

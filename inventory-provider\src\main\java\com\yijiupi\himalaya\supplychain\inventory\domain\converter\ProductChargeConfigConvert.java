package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import org.springframework.stereotype.Component;

import com.github.pagehelper.StringUtil;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductChargeConfigDTO;

/**
 * 仓库标准费率配置转换
 * 
 * @author: lidengfeng
 * @date 2018/9/15 10:36
 */
@Component
public class ProductChargeConfigConvert extends ConvertUtils<ProductChargeConfigPO, ProductChargeConfigDTO> {

    /**
     * 仓库标准费率配置PO转DTO
     * 
     * @param po
     * @return
     */
    @Override
    public ProductChargeConfigDTO convert(ProductChargeConfigPO po) {
        ProductChargeConfigDTO dto = new ProductChargeConfigDTO();
        dto.setId(String.valueOf(po.getId()));
        dto.setProductSpecificationId(po.getProductSpecificationId());
        dto.setUnloadingCharge(po.getUnloadingCharge());
        dto.setSortingCharge(po.getSortingCharge());
        dto.setCustodianCharge(po.getCustodianCharge());
        dto.setLoadingCharge(po.getLoadingCharge());
        dto.setTransportCharge(po.getTransportCharge());
        dto.setLandingCharge(po.getLandingCharge());
        dto.setCreateUser(po.getCreateUser());
        dto.setCreateTime(po.getCreateTime());
        dto.setLastUpdateUser(po.getLastUpdateUser());
        dto.setLastUpdateTime(po.getLastUpdateTime());
        dto.setProductName(po.getProductName());
        dto.setProductBrand(po.getProductBrand());
        dto.setBusinessCity(po.getBusinessCity());
        dto.setWarehouseName(po.getWarehouseName());
        dto.setSpecificationName(po.getSpecificationName());
        dto.setFirstInStockTime(po.getFirstInStockTime());
        dto.setDealerId(po.getDealerId());
        dto.setStatus(po.getStatus());
        dto.setCityId(po.getCityId());
        dto.setWarehouseId(po.getWarehouseId());
        dto.setFacilitatorId(po.getFacilitatorId());
        dto.setDealerName(po.getDealerName());
        dto.setMobileNo(po.getMobileNo());
        return dto;
    }

    /**
     * 仓库标准费率配置DTO转PO
     * 
     * @param dto
     * @return
     */
    @Override
    public ProductChargeConfigPO reverseConvert(ProductChargeConfigDTO dto) {
        ProductChargeConfigPO po = new ProductChargeConfigPO();
        if (StringUtil.isNotEmpty(dto.getId())) {
            po.setId(Long.valueOf(dto.getId()));
        }
        po.setProductSpecificationId(dto.getProductSpecificationId());
        po.setUnloadingCharge(dto.getUnloadingCharge());
        po.setSortingCharge(dto.getSortingCharge());
        po.setCustodianCharge(dto.getCustodianCharge());
        po.setLoadingCharge(dto.getLoadingCharge());
        po.setTransportCharge(dto.getTransportCharge());
        po.setLandingCharge(dto.getLandingCharge());
        po.setCreateUser(dto.getCreateUser());
        po.setCreateTime(dto.getCreateTime());
        po.setLastUpdateUser(dto.getLastUpdateUser());
        po.setLastUpdateTime(dto.getLastUpdateTime());
        po.setProductName(dto.getProductName());
        po.setProductBrand(dto.getProductBrand());
        po.setBusinessCity(dto.getBusinessCity());
        po.setWarehouseName(dto.getWarehouseName());
        po.setSpecificationName(dto.getSpecificationName());
        po.setFirstInStockTime(dto.getFirstInStockTime());
        po.setDealerId(dto.getDealerId());
        po.setStatus(dto.getStatus());
        po.setWarehouseId(dto.getWarehouseId());
        po.setCityId(dto.getCityId());
        po.setFacilitatorId(dto.getFacilitatorId());
        po.setDealerName(dto.getDealerName());
        po.setMobileNo(dto.getMobileNo());
        return po;
    }
}

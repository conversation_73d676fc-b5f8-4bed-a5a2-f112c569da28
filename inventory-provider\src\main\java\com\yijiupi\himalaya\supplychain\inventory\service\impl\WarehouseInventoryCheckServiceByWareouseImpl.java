package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import com.yijiupi.himalaya.supplychain.inventory.dto.check.CheckStoreInventoryByWarehouseInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryCheckByWarehouseBL;
import com.yijiupi.himalaya.supplychain.inventory.service.IWarehouseInventoryCheckByWarehouseService;

/**
 * 库存对账
 *
 * <AUTHOR>
 * @date 2019/1/8 15:21
 */
@Service(timeout = 300000)
public class WarehouseInventoryCheckServiceByWareouseImpl implements IWarehouseInventoryCheckByWarehouseService {

    @Autowired
    private WarehouseInventoryCheckByWarehouseBL warehouseInventoryCheckBL;

    /**
     * 根据ERP库存，存库存对账
     */
    @Override
    public void checkStoreInventoryByWarehouseId(Integer warehouseId, Integer opUserId, boolean isSnap) {
        warehouseInventoryCheckBL.checkStoreInventoryByWarehouseId(new CheckStoreInventoryByWarehouseInfoDTO(warehouseId, opUserId, isSnap, CheckStoreInventoryByWarehouseInfoDTO.OLD_VERSION));
    }

    /**
     * 根据ERP库存，存库存对账
     */
    @Override
    public void checkStoreInventoryByOpenCenterWarehouse(CheckStoreInventoryByWarehouseInfoDTO dto) {
        warehouseInventoryCheckBL.checkStoreInventoryByWarehouseId(dto);
    }

    /**
     * 根据易款店仓库存，存库存对账
     */
    @Override
    public void checkStoreInventoryByEasyChain(Integer warehouseId, Integer opUserId) {
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        warehouseInventoryCheckBL.checkStoreInventoryByEasyChain(warehouseId, opUserId);
    }

    /**
     * 仓库停用校验
     */
    @Override
    public Boolean checkWarehouseDisable(Integer cityId, Integer warehouseId) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        return warehouseInventoryCheckBL.checkWarehouseDisable(cityId, warehouseId);
    }

}

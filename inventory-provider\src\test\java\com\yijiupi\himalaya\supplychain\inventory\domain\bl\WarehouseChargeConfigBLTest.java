package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChargeConfigDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChooseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseChooseReturnDTO;

/**
 * @author: lidengfeng
 * @date 2018/9/15 16:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WarehouseChargeConfigBLTest {

    @Autowired
    private WarehouseChargeConfigBL warehouseChargeConfigBL;

    @Test
    public void saveOrUpdateChargeConfig() {
        WarehouseChargeConfigDTO dto = new WarehouseChargeConfigDTO();
        // dto.setId("97783045163063122");
        dto.setWarehouseId(1045);
        dto.setUnloadingcharge(BigDecimal.valueOf(1.5));
        dto.setSortingcharge(BigDecimal.valueOf(1.5));
        dto.setCustodiancharge(BigDecimal.valueOf(1.5));
        dto.setLoadingcharge(BigDecimal.valueOf(1.5));
        dto.setTransportcharge(BigDecimal.valueOf(1.5));
        dto.setLandingcharge(BigDecimal.valueOf(1.5));
        dto.setStatus((byte)1);
        // dto.setCreateuser(2222L);
        dto.setLastupdateuser(2222L);
        warehouseChargeConfigBL.saveOrUpdateChargeConfig(dto);
    }

    @Test
    public void selectWarehouseChargeConfigById() {
        WarehouseChargeConfigDTO warehouseChargeConfigDTO =
            warehouseChargeConfigBL.selectWarehouseChargeConfigById(1041);
        System.out.println(JSON.toJSONString(warehouseChargeConfigDTO));
    }

    @Test
    public void updateChargeConfigStatus() {
        WarehouseChargeConfigDTO dto = new WarehouseChargeConfigDTO();
        dto.setWarehouseId(1041);
        dto.setStatus((byte)1);
        dto.setLastupdateuser(1112L);
        warehouseChargeConfigBL.updateChargeConfigStatus(dto);

    }

    @Test
    public void selectWarehouseChargeList() {
        List<Integer> list = new ArrayList<>();
        list.add(1041);
        Map<Integer, WarehouseChargeConfigDTO> map = warehouseChargeConfigBL.selectWarehouseChargeList(list);
        System.out.println(JSON.toJSONString(map));

    }

    @Test
    public void findWarehouseChooseList() {
        WarehouseChooseDTO warehouseChooseDTO = new WarehouseChooseDTO();
        warehouseChooseDTO.setWarehouseChooseType(3);
        warehouseChooseDTO.setShopId(Long.valueOf(6));
        warehouseChooseDTO.setCity("北京市");
        PageList<WarehouseChooseReturnDTO> warehouseChooseList =
            warehouseChargeConfigBL.findWarehouseChooseList(warehouseChooseDTO);
        System.out.println(JSON.toJSONString(warehouseChooseList));
    }

    /**
     * 校验仓库是否有库存
     */
    @Test
    public void warehouseStock() {
        String str = "武汉市";
        str = str.replace("日喀则市", "日喀则地区");
        System.out.println(JSON.toJSONString(str));
    }

    // @Test
    // public void findWarehouseAgencyDetailList(){
    //
    // List<WarehouseAgencyDetailDTO> warehouseAgencyDetailList =
    // warehouseChargeConfigBL.findWarehouseAgencyDetailList(1);
    //
    // System.out.println(JSON.toJSONString(warehouseAgencyDetailList));
    // }
}
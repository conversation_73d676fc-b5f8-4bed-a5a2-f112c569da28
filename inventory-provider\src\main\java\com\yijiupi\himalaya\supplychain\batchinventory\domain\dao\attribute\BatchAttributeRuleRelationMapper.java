package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.RuleRelationQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatchAttributeRuleRelationMapper {
    int insert(@Param("batchAttributeRuleRelationPO") BatchAttributeRuleRelationPO batchAttributeRuleRelationPO);

    /**
     * 批量插入
     *
     * @param batchAttributeRuleRelationPOs
     * @return
     */
    int insertList(
        @Param("batchAttributeRuleRelationPOs") List<BatchAttributeRuleRelationPO> batchAttributeRuleRelationPOs);

    /**
     * 修改
     *
     * @param batchAttributeRuleRelationPO
     * @return
     */
    int update(@Param("batchAttributeRuleRelationPO") BatchAttributeRuleRelationPO batchAttributeRuleRelationPO);

    /**
     * 根据关联的rule表id删除
     *
     * @param ruleId
     * @return
     */
    int deleteByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 根据配置类型属性信息查询配置关系
     */
    List<BatchAttributeRuleRelationPO>
        selectRuleRelationByRuleTypeAndAttribute(RuleRelationQueryDTO ruleRelationQueryDTO);
}

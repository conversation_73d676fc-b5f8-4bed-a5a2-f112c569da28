package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.OrgConstant;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IOrgService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;

/**
 * Created by 余明 on 2019-04-29.
 */
@Service
public class OwnerTypeBL {
    @Reference
    private OwnerService ownerService;
    @Reference
    private IOrgService iOrgService;
    // @Reference
    // private IWarehouseQueryService iWarehouseQueryService;

    private final Logger LOG = LoggerFactory.getLogger(OwnerTypeBL.class);

    public Integer getOwnerTypeByOwnerAndCityId(Integer cityId, Long ownerId) {
        Integer ownerTypeFromCity = null;
        if (ownerId != null) {
            OwnerDTO owner = ownerService.getOwnerById(ownerId);
            if (owner != null) {
                ownerTypeFromCity = owner.getOwnerType();
                LOG.info(String.format("第二-》根据OwnerId查询OwnerType OwnerType：%s", ownerTypeFromCity));
            }
        }
        // 根据城市获取类别
        if (ownerTypeFromCity == null) {
            if (cityId != null) {
                OrgDTO org = iOrgService.getOrg(cityId);
                if (org != null) {
                    if (Objects.equals(OrgConstant.ORG_TYPE_JIUPI, org.getFromOrgType())) {
                        ownerTypeFromCity = OwnerTypeConst.易酒批;
                    } else if (Objects.equals(OrgConstant.ORG_TYPE_EASYGO, org.getFromOrgType())) {
                        ownerTypeFromCity = OwnerTypeConst.知花知果;
                    } else if (Objects.equals(OrgConstant.ORG_TYPE_WEIJIU, org.getFromOrgType())) {
                        ownerTypeFromCity = OwnerTypeConst.微酒;
                    } else if (Objects.equals(OrgConstant.ORG_TYPE_YIKUAN, org.getFromOrgType())) {
                        ownerTypeFromCity = OwnerTypeConst.易款连锁;
                    }
                }
                LOG.info(String.format("第三-》根据城市ID获取城市信息 OwnerType：%s,%s", ownerTypeFromCity, JSON.toJSON(org)));
            }
        }
        if (ownerTypeFromCity == null) {
            ownerTypeFromCity = OwnerTypeConst.易酒批;
            LOG.info(String.format("第四-》OwnerType为空，赋默认值 OwnerType：%s", ownerTypeFromCity));
        }

        return ownerTypeFromCity;
    }

    // public OwnerAndSourceDTO getOwnerTypeAndSource(Integer cityId, Integer warehouseId, Long ownerId, Integer source)
    // {
    // Integer sourceFromCity = null;
    // Integer ownerTypeFromCity = null;
    // OwnerAndSourceDTO dto = new OwnerAndSourceDTO();
    // if (ownerId != null) {
    // OwnerDTO owner = ownerService.getOwnerById(ownerId);
    // if (owner != null) {
    // ownerTypeFromCity = owner.getOwnerType();
    // sourceFromCity = getDefaultSourceType(ownerTypeFromCity);
    // LOG.info(String.format("第二-》根据OwnerId查询Source和OwnerType Source：%s,OwnerType：%s", sourceFromCity,
    // ownerTypeFromCity));
    // }
    // }
    // //根据城市获取类别
    // if (sourceFromCity == null || ownerTypeFromCity == null) {
    // cityId = getCityIdByWarehouseId(cityId, warehouseId);
    // if (cityId != null) {
    // OrgDTO org = iOrgService.getOrg(cityId);
    // if (org != null) {
    // if (Objects.equals(OrgConstant.ORG_TYPE_JIUPI, org.getFromOrgType())) {
    // sourceFromCity = ProductSourceType.易酒批;
    // ownerTypeFromCity = OwnerTypeConst.易酒批;
    // } else if (Objects.equals(OrgConstant.ORG_TYPE_EASYGO, org.getFromOrgType())) {
    // sourceFromCity = ProductSourceType.知花知果;
    // ownerTypeFromCity = OwnerTypeConst.知花知果;
    // } else if (Objects.equals(OrgConstant.ORG_TYPE_WEIJIU, org.getFromOrgType())) {
    // sourceFromCity = ProductSourceType.微酒;
    // ownerTypeFromCity = OwnerTypeConst.微酒;
    // } else if (Objects.equals(OrgConstant.ORG_TYPE_YIKUAN, org.getFromOrgType())) {
    // sourceFromCity = ProductSourceType.易款连锁;
    // ownerTypeFromCity = OwnerTypeConst.易款连锁;
    // }
    // }
    // LOG.info(String.format("第四-》根据城市ID获取城市信息 Source：%s,OwnerType：%s,%s", sourceFromCity, ownerTypeFromCity,
    // JSON.toJSON(org)));
    // }
    // }
    // if (sourceFromCity == null || ownerTypeFromCity == null) {
    // if (source != null) {
    // ownerTypeFromCity = getDefaultOwnerType(source);
    // LOG.info(String.format("第五-》根据source查询OwnerType Source：%s,OwnerType：%s", sourceFromCity, ownerTypeFromCity));
    // }
    // if (sourceFromCity == null) {
    // sourceFromCity = ProductSourceType.易酒批;
    // LOG.info(String.format("第六-》Source或者OwnerType存在为空的项，赋默认值 Source：%s,OwnerType：%s", sourceFromCity,
    // ownerTypeFromCity));
    // }
    // if (ownerTypeFromCity == null) {
    // ownerTypeFromCity = OwnerTypeConst.易酒批;
    // LOG.info(String.format("第七-》Source或者OwnerType存在为空的项，赋默认值 Source：%s,OwnerType：%s", sourceFromCity,
    // ownerTypeFromCity));
    // }
    // }
    // dto.setCityId(cityId);
    // dto.setOwnerTypeFromCity(ownerTypeFromCity);
    // dto.setSourceFromCity(ownerTypeFromCity);
    // return dto;
    // }
    //
    // private Integer getCityIdByWarehouseId(Integer cityId, Integer warehouseId) {
    // if (cityId == null && warehouseId != null) {
    // Warehouse warehouse = iWarehouseQueryService.findWarehouseById(warehouseId);
    // LOG.info(String.format("第三-》根据仓库ID获取城市信息：%s", JSON.toJSON(warehouse)));
    // if (warehouse != null) {
    // cityId = warehouse.getCityId();
    // }
    // }
    // return cityId;
    // }
    //
    // private Integer getDefaultSourceType(Integer ownerTypeFromCity) {
    // Integer sourceFromCity;
    // if (Objects.equals(ownerTypeFromCity, OwnerTypeConst.微酒)) {
    // sourceFromCity = ProductSourceType.微酒;
    // } else if (Objects.equals(ownerTypeFromCity, OwnerTypeConst.知花知果)) {
    // sourceFromCity = ProductSourceType.知花知果;
    // } else if (Objects.equals(ownerTypeFromCity, OwnerTypeConst.易款连锁)) {
    // sourceFromCity = ProductSourceType.易款连锁;
    // } else {
    // sourceFromCity = ProductSourceType.易酒批;
    // }
    // return sourceFromCity;
    // }
    //
    // private Integer getDefaultOwnerType(Integer source) {
    // Integer ownerTypeFromCity;
    // if (Objects.equals(source, ProductSourceType.微酒)) {
    // ownerTypeFromCity = ProductSourceType.微酒;
    // } else if (Objects.equals(source, ProductSourceType.知花知果)) {
    // ownerTypeFromCity = ProductSourceType.知花知果;
    // } else if (Objects.equals(source, ProductSourceType.易款连锁)) {
    // ownerTypeFromCity = ProductSourceType.知花知果;
    // } else {
    // ownerTypeFromCity = OwnerTypeConst.易酒批;
    // }
    // return ownerTypeFromCity;
    // }
}

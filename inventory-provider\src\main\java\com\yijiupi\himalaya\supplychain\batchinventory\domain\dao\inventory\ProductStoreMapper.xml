<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper">

    <select id="findSkuIdByStoreId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO">
        select
        psku.ProductSku_Id AS productSkuId,
        ps.Warehouse_Id AS warehouseId,
        ps.City_Id AS cityId
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where ps.Id = #{storeId}
        limit 1
    </select>
    <select id="findInventoryPO"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO">
        SELECT
        id,
        City_Id as cityId,
        Warehouse_Id as warehouseId,
        ProductSpecification_Id as productSpecificationId,
        OwnerType as ownerType,
        Owner_Id as ownerId,
        SecOwner_Id as secOwnerId,
        Channel as channel
        from productstore
        where id = #{storeId} limit 1
    </select>
    <select id="findInventoryPOBySkuId"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO">
        select
        ps.id,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.OwnerType as ownerType,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        ps.Channel as channel
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where psku.ProductSku_Id = #{skuId}
        and ps.Warehouse_Id = #{warehouseId}
        <if test="channel!=null">AND ps.channel = #{channel,jdbcType=TINYINT}</if>
        limit 1
    </select>

    <select id="findNoBatchInventoryStoreIds"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO">
        select
        ps.id,
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.OwnerType as ownerType,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        ps.Channel as channel,
        ps.TotalCount_MinUnit as unitTotalCount,
        ps.lastupdatetime as lastUpdateTime
        from productstore ps
        LEFT JOIN productstorebatch psb
        ON ps.id = psb.productstore_id
        where ps.Warehouse_Id = #{warehouseId}
        and psb.id IS null
    </select>

    <select id="listSkuIdByStoreIds"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO">
        select
        ps.City_Id AS cityId,
        ps.Warehouse_Id AS warehouseId,
        psku.ProductSku_Id AS productSkuId,
        ps.id AS productStoreId
        from productstore ps
        INNER JOIN productsku psku
        ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where ps.Id in
        <foreach collection="storeIdList" item="storeId" open="(" close=")" separator=",">
            #{storeId,jdbcType=BIGINT}
        </foreach>
        and psku.IsDelete = 0
    </select>

    <select id="findInventoryPOByStoreIds"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.ProductInventoryPO">
        SELECT
        id,
        City_Id as cityId,
        Warehouse_Id as warehouseId,
        ProductSpecification_Id as productSpecificationId,
        OwnerType as ownerType,
        Owner_Id as ownerId,
        SecOwner_Id as secOwnerId,
        Channel as channel
        from productstore
        where id in
        <foreach collection="storeIdList" item="storeId" open="(" close=")" separator=",">
            #{storeId,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>
package com.yijiupi.himalaya.supplychain.inventory.domain.message;

import java.util.UUID;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.MessageApplyMapper;

/**
 * 幂等消费 - UT
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class IdempotenceConsumerTest {

    @Autowired
    private IdempotenceConsumer idempotenceConsumer;

    @Autowired
    private MessageApplyMapper messageApplyMapper;

    @Test
    public void applySuccess() throws Exception {

        String messageId = UUID.randomUUID().toString();
        String bizuuid = UUID.randomUUID().toString();

        idempotenceConsumer.apply(messageId, () -> {
            messageApplyMapper.insertMessageApply(bizuuid);
        });

        Assert.assertTrue(messageApplyMapper.countMessageApply(messageId) == 1);
        Assert.assertTrue(messageApplyMapper.countMessageApply(bizuuid) == 1);

    }

    @Test(expected = RuntimeException.class)
    public void applyFail() throws Exception {

        String messageId = UUID.randomUUID().toString();
        String bizuuid = UUID.randomUUID().toString();

        idempotenceConsumer.apply(messageId, () -> {
            messageApplyMapper.insertMessageApply(bizuuid);
            throw new RuntimeException("消费异常");
        });

        Assert.assertTrue(messageApplyMapper.countMessageApply(messageId) == 0);
        Assert.assertTrue(messageApplyMapper.countMessageApply(bizuuid) == 0);

    }

    @Test
    public void applyRepeat() throws Exception {

        String messageId = UUID.randomUUID().toString();
        String bizuuid = UUID.randomUUID().toString();
        String bizuuid2 = UUID.randomUUID().toString();

        idempotenceConsumer.apply(messageId, () -> {
            messageApplyMapper.insertMessageApply(bizuuid);
        });

        idempotenceConsumer.apply(messageId, () -> {
            messageApplyMapper.insertMessageApply(bizuuid2);
        });

        Assert.assertTrue(messageApplyMapper.countMessageApply(messageId) == 1);
        Assert.assertTrue(messageApplyMapper.countMessageApply(bizuuid) == 1);
        Assert.assertTrue(messageApplyMapper.countMessageApply(bizuuid2) == 0);

    }
}
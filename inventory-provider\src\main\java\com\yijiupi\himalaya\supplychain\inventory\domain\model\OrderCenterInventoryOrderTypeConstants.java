package com.yijiupi.himalaya.supplychain.inventory.domain.model;

/**
 * <AUTHOR>
 * @title: OrderCenterInventoryOrderTypeConstants
 * @description:
 * 出入库单类型 WMS出库单类型 1调拨单， 2销售订单， 3线上退货订单， 4线下退货订单， 5自提订单， 6快递直发订单， 7虚仓二次分拣订单， 8内配订单
 * 入库单类型 1线上退货单退货入库， 2销售单回单入库， 3调拨单调拨入库， 4线下退货单退货入库， 5前置仓内配入库
 * @date 2023-03-22 17:52
 */
public class OrderCenterInventoryOrderTypeConstants {

    public static final Integer TYPE_OUT = 1;

    public static final Integer 调拨单 = 1;

    public static final Integer 销售订单 = 2;

    public static final Integer 线上退货订单 = 3;

    public static final Integer 线下退货订单 = 4;

    public static final Integer 自提订单 = 5;

    public static final Integer 快递直发订单 = 6;

    public static final Integer 虚仓二次分拣订单 = 7;

    public static final Integer 内配订单 = 8;

    public static boolean isOut(Integer type) {
        if (调拨单.equals(type)) {
            return Boolean.TRUE;
        }
        if (销售订单.equals(type)) {
            return Boolean.TRUE;
        }
        if (线上退货订单.equals(type)) {
            return Boolean.TRUE;
        }
        if (线下退货订单.equals(type)) {
            return Boolean.TRUE;
        }
        if (自提订单.equals(type)) {
            return Boolean.TRUE;
        }
        if (快递直发订单.equals(type)) {
            return Boolean.TRUE;
        }
        if (虚仓二次分拣订单.equals(type)) {
            return Boolean.TRUE;
        }
        if (内配订单.equals(type)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public static final Integer TYPE_IN = 2;

    public static final Integer 线上退货单退货入库 = 1;

    public static final Integer 销售单回单入库 = 2;

    public static final Integer 调拨单调拨入库 = 3;

    public static final Integer 线下退货单退货入库 = 4;

    public static final Integer 前置仓内配入库 = 5;

}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品库存PO对象
 *
 * <AUTHOR>
 */
public class ProductInventoryPO {
    /**
     * 主键ID
     */
    private String id;
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    /**
     * 库存总量,按最小单位累计
     */
    private Integer totalCountMinUnit;
    /**
     * 库存所属类型
     */
    private Integer ownerType;
    /**
     * 库存所属人ID
     */
    private Long ownerId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * 产品信息规格ID
     */
    private Long productSpecificationId;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * skuid
     */
    private Long productSkuId;
    /**
     * 二级货主Id
     */
    private Long secOwnerId;
    /**
     * 货物渠道
     */
    private Integer channel;
    /**
     * 库存变化值(最小单位)
     */
    private Integer changeCount;
    /**
     * 包装规格名称
     */
    private String specificationName;

    /**
     * 小单位总数量
     */
    private BigDecimal unitTotalCount;

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getTotalCountMinUnit() {
        return totalCountMinUnit;
    }

    public void setTotalCountMinUnit(Integer totalCountMinUnit) {
        this.totalCountMinUnit = totalCountMinUnit;
    }

    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(Integer changeCount) {
        this.changeCount = changeCount;
    }

    public String getSpecificationName() {
        return specificationName;
    }

    public void setSpecificationName(String specificationName) {
        this.specificationName = specificationName;
    }

    public BigDecimal getUnitTotalCount() {
        return unitTotalCount;
    }

    public void setUnitTotalCount(BigDecimal unitTotalCount) {
        this.unitTotalCount = unitTotalCount;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.BatchInventoryQueryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchInventoryConvert;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.converter.BatchInventoryDTOConvertor;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductStoreBatchQueryMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.BatchInventoryPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.LocationBatchInventoryQueryInfoDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductInfoQueryService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/12/8
 */
@Service
public class QueryBatchInventoryBL {

    @Autowired
    private ProductStoreBatchQueryMapper productStoreBatchQueryMapper;
    @Autowired
    private BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private BatchInventoryDTOConvertor batchInventoryDTOConvertor;

    @Reference
    private IProductInfoQueryService iProductInfoQueryService;

    /**
     * @see BatchInventoryQueryBL#findBatchInventoryListNew
     * @param dto
     * @return
     */
    public PageList<BatchInventoryDTO> findBatchInventoryListNew(BatchInventoryQueryDTO dto) {
        // LOGGER.info("BatchInventoryQueryDTO:{}", JSON.toJSONString(dto));
        AssertUtils.notNull(dto.getWarehouseId(), "仓库信息不能为空！");
        BatchInventoryQueryBO queryBO = getQueryBO(dto);

        PageResult<BatchInventoryPO> pageResult =
            productStoreBatchQueryMapper.findBatchInventoryList(queryBO, dto.getPageNum(), dto.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
            batchInventoryDTOConvertor.convertToLocationBatchInventory(batchInventoryPOPageList.getDataList());
        // PageResult<BatchInventoryPO> pageResult =
        // productStoreBatchMapper.findBatchInventoryListNew(dto, dto.getPageNum(), dto.getPageSize());
        // PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        // List<BatchInventoryDTO> batchInventoryDTOS =
        // BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        // LOGGER.info("batchInventoryDTOS:{}", JSON.toJSONString(batchInventoryDTOS));

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;
    }

    /**
     * 查询货位下的库存信息
     * 
     * @param queryInfoDTO
     * @return
     */
    public PageList<BatchInventoryDTO> findLocationBatchInventory(LocationBatchInventoryQueryInfoDTO queryInfoDTO) {
        AssertUtils.notNull(queryInfoDTO.getWarehouseId(), "仓库信息不能为空");

        PageResult<BatchInventoryPO> pageResult = productStoreBatchQueryMapper.findLocationBatchInventory(queryInfoDTO,
            queryInfoDTO.getPageNum(), queryInfoDTO.getPageSize());
        PageList<BatchInventoryPO> batchInventoryPOPageList = pageResult.toPageList();
        List<BatchInventoryDTO> batchInventoryDTOS =
            BatchInventoryConvert.batchInventoryPOS2BatchInventoryDTOS(batchInventoryPOPageList.getDataList());

        PageList<BatchInventoryDTO> batchInventoryDTOPageList = new PageList<>();
        batchInventoryDTOPageList.setDataList(batchInventoryDTOS);
        batchInventoryDTOPageList.setPager(batchInventoryPOPageList.getPager());
        return batchInventoryDTOPageList;

    }

    private BatchInventoryQueryBO getQueryBO(BatchInventoryQueryDTO dto) {
        BatchInventoryQueryBO queryBO = new BatchInventoryQueryBO();
        queryBO.setWarehouseId(dto.getWarehouseId());
        queryBO.setLocationName(dto.getLocationName());
        queryBO.setProductSkuName(dto.getProductSkuName());
        queryBO.setProductSkuId(dto.getProductSkuId());
        queryBO.setLocationFullName(dto.getLocationFullName());
        queryBO.setSubCategoryList(dto.getSubCategoryList());
        queryBO.setCityId(dto.getCityId());

        return queryBO;
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.bl.async;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.aspect.InventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.RetriesBatchAddTransferOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommManageService;

@Service
public class OutStockTransferOrderAsyncBL {

    private static final Logger LOG = LoggerFactory.getLogger(OutStockTransferOrderAsyncBL.class);

    @Reference(timeout = 600000)
    private IOutStockCommManageService outStockCommManageService;

    @Autowired
    private InventorySendFaildMQ inventorySendFaildMQ;

    /**
     * 通过发货仓订单生成收货仓订单
     */
    @Async
    public void asyncBatchAddTransferOrder(List<OutStockOrderDTO> outStockOrderDTOS,
        List<OutStockOrderItemDetailDTO> outStockOrderItemDetailDTOS) {
        try {
            outStockCommManageService.batchAddTransferOrder(outStockOrderDTOS, outStockOrderItemDetailDTOS);
        } catch (Exception e) {
            LOG.error("通过发货仓订单生成收货仓订单失败:{}", JSON.toJSONString(outStockOrderDTOS), e);
            RetriesBatchAddTransferOrderDTO retriesBatchAddTransferOrderDTO = new RetriesBatchAddTransferOrderDTO();
            retriesBatchAddTransferOrderDTO.setOutStockOrderDTOS(outStockOrderDTOS);
            retriesBatchAddTransferOrderDTO.setOutStockOrderItemDetailDTOS(outStockOrderItemDetailDTOS);
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(retriesBatchAddTransferOrderDTO), "batchAddTransferOrder", e);
        }

    }
}

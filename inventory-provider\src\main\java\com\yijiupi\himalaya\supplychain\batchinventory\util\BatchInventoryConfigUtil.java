/**
 * Copyright © 2017 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.batchinventory.util;

import java.lang.reflect.Type;
import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.gson.*;

@Component
public class BatchInventoryConfigUtil {

    public static final Gson GSON_DATE_TIME_FORMAT = new GsonBuilder().setDateFormat("yyyy-MM-dd hh:mm:ss").create();

    public static final Gson GSON = getGson();

    public static String newErpAPIUrl;

    public static String erpAPIUrl;

    public static String erpInventoryAPIUrl;

    @Value("${api.erpAPIUrl}")
    public void setNewErpAPIUrl(String newErpAPIUrl) {
        BatchInventoryConfigUtil.newErpAPIUrl = newErpAPIUrl;
    }

    @Value("${inventory.erpAPIUrl}")
    public void setErpAPIUrl(String url) {
        erpAPIUrl = url;
    }

    @Value("${api.erpInventoryAPIUrl}")
    public void setErpInventoryAPIUrl(String url) {
        erpInventoryAPIUrl = url;
    }

    // 解决日期转换问题."{\"createTime\": 1471919170000}";
    private static Gson getGson() {
        GsonBuilder builder = new GsonBuilder();

        // Register an adapter to manage the date types as long values
        builder.registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
            @Override
            public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                throws JsonParseException {
                return new Date(json.getAsJsonPrimitive().getAsLong());
            }
        });

        return builder.create();
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeDicMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeDicPO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="AttributeName" property="attributeName" jdbcType="VARCHAR"/>
        <result column="AttributeType" property="attributeType" jdbcType="TINYINT"/>
        <result column="AttributeValue" property="attributeValue" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="IsEnable" property="enable" jdbcType="BIT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        AttributeName,
        AttributeType,
        AttributeValue,
        Remark,
        IsEnable,
        CreateUser
    </sql>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="batchAttributeDicPO.id">
        INSERT INTO batchattributedic (
        id,
        AttributeName,
        AttributeType,
        AttributeValue,
        Remark,
        IsEnable,
        CreateUser
        ) VALUES (
        #{batchAttributeDicPO.id,jdbcType=BIGINT},
        #{batchAttributeDicPO.attributeName,jdbcType=VARCHAR},
        #{batchAttributeDicPO.attributeType,jdbcType=TINYINT},
        #{batchAttributeDicPO.attributeValue,jdbcType=VARCHAR},
        #{batchAttributeDicPO.remark,jdbcType=VARCHAR},
        #{batchAttributeDicPO.enable,jdbcType=BIT},
        #{batchAttributeDicPO.createUser,jdbcType=VARCHAR}
        )
    </insert>


    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchattributedic (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="batchAttributeDicPOs" item="batchAttributeDicPO" index="index" separator=",">
            (
            #{batchAttributeDicPO.id,jdbcType=BIGINT},
            #{batchAttributeDicPO.attributeName,jdbcType=VARCHAR},
            #{batchAttributeDicPO.attributeType,jdbcType=TINYINT},
            #{batchAttributeDicPO.attributeValue,jdbcType=VARCHAR},
            #{batchAttributeDicPO.remark,jdbcType=VARCHAR},
            #{batchAttributeDicPO.enable,jdbcType=BIT},
            #{batchAttributeDicPO.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateIsEnable">
        UPDATE batchattributedic
        set IsEnable= #{batchAttributeDicDTO.enable,jdbcType=BIT}
        WHERE id = #{batchAttributeDicDTO.id,jdbcType=BIGINT}
    </update>
    <update id="update">
        UPDATE batchattributedic
        <set>
            <if test="dto.attributeName != null and dto.attributeName !=''">AttributeName=
                #{dto.attributeName,jdbcType=VARCHAR},
            </if>
            <if test="dto.attributeType != null">AttributeType=
                #{dto.attributeType,jdbcType=TINYINT},
            </if>
            <if test="dto.attributeValue != null and dto.attributeValue !=''">AttributeValue=
                #{dto.attributeValue,jdbcType=VARCHAR},
            </if>
            <if test="dto.remark != null and dto.remark !=''">Remark= #{dto.remark,jdbcType=VARCHAR},</if>
            <if test="dto.enable != null">IsEnable= #{dto.enable,jdbcType=BIT},</if>
        </set>
        WHERE id = #{dto.id,jdbcType=BIGINT}
    </update>

    <!--auto generated by codehelper on 2018-04-09 17:46:20-->
    <delete id="deleteById">
        delete from batchattributedic
        where id=#{id,jdbcType=BIGINT}
    </delete>


    <resultMap id="BaseResultMap1"
               type="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeDicDTO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="AttributeName" property="attributeName" jdbcType="VARCHAR"/>
        <result column="AttributeType" property="attributeType" jdbcType="TINYINT"/>
        <result column="AttributeValue" property="attributeValue" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="IsEnable" property="enable" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="VARCHAR"/>
    </resultMap>
    <!--auto generated by codehelper on 2018-04-09 20:39:49-->
    <select id="findBatchAttributeDicList" resultMap="BaseResultMap1">
        select
        id,
        AttributeName,
        AttributeType,
        AttributeValue,
        Remark,
        IsEnable,
        CreateUser,
        DATE_FORMAT(CreateTime,'%Y-%m-%d %H:%i:%s') as CreateTime
        from batchattributedic
        where 1=1
        <if test="batchAttributeDicQueryDTO.attributeType!=null">
            and AttributeType =#{batchAttributeDicQueryDTO.attributeType,jdbcType=TINYINT}
        </if>
        <if test="batchAttributeDicQueryDTO.attributeName!=null and batchAttributeDicQueryDTO.attributeName!=''">
            and AttributeName like concat(concat('%',#{batchAttributeDicQueryDTO.attributeName,jdbcType=VARCHAR}),'%')
        </if>
    </select>

    <!--auto generated by codehelper on 2018-04-09 21:09:32-->
    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchattributedic
        where id=#{id,jdbcType=BIGINT}
    </select>

    <select id="findByIdS" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from batchattributedic
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>


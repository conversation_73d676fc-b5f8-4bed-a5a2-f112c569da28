package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.BatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductLoactionItemDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductCategoryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductLocationService;
import com.yijiupi.supplychain.serviceutils.constant.DeliveryOrderConstant;

@Service
public class CategoryLocationStockBL {

    private final static Logger LOG = LoggerFactory.getLogger(CategoryLocationStockBL.class);

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;

    @Reference
    private IProductLocationService iProductLocationService;

    @Reference
    private IProductCategoryService iProductCategoryService;

    /**
     * 分拆订单项
     */
    private Map<String, List<OrderItemDTO>> spiltOrderItemByLocationStock(List<OrderItemDTO> orderItemDTOList,
        Integer warehouseId) {
        Map<String, List<OrderItemDTO>> spiltOrderItemMap = new HashMap<>(16);

        // List<Long> skuIds =
        // orderItemDTOList.stream().map(OrderItemDTO::getProductSkuId).distinct().collect(Collectors.toList());
        // //根据skuid查询开启货位库存的sku
        // Map<Long, ProductCategoryDTO> openStockSku = null;
        //// Map<Long, ProductCategoryDTO> openStockSku =
        // iProductCategoryService.findOpenLocationCategoryBySkuIds(warehouseId, skuIds);
        // Set<Long> openStockSkuIds = openStockSku.keySet();
        //
        // //移除开启货位的skuId
        // skuIds.removeAll(openStockSkuIds);
        //
        // List<OrderItemDTO> openStockOrderItems = orderItemDTOList.stream().filter(item ->
        // openStockSkuIds.contains(item.getProductSkuId())).collect(Collectors.toList());
        // List<OrderItemDTO> unopenedStockOrderItems = orderItemDTOList.stream().filter(item ->
        // skuIds.contains(item.getProductSkuId())).collect(Collectors.toList());
        //
        // LOG.info("开启货位库存的skuIds:{},未开启货位库存的skuIds:{}", openStockSkuIds, skuIds);
        //
        // spiltOrderItemMap.put("openStock", openStockOrderItems);
        // spiltOrderItemMap.put("unopenedStock", unopenedStockOrderItems);
        return spiltOrderItemMap;
    }

    /**
     * 查询计算好策略
     */
    public List<OrderItemDTO> findStrategyRuleForCategory(List<OrderItemDTO> orderItemDTOList, String billType,
        Integer warehouseId, Integer orgId, Byte deliveryMode) {
        // LOG.info("订单计算入参:{},订单配送类型:{}", JSON.toJSONString(orderItemDTOList),
        // DeliveryOrderConstant.getDeliveryMarkStateName(deliveryMode));
        Map<String, List<OrderItemDTO>> spiltOrderItemMap =
            spiltOrderItemByLocationStock(orderItemDTOList, warehouseId);
        List<OrderItemDTO> openStockOrderItems = spiltOrderItemMap.get("openStock");
        List<OrderItemDTO> unopenedStockOrderItems = spiltOrderItemMap.get("unopenedStock");
        List<Long> unopenedStockSKuIds =
            unopenedStockOrderItems.stream().map(OrderItemDTO::getProductSkuId).distinct().collect(Collectors.toList());

        // 开启货位库存的商品按货位库存逻辑计算
        List<OrderItemDTO> outStockStrategyRule = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(openStockOrderItems)) {
            if (deliveryMode == DeliveryOrderConstant.DELIVERYSTATE_DELAY.intValue()) {
                outStockStrategyRule = batchInventoryQueryBL.findOutStockStrategyRule(openStockOrderItems, billType);
            } else {
                outStockStrategyRule =
                    batchInventoryQueryBL.findDelayOutStockStrategyRule(openStockOrderItems, billType);
            }
        }

        List<OrderItemDTO> lstNoLocationItems =
            outStockStrategyRule.stream().filter(p -> p.getLocationId() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstNoLocationItems)) {
            List<Long> lstNoLocationSkus =
                lstNoLocationItems.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            unopenedStockSKuIds.addAll(lstNoLocationSkus);
            LOG.info(String.format("没有找到出库货位！仓库ID：%s,产品ID：%s", warehouseId, JSON.toJSON(lstNoLocationSkus)));
            outStockStrategyRule.removeAll(lstNoLocationItems);
            unopenedStockOrderItems.addAll(lstNoLocationItems);
        }

        // 未开启货位库存的商品按产品配置货位计算
        // 2019/4/9 解决休食仓没有货位库存时，默认推荐到产品关联货位上
        List<ProductLoactionItemDTO> locationBySkuId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unopenedStockSKuIds)) {
            locationBySkuId = iProductLocationService.findLocationBySkuId(warehouseId, unopenedStockSKuIds);
            LOG.info(String.format("没有找到出库货位的产品关联货位！仓库ID：%s,产品ID：%s", warehouseId, JSON.toJSON(locationBySkuId)));
        }
        List<OrderItemDTO> outStockStrategy = setLocationToOrderItem(locationBySkuId, unopenedStockOrderItems);
        outStockStrategyRule.addAll(outStockStrategy);
        return outStockStrategyRule;
    }

    public List<OrderItemDTO> findStrategyRuleWithReleteLocation(List<OrderItemDTO> orderItemDTOList,
        Integer warehouseId) {
        List<OrderItemDTO> lstNoLocationItems =
            orderItemDTOList.stream().filter(p -> p.getLocationId() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstNoLocationItems)) {
            List<Long> lstNoLocationSkus =
                lstNoLocationItems.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
            LOG.info(String.format("没有找到出库货位 仓库ID：%s,产品ID：%s", warehouseId, JSON.toJSON(lstNoLocationSkus)));
            Map<String, List<OrderItemDTO>> groupList =
                lstNoLocationItems.stream().collect(Collectors.groupingBy(OrderItemDTO::getIdentityKey));
            orderItemDTOList.removeIf(m -> CollectionUtils.isNotEmpty(groupList.get(m.getIdentityKey())));

            // 未开启货位库存的商品按产品配置货位计算
            // 2019/4/9 解决休食仓没有货位库存时，默认推荐到产品关联货位上
            List<ProductLoactionItemDTO> locationBySkuId = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lstNoLocationSkus)) {
                locationBySkuId = iProductLocationService.findLocationBySkuId(warehouseId, lstNoLocationSkus);
                LOG.info(String.format("产品关联货位 仓库ID：%s,产品ID：%s", warehouseId, JSON.toJSON(locationBySkuId)));
            }
            List<OrderItemDTO> outStockStrategy = setLocationToOrderItem(locationBySkuId, lstNoLocationItems);
            LOG.info(String.format("分配关联货位后结果：%s", JSON.toJSON(outStockStrategy)));
            orderItemDTOList.addAll(outStockStrategy);
        }
        return orderItemDTOList;
    }

    /**
     * 分配货位
     *
     * @param locationBySkuId
     * @param unopenedStockOrderItems
     * @return
     */
    private List<OrderItemDTO> setLocationToOrderItem(List<ProductLoactionItemDTO> locationBySkuId,
        List<OrderItemDTO> unopenedStockOrderItems) {
        if (CollectionUtils.isNotEmpty(unopenedStockOrderItems)) {
            Map<Long, List<ProductLoactionItemDTO>> locationMap =
                locationBySkuId.stream().collect(Collectors.groupingBy(ProductLoactionItemDTO::getProductSkuId));
            unopenedStockOrderItems.forEach(item -> {
                List<ProductLoactionItemDTO> productLoactionItemDTOS = locationMap.get(item.getProductSkuId());
                ProductLoactionItemDTO productLoactionItemDTO = null;
                if (!CollectionUtils.isEmpty(productLoactionItemDTOS)) {
                    // 按配置货位拣货出库的时候，如果产品配置了多个货位，优先使用零拣位和分拣位，再存储位
                    if (productLoactionItemDTOS.stream().anyMatch(p -> p.getSubcategory() != null
                        && LocationEnum.零拣位.getType() == p.getSubcategory().intValue())) {
                        productLoactionItemDTO = productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null
                            && LocationEnum.零拣位.getType() == p.getSubcategory().intValue()).findFirst().get();
                    } else if (productLoactionItemDTOS.stream().anyMatch(p -> p.getSubcategory() != null
                        && LocationEnum.分拣位.getType() == p.getSubcategory().intValue())) {
                        productLoactionItemDTO = productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null
                            && LocationEnum.分拣位.getType() == p.getSubcategory().intValue()).findFirst().get();
                    } else if (productLoactionItemDTOS.stream().anyMatch(p -> p.getSubcategory() != null
                        && LocationEnum.存储位.getType() == p.getSubcategory().intValue())) {
                        productLoactionItemDTO = productLoactionItemDTOS.stream().filter(p -> p.getSubcategory() != null
                            && LocationEnum.存储位.getType() == p.getSubcategory().intValue()).findFirst().get();
                    }
                    if (productLoactionItemDTO == null) {
                        productLoactionItemDTO = productLoactionItemDTOS.get(0);
                    }
                    item.setLocationId(productLoactionItemDTO.getLocationId());
                    item.setLocationName(productLoactionItemDTO.getLocationName());
                    item.setSubCategory(productLoactionItemDTO.getSubcategory());
                    item.setAreaId(productLoactionItemDTO.getAreaId());
                    item.setAreaName(productLoactionItemDTO.getAreaName());
                    // 关联货位的，直接用产品项的数量做拣货数量
                    item.setPickUpCount(item.getUnitTotalCount());
                }
            });
        }
        return unopenedStockOrderItems;
    }

    /**
     * 通过分配策略查询货位(销售、调拨、其他、第三方出库)，支持按类目开启货位库存
     */
    public Map<Long, List<BatchLocationInfoDTO>> findLocationForCategory(List<OrderItemDTO> orderItemDTOList,
        String billType, Integer warehouseId) {
        // LOG.info("货位查询参数:{}", JSON.toJSONString(orderItemDTOList));
        Map<Long, List<BatchLocationInfoDTO>> locationMap = new HashMap<>(16);

        Map<String, List<OrderItemDTO>> spiltOrderItemMap =
            spiltOrderItemByLocationStock(orderItemDTOList, warehouseId);
        List<OrderItemDTO> openStockOrderItems = spiltOrderItemMap.get("openStock");
        List<OrderItemDTO> unopenedStockOrderItems = spiltOrderItemMap.get("unopenedStock");

        // 开启货位库存的商品按策略查找货位
        if (CollectionUtils.isNotEmpty(openStockOrderItems)) {
            Map<Long, List<BatchLocationInfoDTO>> openStockLocationMap =
                batchInventoryQueryBL.findLocationByStrategyRule(orderItemDTOList, billType);
            locationMap.putAll(openStockLocationMap);
        }

        // 未开启货位库存的商品按产品配置查找货位
        if (CollectionUtils.isNotEmpty(unopenedStockOrderItems)) {
            List<Long> unopenedStockSKuIds = unopenedStockOrderItems.stream().map(OrderItemDTO::getProductSkuId)
                .distinct().collect(Collectors.toList());
            List<ProductLoactionItemDTO> locationBySkuId =
                iProductLocationService.findLocationBySkuId(warehouseId, unopenedStockSKuIds);
            List<BatchLocationInfoDTO> batchLocationInfoDTOS = new ArrayList<>();
            locationBySkuId.forEach(location -> {
                BatchLocationInfoDTO batchLocationInfoDTO = new BatchLocationInfoDTO();
                BeanUtils.copyProperties(location, batchLocationInfoDTO);
                batchLocationInfoDTO.setLocationCategory(location.getCategory());
                batchLocationInfoDTOS.add(batchLocationInfoDTO);
            });
            Map<Long, List<BatchLocationInfoDTO>> unopenedStockLocationMap =
                batchLocationInfoDTOS.stream().collect(Collectors.groupingBy(BatchLocationInfoDTO::getProductSkuId));
            locationMap.putAll(unopenedStockLocationMap);
        }
        return locationMap;
    }
}

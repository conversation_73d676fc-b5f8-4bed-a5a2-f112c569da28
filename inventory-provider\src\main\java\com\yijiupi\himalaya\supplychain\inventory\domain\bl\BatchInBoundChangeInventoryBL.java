package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.enums.putawayTypeEnum;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemExtContentDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.InBoundTypeEnum;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.ChangeInventoryConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.dto.ChangeInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.InStockOrderState;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemRelatedDTO;

/**
 * 确认入库相关
 *
 * <AUTHOR>
 */
@Service
public class BatchInBoundChangeInventoryBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryOrderBizBL.class);

    @Autowired
    private IdempotenceConsumer idempotenceConsumer;

    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;

    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    @Reference(timeout = 60000)
    private IInStockQueryService iInStockQueryService;

    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;

    public void batchAffirmInBoundAddInventory(ChangeInventoryDTO changeInventoryDTO) {
        // 组装参数
        List<InventoryDeliveryJiupiOrder> deliveryOrders =
            ChangeInventoryConvert.convert(changeInventoryDTO.getOrderList());
        List<String> noProcessOrderNos = new ArrayList<>();
        deliveryOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<String> noProcess = idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders,
                    OrderDeliveryOpType.IN_STOCK_ORDER_TYPE, "订单确认入库");
                if (CollectionUtils.isNotEmpty(noProcess)) {
                    noProcessOrderNos.addAll(noProcess);
                }
            });

        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        idempotenceConsumer.apply(noProcessOrderNos, () -> {
            LOGGER.info("订单确认入库订单列表 deliveryOrders={}", JSON.toJSONString(deliveryOrders));
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {
                warehouseChangListBOConverter.processOrderItemToOrderDeliveryDelayBO(warehouseChangeList,
                    inventoryDeliveryJiupiOrder);
            }
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, false, true, true, true,
                false);
        });
        LOGGER.info("batchAffirmInBoundAddInventory 库存处理结果warehouseChangeList={}",
            JSON.toJSONString(warehouseChangeList));
        List<InStockOrderDTO> inStockOrderByOrderNoList = iInStockQueryService.findInStockOrderByOrderNoList(
            changeInventoryDTO.getOrgId(), changeInventoryDTO.getWarehouseId(), changeInventoryDTO.getOrderList()
                .stream().map(OrderDTO::getRefOrderNo).distinct().collect(Collectors.toList()));
        // 深度拷贝来源数据
        List<WarehouseInventoryChangeBO> originalWarehouseChangeList =
            getOriginalWarehouseChangeList(warehouseChangeList);
        if (CollectionUtils.isNotEmpty(inStockOrderByOrderNoList)) {
            // 根据确认入库残次品数据填充
            fillItemRelatedData(inStockOrderByOrderNoList, changeInventoryDTO.getOrderList());
            // 返回上架后处理销售库存的产品
            List<Long> putAwayTaskProcessSaleSkuIdList =
                inventoryOrderBizBL.createPutAwayTaskByOrderAndScmVersion(inStockOrderByOrderNoList, null,
                    InStockOrderState.已入库.getType(), getPutawayTypeByInStockOrder(inStockOrderByOrderNoList));
            // 移除上架处理销售库存的产品
            warehouseChangeList
                .removeIf(e -> e != null && putAwayTaskProcessSaleSkuIdList.contains(e.getProductSkuId()));

            // 处理入库单中的残次品
            inventoryOrderBizBL.affirmRelatedIInStock(inStockOrderByOrderNoList);
        }
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            // 过滤无需发送销售库存消息数据
            inventoryOrderBizBL.setNotSendSaleInventoryByChangeBO(warehouseChangeList);
            // 处理销售库存发消息
            warehouseInventoryManageBL.processSellInventory(warehouseChangeList, null);
        }

        // 获取退货入残次品位时，因产生上架任务被过滤掉的销售库存消息
        List<WarehouseInventoryChangeBO> defectiveWarehouseChangeList =
            getDefectiveSellChangeBO(inStockOrderByOrderNoList, originalWarehouseChangeList, warehouseChangeList);
        LOGGER.info("batchAffirmInBoundAddInventory 残次品位库存处理结果defectiveWarehouseChangeList={}",
            JSON.toJSONString(defectiveWarehouseChangeList));
        if (CollectionUtils.isNotEmpty(defectiveWarehouseChangeList)) {
            // 处理销售库存发消息
            warehouseInventoryManageBL.processSellInventory(defectiveWarehouseChangeList, null);
        }
    }

    /**
     * 深度拷贝来源数据
     */
    public List<WarehouseInventoryChangeBO>
        getOriginalWarehouseChangeList(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return new ArrayList<>();
        }

        List<WarehouseInventoryChangeBO> originalWarehouseChangeList = new ArrayList<>();
        warehouseChangeList.stream().filter(p -> p != null).forEach(p -> {
            WarehouseInventoryChangeBO changeBO = new WarehouseInventoryChangeBO();
            BeanUtils.copyProperties(p, changeBO);
            originalWarehouseChangeList.add(changeBO);
        });

        return originalWarehouseChangeList;
    }

    /**
     * 补发因上架过滤掉的残次品销售库存变动数据
     */
    public List<WarehouseInventoryChangeBO> getDefectiveSellChangeBO(List<InStockOrderDTO> inStockOrderDTOS,
        List<WarehouseInventoryChangeBO> originalChangeList, List<WarehouseInventoryChangeBO> sendSaleChangeList) {
        if (CollectionUtils.isEmpty(inStockOrderDTOS)) {
            return new ArrayList<>();
        }

        List<InStockOrderDTO> inStockOrderDTOList = inStockOrderDTOS.stream()
            .filter(p -> CollectionUtils.isNotEmpty(p.getInStockOrderItemDTOList())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inStockOrderDTOList)) {
            return new ArrayList<>();
        }

        // 残次品入库数量
        List<WarehouseInventoryChangeBO> defectiveChangeBOS = new ArrayList<>();
        // 扣除残次品数量
        inStockOrderDTOS.stream().filter(order -> order != null).forEach(order -> {
            if (CollectionUtils.isEmpty(order.getInStockOrderItemDTOList())) {
                return;
            }

            List<InStockOrderItemDTO> itemDTOS = order.getInStockOrderItemDTOList().stream()
                .filter(item -> item != null && StringUtils.isNotEmpty(item.getExtContent()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemDTOS)) {
                return;
            }

            itemDTOS.stream().forEach(item -> {
                // 移库残次品数量
                BigDecimal relatedCount = BigDecimal.ZERO;
                InStockOrderItemExtContentDTO extContentDTO =
                    JSON.parseObject(item.getExtContent(), InStockOrderItemExtContentDTO.class);
                if (extContentDTO != null && CollectionUtils.isNotEmpty(extContentDTO.getItemRelatedDTOList())) {
                    relatedCount = extContentDTO.getItemRelatedDTOList().stream()
                        .filter(related -> related != null && related.getUnitTotalCount() != null)
                        .map(related -> related.getUnitTotalCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                if (relatedCount.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }

                WarehouseInventoryChangeBO changeBO = new WarehouseInventoryChangeBO();
                changeBO.setOrderNo(order.getRefOrderNo());
                changeBO.setWarehouseId(order.getWarehouseId());
                changeBO.setProductSkuId(item.getSkuId());
                changeBO.setCount(relatedCount);
                defectiveChangeBOS.add(changeBO);
            });
        });

        if (CollectionUtils.isEmpty(defectiveChangeBOS)) {
            return new ArrayList<>();
        }

        // 所有入库数据
        Map<String, List<WarehouseInventoryChangeBO>> originalChangeMap =
            originalChangeList.stream().filter(p -> p != null && p.getProductSkuId() != null).collect(Collectors
                .groupingBy(p -> String.format("%s-%s-%s", p.getWarehouseId(), p.getOrderNo(), p.getProductSkuId())));
        // 过滤销售库存后的入库数据
        Map<String, List<WarehouseInventoryChangeBO>> sellChangeMap =
            sendSaleChangeList.stream().filter(p -> p != null && p.getProductSkuId() != null).collect(Collectors
                .groupingBy(p -> String.format("%s-%s-%s", p.getWarehouseId(), p.getOrderNo(), p.getProductSkuId())));
        // 残次品入库数据
        Map<String, BigDecimal> defectiveCountMap =
            defectiveChangeBOS.stream().filter(p -> p != null && p.getProductSkuId() != null)
                .collect(Collectors.groupingBy(
                    p -> String.format("%s-%s-%s", p.getWarehouseId(), p.getOrderNo(), p.getProductSkuId()),
                    Collectors.mapping(p -> p.getCount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        if (defectiveCountMap == null || defectiveCountMap.size() <= 0) {
            return new ArrayList<>();
        }

        // 残次品入库补发销售库存消息
        List<WarehouseInventoryChangeBO> defectiveSellMessage = new ArrayList<>();
        defectiveCountMap.forEach((defectiveSku, defectiveCount) -> {
            if (sellChangeMap != null && sellChangeMap.size() > 0 && sellChangeMap.get(defectiveSku) != null) {
                List<WarehouseInventoryChangeBO> sellChangeBOS = sellChangeMap.get(defectiveSku);
                // 已发送销售库存消息数量
                BigDecimal sellCount = sellChangeBOS.stream().filter(p -> p.getCount() != null)
                    .map(WarehouseInventoryChangeBO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                defectiveCount = defectiveCount.subtract(ObjectUtils.defaultIfNull(sellCount, BigDecimal.ZERO));
                if (defectiveCount.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
            }

            // 原所有库存数量数据
            List<WarehouseInventoryChangeBO> originalChangeBOS = originalChangeMap.get(defectiveSku);
            if (CollectionUtils.isEmpty(originalChangeBOS)) {
                return;
            }

            // 分配需要补发残次品销售库存消息
            BigDecimal newSellCount = defectiveCount;
            for (WarehouseInventoryChangeBO changeBO : originalChangeBOS) {
                if (newSellCount.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                if (newSellCount.compareTo(changeBO.getCount()) >= 0) {
                    defectiveSellMessage.add(changeBO);

                    newSellCount = newSellCount.subtract(changeBO.getCount());
                    continue;
                }

                changeBO.setCount(newSellCount);
                defectiveSellMessage.add(changeBO);
                newSellCount = BigDecimal.ZERO;
            }
        });

        return defectiveSellMessage;
    }

    private Byte getPutawayTypeByInStockOrder(List<InStockOrderDTO> inStockOrderDTOS) {
        byte putawayType = putawayTypeEnum.退货上架.getType();
        if (inStockOrderDTOS.stream()
            .anyMatch(p -> Objects.equals(p.getInBoundType(), InBoundTypeEnum.ALLOT_IN_BOUND.getCode())
                && !p.getRefOrderNo().startsWith("NPT"))) {
            putawayType = putawayTypeEnum.调拨上架.getType();
        }

        return putawayType;
    }

    /**
     * 根据确认入库残次品数据填充
     *
     * @param inStockOrderDTOS
     * @param inOrderDTOS
     */
    private void fillItemRelatedData(List<InStockOrderDTO> inStockOrderDTOS, List<OrderDTO> inOrderDTOS) {
        if (CollectionUtils.isEmpty(inStockOrderDTOS) || CollectionUtils.isEmpty(inOrderDTOS)) {
            return;
        }

        LOGGER.info("根据确认入库残次品数据填充 入参{}", JSON.toJSONString(inStockOrderDTOS));
        Map<Long,
            List<OrderItemRelatedDTO>> itemRelatedMap = inOrderDTOS.stream()
                .filter(p -> p != null && CollectionUtils.isNotEmpty(p.getItems())).flatMap(p -> p.getItems().stream())
                .filter(p -> p != null && CollectionUtils.isNotEmpty(p.getItemRelatedDTOList()))
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getItemRelatedDTOList(), (v1, v2) -> v1));
        if (itemRelatedMap == null || itemRelatedMap.size() < 0) {
            return;
        }

        Predicate<InStockOrderItemDTO> needFillPredicatee = (input -> {
            if (!itemRelatedMap.containsKey(input.getId())) {
                return false;
            }
            if (StringUtils.isEmpty(input.getExtContent())) {
                return true;
            }
            InStockOrderItemExtContentDTO extContentDTO =
                JSON.parseObject(input.getExtContent(), InStockOrderItemExtContentDTO.class);
            if (extContentDTO == null || CollectionUtils.isEmpty(extContentDTO.getItemRelatedDTOList())) {
                return true;
            }
            return false;
        });
        List<InStockOrderItemDTO> inStockOrderItemDTOS =
            inStockOrderDTOS.stream().map(InStockOrderDTO::getInStockOrderItemDTOList).flatMap(list -> list.stream())
                .filter(needFillPredicatee).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inStockOrderItemDTOS)) {
            return;
        }

        inStockOrderItemDTOS.forEach(item -> {
            List<OrderItemRelatedDTO> relatedDTOS = itemRelatedMap.get(item.getId());
            if (CollectionUtils.isEmpty(relatedDTOS)) {
                return;
            }

            InStockOrderItemExtContentDTO extContentDTO = new InStockOrderItemExtContentDTO();
            extContentDTO.setItemRelatedDTOList(relatedDTOS);
            item.setExtContent(JSON.toJSONString(extContentDTO));
        });

        LOGGER.info("根据确认入库指定残次品数据填充结果:{}", JSON.toJSONString(inStockOrderItemDTOS));
    }
}

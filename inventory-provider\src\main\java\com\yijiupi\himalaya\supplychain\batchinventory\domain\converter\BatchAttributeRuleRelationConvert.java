package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleRelationDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeRuleRelationConvert {

    public static BatchAttributeRuleRelationPO BatchAttributeRuleRelationDTO2PO(BatchAttributeRuleRelationDTO dto) {
        if (dto == null) {
            return null;
        }
        BatchAttributeRuleRelationPO batchAttributeRuleRelationPO = new BatchAttributeRuleRelationPO();
        batchAttributeRuleRelationPO.setRuleId(dto.getRuleId());
        batchAttributeRuleRelationPO.setRuleType(dto.getRuleType());
        batchAttributeRuleRelationPO.setAttributeValueName(dto.getAttributeValueName());
        batchAttributeRuleRelationPO.setAttributeValueId(dto.getAttributeValueId());
        return batchAttributeRuleRelationPO;
    }

    public static List<BatchAttributeRuleRelationPO>
        BatchAttributeRuleRelationDTOS2POS(List<BatchAttributeRuleRelationDTO> dtoList) {
        ArrayList<BatchAttributeRuleRelationPO> batchAttributeRuleRelationPOS = new ArrayList<>();
        for (BatchAttributeRuleRelationDTO batchAttributeRuleRelationDTO : dtoList) {
            batchAttributeRuleRelationPOS.add(BatchAttributeRuleRelationDTO2PO(batchAttributeRuleRelationDTO));
        }
        return batchAttributeRuleRelationPOS;
    }
}

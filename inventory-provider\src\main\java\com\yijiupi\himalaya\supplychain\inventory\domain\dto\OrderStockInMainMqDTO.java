package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 入库主表
 * 
 * @author: yanpin
 * @date: 2019年4月25日 下午4:28:30
 */
public class OrderStockInMainMqDTO implements Serializable {
    private static final long serialVersionUID = -8430146644914370378L;
    /**
     * 入库消息订单列表
     */
    private List<OrderStockInMqDTO> orderList;
    /**
     * 入库消息退货单列表
     */
    private List<OrderReturnStockInMqDTO> returnList;

    @Override
    public String toString() {
        return "OrderStockInMainMqDTO [orderList=" + orderList + ", returnList=" + returnList + "]";
    }

    public List<OrderReturnStockInMqDTO> getReturnList() {
        return returnList;
    }

    public void setReturnList(List<OrderReturnStockInMqDTO> returnList) {
        this.returnList = returnList;
    }

    /**
     * 获取orderList
     * 
     * @return orderList orderList
     */
    public List<OrderStockInMqDTO> getOrderList() {
        return orderList;
    }

    /**
     * 设置orderList
     * 
     * @param orderList orderList
     */
    public void setOrderList(List<OrderStockInMqDTO> orderList) {
        this.orderList = orderList;
    }
}

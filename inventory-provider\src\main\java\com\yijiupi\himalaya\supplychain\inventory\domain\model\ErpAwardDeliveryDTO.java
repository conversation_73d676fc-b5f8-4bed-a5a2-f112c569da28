package com.yijiupi.himalaya.supplychain.inventory.domain.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by 余明 on 2019-01-14.
 */
public class ErpAwardDeliveryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /// <summary>
    /// 订单id
    /// </summary>
    private String id;
    /// <summary>
    /// 订单编号
    /// </summary>
    private String orderNo;
    private String awardOrderNo;
    /// <summary>
    /// 订单创建日期
    /// </summary>
    private String createTime;
    /// <summary>
    /// 订单完成日期
    /// </summary>
    private String completeTime;
    /// <summary>
    /// 城市id
    /// </summary>
    private Integer cityId;
    /// <summary>
    /// 配送仓库id
    /// </summary>
    private Integer warehouseId;
    /// <summary>
    /// 用户id
    /// </summary>
    private String userId;
    private List<ErpAwardDeliveryItemDTO> itemList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getAwardOrderNo() {
        return awardOrderNo;
    }

    public void setAwardOrderNo(String awardOrderNo) {
        this.awardOrderNo = awardOrderNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<ErpAwardDeliveryItemDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<ErpAwardDeliveryItemDTO> itemList) {
        this.itemList = itemList;
    }
}
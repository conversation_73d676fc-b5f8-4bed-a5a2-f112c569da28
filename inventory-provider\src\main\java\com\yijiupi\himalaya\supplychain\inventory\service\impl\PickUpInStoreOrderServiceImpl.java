package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.PickUpInStoreOrderBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.service.IPickUpInStoreOrderService;

/**
 * 自提订单确认出库变更库存
 *
 * <AUTHOR>
 */
@Service
public class PickUpInStoreOrderServiceImpl implements IPickUpInStoreOrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryOrderBizServiceImpl.class);

    @Autowired
    private PickUpInStoreOrderBL pickUpInStoreOrderBL;

    @Override
    public List<InventoryDeliveryJiupiOrder>
        pickUpInStoreOrderAffirmChangeInventory(List<InventoryDeliveryJiupiOrder> inventoryDeliveryOrders) {
        LOGGER.info("PickUpInStoreOrderServiceImpl.pickUpInStoreOrderAffirmChangeInventory 自提订单确认出库变更库存 "
            + "inventoryDeliveryOrders={}", JSON.toJSONString(inventoryDeliveryOrders));
        return pickUpInStoreOrderBL.pickUpInStoreOrderAffirmChangeInventory(inventoryDeliveryOrders);
    }
}

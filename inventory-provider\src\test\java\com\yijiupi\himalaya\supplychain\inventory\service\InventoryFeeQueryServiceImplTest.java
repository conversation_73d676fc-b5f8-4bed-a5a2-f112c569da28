package com.yijiupi.himalaya.supplychain.inventory.service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.fee.InventoryFeeQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.InventoryFeeQueryServiceImpl;

/**
 * 配送费,托管费接口测试
 *
 * <AUTHOR> 2018/2/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class InventoryFeeQueryServiceImplTest {

    @Autowired
    private InventoryFeeQueryServiceImpl feeQueryService;

    @Test
    public void findInventoryFee() {
        InventoryFeeQueryDTO dto = new InventoryFeeQueryDTO();
        dto.setProductSkuId(40200009542851L);
        dto.setChannel(0);
        dto.setCount(new BigDecimal(61));
        Date date = new Date();
        GregorianCalendar gc = new GregorianCalendar();
        gc.set(Calendar.YEAR, 2018);// 设置年
        gc.set(Calendar.MONTH, 2);// 这里0是1月..以此向后推
        gc.set(Calendar.DAY_OF_MONTH, 21);// 设置天
        date = gc.getTime();
        dto.setGetGoodsTime(date);
        dto.setSource(0);
        dto.setSecOwnerId(null);
        dto.setWarehouseId(4021);
        InventoryFeeDTO inventoryFee = feeQueryService.findInventoryFee(Collections.singletonList(dto));
    }

}

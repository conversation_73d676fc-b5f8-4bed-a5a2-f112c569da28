package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.DeliveryStoreRecordMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;

/**
 * 供应链仓库库存变更事件.
 *
 * <AUTHOR> 2018/1/29
 */
@Service
public class DeliveryCountChangeBL {
    private static final Logger LOG = LoggerFactory.getLogger(DeliveryCountChangeBL.class);

    @Autowired
    private DeliveryStoreRecordMapper deliveryStoreRecordMapper;
    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;

    public void syncApply(String msg) {
        // LOG.info("发货数量变更消息>>>【mq.supplychain.inventory.deliverycountchange】", msg);
        if (StringUtils.isEmpty(msg)) {
            throw new BusinessValidateException("【发货数量】反序列化失败！" + msg);
        }
        List<ProductInventoryPO> productInventoryPOS = JSON.parseArray(msg, ProductInventoryPO.class);
        if (CollectionUtils.isEmpty(productInventoryPOS)) {
            throw new BusinessValidateException("【发货数量】反序列化失败！" + msg);
        }
        processMsg(productInventoryPOS);
        // LOG.info("发货数量变更处理成功: ");
    }

    // 批量处理发货中数量
    private void processMsg(List<ProductInventoryPO> lstMsg) {
        LOG.info("发货数量变更处理:{}", JSON.toJSONString(lstMsg));
        if (lstMsg != null && lstMsg.size() > 0) {
            ArrayList<ProductInventoryPO> lstRecord = new ArrayList<>();
            try {
                // 根据StoreId，合并发货总量
                for (ProductInventoryPO po : lstMsg) {
                    if (lstRecord.stream().anyMatch(p -> p.getId().equals(po.getId()))) {
                        continue;
                    }
                    BigDecimal totalCount = lstMsg.stream().filter(p -> p.getId().equals(po.getId()))
                        .map(p -> p.getChangeCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    po.setChangeCount(totalCount);
                    lstRecord.add(po);
                }
                if (!CollectionUtils.isEmpty(lstRecord)) {
                    LOG.info("发货数量:{}", JSON.toJSONString(lstRecord));
                    deliveryStoreRecordMapper.insertOrUpdateDeliveryRecordBatch(lstRecord);
                }
            } catch (Exception oe) {
                // oe.printStackTrace();
                LOG.info("处理发货数量失败:" + oe.getMessage());
                // if (!CollectionUtils.isEmpty(lstRecord)) {
                // inventoryChangeEventFireBL.deliveryCountInventoryChangeEvent(lstRecord);
                // LOG.info("发货数量失败消息(塞回队列重新处理)：" + JSON.toJSONString(lstRecord));
                // }
            }
        }
    }

}

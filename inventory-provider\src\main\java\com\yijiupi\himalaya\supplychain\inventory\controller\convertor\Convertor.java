/**
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory.controller.convertor;

import com.yijiupi.himalaya.base.dto.UserIdentity;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 转换类基类
 * 
 * <AUTHOR>
 * @param <M> 待转对象类型
 * @param <N> 预转对象类型
 * @date 2016-11-10
 */
public abstract class Convertor<M, N> {
    /**
     * Description: 单个对象转换
     * 
     * @param m 待转对象
     * @return 转换后的对象
     */
    public abstract N convert(M m);

    /**
     * Description: 单个对象反向转换
     * 
     * @param n 待转对象
     * @return 转换后的对象
     */
    public abstract M reverseConvert(N n);

    /**
     * Description: 集合转换
     * 
     * @param mList 待转对象集合
     * @return 转换后的对象集合
     */
    public List<N> convert(List<M> mList) {
        List<N> nList = null;
        if (mList != null) {
            nList = new ArrayList<N>(mList.size());
            for (M m : mList) {
                nList.add(convert(m));
            }
        }
        return nList;
    }

    /**
     * Description: 集合转换
     * 
     * @param mList 待转对象集合
     * @return 转换后的对象集合
     */
    public List<M> reverseConvert(List<N> nList) {
        List<M> mList = null;
        if (nList != null) {
            mList = new ArrayList<M>(nList.size());
            for (N n : nList) {
                mList.add(reverseConvert(n));
            }
        }
        return mList;
    }

    protected UserIdentity userIdentity(int userId) {
        UserIdentity identity = new UserIdentity();
        identity.setUserId(userId);
        return identity;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.supplychain.baseutil.DateUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryManageBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.ProductStoreBatchChangeRecordBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.PickUpCompleteTransferBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import com.yijiupi.himalaya.utils.QuantityShareUtils;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/9/13
 */
@Service
public class BatchTaskItemCompleteTransferBL {

    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;

    @Reference
    private LocationAreaService locationAreaService;
    @Reference
    private WarehouseConfigService warehouseConfigService;

    public PickUpCompleteTransferBO checkInventoryTransferForPickUp(List<PickUpDTO> pickUpDTOList,
        Boolean isIgnoreProductionDate, Boolean isIgnoreHasNotEnoughStore, List<PickUpDTO> realPickUpDTOList) {
        Integer warehouseId = pickUpDTOList.get(0).getWarehouseId();
        boolean isOpenStock = warehouseId != null && warehouseConfigService.isOpenLocationStock(warehouseId);
        if (!isOpenStock) {
            return null;
        }

        // 是否开启货位组
        boolean isOpenLocationGroup = warehouseConfigService.isOpenLocationGroup(warehouseId);
        Map<String, PickUpDTO> openGroupPickUpMap = getOpenGroupPickUpMap(isOpenLocationGroup, pickUpDTOList);

        Map<Long, LocationReturnDTO> locationMap = getLocationDTOs(pickUpDTOList, openGroupPickUpMap);

        List<PickUpDTO> negativePickUpDTOList = pickUpDTOList.stream()
            .filter(m -> m.getCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        List<PickUpDTO> positivePickUpDTOList = pickUpDTOList.stream()
            .filter(m -> m.getCount().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());

        List<BatchInventoryTransferDTO> batchInventoryTransferDTOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(positivePickUpDTOList)) {
            addAndCheckInventoryTransfer(positivePickUpDTOList, isIgnoreProductionDate, isIgnoreHasNotEnoughStore,
                realPickUpDTOList, batchInventoryTransferDTOList);
        }

        if (CollectionUtils.isEmpty(negativePickUpDTOList)) {
            return new PickUpCompleteTransferBO(batchInventoryTransferDTOList);
        }
        List<ProductStoreBatchChangeInfoResultDTO> totalRecordList = new ArrayList<>();
        List<ProductStoreBatchChangeInfoQueryDTO> recordQueryList = getDistinctQueryList(pickUpDTOList);
        for (ProductStoreBatchChangeInfoQueryDTO queryDTO : recordQueryList) {
            List<ProductStoreBatchChangeInfoResultDTO> resultDTOList =
                productStoreBatchChangeRecordBL.findChangeRecordInfoByOrderInfo(queryDTO);
            if (CollectionUtils.isNotEmpty(resultDTOList)) {
                totalRecordList.addAll(resultDTOList);
            }
        }

        if (org.springframework.util.CollectionUtils.isEmpty(totalRecordList)) {
            addAndCheckInventoryTransfer(positivePickUpDTOList, isIgnoreProductionDate, isIgnoreHasNotEnoughStore,
                realPickUpDTOList, batchInventoryTransferDTOList);
            return new PickUpCompleteTransferBO(batchInventoryTransferDTOList);
        }

        Map<String, List<ProductStoreBatchChangeInfoResultDTO>> changeRecordMap =
            totalRecordList.stream().collect(Collectors.groupingBy(this::getSecKey));

        // key 是 库存变更记录id
        Map<String, List<ProductStoreBatchChangeInfoResultDTO>> storeResultMap = totalRecordList.stream()
            .collect(Collectors.groupingBy(ProductStoreBatchChangeInfoResultDTO::getStoreChangeRecordId));

        List<ProductStoreBatchDTO> totalStoreBatchList = new ArrayList<>();
        List<PickUpDTO> notHandledList = new ArrayList<>();
        // 一个规格是否有多个货主
        for (PickUpDTO pickUpDTO : negativePickUpDTOList) {
            PickUpDTO tmpPickUpDTO = openGroupPickUpMap.getOrDefault(pickUpDTO.getTempId(), pickUpDTO);
            List<ProductStoreBatchChangeInfoResultDTO> fromRecordDTOList = changeRecordMap
                .get(getSecKey(pickUpDTO.getProductSkuId(), tmpPickUpDTO.getFromLocationId(), pickUpDTO.getFromSource(),
                    pickUpDTO.getProductionDateStr(), pickUpDTO.getFromChannel(), pickUpDTO.getSecOwnerId()));

            Long toLocationId = getTransferLocationId(isOpenLocationGroup, pickUpDTO.getLocationId(), locationMap);
            tmpPickUpDTO.setLocationId(toLocationId);

            List<ProductStoreBatchChangeInfoResultDTO> toRecordDTOList = changeRecordMap
                .get(getSecKey(pickUpDTO.getProductSkuId(), tmpPickUpDTO.getLocationId(), pickUpDTO.getToSource(),
                    pickUpDTO.getProductionDateStr(), pickUpDTO.getToChannel(), pickUpDTO.getSecOwnerId()));

            if (CollectionUtils.isEmpty(fromRecordDTOList)) {
                notHandledList.add(pickUpDTO);
                continue;
            }

            List<ProductStoreBatchChangeInfoResultDTO> filterFromRecordList =
                getLatestChangeRecord(fromRecordDTOList, pickUpDTO, storeResultMap);
            if (CollectionUtils.isEmpty(filterFromRecordList)) {
                notHandledList.add(pickUpDTO);
                continue;
            }

            List<ProductStoreBatchChangeInfoResultDTO> filterToRecordList =
                getLatestChangeRecord(toRecordDTOList, pickUpDTO, storeResultMap);

            realPickUpDTOList.add(pickUpDTO);
            List<ProductStoreBatchDTO> storeBatchList = createBatchInventoryTransferDTO(pickUpDTO, filterFromRecordList,
                filterToRecordList, openGroupPickUpMap);
            totalStoreBatchList.addAll(storeBatchList);
        }

        if (CollectionUtils.isNotEmpty(notHandledList)) {
            addAndCheckInventoryTransfer(notHandledList, isIgnoreProductionDate, isIgnoreHasNotEnoughStore,
                realPickUpDTOList, batchInventoryTransferDTOList);
        }

        return new PickUpCompleteTransferBO(batchInventoryTransferDTOList, totalStoreBatchList);
    }

    private Long getTransferLocationId(boolean isOpenLocationGroup, Long locationId,
        Map<Long, LocationReturnDTO> locationMap) {
        LocationReturnDTO locationReturnDTO = locationMap.get(locationId);
        if (BooleanUtils.isFalse(isOpenLocationGroup)) {
            return locationId;
        }

        if (Objects.isNull(locationReturnDTO)) {
            throw new BusinessValidateException("未查询到货位，货位id: " + locationId);
        }

        if (locationReturnDTO.getSubcategory() == CategoryEnum.CARGO_AREA.getValue().byteValue()) {
            return locationId;
        }
        return locationReturnDTO.getArea_Id();
    }

    private List<ProductStoreBatchChangeInfoQueryDTO> getDistinctQueryList(List<PickUpDTO> negativePickUpDTOList) {
        List<ProductStoreBatchChangeInfoQueryDTO> queryDTOList = new ArrayList<>();
        Map<String, PickUpDTO> pickUpDTOMap = negativePickUpDTOList.stream()
            .collect(Collectors.toMap(this::getProductStoreBatchChangeInfoQueryKey, v -> v, (v1, v2) -> v1));
        for (Map.Entry<String, PickUpDTO> entry : pickUpDTOMap.entrySet()) {
            PickUpDTO pickUpDTO = entry.getValue();
            ProductStoreBatchChangeInfoQueryDTO queryDTO = new ProductStoreBatchChangeInfoQueryDTO();
            queryDTO.setSkuId(pickUpDTO.getProductSkuId());
            queryDTO.setWarehouseId(pickUpDTO.getWarehouseId());
            queryDTO.setProductSpecificationId(pickUpDTO.getProductSpecificationId());
            queryDTO.setOrderNo(pickUpDTO.getBusinessNo());
            queryDTOList.add(queryDTO);
        }

        return queryDTOList;
    }

    private String getProductStoreBatchChangeInfoQueryKey(PickUpDTO pickUpDTO) {
        return String.format("%s-%s-%s-%s", pickUpDTO.getProductSkuId(), pickUpDTO.getBusinessNo(),
            pickUpDTO.getProductSpecificationId(), pickUpDTO.getWarehouseId());
    }

    private Map<Long, LocationReturnDTO> getLocationDTOs(List<PickUpDTO> pickUpDTOList,
        Map<String, PickUpDTO> pickUpDTOMap) {
        Set<String> locationIdSet =
            pickUpDTOList.stream().map(PickUpDTO::getLocationId).map(String::valueOf).collect(Collectors.toSet());
        locationIdSet.addAll(
            pickUpDTOList.stream().map(PickUpDTO::getFromLocationId).map(String::valueOf).collect(Collectors.toSet()));

        if (!org.springframework.util.CollectionUtils.isEmpty(pickUpDTOMap)) {
            locationIdSet.addAll(pickUpDTOMap.values().stream().map(PickUpDTO::getLocationId).map(String::valueOf)
                .collect(Collectors.toSet()));
            locationIdSet.addAll(pickUpDTOMap.values().stream().map(PickUpDTO::getFromLocationId).map(String::valueOf)
                .collect(Collectors.toSet()));
        }
        List<LocationReturnDTO> lstLocation = locationAreaService.findLocationListById(new ArrayList<>(locationIdSet));

        return lstLocation.stream().collect(Collectors.toMap(LocationReturnDTO::getId, v -> v));
    }

    private Map<String, PickUpDTO> getOpenGroupPickUpMap(boolean isOpenLocationGroup, List<PickUpDTO> pickUpDTOList) {
        // 转换[from]_Location货位为货位组
        List<PickUpDTO> pickUpDTOTransformList =
            isOpenLocationGroup ? batchInventoryManageBL.transformPickUpToLocGroup(pickUpDTOList) : pickUpDTOList;
        // if (isOpenLocationGroup) {
        // List<String> toLocationIds = pickUpDTOTransformList.stream().map(PickUpDTO::getLocationId).distinct()
        // .map(String::valueOf).collect(Collectors.toList());
        // List<LocationReturnDTO> toLocationInfoList = locationAreaService.findLocationListById(toLocationIds);
        //
        // }
        if (isOpenLocationGroup) {
            return pickUpDTOTransformList.stream().collect(Collectors.toMap(PickUpDTO::getTempId, v -> v));
        }

        return Collections.emptyMap();
    }

    private void addAndCheckInventoryTransfer(List<PickUpDTO> pickUpDTOList, Boolean isIgnoreProductionDate,
        Boolean isIgnoreHasNotEnoughStore, List<PickUpDTO> realPickUpDTOList,
        List<BatchInventoryTransferDTO> batchInventoryTransferDTOList) {
        List<BatchInventoryTransferDTO> transferList = batchInventoryManageBL.checkInventoryTransfer(pickUpDTOList,
            isIgnoreProductionDate, isIgnoreHasNotEnoughStore, realPickUpDTOList);
        if (CollectionUtils.isNotEmpty(transferList)) {
            batchInventoryTransferDTOList.addAll(transferList);
        }
    }

    private List<ProductStoreBatchChangeInfoResultDTO> getLatestChangeRecord(
        List<ProductStoreBatchChangeInfoResultDTO> resultDTOList, PickUpDTO pickUpDTO,
        Map<String, List<ProductStoreBatchChangeInfoResultDTO>> storeResultMap) {
        List<ProductStoreBatchChangeInfoResultDTO> sortedRecord = resultDTOList.stream()
            .filter(m -> m.getSecOwnerId().equals(pickUpDTO.getSecOwnerId()))
            .sorted(
                Comparator.comparing(ProductStoreBatchChangeInfoResultDTO::getCreateTime, Comparator.reverseOrder()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortedRecord)) {
            return Collections.emptyList();
        }
        String productStoreId = sortedRecord.get(0).getStoreChangeRecordId();
        List<ProductStoreBatchChangeInfoResultDTO> fileredList = storeResultMap.get(productStoreId);
        if (CollectionUtils.isEmpty(fileredList)) {
            return Collections.emptyList();
        }

        return fileredList;
    }

    private List<ProductStoreBatchDTO> createBatchInventoryTransferDTO(PickUpDTO pickUpDTO,
        List<ProductStoreBatchChangeInfoResultDTO> filterRecordList,
        List<ProductStoreBatchChangeInfoResultDTO> filterToRecordList, Map<String, PickUpDTO> openGroupPickUpMap) {
        PickUpDTO tmpPickUpDTO = openGroupPickUpMap.getOrDefault(pickUpDTO.getTempId(), pickUpDTO);
        Long fromLocationId = tmpPickUpDTO.getFromLocationId();
        Long toLocationId = tmpPickUpDTO.getLocationId();

        List<ProductStoreBatchChangeInfoResultDTO> fromRecordList =
            filterRecordList.stream().filter(m -> m.getLocationId().equals(fromLocationId))
                .filter(m -> m.getSecOwnerId().equals(pickUpDTO.getSecOwnerId())).collect(Collectors.toList());
        List<ProductStoreBatchChangeInfoResultDTO> toRecordList =
            filterToRecordList.stream().filter(m -> m.getLocationId().equals(toLocationId))
                .filter(m -> m.getSecOwnerId().equals(pickUpDTO.getSecOwnerId())).collect(Collectors.toList());
        BigDecimal changeCount = pickUpDTO.getCount();

        List<ProductStoreBatchDTO> totalStoreBatchList = new ArrayList<>();

        List<ProductStoreBatchDTO> fromProductStoreBatchList =
            createFromLocationProductStoreBatch(fromRecordList, tmpPickUpDTO);
        totalStoreBatchList.addAll(fromProductStoreBatchList);

        List<ProductStoreBatchDTO> toProductStoreBatchList =
            createToLocationProductStoreBatch(toRecordList, tmpPickUpDTO);
        totalStoreBatchList.addAll(toProductStoreBatchList);

        return totalStoreBatchList;
    }

    private List<ProductStoreBatchDTO> createFromLocationProductStoreBatch(
        List<ProductStoreBatchChangeInfoResultDTO> fromRecordList, PickUpDTO tmpPickUpDTO) {
        List<ProductStoreBatchDTO> productStoreBatchDTOList = new ArrayList<>();
        BigDecimal changeCount = tmpPickUpDTO.getCount();

        fromRecordList.forEach(record -> {
            record.setTotalCountMinUnit(record.getTotalCountMinUnit().abs());
        });

        List<QuantityShareUtils.CountHelper> helperList = fromRecordList.stream()
            .map(m -> new QuantityShareUtils.CountHelper(m.getStoreBatchChangeRecordId(), m.getTotalCountMinUnit()))
            .collect(Collectors.toList());
        QuantityShareUtils.CountShareResultHelper countShareResultHelper =
            QuantityShareUtils.shareCount(helperList, changeCount.abs());

        Map<String, ProductStoreBatchChangeInfoResultDTO> changeRecordMap = fromRecordList.stream()
            .collect(Collectors.toMap(ProductStoreBatchChangeInfoResultDTO::getStoreBatchChangeRecordId, v -> v));

        countShareResultHelper.getShareHelperList().stream().filter(QuantityShareUtils.CountHelper::isHaveChanged)
            .forEach(share -> {
                ProductStoreBatchChangeInfoResultDTO resultDTO = changeRecordMap.get(share.getId());
                ProductStoreBatchDTO productStoreBatchDTO = new ProductStoreBatchDTO();
                productStoreBatchDTO.setId(resultDTO.getProductStoreBatchId());
                productStoreBatchDTO.setTotalCount(resultDTO.getTotalCountMinUnit().subtract(share.getCount()));
                productStoreBatchDTOList.add(productStoreBatchDTO);
            });

        return productStoreBatchDTOList;
    }

    private List<ProductStoreBatchDTO> createToLocationProductStoreBatch(
        List<ProductStoreBatchChangeInfoResultDTO> toRecordList, PickUpDTO tmpPickUpDTO) {
        List<ProductStoreBatchDTO> productStoreBatchDTOList = new ArrayList<>();
        BigDecimal changeCount = tmpPickUpDTO.getCount();

        toRecordList.forEach(record -> {
            record.setTotalCountMinUnit(record.getTotalCountMinUnit().abs());
        });

        List<QuantityShareUtils.CountHelper> helperList = toRecordList.stream()
            .map(m -> new QuantityShareUtils.CountHelper(m.getStoreBatchChangeRecordId(), m.getTotalCountMinUnit()))
            .collect(Collectors.toList());
        QuantityShareUtils.CountShareResultHelper countShareResultHelper =
            QuantityShareUtils.shareCount(helperList, changeCount.abs());

        Map<String, ProductStoreBatchChangeInfoResultDTO> changeRecordMap = toRecordList.stream()
            .collect(Collectors.toMap(ProductStoreBatchChangeInfoResultDTO::getStoreBatchChangeRecordId, v -> v));

        countShareResultHelper.getShareHelperList().stream().filter(QuantityShareUtils.CountHelper::isHaveChanged)
            .forEach(share -> {
                ProductStoreBatchChangeInfoResultDTO resultDTO = changeRecordMap.get(share.getId());
                ProductStoreBatchDTO productStoreBatchDTO = new ProductStoreBatchDTO();
                productStoreBatchDTO.setId(resultDTO.getProductStoreBatchId());
                productStoreBatchDTO
                    .setTotalCount(resultDTO.getTotalCountMinUnit().subtract(share.getCount()).negate());
                productStoreBatchDTOList.add(productStoreBatchDTO);
            });

        return productStoreBatchDTOList;
    }

    private String getKey(ProductStoreBatchChangeInfoResultDTO resultDTO) {
        String productionDateStr = "";
        if (Objects.nonNull(resultDTO.getProductiondate())) {
            productionDateStr = DateUtils.getDateFormat(resultDTO.getProductiondate());
        }
        return getKey(resultDTO.getProductSkuId(), resultDTO.getLocationId(), resultDTO.getSource(), productionDateStr,
            resultDTO.getChannel());
    }

    private String getKey(Long skuId, Long locationId, Integer source, String productionDate, Integer channel) {
        return String.format("%s|%s|%s|%s|%s", skuId, locationId, source, productionDate, channel);
    }

    private String getSecKey(ProductStoreBatchChangeInfoResultDTO resultDTO) {
        String productionDateStr = "";
        if (Objects.nonNull(resultDTO.getProductiondate())) {
            productionDateStr = DateUtils.getDateFormat(resultDTO.getProductiondate());
        }
        String key = getKey(resultDTO.getProductSkuId(), resultDTO.getLocationId(), resultDTO.getSource(),
            productionDateStr, resultDTO.getChannel());

        return String.format("%s|%s", key, resultDTO.getSecOwnerId());
    }

    private String getSecKey(Long skuId, Long locationId, Integer source, String productionDate, Integer channel,
        Long secOwnerId) {
        String key = String.format("%s|%s|%s|%s|%s", skuId, locationId, source, productionDate, channel);

        return String.format("%s|%s", key, secOwnerId);
    }

    /**
     * 拣货完成移库
     *
     * @param pickUpDTOList
     * @param pickUpChangeRecordDTO
     * @param isIgnoreProductionDate
     * @param isIgnoreHasNotEnoughStore
     * @return
     */
    public List<PickUpDTO> batchTaskItemComplete(List<PickUpDTO> pickUpDTOList,
        PickUpChangeRecordDTO pickUpChangeRecordDTO, Boolean isIgnoreProductionDate,
        Boolean isIgnoreHasNotEnoughStore) {
        List<PickUpDTO> realPickUpDTOList = new ArrayList<>();
        // 校验移库
        PickUpCompleteTransferBO bo = checkInventoryTransferForPickUp(pickUpDTOList, isIgnoreProductionDate,
            isIgnoreHasNotEnoughStore, realPickUpDTOList);
        if (Objects.isNull(bo)) {
            return Collections.emptyList();
        }

        List<BatchInventoryTransferDTO> batchInventoryTransferDTOList = bo.getTransferDTOList();
        // 转换[to]_location货位为货位组
        batchInventoryManageBL.transformBatchInventoryTransferToLocGroup(batchInventoryTransferDTOList,
            pickUpDTOList.get(0).getWarehouseId());

        // 批次库存转移货位
        batchInventoryManageBL.batchInventoryTransfer(batchInventoryTransferDTOList, pickUpChangeRecordDTO);
        if (CollectionUtils.isNotEmpty(bo.getUpdateStoreBatchList())) {
            List<ProductStoreBatchPO> productStoreBatchPOList = bo.getUpdateStoreBatchList().stream().map(m -> {
                ProductStoreBatchPO updateProductStoreBatchPO = new ProductStoreBatchPO();
                updateProductStoreBatchPO.setId(m.getId());
                updateProductStoreBatchPO.setTotalCount(m.getTotalCount());
                return updateProductStoreBatchPO;
            }).collect(Collectors.toList());
            batchInventoryManageBL.updateBatchInventory(productStoreBatchPOList, pickUpChangeRecordDTO);
        }

        // 返回实际移库数量
        return realPickUpDTOList;
    }

}

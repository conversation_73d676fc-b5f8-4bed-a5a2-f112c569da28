<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductStorePOMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStorePO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="City_Id" property="cityId" jdbcType="INTEGER"/>
        <result column="Warehouse_Id" property="warehouseId" jdbcType="INTEGER"/>
        <result column="TotalCount_MinUnit" property="totalCountMinUnit" jdbcType="DECIMAL"/>
        <result column="ProductSpecification_Id" property="productSpecificationId" jdbcType="BIGINT"/>
        <result column="OwnerType" property="ownerType" jdbcType="INTEGER"/>
        <result column="Owner_Id" property="ownerId" jdbcType="BIGINT"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="Channel" property="channel" jdbcType="INTEGER"/>
        <result column="deliveryedCount" property="deliveryedCount" jdbcType="DECIMAL"/>
        <result column="specificationName" property="specificationName" jdbcType="INTEGER"/>
        <result column="packageName" property="packageName" jdbcType="INTEGER"/>
        <result column="unitName" property="unitName" jdbcType="INTEGER"/>
        <result column="packageQuantity" property="packageQuantity" jdbcType="DECIMAL"/>
        <result column="Source" property="source" jdbcType="INTEGER"/>
        <result column="SecOwner_Id" property="secOwnerId" jdbcType="BIGINT"/>
        <result column="UnifySkuId" property="unifySkuId" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Base_Column_List_Ext">
        ps.Id, ps.City_Id as City_Id, ps.Warehouse_Id
        as Warehouse_Id,
        ps.TotalCount_MinUnit as TotalCount_MinUnit,
        ps.ProductSpecification_Id as ProductSpecification_Id,
        ps.OwnerType, Owner_Id as Owner_Id,
        ps.CreateTime, ps.LastUpdateTime,ps.OwnerName,
        ps.SecOwner_Id
    </sql>
    <sql id="Base_Column_List">
        Id, City_Id, Warehouse_Id,
        TotalCount_MinUnit,
        ProductSpecification_Id,
        OwnerType, Owner_Id,
        CreateTime, LastUpdateTime,
        SecOwner_Id
    </sql>

    <select id="getProductStoreById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productstore
        where Id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="findProductWarehouseStoreList" resultMap="BaseResultMap">
        SELECT
        ps.Id,
        ps.City_Id,
        ps.Warehouse_Id,
        ps.TotalCount_MinUnit,
        ps.ProductSpecification_Id,
        ps.OwnerType,
        ps.Owner_Id,
        ps.CreateTime,
        ps.LastUpdateTime,
        ps.Channel,
        0 as deliveryedCount,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id
        FROM
        productsku psku
        INNER JOIN productstore ps ON ps.City_Id = psku.City_Id
        AND ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        <if test="warehouseStoreSO.warehouseId!=null">
            and ps.Warehouse_Id =#{warehouseStoreSO.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="shopId != null">
            and ps.Owner_Id = #{shopId,jdbcType=BIGINT}
        </if>
        <if test="warehouseStoreSO.saleMode!=null">
            and psku.SaleModel =#{warehouseStoreSO.saleMode,jdbcType=INTEGER}
        </if>
        <where>
            <if test="warehouseStoreSO.source!=null">
                and psku.Source =#{warehouseStoreSO.source,jdbcType=INTEGER}
            </if>
            <if test="warehouseStoreSO.secOwnerId!=null">
                and ps.SecOwner_Id =#{warehouseStoreSO.secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="warehouseStoreSO.productName!=null and warehouseStoreSO.productName!=''">
                AND psku.Name like concat('%',#{warehouseStoreSO.productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="warehouseStoreSO.ownerType!=null">
                and ps.OwnerType=#{warehouseStoreSO.ownerType,jdbcType=INTEGER}
            </if>
            <if test="warehouseStoreSO.cityId!=null">
                and ps.City_Id=#{warehouseStoreSO.cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseStoreSO.saleMode!=null">
                <if test="warehouseStoreSO.saleMode==2">
                    and ps.OwnerType=1
                </if>
                <if test="warehouseStoreSO.saleMode!=2 and warehouseStoreSO.saleMode!=6">
                    and ps.OwnerType=0
                </if>
                <if test="warehouseStoreSO.saleMode==6">
                    and ps.OwnerType=2
                </if>
            </if>
        </where>
    </select>

    <select id="getProductStoreForSupplierOp"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.StoreDTOBySupplierOp">
        SELECT
        IFNULL(SUM(TotalCount_MinUnit), 0) AS warehouseStoreCount,
        ps.ProductSpecification_Id AS productSpecificationId,
        ps.Channel as channel,
        sku.Name as productName,
        sku.specificationName, <!--包装规格名称-->
        sku.packageName,<!--大单位名称-->
        sku.unitName,<!--小单位名称-->
        sku.packageQuantity,<!--转换系数-->
        sku.Source,<!--产品来源，0:酒批，1:微酒（贷款/抵押）-->
        ps.SecOwner_Id as secOwnerId <!--二级货主-->
        FROM
        productstore ps
        INNER JOIN productsku sku on sku.ProductSpecification_Id=ps.ProductSpecification_Id and sku.City_Id=ps.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        WHERE
        ps.ProductSpecification_Id =#{so.productInfoSpecId,jdbcType=BIGINT}
        <if test="so.saleModes != null">
            AND sku.salemodel in
            <foreach item="item" collection="so.saleModes" separator=","
                     open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.source!=null">
            and sku.Source= #{so.source,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            and ps.SecOwner_Id= #{so.secOwnerId,jdbcType=BIGINT}
        </if>
        <if test="so.channel!=null">
            and ps.Channel= #{so.channel,jdbcType=INTEGER}
        </if>
        <!--and ps.City_Id BETWEEN 100 and 900 and ps.City_Id !=427-->
        GROUP BY
        ps.ProductSpecification_Id, sku.specificationName,sku.packageName,sku.unitName
        ,sku.packageQuantity,sku.Source,sku.Name,ps.SecOwner_Id,
        ps.Channel
    </select>


    <sql id="findStoreReportPageByAuthPage">
        SELECT
        ps.Id as productStoreId,
        psku.ProductSku_Id as productSkuId,
        psku.ProductSku_Id as productId,
        psku.Name as productSkuName,
        ps.Warehouse_Id as warhouseId,
        ps.OwnerType as storeOwnerType,
        ps.Owner_Id as ownerId,
        ps.TotalCount_MinUnit as storeTotalCount
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND psku.City_Id = ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        AND ps.City_Id=psku.City_Id
        where ps.Channel=#{channel,jdbcType=INTEGER}
        <if test="so.productSkuName!=null and so.productSkuName!=''">
            AND psku.Name like concat('%',#{so.productSkuName,jdbcType=VARCHAR},'%')
        </if>
        <if test="so.warehouseIds!=null">
            and ps.Warehouse_Id in
            <foreach item="item" collection="so.warehouseIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="so.supplierId!=null">
            and ps.Owner_Id=#{so.supplierId,jdbcType=BIGINT}
        </if>
        <if test="so.storeOwnerType!=null">
            and ps.OwnerType=#{so.storeOwnerType,jdbcType=INTEGER}
        </if>
    </sql>

    <select id="findProductSpecCityListStore"
            resultType="com.yijiupi.himalaya.supplychain.dto.standard.ProductSpecCityStoreDTO">
        SELECT
        ps.City_Id as cityId,
        ps.ProductSpecification_Id as productSpecId,
        ps.Warehouse_Id AS warehouseId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.Channel as channel,
        0 as deliveryedCount,
        ps.SecOwner_Id as secOwnerId
        FROM
        productstore ps
        where
        ps.ProductSpecification_Id = #{so.productSpecId,jdbcType=BIGINT}
        <if test="so.channel!=null">
            AND ps.Channel=#{so.channel,jdbcType=INTEGER}
        </if>
        <if test="so.secOwnerId!=null">
            AND ps.SecOwner_Id=#{so.secOwnerId,jdbcType=BIGINT}
        </if>
        and ps.City_Id in
        <foreach collection="so.cityIds" item="item" separator="," close=")" open="(">
            ${item}
        </foreach>
    </select>

    <select id="findProductWarehouse"
            resultType="com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO">
        SELECT
        ps.City_Id as cityId,
        psku.ProductSku_Id as productSkuId,
        ps.Warehouse_Id as warehouseId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.channel as channel,
        0 as deliveryedCount
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where
        psku.ProductSku_Id = #{productSkuId}
        and psku.Source = #{source}
        and ps.Warehouse_Id =#{warehouseId}
        and ps.Channel =#{channel}
        <if test="secOwnerId==null">
            and ps.SecOwner_Id is null
        </if>
        <if test="secOwnerId!=null">
            and ps.SecOwner_Id = #{secOwnerId}
        </if>
    </select>

    <select id="findProductWarehouseBySku"
            resultType="com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO">
        SELECT
        ps.City_Id as cityId,
        psku.ProductSku_Id as productSkuId,
        ps.Warehouse_Id as warehouseId,
        ps.TotalCount_MinUnit as storeTotalCount,
        ps.channel as channel,
        0 as deliveryedCount
        FROM
        productstore ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where
        psku.ProductSku_Id in
        <foreach collection="productSkuIds" item="item" separator="," close=")" open="(">
            ${item}
        </foreach>
        and psku.Source = #{source}
        and ps.Warehouse_Id =#{warehouseId}
        and ps.Channel =#{channel}
        <if test="secOwnerId==null">
            and ps.SecOwner_Id is null
        </if>
        <if test="secOwnerId!=null">
            and ps.SecOwner_Id = #{secOwnerId}
        </if>
    </select>
    <select id="findProductWarehouseStoreListForAllocation"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.StoreDTOBySupplierOp">
        SELECT
        ps.TotalCount_MinUnit as warehouseStoreCount,
        ps.Warehouse_Id as warehouseId,
        ps.City_Id as cityId,
        ps.Channel as channel,
        psku.name as productName,
        psku.ProductSpecification_Id as productSpecificationId,
        pSku.ProductSku_Id as productSkuId,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id AS secOwnerId
        from productstore ps
        INNER JOIN productsku psku on psku.ProductSpecification_Id=ps.ProductSpecification_Id and
        psku.City_Id=ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        <where>
            <if test="so.productInfoSpecId != null">
                AND ps.ProductSpecification_Id=#{so.productInfoSpecId,jdbcType=BIGINT}
            </if>
            <if test="so.saleModes != null">
                AND psku.salemodel in
                <foreach item="item" collection="so.saleModes" separator=","
                         open="(" close=")" index="">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="so.cityId!=null">
                and ps.City_Id=#{so.cityId,jdbcType=INTEGER}
            </if>
            <if test="so.source!=null">
                and psku.Source= #{so.source,jdbcType=INTEGER}
            </if>
            <if test="so.secOwnerId!=null">
                and ps.SecOwner_Id= #{so.secOwnerId,jdbcType=BIGINT}
            </if>
            <if test="so.channel!=null">
                and ps.Channel= #{so.channel,jdbcType=INTEGER}
            </if>
        </where>
    </select>


    <select id="findProductStore" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductShopStoreReturnDTO">
        SELECT
        ps.Id as id,
        ps.TotalCount_MinUnit as totalCountMinUnit
        from productstore ps
        where ps.Warehouse_Id= #{warehouseId,jdbcType=INTEGER}
        and ps.Owner_Id= #{dealerId,jdbcType=BIGINT}
    </select>


    <select id="findWarehouseStore" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreReturnDTO">
        SELECT
        ps.Id as id,
        ps.Owner_Id as ownerId,
        ps.TotalCount_MinUnit as totalCountMinUnit
        from productstore ps
        where ps.Warehouse_Id= #{warehouseId,jdbcType=INTEGER}

    </select>


    <select id="findProductStoreList" resultType="com.yijiupi.himalaya.supplychain.dto.product.ProductStockStoreDTO">
        SELECT
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.TotalCount_MinUnit as totalCountMinUnit,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.OwnerType as ownerType,
        ps.Owner_Id as ownerId
        FROM productstore ps
        <where>
            <if test="dealerId != null">
                and ps.Owner_Id = #{dealerId,jdbcType=BIGINT}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="findDynamicProductList" resultType="com.yijiupi.himalaya.supplychain.dto.product.DynamicProductDTO">
        select
        ps.City_Id as cityId,
        ps.Warehouse_Id as warehouseId,
        ps.ProductSpecification_Id as productSpecificationId,
        ps.Owner_Id as ownerId,
        ps.SecOwner_Id as secOwnerId,
        ps.location_id as locationId,
        ps.location_name as locationName,
        ps.totalcount as unitTotalCount,
        psku.ProductSku_Id as productSkuId,
        psku.Name as productName,
        psku.specificationName as specificationName,
        psku.unitName as unitName,
        psku.packageName as packageName,
        psku.packageQuantity as packageQuantity,
        psku.productBrand as productBrand,
        psku.OwnerName as ownerName,
        own.OwnerName as secOwnerName,
        pct.StatisticsClass as statisticsClass,
        pct.StatisticsClassName as statisticsClassName
        from (
        SELECT
        ps.City_Id,
        ps.Warehouse_Id,
        ps.ProductSpecification_Id,
        ps.Owner_Id,
        ps.SecOwner_Id,
        pb.location_id,
        max(pb.location_name) as location_name,
        sum(pb.totalcount_minunit) as totalcount
        FROM
        productstorebatch pb
        INNER JOIN productstore ps on ps.id = pb.productstore_id
        WHERE
        ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        <if test="cityId != null">
            and ps.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="dynamicDailyPicking == null or dynamicDailyPicking == false">
            and ps.lastupdatetime <![CDATA[ >= ]]> #{dynamicStartTime, jdbcType=VARCHAR}
            and ps.lastupdatetime <![CDATA[ <= ]]> #{dynamicEndTime, jdbcType=VARCHAR}
        </if>
        <if test="dynamicDailyPicking != null and dynamicDailyPicking == true">
            and pb.lastupdatetime <![CDATA[ >= ]]> #{dynamicStartTime, jdbcType=VARCHAR}
            and pb.lastupdatetime <![CDATA[ <= ]]> #{dynamicEndTime, jdbcType=VARCHAR}
        </if>
        <if test="locationName != null">
            and pb.location_name like concat(#{locationName,jdbcType=VARCHAR},'%')
        </if>
        <if test="subCategorys != null">
            and pb.subcategory in
            <foreach collection="subCategorys" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY ps.City_Id, ps.Warehouse_Id, ps.ProductSpecification_Id, ps.Owner_Id , ps.SecOwner_Id, pb.location_id
        order by ps.City_Id, ps.ProductSpecification_Id, ps.Owner_Id, pb.location_id desc
        ) ps
        INNER JOIN productsku psku ON ps.ProductSpecification_Id = psku.ProductSpecification_Id
        AND ps.City_Id = psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productinfocategory pct on pct.id = psku.ProductInfoCategory_Id
        LEFT JOIN owner own on ps.SecOwner_Id is not null and own.id = ps.SecOwner_Id
        <where>
            <if test="statisticsClass != null">
                pct.StatisticsClass = #{statisticsClass,jdbcType=BIGINT}
            </if>
            <if test="productName != null">
                AND psku.name like concat('%',#{productName,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>

    <select id="findProductStoreBySpec" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreBaseDTO">
        select
        Id, City_Id as cityId, Warehouse_Id as warehouseId,
        TotalCount_MinUnit as totalCountMinUnit,
        ProductSpecification_Id as productSpecificationId,
        OwnerType as ownerType, Owner_Id as ownerId,
        CreateTime, LastUpdateTime,
        SecOwner_Id as secOwnerId, Channel as channel
        from productstore
        where 1=1
        <if test="query.warehouseId != null ">
            and Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="query.cityId != null ">
            and City_Id = #{query.cityId,jdbcType=INTEGER}
        </if>
        <if test="query.specAndOwnerIds != null and query.specAndOwnerIds.size() > 0 ">
            and
            <foreach collection="query.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                (
                ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">AND Owner_Id is null</if>
                <if test="item.ownerId != null">AND Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                <if test="item.secOwnerId == null">AND SecOwner_Id is null</if>
                <if test="item.secOwnerId != null">AND SecOwner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                )
            </foreach>
        </if>
    </select>

    <select id="getProductStoreByOwnerIdAndWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productstore
        where Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and Owner_Id = #{ownerId,jdbcType=BIGINT}
        and TotalCount_MinUnit > 0
        limit 1
    </select>

    <select id="findNotOpenStockDynamicProductList"
            resultType="com.yijiupi.himalaya.supplychain.dto.product.NotOpenStockDailyPickingProductDTO">
        SELECT
        ps.TotalCount_MinUnit as totalCountMinUnit,
        ps.Warehouse_Id as warehouseId,
        ps.City_Id as cityId,
        ps.Channel as channel,
        psku.name as productName,
        psku.ProductSpecification_Id as productSpecificationId,
        pSku.ProductSku_Id as productSkuId,
        psku.specificationName,
        psku.packageName,
        psku.unitName,
        psku.packageQuantity,
        psku.Source,
        ps.SecOwner_Id AS secOwnerId
        from productstore ps
        INNER JOIN productsku psku on psku.ProductSpecification_Id=ps.ProductSpecification_Id and
        psku.City_Id=ps.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        where ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        and ps.LastModifyTime <![CDATA[ >= ]]> #{dynamicStartTime, jdbcType=TIMESTAMP}
        and ps.LastModifyTime <![CDATA[ <= ]]> #{dynamicEndTime, jdbcType=TIMESTAMP}
        <if test="skuIdList != null and skuIdList.size() > 0">
            and pSku.ProductSku_Id in
            <foreach collection="skuIdList" item="skuId" open="(" close=")" separator=",">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="cityId != null">
            and ps.City_Id = #{cityId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getDisposedProductInventories"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO">
        select ps.warehouse_id as warehouseId, ps.Owner_Id as ownerId,
        ps.ProductSpecification_Id as productSpecId, ps.OwnerType as ownerType, psbr.totalcount_minunit as
        warehouseTotalCount,
        ps.Id as id, ps.SecOwner_Id as secOwnerId,psbr.id as psbId,ps.city_id as cityId
        from productstore ps
        <if test="query.productSkuIds != null and query.productSkuIds.size() != 0">
            INNER JOIN productsku sku ON ps.ProductSpecification_Id=sku.ProductSpecification_Id
            AND ps.City_Id=sku.City_Id
            AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
            AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        </if>
        INNER JOIN productstorebatch psbr on psbr.productstore_id =ps.id
        INNER JOIN Location loc on loc.id= psbr.location_id and loc.warehouse_id = ps.warehouse_id
        where
        psbr.totalcount_minunit != 0
        <if test="query.cityId != null">
            and ps.City_Id = #{query.cityId,jdbcType=INTEGER}
        </if>
        <if test="query.warehouseId != null">
            and ps.warehouse_id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="subCategoryList != null and subCategoryList.size() != 0">
            and loc.subcategory in
            <foreach collection="subCategoryList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="query.OwnType != null">
            and ps.OwnerType = #{query.OwnType,jdbcType=INTEGER}
        </if>
        <if test="query.productSkuIds != null and query.productSkuIds.size() != 0">
            and sku.ProductSku_Id in
            <foreach collection="query.productSkuIds" item="skuId" open="(" separator="," close=")">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.specAndOwnerIds != null and query.specAndOwnerIds.size() > 0">
            AND
            <foreach collection="query.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=INTEGER}
                <if test="item.ownerId == null">AND ps.Owner_Id is null</if>
                <if test="item.ownerId != null">AND ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                )
            </foreach>
        </if>
    </select>

    <select id="findDisposedProductInventorDetails"
            resultType="com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.DisposedProductInventorDTO">
        select ps.City_Id as cityId, ps.warehouse_id as warehouseId, sku.ProductSku_Id as productSkuId,
        psbr.totalcount_minunit as disposeProductCount, psbr.productiondate as productionDate, psbr.batchtime as
        batchTime,
        loc.id as locationId, loc.Name as locationName
        from productstore ps
        INNER JOIN productsku sku ON ps.ProductSpecification_Id=sku.ProductSpecification_Id
        AND ps.City_Id=sku.City_Id
        AND ((sku.Company_Id is null and ps.Owner_Id is null) or (sku.Company_Id = ps.Owner_Id))
        AND ((sku.secOwner_Id is null) or (sku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psbr on psbr.productstore_id =ps.id
        INNER JOIN Location loc on loc.id= psbr.location_id and loc.warehouse_id = ps.warehouse_id
        where
        psbr.totalcount_minunit != 0
        <if test="query.cityId != null">
            and ps.City_Id = #{query.cityId,jdbcType=INTEGER}
        </if>
        <if test="subCategoryList != null and subCategoryList.size() != 0">
            and loc.subcategory in
            <foreach collection="subCategoryList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="query.warehouseId != null">
            and ps.warehouse_id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="query.OwnType != null">
            and ps.OwnerType = #{query.OwnType,jdbcType=INTEGER}
        </if>
        <if test="query.source != null">
            and sku.Source = #{query.source,jdbcType=INTEGER}
        </if>
        <if test="query.productSkuIds != null and query.productSkuIds.size() != 0">
            and sku.ProductSku_Id in
            <foreach collection="query.productSkuIds" item="skuId" open="(" separator="," close=")">
                #{skuId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findProductStoreByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from productstore
        where Id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 根据中台SKU+仓库查询库存 -->
    <select id="findProductStoreByUnifySkuId"
            resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreBaseDTO">
        select
        Id, City_Id as cityId, Warehouse_Id as warehouseId,
        TotalCount_MinUnit as totalCountMinUnit,
        ProductSpecification_Id as productSpecificationId,
        OwnerType as ownerType, Owner_Id as ownerId,
        CreateTime, LastUpdateTime,
        SecOwner_Id as secOwnerId, Channel as channel
        from productstore
        where Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        <if test="query.unifySkuIds != null and query.unifySkuIds.size() != 0">
            and UnifySkuId in
            <foreach collection="query.unifySkuIds" item="unifySkuId" open="(" separator="," close=")">
                #{unifySkuId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findStoreBySpecOwner" resultType="com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreBaseDTO">
        select
        Id, City_Id as cityId, Warehouse_Id as warehouseId,
        TotalCount_MinUnit as totalCountMinUnit,
        ProductSpecification_Id as productSpecificationId,
        OwnerType as ownerType, Owner_Id as ownerId,
        CreateTime, LastUpdateTime,
        SecOwner_Id as secOwnerId, Channel as channel
        from productstore
        where TotalCount_MinUnit > 0
        <if test="query.warehouseId != null ">
            and Warehouse_Id = #{query.warehouseId,jdbcType=INTEGER}
        </if>
        <if test="query.cityId != null ">
            and City_Id = #{query.cityId,jdbcType=INTEGER}
        </if>
        <if test="query.specAndOwnerIds != null and query.specAndOwnerIds.size() > 0 ">
            and
            <foreach collection="query.specAndOwnerIds" item="item" open="(" close=")" separator="or">
                (
                ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">AND Owner_Id is null</if>
                <if test="item.ownerId != null">AND Owner_Id = #{item.ownerId,jdbcType=BIGINT}</if>
                )
            </foreach>
        </if>
    </select>
</mapper>
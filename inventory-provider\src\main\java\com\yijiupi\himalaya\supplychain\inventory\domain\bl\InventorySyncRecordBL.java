package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.enums.CheckStateEnum;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.InventoryConvertor;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventorySyncRecordMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.InventorySyncRecordPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordSO;

/**
 * 库存对账结果记录
 *
 * <AUTHOR>
 * @date 2019/1/10 12:00
 */
@Service
public class InventorySyncRecordBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventorySyncRecordBL.class);

    @Autowired
    private InventorySyncRecordMapper inventorySyncRecordMapper;

    /**
     * 批量新增库存对账记录
     *
     * @param list
     */
    public void saveInventorySyncRecordList(List<InventorySyncRecordDTO> inventorySyncRecordDTOS) {
        if (CollectionUtils.isEmpty(inventorySyncRecordDTOS)) {
            LOG.info("批量新增库存对账记录不能为空");
            return;
        }
        long startTime = System.currentTimeMillis();
        List<InventorySyncRecordPO> inventorySyncRecordPOS = inventorySyncRecordDTOS.stream().map(dto -> {
            InventorySyncRecordPO po = new InventorySyncRecordPO();
            BeanUtils.copyProperties(dto, po);
            return po;
        }).collect(Collectors.toList());
        inventorySyncRecordMapper.insertList(inventorySyncRecordPOS);
        LOG.info("新增库存对账记录：{}条，耗时{}ms", inventorySyncRecordPOS.size(), (System.currentTimeMillis() - startTime));
    }

    /**
     * 查看库存对账记录
     *
     * @return
     */
    public PageList<InventorySyncRecordDTO> listInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO) {
        LOG.info("查看库存对账记录参数：{}", JSON.toJSONString(inventorySyncRecordSO));
        PageResult<InventorySyncRecordPO> pageResult =
            inventorySyncRecordMapper.listInventorySyncRecord(inventorySyncRecordSO);
        List<InventorySyncRecordPO> poList = pageResult.toPageList().getDataList();
        List<InventorySyncRecordDTO> dtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(poList)) {
            dtoList = poList.stream().map(po -> {
                InventorySyncRecordDTO dto = new InventorySyncRecordDTO();
                BeanUtils.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());
        }
        PageList<InventorySyncRecordDTO> pageList = new PageList<>();
        pageList.setDataList(dtoList);
        pageList.setPager(pageResult.toPageList().getPager());
        return pageList;
    }

    /**
     * 库存对账记录批量标记为已处理
     */
    public void batchMarkInventorySyncRecord(InventorySyncRecordSO inventorySyncRecordSO) {
        LOG.info("库存对账记录批量标记为已处理参数：{}", JSON.toJSONString(inventorySyncRecordSO));
        inventorySyncRecordSO.setState(CheckStateEnum.未处理.getType());
        inventorySyncRecordSO.setPageNum(null);
        inventorySyncRecordSO.setPageSize(null);
        PageResult<InventorySyncRecordPO> pageResult =
            inventorySyncRecordMapper.listInventorySyncRecord(inventorySyncRecordSO);
        List<InventorySyncRecordPO> poList = pageResult.toPageList().getDataList();
        if (CollectionUtils.isEmpty(poList)) {
            LOG.info("批量标记找不到数据");
            return;
        }
        // 200一组批量更新
        List<Long> idList = poList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<List<Long>> lists = InventoryConvertor.splitListNew(idList, 200);
        for (List<Long> list : lists) {
            inventorySyncRecordMapper.batchUpdateRecordState(list, CheckStateEnum.已处理.getType());
            LOG.info("批量标记为已处理：{}", JSON.toJSONString(list));
        }
    }

    /**
     * 库存对账记录标记为已处理
     */
    public void markInventorySyncRecord(InventorySyncRecordDTO dto) {
        AssertUtils.notNull(dto, "标记的参数不能为空");
        AssertUtils.notNull(dto.getId(), "校正记录Id不能为空");
        LOG.info("库存对账记录标记为已处理参数：{}", JSON.toJSONString(dto));
        InventorySyncRecordPO po = new InventorySyncRecordPO();
        BeanUtils.copyProperties(dto, po);
        po.setState(CheckStateEnum.已处理.getType());
        inventorySyncRecordMapper.updateInventorySyncRecord(po);
    }

    /**
     * 删除当天已经同步过的记录
     * 
     * @param cityId
     */
    public void deleteTodaySyncRecordByCityId(Integer cityId) {
        inventorySyncRecordMapper.deleteTodaySyncRecordByCityId(cityId);
    }

}

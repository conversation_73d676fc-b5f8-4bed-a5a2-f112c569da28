package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.StringUtil;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.enums.OrderDeliveryOpType;
import com.yijiupi.himalaya.supplychain.framework.businessaudit.BusinessAuditEntry;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.SellInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.message.IdempotenceConsumer;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IContentConfigurationService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderStateEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.OutStockOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderUpdateDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IInStockOrderService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommManageService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommQueryService;
import com.yijiupi.himalaya.supplychain.waves.batch.IBatchManageService;
import com.yijiupi.himalaya.supplychain.waves.dto.batch.BatchUpdateDTO;
import com.yijiupi.himalaya.supplychain.waves.enums.BatchStateEnum;
import com.yijiupi.himalaya.supplychain.waves.enums.OrderPickTypeConstant;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;

/**
 * 确认出库相关
 *
 * <AUTHOR>
 */
@Service
public class BatchOutBoundChangeInventoryBL {
    private static final Logger LOG = LoggerFactory.getLogger(InventoryOrderBizBL.class);

    @Autowired
    private IdempotenceConsumer idempotenceConsumer;

    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;

    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;

    @Reference
    private IContentConfigurationService iContentConfigurationService;

    @Reference
    private IOutStockCommQueryService outStockCommQueryService;

    @Reference
    private IOutStockCommManageService outStockCommManageService;

    @Reference(timeout = 60000)
    private IInStockOrderService iInStockOrderService;

    @Reference
    private IBatchManageService iBatchManageService;

    /**
     * 根据key检查内配配置
     *
     * @param toWarehouseId
     * @return
     */
    private Boolean checkContentConfig(Integer toWarehouseId, String key) {
        Boolean flag = false;
        if (toWarehouseId == null) {
            return flag;
        }
        String contentValue = iContentConfigurationService.getContentValue(key, null, "");
        String[] warehouseIds = contentValue.split("、");

        for (String id : warehouseIds) {
            if (id.equals(toWarehouseId.toString())) {
                flag = true;
                break;
            }
        }

        return flag;
    }

    /**
     * 按批次确认出库: 扣除仓库库存 todo
     */
    public List<InventoryDeliveryJiupiOrder> affirmOutStock(List<InventoryDeliveryJiupiOrder> deliveryOrders,
        boolean checkWarehouseInventory, boolean checkOrder) {
        LOG.info("确认出库入参:{}", JSON.toJSONString(deliveryOrders));
        Integer warehouseId = deliveryOrders.get(0).getWarehouseId();
        Integer cityId = deliveryOrders.get(0).getCityId();
        List<String> noProcessOrderIds = idempotenceConsumer.getNoProcessOrderNos(warehouseId, deliveryOrders,
            OrderDeliveryOpType.ORDER_DELIVER_MESSAGE_TYPE, "订单配送出库");

        if (CollectionUtils.isEmpty(noProcessOrderIds)) {
            LOG.info(String.format("订单配送出库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            return null;
        }

        if (checkWarehouseInventory) {
            // 检验是否店仓或者配置可以负库存发货仓库
            Boolean allowNegative = checkContentConfig(warehouseId, "AllowNegativeDelivery");
            if (allowNegative) {
                checkWarehouseInventory = false;
            }
        }

        List<SellInventoryChangeBO> sellChangeList = new ArrayList<>();
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        boolean finalCheckWarehouseInventory = checkWarehouseInventory;
        idempotenceConsumer.apply(noProcessOrderIds, () -> {

            // 普通发货allotType转为普通，避免影响后续单据校验
            deliveryOrders.forEach(order -> order.setAllotType(OrderConstant.ALLOT_TYPE_DEFAULT));
            if (checkOrder) {
                checkOrder(deliveryOrders);
            }

            // 遍历添加批次所有BO到list中
            for (InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder : deliveryOrders) {

                // //招商订单需要将下单SKU转为发货城市的SKU
                // productSkuZhaoShangBL.processOrderItemProductSkuId(inventoryDeliveryJiupiOrder);
                // 跨库订单不处理
                if (inventoryDeliveryJiupiOrder.getCrossWareHouse() == null
                    || !inventoryDeliveryJiupiOrder.getCrossWareHouse()) {
                    warehouseChangListBOConverter.processOrderItemToOrderDeliveryBO(sellChangeList, warehouseChangeList,
                        inventoryDeliveryJiupiOrder, finalCheckWarehouseInventory);
                }
            }

            if (CollectionUtils.isEmpty(warehouseChangeList)) {
                LOG.info(String.format("订单配送出库-订单已经全部处理过！%s", JSON.toJSONString(deliveryOrders)));
            }

            // 订单直发不需要发货数量记录
            Boolean isUpdateDeliveryCount = true;
            if (deliveryOrders.get(0).getPackageAttribute() != null
                && deliveryOrders.get(0).getPackageAttribute().equals(OrderPickTypeConstant.PICKTYPE_LOGIC)) {
                isUpdateDeliveryCount = false;
            }
            warehouseInventoryManageBL.validateAndProcessProductStore(warehouseChangeList, finalCheckWarehouseInventory,
                isUpdateDeliveryCount, true, true, false);

        });
        // 添加审计日志
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            for (InventoryDeliveryJiupiOrder deliveryOrder : deliveryOrders) {
                BusinessAuditEntry.Builder builder = BusinessAuditEntry.create("WMS", "outstockorder");
                builder.businessId(String.valueOf(deliveryOrder.getId())).addFeature("OrderNo",
                    deliveryOrder.getOrderNo());
                if (deliveryOrder.getOrderType() != null) {
                    builder.addFeature("OrderType", String.valueOf(deliveryOrder.getOrderType()));
                    OutStockOrderTypeEnum outStockOrderTypeEnum =
                        OutStockOrderTypeEnum.getEnum(deliveryOrder.getOrderType().byteValue());
                    if (outStockOrderTypeEnum != null) {
                        builder.content(String.format("%s-订单按批次发货", outStockOrderTypeEnum.name()));
                    } else {
                        builder.content("订单按批次发货");
                    }
                } else {
                    builder.content("订单按批次发货");
                }
                builder.done();
            }
        }

        // 更新订单状态为已出库
        updateOutStockOrderState(deliveryOrders, OutStockOrderStateEnum.已出库.getType());

        // // 删除掉入库单.
        // List<String> deliveryOrderNOs =
        // deliveryOrders.stream().filter(e -> e != null && StringUtils.isNotEmpty(e.getOrderNo()))
        // .map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
        // if (CollectionUtils.isNotEmpty(deliveryOrderNOs)) {
        // iInStockOrderService.deleteInStockOrderByNOList(cityId, warehouseId, deliveryOrderNOs);
        //
        // // 删除已经执行过的入库消息Id
        // idempotenceConsumer.deleteKeyByMessageType(warehouseId, deliveryOrderNOs,
        // OrderDeliveryOpType.IN_STOCK_ORDER_TYPE);
        // }

        // 移除生成内配单的订单，内配退入库时再加销售库存
        // setNotReturnSaleInventoryBySellBO(sellChangeList);

        // 处理生成内配单的订单，不需要返销售库存
        setNotReturnSaleInventoryByChangeBO(warehouseChangeList, null);

        // 处理销售库存发消息(由OMS处理)
        // warehouseInventoryManageBL.processSellInventory(warehouseChangeList, sellChangeList);

        // 更新波次状态为已出库
        List<String> orderNos = warehouseChangeList.stream().filter(p -> StringUtils.isNotEmpty(p.getOrderNo()))
            .map(WarehouseInventoryChangeBO::getOrderNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderNos)) {
            BatchUpdateDTO batchUpdateDTO = new BatchUpdateDTO();
            batchUpdateDTO.setOrgId(warehouseChangeList.get(0).getCityId());
            batchUpdateDTO.setWarehouseId(warehouseChangeList.get(0).getWarehouseId());
            batchUpdateDTO.setOrderNos(orderNos);
            batchUpdateDTO.setBatchState(BatchStateEnum.ALREADYOUTOFSTORE.getType());
            batchUpdateDTO.setOperateUser(warehouseChangeList.get(0).getCreateUserName());
            iBatchManageService.updateBatchStateByOrderNos(batchUpdateDTO);
        }

        // 返回实际库存处理结果
        return getInventoryProcessResults(deliveryOrders, warehouseChangeList);
    }

    /**
     * 订单数据与WMS订单数据比对
     *
     * @param deliveryOrders
     */
    private void checkOrder(List<InventoryDeliveryJiupiOrder> deliveryOrders) {
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return;
        }

        // 按照规则过滤不会下推的订单
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 -- 代码删除
        List<InventoryDeliveryJiupiOrder> orders = deliveryOrders;
        // .stream().filter(order ->
        // orderPushRoolService.canWmsPush(order.getOmsOrderId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<Byte> allotTypes = new ArrayList<>();
        allotTypes.add(OrderConstant.ALLOT_TYPE_ALLOCATION);
        allotTypes.add(OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN);
        allotTypes.add(OrderConstant.ALLOT_TYPE_DELIVERY);
        allotTypes.add(OrderConstant.ALLOT_TYPE_DELIVERY_RETURN);

        List<OutStockOrderDTO> outStockOrderDTOS = new ArrayList<>();

        orders.stream().filter(order -> allotTypes.contains(order.getAllotType()))
            .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getFromWarehouseId))
            .forEach((fromWarehouseId, orderList) -> {
                List<String> orderNos =
                    orderList.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderQueryDTO outStockOrderQueryDTO = new OutStockOrderQueryDTO();
                outStockOrderQueryDTO.setRefOrderNos(orderNos);
                outStockOrderQueryDTO.setWarehouseId(fromWarehouseId);
                outStockOrderQueryDTO.setOrgId(orderList.get(0).getFromCityId());
                List<OutStockOrderDTO> dataList =
                    outStockCommQueryService.pageListOutStockOrder(outStockOrderQueryDTO).getDataList();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    outStockOrderDTOS.addAll(dataList);
                }
            });

        orders.stream().filter(order -> !allotTypes.contains(order.getAllotType()))
            .collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orderList) -> {
                List<String> orderNos =
                    orderList.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderQueryDTO outStockOrderQueryDTO = new OutStockOrderQueryDTO();
                outStockOrderQueryDTO.setRefOrderNos(orderNos);
                outStockOrderQueryDTO.setWarehouseId(warehouseId);
                outStockOrderQueryDTO.setOrgId(orderList.get(0).getCityId());
                List<OutStockOrderDTO> dataList =
                    outStockCommQueryService.pageListOutStockOrder(outStockOrderQueryDTO).getDataList();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    outStockOrderDTOS.addAll(dataList);
                }
            });

        List<String> errOrderNos = new ArrayList<>();
        List<Long> noOmsOrderItemIds = new ArrayList<>();
        List<Long> noOrderItemIds = new ArrayList<>();
        List<Long> errOrderItemIds = new ArrayList<>();

        if (CollectionUtils.isEmpty(outStockOrderDTOS)) {
            String errNos =
                orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.joining(","));
            throw new BusinessException("发货数据异常！OrderNo:" + errNos);
        }

        Map<String, List<OutStockOrderDTO>> outStockOrderMap =
            outStockOrderDTOS.stream().collect(Collectors.groupingBy(OutStockOrderDTO::getRefOrderNo));

        for (InventoryDeliveryJiupiOrder order : orders) {
            String orderNo = order.getOrderNo();
            List<OutStockOrderDTO> outStockOrders = outStockOrderMap.get(orderNo);

            if (CollectionUtils.isEmpty(outStockOrders)) {
                errOrderNos.add(orderNo);
                continue;
            }
            order.setOrderId(Long.valueOf(outStockOrders.get(0).getId()));
            order.setCrossWareHouse(outStockOrders.get(0).getCrossWareHouse());

            List<InventoryDeliveryJiupiOrderItem> orderItems = order.getItems().stream()
                .filter(item -> item.getDeliverCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Map<Long, List<InventoryDeliveryJiupiOrderItem>> orderItemMap =
                orderItems.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrderItem::getOmsOrderItemId));
            List<OutStockOrderItemDTO> outStockOrderItems = outStockOrders.stream()
                .flatMap(outStockOrderDTO -> outStockOrderDTO.getOutStockOrderItemDTOS().stream())
                .filter(item -> item.getUnitTotalCount().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            Map<String, OutStockOrderItemDTO> outStockOrderBusinessItemMap =
                outStockOrderItems.stream().filter(item -> item.getBusinessItemId() != null).collect(Collectors
                    .toMap(OutStockOrderItemDTO::getBusinessItemId, Function.identity(), (key1, key2) -> key2));
            Map<String, OutStockOrderItemDTO> outStockOrderItemMap =
                outStockOrderItems.stream().filter(item -> item.getBusinessItemId() != null)
                    .collect(Collectors.toMap(OutStockOrderItemDTO::getId, Function.identity(), (key1, key2) -> key2));

            for (Map.Entry<Long, List<InventoryDeliveryJiupiOrderItem>> entry : orderItemMap.entrySet()) {
                BigDecimal deliverCount = entry.getValue().stream()
                    .map(InventoryDeliveryJiupiOrderItem::getDeliverCount).reduce(BigDecimal.ZERO, BigDecimal::add);
                OutStockOrderItemDTO outStockOrderItemDTO =
                    outStockOrderBusinessItemMap.get(entry.getKey().toString()) == null
                        ? outStockOrderItemMap.get(entry.getKey().toString())
                        : outStockOrderBusinessItemMap.get(entry.getKey().toString());
                if (outStockOrderItemDTO == null) {
                    noOmsOrderItemIds.add(entry.getKey());
                    continue;
                }
                BigDecimal unitTotalCount = outStockOrderItemDTO.getUnitTotalCount();

                if (!outStockOrderItemDTO.getProductSpecificationId()
                    .equals(entry.getValue().get(0).getProductSpecification_Id())
                    || !Objects.equals(outStockOrderItemDTO.getOwnerId(), entry.getValue().get(0).getOwnerId())
                    || unitTotalCount.compareTo(deliverCount) != 0) {
                    errOrderItemIds.add(entry.getKey());
                    continue;
                }
                entry.getValue().forEach(item -> item.setOrderItem_Id(Long.valueOf(outStockOrderItemDTO.getId())));
            }

            for (OutStockOrderItemDTO outStockOrderItem : outStockOrderItems) {
                Long itemId = outStockOrderItem.getBusinessItemId() == null ? Long.valueOf(outStockOrderItem.getId())
                    : Long.valueOf(outStockOrderItem.getBusinessItemId());
                List<InventoryDeliveryJiupiOrderItem> deliveryOrderItems = orderItemMap.get(itemId) != null
                    ? orderItemMap.get(itemId) : orderItemMap.get(Long.valueOf(outStockOrderItem.getId()));
                if (CollectionUtils.isEmpty(deliveryOrderItems)) {
                    noOrderItemIds.add(itemId);
                    continue;
                }
                BigDecimal deliverCount = deliveryOrderItems.stream()
                    .map(InventoryDeliveryJiupiOrderItem::getDeliverCount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal unitTotalCount = outStockOrderItem.getUnitTotalCount();
                if (!deliveryOrderItems.get(0).getProductSpecification_Id()
                    .equals(outStockOrderItem.getProductSpecificationId())
                    || !Objects.equals(deliveryOrderItems.get(0).getOwnerId(), outStockOrderItem.getOwnerId())
                    || deliverCount.compareTo(unitTotalCount) != 0) {
                    errOrderItemIds.add(itemId);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(errOrderNos)) {
            errOrderNos = errOrderNos.stream().distinct().collect(Collectors.toList());
            throw new BusinessException("订单在WMS中不存在！OrderNo:" + StringUtils.join(errOrderNos, ","));
        }

        if (CollectionUtils.isNotEmpty(noOmsOrderItemIds)) {
            String noOmsOrderItem =
                noOmsOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单项在WMS中不存在！OmsItemId:" + noOmsOrderItem);
        }

        if (CollectionUtils.isNotEmpty(noOrderItemIds)) {
            String noOrderItem =
                noOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("订单项在OMS中不存在！WmsItemId:" + noOrderItem);
        }

        if (CollectionUtils.isNotEmpty(errOrderItemIds)) {
            String errOrderItem =
                errOrderItemIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
            throw new BusinessException("OMS与WMS订单项数量不一致！ItemId:" + errOrderItem);
        }

    }

    /**
     * 修改订单状态
     *
     * @param jiupiOrders
     * @param state
     */
    private void updateOutStockOrderState(List<InventoryDeliveryJiupiOrder> jiupiOrders, byte state) {
        jiupiOrders.stream().collect(Collectors.groupingBy(InventoryDeliveryJiupiOrder::getWarehouseId))
            .forEach((warehouseId, orders) -> {
                List<String> orderNos =
                    orders.stream().map(InventoryDeliveryJiupiOrder::getOrderNo).collect(Collectors.toList());
                OutStockOrderUpdateDTO outStockOrderUpdateDTO = new OutStockOrderUpdateDTO();
                outStockOrderUpdateDTO.setOrgId(orders.get(0).getCityId());
                outStockOrderUpdateDTO.setWarehouseId(warehouseId);
                outStockOrderUpdateDTO.setRefOrderNos(orderNos);
                outStockOrderUpdateDTO.setState(state);
                if (state == OutStockOrderStateEnum.已出库.getType()) {
                    outStockOrderUpdateDTO.setOutStockTime(new Date());
                }
                outStockCommManageService.updateOutStockOrderInfo(outStockOrderUpdateDTO);
            });
    }

    /**
     * 生成内配单的订单，不需要返销售库存 如果有部分配送或者退货的情况，到内配退入库时再处理
     *
     * @param warehouseChangeList
     */
    private void setNotReturnSaleInventoryByChangeBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
        List<String> lstExOrderNos) {
        List<String> lstOrderNos = warehouseChangeList.stream()
            .filter(p -> p.getHasUpdateOPInventory() && StringUtil.isNotEmpty(p.getOrderNo())).map(p -> p.getOrderNo())
            .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lstOrderNos)) {
            return;
        }
        Integer orgId = warehouseChangeList.get(0).getCityId();
        List<String> lstNotReturnOrderNos = findNotReturnSaleInventoryOrderByIds(orgId, lstOrderNos);
        if (CollectionUtils.isNotEmpty(lstExOrderNos)) {
            lstNotReturnOrderNos.addAll(lstExOrderNos);
        }
        if (CollectionUtils.isNotEmpty(lstNotReturnOrderNos)) {
            warehouseChangeList.forEach(p -> {
                if (StringUtil.isNotEmpty(p.getOrderNo()) && lstNotReturnOrderNos.contains(p.getOrderNo())) {
                    p.setHasUpdateOPInventory(false);
                }
            });
        }
    }

    /**
     * 组装库存实际处理结果
     *
     * @param deliveryOrders 原订单
     * @param warehouseChangeList 库存处理结果
     * @return
     */
    private List<InventoryDeliveryJiupiOrder> getInventoryProcessResults(
        List<InventoryDeliveryJiupiOrder> deliveryOrders, List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return null;
        }
        List<InventoryDeliveryJiupiOrder> orders = new ArrayList<>();
        Map<Long, List<WarehouseInventoryChangeBO>> warehouseChangeMap =
            warehouseChangeList.stream().collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getOmsOrderItemId));
        deliveryOrders.forEach(order -> {
            InventoryDeliveryJiupiOrder newOrder = new InventoryDeliveryJiupiOrder();
            BeanUtils.copyProperties(order, newOrder);

            List<InventoryDeliveryJiupiOrderItem> items = new ArrayList<>();
            order.getItems().stream()
                .filter(StreamUtils.distinctByKey(InventoryDeliveryJiupiOrderItem::getOmsOrderItemId)).forEach(item -> {
                    List<WarehouseInventoryChangeBO> changeBOS = warehouseChangeMap.get(item.getOmsOrderItemId());
                    if (CollectionUtils.isNotEmpty(changeBOS)) {
                        changeBOS.forEach(bo -> {
                            InventoryDeliveryJiupiOrderItem newItem = new InventoryDeliveryJiupiOrderItem();
                            BeanUtils.copyProperties(item, newItem);

                            newItem.setOrderItemDetailId(bo.getOrderItemDetailId());
                            newItem.setDeliverCount(bo.getCount().abs());
                            newItem.setOwnerId(bo.getOwnId());
                            newItem.setSecOwnerId(bo.getSecOwnerId());
                            newItem.setOriginOrderItemDetailId(bo.getOriginOrderItemDetailId());

                            items.add(newItem);
                        });
                    }
                });

            if (CollectionUtils.isNotEmpty(items)) {
                newOrder.setItems(items);
            }
            orders.add(newOrder);
        });

        LOG.info("库存实际处理订单数据:{}", JSON.toJSONString(orders));
        return orders;
    }

    private List<String> findNotReturnSaleInventoryOrderByIds(Integer orgId, List<String> orderNos) {
        List<String> lstOrderNo = new ArrayList<>();
        // 2019-07-05 前置仓统一返销售库存，交易系统自己处理
        // // 获取订单是否生成内配单
        // List<OrderDTO> outStockOrders = iOutStockQueryService.findOutStockOrderByNoS(orgId, orderNos);
        // if (CollectionUtils.isNotEmpty(outStockOrders)) {
        // lstOrderNo = outStockOrders.stream().filter(p -> p.getCreateAllocation() != null &&
        // p.getCreateAllocation()).map(p -> p.getRefOrderNo()).distinct().collect(Collectors.toList());
        // if (CollectionUtils.isNotEmpty(lstOrderNo)) {
        // LOG.info(String.format("配送单已生成内配单，不需要处理销售库存！OrderNo：%s", JSON.toJSONString(lstOrderNo)));
        // }
        // }
        return lstOrderNo;
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute;

/**
 * 批属性配置管理管理表
 *
 * <AUTHOR> 2018/4/9
 */
public class BatchAttributeRuleRelationPO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * rule表主键
     */
    private Long ruleId;
    /**
     * 配置类型 适用仓库(1),适用货主(2),适用类名(3),适用品牌(4)
     */
    private Byte ruleType;
    /**
     * 属性值ID
     */
    private String attributeValueId;
    /**
     * 配置名称
     */
    private String attributeValueName;

    /**
     * 获取 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 rule表主键
     */
    public Long getRuleId() {
        return this.ruleId;
    }

    /**
     * 设置 rule表主键
     */
    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * 获取 配置类型 适用仓库(1),适用货主(2),适用类名(3),适用品牌(4)
     */
    public Byte getRuleType() {
        return this.ruleType;
    }

    /**
     * 设置 配置类型 适用仓库(1),适用货主(2),适用类名(3),适用品牌(4)
     */
    public void setRuleType(Byte ruleType) {
        this.ruleType = ruleType;
    }

    /**
     * 获取 配置名称
     */
    public String getAttributeValueName() {
        return this.attributeValueName;
    }

    /**
     * 设置 配置名称
     */
    public void setAttributeValueName(String attributeValueName) {
        this.attributeValueName = attributeValueName;
    }

    /**
     * 获取 属性值ID
     */
    public String getAttributeValueId() {
        return this.attributeValueId;
    }

    /**
     * 设置 属性值ID
     */
    public void setAttributeValueId(String attributeValueId) {
        this.attributeValueId = attributeValueId;
    }
}

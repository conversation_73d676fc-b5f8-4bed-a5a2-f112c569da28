package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
public class OrderCenterInventoryResultDTO<T> implements Serializable {


    private Integer code;

    private T data;

    private static Integer SUCCESS = 200;


    /**
     * 获取
     *
     * @return code
     */
    public Integer getCode() {
        return this.code;
    }

    /**
     * 设置
     *
     * @param code
     */
    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 获取
     *
     * @return data
     */
    public T getData() {
        return this.data;
    }

    /**
     * 设置
     *
     * @param data
     */
    public void setData(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        if (SUCCESS.equals(code)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

}

package com.yijiupi.himalaya.supplychain.inventory.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓库标准费率配置PO
 * 
 * <AUTHOR>
 */
public class WarehouseChargeConfigPO implements Serializable {
    /**
     * 编号
     */
    private Long id;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 下车费
     */
    private BigDecimal unloadingcharge;

    /**
     * 分拣费
     */
    private BigDecimal sortingcharge;

    /**
     * 托管费
     */
    private BigDecimal custodiancharge;

    /**
     * 装车费
     */
    private BigDecimal loadingcharge;

    /**
     * 运输费
     */
    private BigDecimal transportcharge;

    /**
     * 卸货费
     */
    private BigDecimal landingcharge;

    /**
     * 状态 0=停用 1=启用
     */
    private Byte status;

    /**
     * 创建人
     */
    private Long createuser;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 最后更新人
     */
    private Long lastupdateuser;

    /**
     * 最后更新时间
     */
    private Date lastupdatetime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getUnloadingcharge() {
        return unloadingcharge;
    }

    public void setUnloadingcharge(BigDecimal unloadingcharge) {
        this.unloadingcharge = unloadingcharge;
    }

    public BigDecimal getSortingcharge() {
        return sortingcharge;
    }

    public void setSortingcharge(BigDecimal sortingcharge) {
        this.sortingcharge = sortingcharge;
    }

    public BigDecimal getCustodiancharge() {
        return custodiancharge;
    }

    public void setCustodiancharge(BigDecimal custodiancharge) {
        this.custodiancharge = custodiancharge;
    }

    public BigDecimal getLoadingcharge() {
        return loadingcharge;
    }

    public void setLoadingcharge(BigDecimal loadingcharge) {
        this.loadingcharge = loadingcharge;
    }

    public BigDecimal getTransportcharge() {
        return transportcharge;
    }

    public void setTransportcharge(BigDecimal transportcharge) {
        this.transportcharge = transportcharge;
    }

    public BigDecimal getLandingcharge() {
        return landingcharge;
    }

    public void setLandingcharge(BigDecimal landingcharge) {
        this.landingcharge = landingcharge;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Long getCreateuser() {
        return createuser;
    }

    public void setCreateuser(Long createuser) {
        this.createuser = createuser;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Long getLastupdateuser() {
        return lastupdateuser;
    }

    public void setLastupdateuser(Long lastupdateuser) {
        this.lastupdateuser = lastupdateuser;
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

}
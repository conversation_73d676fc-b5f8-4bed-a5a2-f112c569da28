package com.yijiupi.himalaya.supplychain.inventory.listener;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryErpBL;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.ErpStoreOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.StockOrderStoreDTO;

/**
 * Created by 余明 on 2018-05-25.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class ErpSyncListenerTest {

    @Autowired
    private ErpSyncListener erpSyncListener;
    @Autowired
    private InventoryErpBL inventoryErpBL;

    @Test
    public void processErpMsg() {
        // String json =
        // "{\"cityId\":\"999\",\"description\":\"审核盘点单\",\"erpEventType\":4,\"erpType\":17,\"notChangeStock\":false,\"orderId\":\"09af0c9c2dc74571a41756592a449153\"}";
        String json =
            "{\"cityId\":\"999\",\"description\":\"【ERP】库存盘点单审核\",\"erpEventType\":4,\"erpType\":7,\"notChangeStock\":false,\"orderId\":\"KC99920180604009-1\",\"parentId\":\"da58ba3de0304639aaedafcf49fda170\",\"productSkuList\":[{\"categoryName\":\"饮料\",\"channel\":0,\"description\":\"【ERP】库存盘点单审核\",\"packageName\":\"件\",\"productBrand\":\"百事\",\"productName\":\"巴厘岛进口蓝色百事可乐梅子味汽水（测试盘点，勿用）\",\"productSkuId\":\"99900065767480\",\"specName\":\"12瓶/件\",\"specQuantity\":\"12\",\"totalStoreCountMinUnit\":-12000,\"unitName\":\"瓶\",\"warehouseId\":\"9991\"}],\"userName\":\"平台管理员\"}";
        ErpStoreOrderDTO order = JSON.parseObject(json, ErpStoreOrderDTO.class);
        erpSyncListener.processErpMsg(order);
    }

    @Test
    public void processErpMsg2() {
        String json =
            "[{\"cityId\":\"999\",\"description\":\"【ERP】采购审核采购入库单\",\"erpEventType\":4,\"erpOrderId\":\"CR99920180529018\",\"erpType\":3,\"notChangeStock\":false,\"outInType\":1,\"productSkuList\":[{\"batchTime\":1527602263000,\"channel\":0,\"expireTime\":1540771200000,\"locationId\":169675656907578725,\"locationName\":\"SH1\",\"productSkuId\":\"99900065754568\",\"productionDate\":1525219200000,\"totalStoreCountMinUnit\":30}],\"stockOrderId\":\"999118052900087\",\"userName\":\"黄正全\",\"warehouseId\":9991}]";
        List<StockOrderStoreDTO> stockOrderStoreDTOS = JSON.parseArray(json, StockOrderStoreDTO.class);
        for (StockOrderStoreDTO stockOrderStoreDTO : stockOrderStoreDTOS) {
            inventoryErpBL.applyErpOrder(stockOrderStoreDTO, false, false, false, true, true);
        }
    }
}
package com.yijiupi.himalaya.supplychain.batchinventory.domain.po.promotion;

import java.io.Serializable;
import java.util.Date;

/**
 * 促销批次库存
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
public class ProductPromotionStoreBatchPO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 批属性编号
     */
    private String batchAttributeInfoNo;
    /**
     * 仓id
     */
    private Integer warehouseId;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 生产日期
     */
    private Date productionDate;
    /**
     * 是否删除
     */
    private Byte isDelete;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后修改人
     */
    private String lastUpdateUser;
    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchAttributeInfoNo() {
        return batchAttributeInfoNo;
    }

    public void setBatchAttributeInfoNo(String batchAttributeInfoNo) {
        this.batchAttributeInfoNo = batchAttributeInfoNo;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getIdentityKey() {
        return String.format("%s-%s", getWarehouseId(), getBatchAttributeInfoNo());
    }
}

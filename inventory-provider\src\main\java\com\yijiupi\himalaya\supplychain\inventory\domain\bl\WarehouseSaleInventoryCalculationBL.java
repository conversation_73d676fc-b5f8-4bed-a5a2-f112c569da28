package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryWaitDeliveryDTO;
import com.yijiupi.himalaya.supplychain.dto.ordercenter.SaleInventoryWaitDeliveryQueryDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.dto.PutawayTaskItemWaitCountDTO;
import com.yijiupi.himalaya.supplychain.goodspursueoperate.service.IPutawayTaskService;
import com.yijiupi.himalaya.supplychain.instockorder.dto.qc.QualityControlBillDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.qc.QualityControlBillQueryDTO;
import com.yijiupi.himalaya.supplychain.instockorder.service.qc.IQualityControlBillService;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ordercenter.InventoryOrderCenterBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.WareHoseInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseProductStoreQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.InternalWaitDeliveryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.outstockorder.UnshippedQuantityQueryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockOrderStatisticsService;

/**
 * 仓库销售库存计算
 *
 * <AUTHOR>
 * @date 2024/11/15
 */
@Service
public class WarehouseSaleInventoryCalculationBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseSaleInventoryCalculationBL.class);
    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private WarehouseInventoryCheckBL warehouseInventoryCheckBL;
    @Autowired
    private InventoryOrderCenterBL inventoryOrderCenterBL;

    @Reference(timeout = 60000)
    private IOutStockOrderStatisticsService iOutStockOrderStatisticsService;
    @Reference(timeout = 60000)
    private IPutawayTaskService iPutawayTaskService;
    @Reference(timeout = 60000)
    private IQualityControlBillService iQualityControlBillService;

    /**
     * 根据仓库数据计算销售库存
     */
    public List<WarehouseStoreDTO> calculationSaleInventory(WarehouseProductStoreQueryDTO productStoreQueryDTO) {
        LOGGER.info("根据仓库数据计算销售库存 入参：{}", JSON.toJSONString(productStoreQueryDTO));
        AssertUtils.notNull(productStoreQueryDTO, "查询销售库存参数不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getCityId(), "城市id不能为空");
        AssertUtils.notNull(productStoreQueryDTO.getWarehouseId(), "仓库id不能为空");
        if (productStoreQueryDTO.getPageSize() != null && productStoreQueryDTO.getPageSize() > 1000) {
            productStoreQueryDTO.setPageSize(5000);
        }
        Integer cityId = productStoreQueryDTO.getCityId();
        Integer warehouseId = productStoreQueryDTO.getWarehouseId();

        // 1、查询仓库库存，并将将仓库库存中的大宗库存合并到酒批库存中
        PageList<WarehouseStoreDTO> storePageList =
            warehouseInventoryCheckBL.getWarehouseInventoryList(productStoreQueryDTO);
        if (storePageList == null || CollectionUtils.isEmpty(storePageList.getDataList())) {
            return Collections.emptyList();
        }
        List<WarehouseStoreDTO> warehouseStoreDTOList = storePageList.getDataList();
        List<Long> productSkuIdList =
            warehouseStoreDTOList.stream().map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        // 2、获取中台待发货数量
        Map<String, BigDecimal> waitDeliveryMap = getWaitDeliveryMap(warehouseId, warehouseStoreDTOList);
        // 3、查询预占和残次品数量
        Map<String, BigDecimal> preemptCountMap = getPreemptProductMap(productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId(), warehouseStoreDTOList);
        // 4、获取待上架数量
        Map<String, BigDecimal> waitPutawayCountMap = getWaitPutawayMap(productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId(), productSkuIdList);
        // 5、获取店仓安全库存数量(目前无数据，作废)
        Map<String, BigDecimal> safetyStoreCountMap = getEasyChainSafetyStoreCountMap(warehouseStoreDTOList);
        // 6、查询未质检产品数量
        Map<String, BigDecimal> qualityControlCountMap = getQualityControlMap(productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId(), productSkuIdList);
        // 7、查询未质检产品数量
        Map<String, BigDecimal> internalWaitDeliveryMap = getInternalWaitDeliveryMap(productStoreQueryDTO.getCityId(),
            productStoreQueryDTO.getWarehouseId(), productSkuIdList);
        // 8、计算销售库存 = 仓库库存-已下单未发货库存-处理品库存-待上架数量-安全库存-未质检产品数量
        fillSellInventorys(warehouseStoreDTOList, waitDeliveryMap, preemptCountMap, waitPutawayCountMap,
            safetyStoreCountMap, qualityControlCountMap, internalWaitDeliveryMap);
        // // 9、处理没有入过库的，返回销售库存数量传null
        // saleInventoryByOmsQueryBL.fillNotWarehouseStoreDTOList(warehouseStoreDTOList,
        // productStoreQueryDTO.getSpecAndOwnerIds(), productStoreQueryDTO.getCityId(),
        // productStoreQueryDTO.getWarehouseId());
        LOGGER.info("根据仓库数据计算销售库存 完成");
        return warehouseStoreDTOList;
    }

    /**
     * 查询中台待发货数量
     *
     * @return
     */
    @Deprecated
    private Map<String, BigDecimal> getWaitDeliveryMap(Integer warehouseId,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        if (CollectionUtils.isEmpty(warehouseStoreDTOList)) {
            return Collections.EMPTY_MAP;
        }

        List<Long> specIds = warehouseStoreDTOList.stream().filter(p -> p.getProductSpecId() != null)
            .map(p -> p.getProductSpecId()).distinct().collect(Collectors.toList());
        SaleInventoryWaitDeliveryQueryDTO queryDTO = new SaleInventoryWaitDeliveryQueryDTO();
        queryDTO.setWarehouseId(Long.valueOf(warehouseId));
        queryDTO.setProductSpecIds(specIds);
        queryDTO.setQueryWhenOwnerIsNull(false);
        queryDTO.setQueryWhenSecOwnerIsNull(false);
        queryDTO.setDeleted(false);
        List<SaleInventoryWaitDeliveryDTO> waitDeliveryDTOS = inventoryOrderCenterBL.findWaitDeliveryByQuery(queryDTO);
        if (CollectionUtils.isEmpty(waitDeliveryDTOS)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, BigDecimal> waitDeliveryCountMap = waitDeliveryDTOS.stream()
            .filter(p -> p.getProductSpecId() != null)
            .collect(Collectors.groupingBy(
                p -> getInternalKey(p.getWarehouseId().intValue(), p.getProductSpecId(), p.getOwnerId(),
                    p.getSecOwnerId()),
                Collectors.mapping(p -> p.getCurrentCount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        LOGGER.info("查询中台待发货数量 结果：{}", JSON.toJSONString(waitDeliveryCountMap));
        return waitDeliveryCountMap;
    }

    /**
     * 查询预占和残次品数量
     *
     * @return
     */
    @Deprecated
    private Map<String, BigDecimal> getPreemptProductMap(Integer cityId, Integer warehouseId,
        List<WarehouseStoreDTO> warehouseStoreDTOList) {
        if (CollectionUtils.isEmpty(warehouseStoreDTOList)) {
            return Collections.EMPTY_MAP;
        }
        List<Long> specIds = warehouseStoreDTOList.stream().filter(p -> p.getProductSpecId() != null)
            .map(p -> p.getProductSpecId()).distinct().collect(Collectors.toList());
        List<Long> skuIds = warehouseStoreDTOList.stream().filter(p -> p.getProductSkuId() != null)
            .map(p -> p.getProductSkuId()).distinct().collect(Collectors.toList());
        WareHoseInventoryQueryDTO queryDTO = new WareHoseInventoryQueryDTO();
        queryDTO.setCityId(cityId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setProductSkuIds(skuIds);
        List<WarehouseStoreDTO> preemptProductList = warehouseInventoryQueryBL.getPreemptProductInventories(queryDTO)
            .stream().filter(p -> specIds.contains(p.getProductSpecId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(preemptProductList)) {
            return Collections.EMPTY_MAP;
        }

        Map<String,
            BigDecimal> waitDeliveryCountMap = preemptProductList.stream().filter(p -> p.getProductSpecId() != null)
                .collect(Collectors.groupingBy(
                    p -> getInternalKey(p.getWarehouseId(), p.getProductSpecId(), p.getOwnerId(), p.getSecOwnerId()),
                    Collectors.mapping(p -> p.getWarehouseTotalCount(),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        LOGGER.info("查询预占和残次品数量 结果：{}", JSON.toJSONString(waitDeliveryCountMap));
        return waitDeliveryCountMap;
    }

    /**
     * 查询待上架数量
     *
     * @return
     */
    private Map<String, BigDecimal> getWaitPutawayMap(Integer cityId, Integer warehouseId, List<Long> productSkuIds) {
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.EMPTY_MAP;
        }

        List<PutawayTaskItemWaitCountDTO> waitPutawayList =
            iPutawayTaskService.listWaitPutawayCount(cityId, warehouseId, productSkuIds);
        if (CollectionUtils.isEmpty(waitPutawayList)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, BigDecimal> waitPutawayCountMap =
            waitPutawayList.stream().filter(p -> p.getProductSpecificationId() != null)
                .collect(Collectors.groupingBy(
                    p -> getInternalKey(p.getWarehouseId(), p.getProductSpecificationId(), p.getOwnerId(),
                        p.getSecOwnerId()),
                    Collectors.mapping(p -> p.getCount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        LOGGER.info("查询待上架数量 结果：{}", JSON.toJSONString(waitPutawayCountMap));
        return waitPutawayCountMap;
    }

    /**
     * 获取易款连锁店仓-安全库存数量
     *
     * @return
     */
    private Map<String, BigDecimal> getEasyChainSafetyStoreCountMap(List<WarehouseStoreDTO> warehouseStoreDTOList) {
        return Collections.emptyMap();
    }

    /**
     * 查询未质检产品数量
     *
     * @return
     */
    private Map<String, BigDecimal> getQualityControlMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.EMPTY_MAP;
        }

        // 查询未质检产品数量
        QualityControlBillQueryDTO queryDTO = new QualityControlBillQueryDTO();
        queryDTO.setOrgId(cityId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSkuIdList(productSkuIds);
        List<QualityControlBillDTO> qualityControlBillList =
            iQualityControlBillService.findQcCountListBySkuIds(queryDTO);
        if (CollectionUtils.isEmpty(qualityControlBillList)) {
            return Collections.EMPTY_MAP;
        }

        Map<String,
            BigDecimal> qualityControlCountMap = qualityControlBillList.stream()
                .filter(p -> p.getSpecificationId() != null)
                .collect(Collectors.groupingBy(
                    p -> getInternalKey(p.getWarehouseId(), p.getSpecificationId(), p.getOwnerId(), p.getSecOwnerId()),
                    Collectors.mapping(p -> p.getOrderUnitTotalCount(),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        LOGGER.info("查询未质检产品数量 结果：{}", JSON.toJSONString(qualityControlCountMap));
        return qualityControlCountMap;
    }

    /**
     * 查询内配待发货产品数量
     *
     * @return
     */
    private Map<String, BigDecimal> getInternalWaitDeliveryMap(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.EMPTY_MAP;
        }

        // 查询内配待发货产品数量
        UnshippedQuantityQueryDTO queryDTO = new UnshippedQuantityQueryDTO();
        queryDTO.setOrgId(cityId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setSkuIds(productSkuIds);
        List<InternalWaitDeliveryDTO> internalWaitDeliveryList =
            iOutStockOrderStatisticsService.getInternalWaitDeliveryInfo(queryDTO);
        if (CollectionUtils.isEmpty(internalWaitDeliveryList)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, BigDecimal> qualityControlCountMap = internalWaitDeliveryList.stream()
            .filter(p -> p.getProductSpecificationId() != null)
            .collect(Collectors.groupingBy(
                p -> getInternalKey(p.getWarehouseId(), p.getProductSpecificationId(), p.getOwnerId(),
                    p.getSecOwnerId()),
                Collectors.mapping(p -> p.getUnitTotalCount(), Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        LOGGER.info("查询内配待发货产品数量 结果：{}", JSON.toJSONString(qualityControlCountMap));
        return qualityControlCountMap;
    }

    /**
     * 填充销售库存数量
     *
     * @param warehouseStoreDTOList
     */
    private void fillSellInventorys(List<WarehouseStoreDTO> warehouseStoreDTOList,
        Map<String, BigDecimal> waitDeliveryCountMap, Map<String, BigDecimal> preemptCountMap,
        Map<String, BigDecimal> waitPutawayCountMap, Map<String, BigDecimal> safetyStoreCountMap,
        Map<String, BigDecimal> qualityControlCountMap, Map<String, BigDecimal> internalWaitDeliveryMap) {
        warehouseStoreDTOList.forEach(p -> {
            String productStoreKey =
                getInternalKey(p.getWarehouseId(), p.getProductSpecId(), p.getOwnerId(), p.getSecOwnerId());
            BigDecimal waitDeliveryCount =
                (waitDeliveryCountMap == null || waitDeliveryCountMap.get(productStoreKey) == null) ? BigDecimal.ZERO
                    : waitDeliveryCountMap.get(productStoreKey);
            BigDecimal preemptCount = (preemptCountMap == null || preemptCountMap.get(productStoreKey) == null)
                ? BigDecimal.ZERO : preemptCountMap.get(productStoreKey);
            BigDecimal waitPutawayCount =
                (waitPutawayCountMap == null || waitPutawayCountMap.get(productStoreKey) == null) ? BigDecimal.ZERO
                    : waitPutawayCountMap.get(productStoreKey);
            BigDecimal safeCount = (safetyStoreCountMap == null || safetyStoreCountMap.get(productStoreKey) == null)
                ? BigDecimal.ZERO : safetyStoreCountMap.get(productStoreKey);
            BigDecimal qualityCount =
                (qualityControlCountMap == null || qualityControlCountMap.get(productStoreKey) == null)
                    ? BigDecimal.ZERO : qualityControlCountMap.get(productStoreKey);
            BigDecimal internalWaitDeliveryCount =
                (internalWaitDeliveryMap == null || internalWaitDeliveryMap.get(productStoreKey) == null)
                    ? BigDecimal.ZERO : internalWaitDeliveryMap.get(productStoreKey);
            // 销售库存数量
            p.setSaleStoreTotalCount(
                p.getWarehouseTotalCount().subtract(waitDeliveryCount).subtract(preemptCount).subtract(waitPutawayCount)
                    .subtract(safeCount).subtract(qualityCount).subtract(internalWaitDeliveryCount));
            LOGGER.info(String.format(
                "填充销售库存数量 WarehouseId：%s, Sku：%s, Store:%s, waitDeliveryCount:%s, preemptCount:%s, waitPutawayCount:%s, safeCount:%s, qualityCount:%s, internalWaitDeliveryCount:%s, saleStore:%s",
                p.getWarehouseId(), p.getProductSkuId(), p.getWarehouseTotalCount(), waitDeliveryCount, preemptCount,
                waitPutawayCount, safeCount, qualityCount, internalWaitDeliveryCount, p.getSaleStoreTotalCount()));
        });
    }

    private String getInternalKey(Integer warehouseId, Long productSpecId, Long ownerId, Long secOwnerId) {
        return String.format("%s-%s-%s-%s", warehouseId, productSpecId, ownerId, secOwnerId);
    }
}

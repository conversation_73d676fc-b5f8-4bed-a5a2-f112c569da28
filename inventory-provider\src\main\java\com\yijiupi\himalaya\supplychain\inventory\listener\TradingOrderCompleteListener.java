package com.yijiupi.himalaya.supplychain.inventory.listener;

import org.springframework.stereotype.Component;

/**
 * 供应链同步消息监听.
 *
 * <AUTHOR> 2018/1/12
 */
@Component
public class TradingOrderCompleteListener {
    // private static final Logger LOG = LoggerFactory.getLogger(TradingOrderCompleteListener.class);
    //
    //
    // @Autowired
    // private IdempotenceConsumer idempotenceConsumer;
    // @Autowired
    // private WarehouseChangListBOConverter warehouseChangListBOConverter;
    // @Autowired
    // private WarehouseInventoryManageBL warehouseInventoryManageBL;
    // @Autowired
    // private ProductSkuZhaoShangBL productSkuZhaoShangBL;
    // //jiupiOrderType=10 合作商订单
    // public static final Integer JIUPIORDERTYPE_PARTNERTOJIUPI = 10;
    // //合作商取货
    // public static final Integer PICKUP_PARTNER = 1;
    // //招商城市ID
    // public static final String ZHAOSHANG_CITYID = "898";
    // @Reference
    // private IOutStockCommQueryService iOutStockCommQueryService;
    //
    //
    // @RabbitListener(queues = "${mq.supplychain.jiupiOrder.tradingordercomplete}")
    // public void syncApply(TradingJiupiOrderDTO tradingJiupiOrderDTO, Message message) {
    // //此处只处理：招商城市下单，且招商城市发货的订单
    // if (tradingJiupiOrderDTO.getJiupiOrderType() == JiupiOrderTypeEnum.ORDER_TYPE_BUS
    // && tradingJiupiOrderDTO.getWarehouse_Id().toString().startsWith(ZHAOSHANG_CITYID)) {
    // idempotenceConsumer.apply("tradingordercompleteListener:" + tradingJiupiOrderDTO.getId(), () -> {
    //
    // //根据orderId去查询 outstock服务得到items
    // OutStockOrderDTO outStockOrderDTO = iOutStockCommQueryService.getOutStockOrderById(tradingJiupiOrderDTO.getId());
    //
    // if (outStockOrderDTO == null) {
    // LOG.info("TradingOrder完成事件，没有找到对应的出库订单：" + tradingJiupiOrderDTO.getId());
    // return;
    // }
    // InventoryDeliveryJiupiOrder order = getInventoryDeliveryJiupiOrder(outStockOrderDTO);
    //
    // String syncData = JSON.toJSONString(order);
    // LOG.info("交易平台招商订单同步消息监听：{}", syncData);
    // List<WarehouseInventoryChangeBO> deliveryBOList = warehouseChangListBOConverter.createDeliveryBOList(order);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // LOG.info("交易平台招商订单完成确认成功监听: {}", order.getOrderId());
    //
    // });
    // }
    // }
    //
    // private InventoryDeliveryJiupiOrder getInventoryDeliveryJiupiOrder(OutStockOrderDTO outStockOrderDTO) {
    // List<OutStockOrderItemDTO> outStockOrderItemDTOS = outStockOrderDTO.getOutStockOrderItemDTOS();
    //
    // List<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
    // for (OutStockOrderItemDTO item : outStockOrderItemDTOS) {
    // item.getOutStockOrderItemDetailDTOS().forEach(detail -> {
    // InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
    // inventoryDeliveryJiupiOrderItem.setProductSkuId(item.getSkuId());
    // inventoryDeliveryJiupiOrderItem.setDeliverCount(detail.getUnitTotalCount());
    // inventoryDeliveryJiupiOrderItem.setSaleSpecQuantity(item.getSaleSpecQuantity());
    // inventoryDeliveryJiupiOrderItem.setOwnerId(detail.getOwnerId());
    // inventoryDeliveryJiupiOrderItem.setSecOwnerId(detail.getSecOwnerId());
    //// inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItem.getOrderItemId());
    // inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
    // });
    // }
    //
    // InventoryDeliveryJiupiOrder order = new InventoryDeliveryJiupiOrder();
    //
    // order.setId(Long.valueOf(outStockOrderDTO.getId()));
    // order.setOrderId(Long.valueOf(outStockOrderDTO.getId()));
    // order.setOrderNo(outStockOrderDTO.getRefOrderNo());
    // order.setCityId(outStockOrderDTO.getOrgId());
    // order.setWarehouseId(outStockOrderDTO.getWarehouseId());
    // order.setItems(inventoryDeliveryJiupiOrderItems);
    // order.setJiupiOrderType(Integer.valueOf(outStockOrderDTO.getOrderType()));
    //// order.setPickupType(outStockOrderById.getPickupType());
    // return order;
    // }

}

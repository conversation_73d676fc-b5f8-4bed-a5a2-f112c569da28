package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRulePO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationReturnPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeRuleRelationDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 2018/4/10
 */
public class BatchAttributeRuleConvert {

    public static BatchAttributeRulePO batchAttributeRuleDTO2PO(BatchAttributeRuleDTO dto) {
        if (dto == null) {
            return null;
        }
        BatchAttributeRulePO batchAttributeRulePO = new BatchAttributeRulePO();
        batchAttributeRulePO.setTemplateId(dto.getTemplateId());
        batchAttributeRulePO.setTemplateName(dto.getTemplateName());
        batchAttributeRulePO.setRemark(dto.getRemark());
        batchAttributeRulePO.setCreateUser(dto.getCreateUser());
        return batchAttributeRulePO;
    }

    public static List<BatchAttributeRuleRelationDTO> batchAttributeRuleRelationReturnPOS2DTOS(
        List<BatchAttributeRuleRelationReturnPO> batchAttributeRuleRelationReturnPOS) {
        if (batchAttributeRuleRelationReturnPOS == null) {
            return Collections.emptyList();
        }
        ArrayList<BatchAttributeRuleRelationDTO> batchAttributeRuleRelationDTOS = new ArrayList<>();
        for (BatchAttributeRuleRelationReturnPO batchAttributeRuleRelationReturnPO : batchAttributeRuleRelationReturnPOS) {
            batchAttributeRuleRelationDTOS
                .add(batchAttributeRuleRelationReturnPO2DTO(batchAttributeRuleRelationReturnPO));
        }
        return batchAttributeRuleRelationDTOS;
    }

    public static BatchAttributeRuleRelationDTO
        batchAttributeRuleRelationReturnPO2DTO(BatchAttributeRuleRelationReturnPO batchAttributeRuleRelationReturnPO) {
        if (batchAttributeRuleRelationReturnPO == null) {
            return null;
        }
        BatchAttributeRuleRelationDTO batchAttributeRuleRelationDTO = new BatchAttributeRuleRelationDTO();
        batchAttributeRuleRelationDTO.setId(batchAttributeRuleRelationReturnPO.getId());
        batchAttributeRuleRelationDTO.setRuleId(batchAttributeRuleRelationReturnPO.getRuleId());
        batchAttributeRuleRelationDTO.setRuleType(batchAttributeRuleRelationReturnPO.getRuleType());
        batchAttributeRuleRelationDTO.setAttributeValueName(batchAttributeRuleRelationReturnPO.getAttributeValueName());
        batchAttributeRuleRelationDTO.setAttributeValueId(batchAttributeRuleRelationReturnPO.getAttributeValueId());
        return batchAttributeRuleRelationDTO;
    }
}

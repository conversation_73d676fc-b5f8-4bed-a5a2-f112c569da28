<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductStoreBatchChangeRecordMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchChangeRecordPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="changerecord_id" property="changeRecordId" jdbcType="VARCHAR"/>
        <result column="totalcount_minunit" property="changeCount" jdbcType="DECIMAL"/>
        <result column="createtime" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insertProductStoreBatchChangeRecord">
        INSERT into
        productstorebatchchangerecord(id,batch_id,changerecord_id,totalcount_minunit,createtime)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.batchId},#{item.changeRecordId},#{item.changeCount},NOW())
        </foreach>
    </insert>

    <select id="listProductStoreBatchChangeRecord"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO">
        select
        psbr.id,
        psbr.batch_id as storeBatchId,
        psbr.changerecord_id as changeRecordId,
        psbr.totalcount_minunit as changeCount,
        psbr.createtime as createTime,
        psku.packageQuantity,
        psku.packageName,
        psku.unitName,
        psr.OrderType as orderType,
        psr.Order_Id as orderId,
        psr.OrderNo as orderNo,
        psr.JiupiEventType as jiupiEventType,
        psr.ERPEventType as erpEventType,
        psr.Description as description,
        psr.CreateUser as createUser,
        psr.ProductStore_Id as storeId,
        loc.id as locationId,
        loc.name as locationName,
        loc.category as locationCategory,
        loc.subcategory as locationSubcategory,
        psb.BatchAttributeInfoNo as batchInfoNo,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate
        from productstore ps
        inner join productsku psku ON ps.ProductSpecification_Id=psku.ProductSpecification_Id AND
        ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null and ps.Owner_Id is null) or (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = ps.secOwner_Id))
        inner join productstorebatch psb on psb.productstore_id =ps.id
        inner join productstorebatchchangerecord psbr on psb.id = psbr.batch_id
        left join productstorechangerecord psr on psr.id = psbr.changerecord_id
        left join Location loc on loc.id= psb.location_id and loc.warehouse_id = ps.warehouse_id
        <where>
            <if test="cityId != null">
                ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="storeBatchId != null and storeBatchId != ''">
                and psbr.batch_id = #{storeBatchId,jdbcType=VARCHAR}
            </if>
            <if test="changeRecordId != null and changeRecordId != ''">
                and psbr.changerecord_id = #{changeRecordId,jdbcType=VARCHAR}
            </if>
            <if test="productStoreId != null and productStoreId != ''">
                and psb.productstore_id = #{productStoreId,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                and <![CDATA[ psbr.createtime >= #{startTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="endTime != null">
                and <![CDATA[ psbr.createtime <= #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="deleted != null and deleted.size > 0">
                and psku.IsDelete in
                <foreach item="item" collection="deleted" separator="," open="(" close=")" index="">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
        order by psbr.createtime desc, psbr.id asc
    </select>

    <select id="listProductStoreBatchChangeFlow"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeFlowDTO">
        SELECT
        psbc.id,
        psbc.batch_id AS batchId,
        psbc.changerecord_id AS changeRecordId,
        psbc.totalcount_minunit AS totalCountMinUnit,
        psc.CreateTime,
        psc.CreateUser,
        psc.Description,
        psc.ERPEventType,
        psc.JiupiEventType,
        psc.OrderNo,
        psc.Order_Id AS orderId,
        psc.OrderType,
        psb.BatchAttributeInfoNo AS batchInfoNo,
        psb.batchtime,
        psb.productiondate,
        psb.expiretime,
        loc.Area AS areaName,
        loc.area_id AS areaId,
        ifnull( loc.id, psb.location_id ) AS locationId,
        ifnull(
        loc.NAME,
        CONCAT( '[失效]', psb.location_name )) AS locationName,
        ifnull( loc.category, psb.locationCategory ) AS locationCategory,
        ifnull( loc.subcategory, psb.subcategory ) AS locationSubcategory
        FROM
        productstorebatchchangerecord psbc
        INNER JOIN productstorechangerecord psc ON psc.id = psbc.changerecord_id
        INNER JOIN productstorebatch psb ON psb.id = psbc.batch_id
        LEFT JOIN location loc ON loc.id = psb.Location_Id
        AND loc.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        INNER JOIN (
        SELECT
        psb.id
        FROM
        productstorebatch psb
        INNER JOIN productstore ps ON ps.id = psb.ProductStore_Id
        WHERE
        ps.warehouse_id = #{warehouseId,jdbcType=INTEGER}
        AND ps.ProductSpecification_Id = #{productSpecId,jdbcType=BIGINT}
        <if test="ownerId == null">
            and ps.Owner_Id is null
        </if>
        <if test="ownerId != null">
            and ps.Owner_Id = #{ownerId,jdbcType=BIGINT}
        </if>
        <if test="secOwnerId == null">
            and ps.secOwner_Id is null
        </if>
        <if test="secOwnerId != null">
            and ps.secOwner_Id = #{secOwnerId,jdbcType=BIGINT}
        </if>
        ) tmp ON tmp.id = psb.id
        <where>
            1=1
            <if test="orderNos != null and orderNos.size() != 0">
                and psc.OrderNo in
                <foreach collection="orderNos" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="areaName != null">
                and loc.Area = #{areaName,jdbcType=VARCHAR}
            </if>
            <if test="locationName != null">
                and psb.location_name = #{locationName,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                and <![CDATA[ psbc.createtime >= #{startTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="endTime != null">
                and <![CDATA[ psbc.createtime <= #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="createUser != null">
                and psc.CreateUser = #{createUser,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY
        psbc.createtime DESC,
        psbc.changeRecord_Id,
        psbc.totalcount_minunit desc
    </select>

    <select id="selectBatchInventoryByChangeRecordId" resultMap="BaseResultMap">
        SELECT brecord.id,
        brecord.batch_id,
        brecord.changerecord_id,
        brecord.totalcount_minunit,
        brecord.createtime
        FROM (SELECT rec.id
        FROM productstorechangerecord rec
        LEFT JOIN productstorechangerecord old ON old.ProductStore_Id = rec.ProductStore_Id
        AND old.ProductStore_Id = #{productStoreId,jdbcType=VARCHAR}
        AND old.OrderNo = #{orderNo,jdbcType=VARCHAR}
        WHERE rec.CreateTime &lt;= old.CreateTime
        ORDER BY rec.CreateTime DESC LIMIT 1
        ) a
        INNER JOIN productstorebatchchangerecord brecord ON brecord.changerecord_id = a.Id
        ORDER BY brecord.createtime ASC
    </select>

    <select id="selectProductStoreBatchChangeRecords"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO">
        select
        psbr.id,
        psbr.batch_id as storeBatchId,
        psbr.changerecord_id as changeRecordId,
        psbr.totalcount_minunit as changeCount,
        psbr.createtime as createTime,
        psr.OrderType as orderType,
        psr.Order_Id as orderId,
        psr.OrderNo as orderNo,
        psr.JiupiEventType as jiupiEventType,
        psr.ERPEventType as erpEventType,
        psr.Description as description,
        psr.CreateUser as createUser,
        psr.ProductStore_Id as storeId,
        psb.BatchAttributeInfoNo as batchInfoNo,
        psb.batchtime as batchTime,
        psb.expiretime as expireTime,
        psb.productiondate as productionDate,
        ps.secOwner_Id as secOwnerId,
        ps.Owner_Id as ownerId,
        ps.ProductSpecification_Id as productSpecificationId
        from productstore ps
        inner join productstorebatch psb on psb.productstore_id =ps.id
        inner join productstorebatchchangerecord psbr on psb.id = psbr.batch_id
        left join productstorechangerecord psr on psr.id = psbr.changerecord_id
        <where>
            <if test="cityId != null">
                ps.City_Id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="orderNo != null">
                and psr.OrderNo = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderNos != null and orderNos.size() != 0">
                and psr.OrderNo in
                <foreach collection="orderNos" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by psbr.createtime desc, psbr.id asc
    </select>


    <select id="findChangeRecordInfoByOrderInfo"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeInfoResultDTO">
        select
        b.id as id,
        a.id as storeChangeRecordId,b.id as storeBatchChangeRecordId,s.id as
        productstoreId,a.OrderNo,b.batch_id,b.totalcount_minunit as totalCountMinUnit,
        b.createtime, c.batchattributeinfono as batchAttributeInfoNo,c.location_id as
        locationId,c.location_name as locationName,s.warehouse_id as warehouseId,s.ProductSpecification_Id as
        productSpecificationId,c.Id as productStoreBatchId,
        s.Owner_Id,s.SecOwner_Id as secOwnerId,s.channel,c.productiondate,psku.productsku_id as productSkuId,psku.source
        from productstorechangerecord a
        inner join productstorebatchchangerecord b
        on a.id=b.changerecord_id
        inner join productstorebatch c
        on b.batch_id = c.id
        inner join productstore s
        on c.productstore_id = s.id
        INNER JOIN productsku psku
        ON s.ProductSpecification_Id = psku.ProductSpecification_Id
        AND psku.City_Id = s.City_Id
        AND ((psku.Company_Id is null and s.Owner_Id is null) or (psku.Company_Id = s.Owner_Id))
        AND ((psku.secOwner_Id is null) or (psku.secOwner_Id = s.secOwner_Id))
        where a.order_Id = #{orderId,jdbcType=VARCHAR}
        <if test="orderNo != null">
            and a.orderno = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="warehouseId != null">
            AND s.warehouse_id = #{warehouseId}
        </if>
        <if test="orgId != null">
            AND a.City_id = #{orgId}
        </if>
        <if test="productSpecificationId != null">
            and s.productspecification_id = #{productSpecificationId,jdbcType=INTEGER}
        </if>
        <if test="skuId != null">
            AND psku.productsku_id = #{skuId,jdbcType=BIGINT}
        </if>
        <if test="jiupiEventType != null">
            AND a.jiupiEventType = #{jiupiEventType}
        </if>
        order by b.createtime desc;
    </select>

</mapper>
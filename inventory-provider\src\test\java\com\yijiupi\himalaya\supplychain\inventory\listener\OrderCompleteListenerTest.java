package com.yijiupi.himalaya.supplychain.inventory.listener;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.ProductSkuZhaoShangBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.WarehouseInventoryManageBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrder;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryDeliveryJiupiOrderItem;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockQueryService;

/**
 * <AUTHOR> 2018/1/12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class OrderCompleteListenerTest {

    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;
    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;
    @Autowired
    private ProductSkuZhaoShangBL productSkuZhaoShangBL;
    @Reference
    private IOutStockQueryService iOutStockQueryService;

    @Test
    public void testSupplyListener() {
        String json =
            "{\"cityId\":898,\"deliveryState\":0,\"id\":89811801121473769,\"items\":[{\"buyCount\":3,\"deliverCount\":3,\"orderItem_Id\":169481001122224659,\"productSkuId\":89800050716672,\"saleSpecQuantity\":6,\"takeCount\":3}],\"jiupiOrderType\":1,\"orderId\":898118011214031761,\"orderNo\":\"************\",\"warehouseId\":9991}";
        InventoryDeliveryJiupiOrder order = JSON.parseObject(json, InventoryDeliveryJiupiOrder.class);
        // 判断是否是招商订单
        // InventoryDeliveryJiupiOrder order1 = productSkuZhaoShangBL.processOrderItemProductSkuId(order);
        List<WarehouseInventoryChangeBO> deliveryBOList = warehouseChangListBOConverter.createDeliveryBOList(order);
        warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    }

    // @Test
    // public void distributtionOrderComplete() {
    // String json =
    // "{\"fromCityId\":999,\"createTime\":\"2018-06-07
    // 18:13:49\",\"id\":\"241782412432904608\",\"cityId\":\"999\",\"orderId\":\"9991806071874442\",\"orderNo\":\"241782412432904608\",\"orderType\":null,\"orderAmount\":0.03,\"payableAmount\":0.03,\"reduceAmount\":0.00,\"productReduceAmount\":null,\"useCouponAmount\":null,\"useBonusAmount\":0.00,\"user_Id\":\"0\",\"salesman_Id\":\"0\",\"province\":\"西藏自治区\",\"city\":\"拉萨市\",\"county\":\"城关区\",\"detailAddress\":\"西藏自治区拉萨市城关区东孜苏路6号拉萨清真大寺\",\"contact\":\"直江巽天\",\"phone\":\"18186663336\",\"classify\":\"0\",\"deliveryMode\":\"0\",\"orderTime\":{\"completeTime\":\"2018-06-07
    // 20:56:00\"},\"shopId\":\"56\",\"itemList\":[{\"id\":\"241782412432904609\",\"remark\":null,\"sourceType\":null,\"productType\":null,\"itemPrice\":{\"sellPrice\":0.01,\"sellUnit\":\"瓶\",\"minUnitPrice\":0.01,\"reduceProductPrice\":null},\"itemAmount\":{\"reduceProductAmount\":null,\"reduceCouponAmount\":null,\"reduceBonusAmount\":null,\"reduceOrderAmount\":null,\"totalAmount\":0.02,\"payAmount\":0.02,\"saleCount\":2,\"minUnitTotalCount\":2},\"itemProduct\":{\"productId\":\"99900000003779\",\"saleSpecQuantity\":1,\"sellUnit\":\"瓶\",\"specQuantity\":12,\"packageName\":\"件\",\"unitName\":\"瓶\",\"saleMode\":null,\"isUseBonus\":false,\"isUseCoupon\":false}}],\"state\":\"10\",\"pickupWarehouseId\":\"9991\",\"warehouse_Id\":\"9991\",\"transportFee\":0.01}";
    // InventoryDeliveryJiupiOrder orderTmp = JSON.parseObject(json, InventoryDeliveryJiupiOrder.class);
    //
    // OrderDTO outStockOrderById = iOutStockQueryService.findOutStockOrderByNo(orderTmp.getOrderNo());
    // InventoryDeliveryJiupiOrder order = getInventoryDeliveryJiupiOrder(outStockOrderById);
    //
    // InventoryDeliveryJiupiOrder order2 = productSkuZhaoShangBL.processOrderItemProductSkuId(order);
    // List<WarehouseInventoryChangeBO> deliveryBOList = warehouseChangListBOConverter.createDeliveryBOList(order2);
    // warehouseInventoryManageBL.processOrderDeliveryCount(deliveryBOList);
    // }

    private InventoryDeliveryJiupiOrder getInventoryDeliveryJiupiOrder(OrderDTO outStockOrderById) {
        List<OrderItemDTO> items = outStockOrderById.getItems();

        List<InventoryDeliveryJiupiOrderItem> inventoryDeliveryJiupiOrderItems = new ArrayList<>();
        for (OrderItemDTO orderItem : items) {
            InventoryDeliveryJiupiOrderItem inventoryDeliveryJiupiOrderItem = new InventoryDeliveryJiupiOrderItem();
            inventoryDeliveryJiupiOrderItem.setProductSkuId(orderItem.getSkuId());
            inventoryDeliveryJiupiOrderItem.setDeliverCount(orderItem.getUnitTotalCount());
            inventoryDeliveryJiupiOrderItems.add(inventoryDeliveryJiupiOrderItem);
            inventoryDeliveryJiupiOrderItem.setSaleSpecQuantity(orderItem.getSaleSpecQuantity());
            // inventoryDeliveryJiupiOrderItem.setOrderItem_Id(orderItem.getOrderItemId());
        }

        InventoryDeliveryJiupiOrder order = new InventoryDeliveryJiupiOrder();

        order.setId(outStockOrderById.getId());
        order.setOrderId(outStockOrderById.getId());
        order.setOrderNo(outStockOrderById.getRefOrderNo());
        order.setCityId(outStockOrderById.getOrgId());
        order.setWarehouseId(outStockOrderById.getWarehouseId());
        order.setItems(inventoryDeliveryJiupiOrderItems);
        order.setJiupiOrderType(Integer.valueOf(outStockOrderById.getOrderType()));
        // order.setPickupType(outStockOrderById.getPickupType());
        return order;
    }
}

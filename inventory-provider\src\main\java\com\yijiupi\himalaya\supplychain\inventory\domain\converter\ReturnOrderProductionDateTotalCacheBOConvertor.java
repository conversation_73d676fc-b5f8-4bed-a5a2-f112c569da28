package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.inventory.domain.bo.ReturnOrderProductionDateCacheBO;
import com.yijiupi.himalaya.supplychain.inventory.dto.returnorderdate.*;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
public class ReturnOrderProductionDateTotalCacheBOConvertor {

    public static List<ReturnOrderProductionDateCacheBO> convert(ReturnOrderProductDateQueryDTO queryDTO) {
        List<ReturnOrderProductionDateCacheBO> cacheBOList = new ArrayList<>();

        for (ReturnOrderProductDateQueryItemDTO queryItemDTO : queryDTO.getQueryCondition()) {
            for (ProductSpecInfoDTO productSpecInfoDTO : queryItemDTO.getSpecInfoDTOS()) {
                ReturnOrderProductionDateCacheBO productionDateCacheBO = new ReturnOrderProductionDateCacheBO();
                productionDateCacheBO.setOrderNo(queryItemDTO.getOrderNo());
                productionDateCacheBO.setProductSpecId(productSpecInfoDTO.getProductSpecId());
                productionDateCacheBO.setOwnerId(productSpecInfoDTO.getOwnerId());
                productionDateCacheBO.setKey(productionDateCacheBO.getDefaultKey());
                cacheBOList.add(productionDateCacheBO);
            }
        }

        return cacheBOList;
    }

    public static List<ReturnOrderProductDateDTO>
        convertToReturnOrderProductDateDTO(List<ReturnOrderProductionDateCacheBO> cacheBOList) {
        Map<String, List<ReturnOrderProductionDateCacheBO>> cacheBOMap =
            cacheBOList.stream().collect(Collectors.groupingBy(ReturnOrderProductionDateCacheBO::getOrderNo));
        List<ReturnOrderProductDateDTO> list = new ArrayList<>();
        for (Map.Entry<String, List<ReturnOrderProductionDateCacheBO>> entry : cacheBOMap.entrySet()) {
            ReturnOrderProductDateDTO productDateDTO = new ReturnOrderProductDateDTO();
            productDateDTO.setOrderNo(entry.getKey());
            List<ReturnOrderProductDateItemDTO> items = entry.getValue().stream()
                .map(ReturnOrderProductionDateCacheBO::getItemDTO).collect(Collectors.toList());
            productDateDTO.setItems(items);
            list.add(productDateDTO);
        }

        return list;
    }

    public static List<ReturnOrderProductDateDTO>
        convertToReturnOrderProductDateDefaultDTO(List<ReturnOrderProductionDateCacheBO> cacheBOList) {
        Map<String, List<ReturnOrderProductionDateCacheBO>> cacheBOMap =
            cacheBOList.stream().collect(Collectors.groupingBy(ReturnOrderProductionDateCacheBO::getOrderNo));
        List<ReturnOrderProductDateDTO> list = new ArrayList<>();
        for (Map.Entry<String, List<ReturnOrderProductionDateCacheBO>> entry : cacheBOMap.entrySet()) {
            ReturnOrderProductDateDTO productDateDTO = new ReturnOrderProductDateDTO();
            productDateDTO.setOrderNo(entry.getKey());
            productDateDTO.setItems(Collections.emptyList());
            list.add(productDateDTO);
        }

        return list;
    }

}

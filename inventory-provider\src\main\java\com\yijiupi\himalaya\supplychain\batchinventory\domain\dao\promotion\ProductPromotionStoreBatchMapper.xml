<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.promotion.ProductPromotionStoreBatchMapper">
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.promotion.ProductPromotionStoreBatchPO">
        <id column="Id" property="id" jdbcType="BIGINT"/>
        <result column="BatchAttributeInfoNo" property="batchAttributeInfoNo" jdbcType="VARCHAR"/>
        <result column="WarehouseId" property="warehouseId" jdbcType="INTEGER"/>
        <result column="SkuId" property="skuId" jdbcType="BIGINT"/>
        <result column="ProductName" property="productName" jdbcType="VARCHAR"/>
        <result column="ProductionDate" property="productionDate" jdbcType="TIMESTAMP"/>
        <result column="IsDelete" property="isDelete" jdbcType="TINYINT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LastUpdateUser" property="lastUpdateUser" jdbcType="VARCHAR"/>
        <result column="LastUpdateTime" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,BatchAttributeInfoNo,WarehouseId,SkuId,ProductName,ProductionDate,
        IsDelete,CreateUser,CreateTime,LastUpdateUser,LastUpdateTime
    </sql>

    <insert id="batchInsert">
        INSERT INTO productpromotionstorebatch
        (Id, BatchAttributeInfoNo, WarehouseId, SkuId, ProductName, ProductionDate, IsDelete, CreateUser, CreateTime,
        LastUpdateUser, LastUpdateTime)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.batchAttributeInfoNo}, #{item.warehouseId}, #{item.skuId}, #{item.productName},
            #{item.productionDate}, #{item.isDelete}, #{item.createUser}, #{item.createTime},
            #{item.lastUpdateUser}, #{item.lastUpdateTime})
        </foreach>
    </insert>

    <delete id="batchDeleteByIds">
        DELETE FROM productpromotionstorebatch
        WHERE Id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="batchUpdate">
        UPDATE productpromotionstorebatch
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="BatchAttributeInfoNo = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.batchAttributeInfoNo != null">
                        WHEN Id = #{item.id} THEN #{item.batchAttributeInfoNo}
                    </if>
                </foreach>
            </trim>
            <trim prefix="WarehouseId = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.warehouseId != null">
                        WHEN Id = #{item.id} THEN #{item.warehouseId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SkuId = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.skuId != null">
                        WHEN Id = #{item.id} THEN #{item.skuId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductName = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.productName != null">
                        WHEN Id = #{item.id} THEN #{item.productName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ProductionDate = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.productionDate != null">
                        WHEN Id = #{item.id} THEN #{item.productionDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IsDelete = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.isDelete != null">
                        WHEN Id = #{item.id} THEN #{item.isDelete}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateUser = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.createUser != null">
                        WHEN Id = #{item.id} THEN #{item.createUser}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CreateTime = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.createTime != null">
                        WHEN Id = #{item.id} THEN #{item.createTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateUser = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.lastUpdateUser != null">
                        WHEN Id = #{item.id} THEN #{item.lastUpdateUser}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LastUpdateTime = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.lastUpdateTime != null">
                        WHEN Id = #{item.id} THEN #{item.lastUpdateTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE Id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM productpromotionstorebatch
        WHERE Id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and IsDelete = 0
    </select>

    <select id="queryByCondition"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.ProductPromotionStoreBatchDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM productpromotionstorebatch
        <where>
            IsDelete = 0
            <if test="warehouseId != null">
                AND WarehouseId = #{warehouseId}
            </if>
            <if test="productionDate != null">
                AND ProductionDate = #{productionDate}
            </if>
            <if test="skuIdList != null and skuIdList.size() > 0">
                and SkuId in
                <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="batchAttributeInfoNos != null and batchAttributeInfoNos.size() > 0">
                AND BatchAttributeInfoNo IN
                <foreach collection="batchAttributeInfoNos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listPromotionStoreBatchProduct"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchResultDTO">
        SELECT
        pp.WarehouseId,
        pp.SkuId,
        ps.ProductSpecification_Id as productSpecId,
        ps.Owner_Id as ownerId
        FROM productpromotionstorebatch pp
        inner join productstorebatch psb on pp.BatchAttributeInfoNo = psb.BatchAttributeInfoNo AND
        psb.totalcount_minunit != 0
        inner join productstore ps on psb.productstore_id = ps.id and ps.Warehouse_Id = pp.WarehouseId
        AND ps.totalcount_minunit != 0
        WHERE
        pp.IsDelete = 0
        AND ps.Warehouse_Id = #{warehouseId}
        <if test="specIdAndOwnerIdList!=null and specIdAndOwnerIdList.size()>0">
            AND
            <foreach collection="specIdAndOwnerIdList" item="item" index="index" separator="or" open="(" close=")">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    and ps.Owner_Id is null
                </if>
                <if test="item.ownerId != null">
                    and ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
        <if test="skuIdList != null and skuIdList.size() > 0">
            and pp.SkuId in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        group by pp.WarehouseId, ps.ProductSpecification_Id, ps.Owner_Id, pp.SkuId
    </select>

    <select id="listProductMixedBatchFlag"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchResultDTO">
        SELECT
        ps.Warehouse_Id as warehouseId,
        psku.ProductSku_Id AS skuId,
        CASE
        WHEN
        <![CDATA[ COUNT(CASE WHEN ppsb.Id IS NOT NULL THEN 1 END) > 0 AND COUNT(CASE WHEN ppsb.Id IS NULL THEN 1 END) > 0 ]]>
        THEN 1
        ELSE 0
        END AS isMixedBatch,
        CASE
        WHEN COUNT(CASE WHEN ppsb.Id IS NOT NULL THEN 1 END) > 0
        THEN 1
        ELSE 0
        END AS hasPromotionBatch
        FROM
        productsku psku
        INNER JOIN productstore ps ON ps.ProductSpecification_Id=psku.ProductSpecification_Id
        AND ps.City_Id=psku.City_Id
        AND ((psku.Company_Id is null AND ps.Owner_Id is null) OR (psku.Company_Id = ps.Owner_Id))
        AND ((psku.secOwner_Id is null) OR (psku.secOwner_Id = ps.secOwner_Id))
        INNER JOIN productstorebatch psb ON psb.productstore_id = ps.id
        LEFT JOIN productpromotionstorebatch ppsb ON ppsb.WarehouseId = ps.Warehouse_Id
        AND ppsb.BatchAttributeInfoNo = psb.BatchAttributeInfoNo AND ppsb.IsDelete = 0
        WHERE
        ps.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
        AND ps.totalcount_minunit > 0 AND psb.totalcount_minunit > 0
        AND psb.subcategory not in (29,60)
        AND psku.IsDelete = 0
        <if test="specIdAndOwnerIdList != null and specIdAndOwnerIdList.size() > 0">
            AND
            <foreach collection="specIdAndOwnerIdList" item="item" index="index" separator="or" open="(" close=")">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    and ps.Owner_Id is null
                </if>
                <if test="item.ownerId != null">
                    and ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
        <if test="skuIdList != null and skuIdList.size() > 0">
            and psku.ProductSku_Id in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        GROUP BY ps.Warehouse_Id, psku.ProductSku_Id
        HAVING
        <![CDATA[ COUNT(CASE WHEN ppsb.Id IS NOT NULL THEN 1 END) > 0 OR COUNT(CASE WHEN ppsb.Id IS NULL THEN 1 END) > 0 ]]>
    </select>

    <select id="getStoreBatchPromotionCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM productpromotionstorebatch ppsb
        INNER JOIN productstorebatch psb ON ppsb.BatchAttributeInfoNo = psb.BatchAttributeInfoNo AND
        psb.totalcount_minunit != 0 AND ppsb.IsDelete = 0
        inner join productstore ps on psb.productstore_id = ps.id and ps.warehouse_id = ppsb.WarehouseId and
        ps.totalcount_minunit != 0
        <where>
            ppsb.WarehouseId = #{warehouseId}
            <if test="skuId != null">
                and ppsb.SkuId = #{skuId}
            </if>
            <if test="batchAttributeInfoNo != null and batchAttributeInfoNo != '' ">
                and ppsb.BatchAttributeInfoNo = #{batchAttributeInfoNo}
            </if>
        </where>
    </select>

    <select id="listPromotionStoreBatchNoGroup"
            resultType="com.yijiupi.himalaya.supplychain.batchinventory.dto.promotion.PromotionStoreBatchResultDTO">
        SELECT
        pp.Id,
        pp.WarehouseId,
        pp.SkuId,
        pp.BatchAttributeInfoNo,
        ps.ProductSpecification_Id as productSpecId,
        ps.Owner_Id as ownerId,
        ps. SecOwner_Id as secOwnerId,
        psb.productiondate as productionDate
        FROM productpromotionstorebatch pp
        inner join productstorebatch psb on pp.BatchAttributeInfoNo = psb.BatchAttributeInfoNo AND
        psb.totalcount_minunit != 0
        inner join productstore ps on psb.productstore_id = ps.id and ps.Warehouse_Id = pp.WarehouseId
        AND ps.totalcount_minunit != 0
        WHERE
        pp.IsDelete = 0
        <if test="warehouseId != null">
            AND ps.Warehouse_Id = #{warehouseId}
        </if>
        <if test="specIdAndOwnerIdList!=null and specIdAndOwnerIdList.size()>0">
            AND
            <foreach collection="specIdAndOwnerIdList" item="item" index="index" separator="or" open="(" close=")">
                (
                ps.ProductSpecification_Id = #{item.productSpecId,jdbcType=BIGINT}
                <if test="item.ownerId == null">
                    and ps.Owner_Id is null
                </if>
                <if test="item.ownerId != null">
                    and ps.Owner_Id = #{item.ownerId,jdbcType=BIGINT}
                </if>
                )
            </foreach>
        </if>
        <if test="skuIdList != null and skuIdList.size() > 0">
            and pp.SkuId in
            <foreach collection="skuIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="locationIdList != null and locationIdList.size() > 0">
            and psb.location_id in
            <foreach collection="locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="batchAttributeInfoNos != null and batchAttributeInfoNos.size() > 0">
            and pp.BatchAttributeInfoNo IN
            <foreach collection="batchAttributeInfoNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
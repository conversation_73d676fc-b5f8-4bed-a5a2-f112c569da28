/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */

package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.dto.product.*;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductSpecCityStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStorePO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.search.WarehouseStoreBySupplierOpSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductSpecCityListStoreSO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.product.DisposedProductInventorDTO;

public interface ProductStorePOMapper {

    /**
     * 根据库存ID获取库存信息
     * 
     * @return
     */
    ProductStorePO getProductStoreById(String id);

    /**
     * 调拨系统库存信息汇总
     */
    StoreDTOBySupplierOp getProductStoreForSupplierOp(@Param("so") WarehouseStoreBySupplierOpSO so);

    /**
     * 通过产品信息规格id和cityid,查询库存.
     */
    List<ProductSpecCityStoreDTO> findProductSpecCityListStore(@Param("so") ProductSpecCityListStoreSO so);

    /**
     * 获取产品SKU信息对应仓库库存(条件productSkuId,warehouseId,channel)
     */
    ProductWarehouseStoreDTO findProductWarehouse(@Param("warehouseId") Integer warehouseId,
        @Param("productSkuId") Long productSkuId, @Param("channel") Integer channel,
        @Param("secOwnerId") Long secOwnerId, @Param("source") Integer source);

    /**
     * 获取产品SKU信息对应仓库库存(条件productSkuId,warehouseId,channel)
     */
    List<ProductWarehouseStoreDTO> findProductWarehouseBySku(@Param("warehouseId") Integer warehouseId,
        @Param("productSkuIds") List<Long> productSkuIds, @Param("channel") Integer channel,
        @Param("secOwnerId") Long secOwnerId, @Param("source") Integer source);

    /**
     * 调拨系统产品查询库存用的
     */
    PageResult<StoreDTOBySupplierOp> findProductWarehouseStoreListForAllocation(
        @Param("so") WarehouseStoreBySupplierOpSO so, @Param("pageNum") Integer pageNum,
        @Param("pageSize") Integer pageSize);

    /**
     * 查询仓库是否有库存
     * 
     * @param
     * @return
     */
    ProductShopStoreReturnDTO findProductStore(ProductWarehouseDTO productWarehouseDTO);

    /**
     * 查找仓库库存
     * 
     * @param warehouseId
     * @return
     */
    List<WarehouseStoreReturnDTO> findWarehouseStore(@Param("warehouseId") Integer warehouseId);

    /**
     * 根据经销商id，仓库id查询仓库商品的库存
     * 
     * @param productStockStoreQuery
     * @return
     */
    PageResult<ProductStockStoreDTO> findProductStoreList(ProductStockStoreQuery productStockStoreQuery);

    PageResult<DynamicProductDTO> findDynamicProductList(DynamicProductQueryDTO dynamicProductQueryDTO);

    /**
     * 根据规格+货主查询仓库库存
     */
    List<ProductStoreBaseDTO> findProductStoreBySpec(@Param("query") ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 查询经销商在指定仓库是否有库存
     * 
     * @return
     */
    ProductStorePO getProductStoreByOwnerIdAndWarehouseId(@Param("warehouseId") Integer warehouseId,
        @Param("ownerId") Long ownerId);

    /**
     * 查询每日拣货盘点产品
     * 
     * @param queryDTO
     * @return
     */
    List<NotOpenStockDailyPickingProductDTO>
        findNotOpenStockDynamicProductList(NotOpenStockDailyPickingProductQueryDTO queryDTO);

    /**
     * 查询残次品批次库存
     * 
     * @param wareHoseInventoryQueryDTO
     * @return
     */
    List<WarehouseStoreDTO> getDisposedProductInventories(
        @Param("query") WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO,
        @Param("subCategoryList") List<Integer> subCategoryList);

    /**
     * 查询残次品批次库存详情
     */
    List<DisposedProductInventorDTO> findDisposedProductInventorDetails(
        @Param("query") WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO,
        @Param("subCategoryList") List<Integer> subCategoryList);

    List<ProductStorePO> findProductStoreByIds(@Param("ids") List<String> ids);

    List<ProductStoreBaseDTO> findStoreBySpecOwner(@Param("query") ProductStoreQueryDTO productStoreQueryDTO);

    /**
     * 根据中台Sku+仓库查询仓库库存
     * 
     * @return
     */
    List<ProductStoreBaseDTO> findProductStoreByUnifySkuId(@Param("query") ProductStoreQueryDTO productStoreQueryDTO);

}

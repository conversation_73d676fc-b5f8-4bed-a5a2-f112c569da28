package com.yijiupi.himalaya.supplychain.inventory.service;

import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.ProductInventoryRecordQueryServiceImpl;

/**
 * <AUTHOR> 2017/12/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class ProductInventoryRecordQueryServiceImplTest {
    @Autowired
    private ProductInventoryRecordQueryServiceImpl productInventoryRecordQueryService;
    //
    // /**
    // * 查询sku在对应城市下所有仓库库存变更记录集合
    // */
    // @Test
    // public void findCityProductInventoryRecordList() {
    // CityInventoryRecordQueryDTO dto = new CityInventoryRecordQueryDTO();
    // dto.setCityId(999);
    // dto.setProductSkuId(99900050733641L);
    // PagerCondition pager = new PagerCondition();
    // pager.setCurrentPage(1);
    // pager.setPageSize(5);
    // PageList<CityInventoryRecordDTO> pageList = productInventoryRecordQueryService.
    // findCityProductInventoryRecordList(dto, pager);
    // }
    //
    // @Test
    // public void findProductStoreRecordList() {
    // ProductStoreRecordSO so = new ProductStoreRecordSO();
    // so.setStoreId("20c718339c4a48f3877943b0d4605ef8");
    // PagerCondition pager = new PagerCondition();
    // pager.setCurrentPage(1);
    // pager.setPageSize(20);
    // PageList<ProductStoreChangeRecordDTO> productStoreRecordList =
    // productInventoryRecordQueryService.findProductStoreRecordList(so, pager, 100000000014951L, 0);
    // }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.DefaultLocationConfigDTO;
import com.yijiupi.himalaya.supplychain.dto.SecOwnerDetailDTO;
import com.yijiupi.himalaya.supplychain.enums.DefaultTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.enums.OutStockMode;
import com.yijiupi.himalaya.supplychain.enums.StoreOrderType;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.instockorder.enums.YesOrNoEnum;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.InventoryChangeFactory;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.SellInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderItemStockInMqDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.OrderStockInMqDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.ErpAwardDeliveryDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.ErpAwardDeliveryItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryOrderDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.InventoryOrderItemDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.*;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderItemDetailDTO;
import com.yijiupi.himalaya.supplychain.pushorder.enums.pushCapabilityTypeEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LoactionDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.ProductSkuDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.ILocationService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IProductSkuQueryService;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品库存转换类
 *
 * <AUTHOR>
 */
@Service
public class WarehouseChangListBOConverter {

    @Autowired
    private InventoryChangeFactory inventoryChangeFactory;
    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;

    @Reference
    private ILocationService iLocationService;

    @Reference
    private IProductSkuQueryService iProductSkuQueryService;

    /**
     * 缺货标记
     *
     * @param sellChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processOutOfStockOrderItemToOrderDeliveryBO(List<SellInventoryChangeBO> sellChangeList,
                                                            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {

        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();
        Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            // 如果不是大宗订单&&存在部分配送,才发送销售库存变更
            if (channel != ProductChannelType.LARGE) {
                BigDecimal deliverCount = deliveryOrderItem.getDeliverCount();// 配送出库数量
                BigDecimal saleSpecQuantity = deliveryOrderItem.getSaleSpecQuantity();
                BigDecimal changeCount = deliverCount.multiply(saleSpecQuantity.multiply(BigDecimal.valueOf(-1)));// 出库--->库存变化数量
                // 负数
                BigDecimal totalCount = deliveryOrderItem.getBuyCount().multiply(saleSpecQuantity);// 订单购买数量
                BigDecimal noDeliveryCount = totalCount.add(changeCount); // >=0 //没有配送的数量

                if (noDeliveryCount.compareTo(BigDecimal.ZERO) != 0) {
                    SellInventoryChangeBO sellInventoryChangeBO =
                            inventoryChangeFactory.createSellInventoryChangeBO(productSkuId, warehouseId, noDeliveryCount);
                    // 根据入参的订单类型来设置
                    sellInventoryChangeBO.setOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
                    sellInventoryChangeBO.setJiupiEventType(JiupiEventType.部分配送返销售库存.getType());
                    sellInventoryChangeBO.setChannel(channel);
                    sellInventoryChangeBO.setDescription("缺货标记返还销售库存");
                    sellInventoryChangeBO.setOwnId(deliveryOrderItem.getOwnerId());
                    sellInventoryChangeBO.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
                    sellInventoryChangeBO.setWarehouseId(inventoryDeliveryJiupiOrder.getWarehouseId());
                    sellInventoryChangeBO.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
                    setSellInventoryChangeBO(inventoryDeliveryJiupiOrder, null, sellInventoryChangeBO);
                    sellInventoryChangeBO.setSendMsgToOms(false);
                    sellChangeList.add(sellInventoryChangeBO);
                }
            }
        }
    }

    /**
     * 下单扣销售库存
     *
     * @param sellChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processReturnSaleInventoryOrderItemToOrderDeliveryBO(List<SellInventoryChangeBO> sellChangeList,
                                                                     InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {

        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();
        Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();

            // 订单购买数量
            BigDecimal totalCount =
                    deliveryOrderItem.getUnitTotalCount() != null ? deliveryOrderItem.getUnitTotalCount()
                            : deliveryOrderItem.getBuyCount().multiply(deliveryOrderItem.getSaleSpecQuantity());

            if (totalCount.compareTo(BigDecimal.ZERO) != 0) {
                SellInventoryChangeBO sellInventoryChangeBO =
                        inventoryChangeFactory.createSellInventoryChangeBO(productSkuId, warehouseId, totalCount);
                // 根据入参的订单类型来设置
                sellInventoryChangeBO.setOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
                if (inventoryDeliveryJiupiOrder.getJiupiEventType() != null) {
                    sellInventoryChangeBO.setJiupiEventType(inventoryDeliveryJiupiOrder.getJiupiEventType());
                } else {
                    sellInventoryChangeBO.setJiupiEventType(JiupiEventType.下单扣销售库存.getType());
                }
                sellInventoryChangeBO.setChannel(channel);
                try {
                    if (StringUtils.isNotEmpty(inventoryDeliveryJiupiOrder.getDescription())) {
                        sellInventoryChangeBO.setDescription(inventoryDeliveryJiupiOrder.getDescription());
                    } else {
                        sellInventoryChangeBO
                                .setDescription(JiupiEventType.getEnum(sellInventoryChangeBO.getJiupiEventType()).name());
                    }

                } catch (Exception oe) {
                    sellInventoryChangeBO.setDescription("下单扣销售库存");
                }
                sellInventoryChangeBO.setOwnId(deliveryOrderItem.getOwnerId());
                sellInventoryChangeBO.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
                sellInventoryChangeBO.setWarehouseId(inventoryDeliveryJiupiOrder.getWarehouseId());
                sellInventoryChangeBO.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
                setSellInventoryChangeBO(inventoryDeliveryJiupiOrder, null, sellInventoryChangeBO);
                sellInventoryChangeBO.setSendMsgToOms(false);
                sellChangeList.add(sellInventoryChangeBO);
            }
        }
    }

    /**
     * 发货BO
     *
     * @param sellChangeList
     * @param warehouseChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processOrderItemToOrderDeliveryBO(List<SellInventoryChangeBO> sellChangeList,
                                                  List<WarehouseInventoryChangeBO> warehouseChangeList, InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder,
                                                  boolean checkWarehouseInventory) {

        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();
        Integer deliveryMode = inventoryDeliveryJiupiOrder.getDeliveryMode();
        Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            BigDecimal deliverCount = deliveryOrderItem.getDeliverCount();// 配送出库数量
            BigDecimal changeCount = deliverCount.multiply(BigDecimal.valueOf(-1));// 出库--->库存变化数量 负数

            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, changeCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(JiupiEventType.仓库发货扣仓库库存.getType());
            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);
            // warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getOrderId().toString());
            // warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setValidateSelf(checkWarehouseInventory);
            warehouseInventoryChange.setChannel(channel);
            // 设置配送类型,出入库类型.
            warehouseInventoryChange.setDeliveryMode(deliveryMode);

            // 不需要更新销售库存
            warehouseInventoryChange.setHasUpdateOPInventory(false);

            // 添加规格Id项
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            // 出库单项id
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getOrderItem_Id());
            warehouseInventoryChange.setOmsOrderItemId(deliveryOrderItem.getOmsOrderItemId());
            // 指定单据类型
            warehouseInventoryChange.setOutInType(outInType.out);
            // 记录原始详细id
            warehouseInventoryChange.setOriginOrderItemDetailId(deliveryOrderItem.getOrderItemDetailId());
            // 记录是否临期
            warehouseInventoryChange.setIsAdvent(Objects.equals(deliveryOrderItem.getIsAdvent(), YesOrNoEnum.YES.getValue().byteValue()) ? true : false);

            warehouseChangeList.add(warehouseInventoryChange);

            BigDecimal totalCount = deliveryOrderItem.getBuyCount();// 订单购买数量
            BigDecimal noDeliveryCount = totalCount.add(changeCount); // >=0 //没有配送的数量
            // 如果不是大宗订单&&存在部分配送,才发送销售库存变更
            if (warehouseInventoryChange.getChannel() != ProductChannelType.LARGE
                    && noDeliveryCount.compareTo(BigDecimal.ZERO) != 0) {
                SellInventoryChangeBO sellInventoryChangeBO =
                        inventoryChangeFactory.createSellInventoryChangeBO(productSkuId, warehouseId, noDeliveryCount);
                // 根据入参的订单类型来设置
                sellInventoryChangeBO.setOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
                sellInventoryChangeBO.setJiupiEventType(JiupiEventType.部分配送返销售库存.getType());
                // 根据jiupiordertype来判断是否为大宗产品
                warehouseInventoryChange.setChannel(channel);
                sellInventoryChangeBO.setDescription("部分发货返还销售库存");
                sellInventoryChangeBO.setOwnId(deliveryOrderItem.getOwnerId());
                sellInventoryChangeBO.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
                sellInventoryChangeBO.setOwnType(OwnerTypeConst.易酒批);
                sellInventoryChangeBO.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
                setSellInventoryChangeBO(inventoryDeliveryJiupiOrder, null, sellInventoryChangeBO);
                sellChangeList.add(sellInventoryChangeBO);
            }

        }
    }

    /**
     * 内配单出库BO
     *
     * @param warehouseChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processOrderItemToAllocationOrderDeliveryBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
                                                            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder, boolean checkWarehouseInventory) {

        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();
        Integer deliveryMode = inventoryDeliveryJiupiOrder.getDeliveryMode();
        Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            BigDecimal deliverCount = deliveryOrderItem.getDeliverCount();// 配送出库数量
            BigDecimal changeCount = deliverCount.multiply(BigDecimal.valueOf(-1));// 出库--->库存变化数量 负数

            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, changeCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(JiupiEventType.内配单出库减库存.getType());
            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);

            warehouseInventoryChange.setValidateSelf(checkWarehouseInventory);
            // 设置配送类型,出入库类型.
            warehouseInventoryChange.setDeliveryMode(deliveryMode);
            // 订单项ID
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getRelationOrderItemId() == null
                    ? deliveryOrderItem.getOrderItem_Id() : deliveryOrderItem.getRelationOrderItemId());
            warehouseInventoryChange.setOmsOrderItemId(deliveryOrderItem.getOmsOrderItemId());

            // 不需要更新销售库存
            warehouseInventoryChange.setHasUpdateOPInventory(false);

            // 添加规格Id项
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOutInType(outInType.out);

            warehouseChangeList.add(warehouseInventoryChange);
        }
    }

    /**
     * 延迟配送BO
     *
     * @param warehouseChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processOrderItemToOrderDeliveryDelayBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
                                                       InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {

        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

            BigDecimal returnCount = deliveryOrderItem.getTakeCount();
            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, returnCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());

            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(JiupiEventType.仓管确认入库.getType());
            warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getRelationOrderId() != null
                    ? inventoryDeliveryJiupiOrder.getRelationOrderId().toString()
                    : inventoryDeliveryJiupiOrder.getOrderId().toString());
            warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setValidateSelf(false);
            // 根据jiupiordertype来判断是否为大宗产品
            warehouseInventoryChange.setChannel(channel);

            // 产品规格id
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getRelationOrderItemId() != null
                    ? deliveryOrderItem.getRelationOrderItemId() : deliveryOrderItem.getOrderItem_Id());
            warehouseInventoryChange.setLocationId(deliveryOrderItem.getLocationId());
            warehouseInventoryChange.setLocationName(deliveryOrderItem.getLocationName());
            warehouseInventoryChange.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
            if (warehouseInventoryChange.getCount() != null
                    && warehouseInventoryChange.getCount().compareTo(BigDecimal.ZERO) > 0) {
                warehouseInventoryChange.setOutInType(outInType.in);
            } else {
                warehouseInventoryChange.setOutInType(outInType.out);
            }

            // 保存生产日期
            if (null != deliveryOrderItem.getProductionDate()) {
                warehouseInventoryChange.setProductionDate(deliveryOrderItem.getProductionDate());
            }

            // 入残次品不发送销售库存变动消息
            if (deliveryOrderItem.getDefective() != null && deliveryOrderItem.getDefective()) {
                warehouseInventoryChange.setHasUpdateOPInventory(false);
            }

            warehouseChangeList.add(warehouseInventoryChange);
        }

    }

    /**
     * 招商订单出库BO
     *
     * @param inventoryDeliveryJiupiOrder
     */
    public List<WarehouseInventoryChangeBO>
    processZhaoShangOrderItemToBO(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
        List<WarehouseInventoryChangeBO> warehouseChangeList = new ArrayList<>();
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

            BigDecimal returnCount = deliveryOrderItem.getTakeCount();
            if (returnCount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, returnCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());

            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(inventoryDeliveryJiupiOrder.getJiupiEventType());
            warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getOrderId().toString());
            warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setValidateSelf(false);
            warehouseInventoryChange.setDescription("自提订单出库扣库存");
            // 根据jiupiordertype来判断是否为大宗产品
            warehouseInventoryChange.setChannel(channel);
            // 需要更新销售库存
            if (warehouseInventoryChange.getChannel().equals(ProductChannelType.LARGE)) {
                warehouseInventoryChange.setHasUpdateOPInventory(false);
            }
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOutInType(outInType.out);
            warehouseChangeList.add(warehouseInventoryChange);
        }
        return warehouseChangeList;
    }

    // 构建boList,(订单完成变更发货中数量)
    public List<WarehouseInventoryChangeBO>
    createDeliveryDetailBOList(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
        List<WarehouseInventoryChangeBO> deliveryBOList = new ArrayList<>();
        List<InventoryDeliveryJiupiOrderItem> items = inventoryDeliveryJiupiOrder.getItems();
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem item : items) {
            Long productSkuId = item.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }

            List<InventoryDeliveryJiupiOrderItemDetailDTO> itemDetails = item.getSecOwnerDetail();
            if (CollectionUtils.isEmpty(itemDetails)) {
                continue;
            }

            for (InventoryDeliveryJiupiOrderItemDetailDTO itemDetail : itemDetails) {
                Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
                // 用来操作 发货中库存记录
                WarehouseInventoryChangeBO deliveryBO =
                        inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId,
                                itemDetail.getDispatchCount(), channel, itemDetail.getOwnerId(), itemDetail.getSecOwnerId());

                setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, deliveryBO);
                // 根据入参的订单类型来设置
                deliveryBO.setOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
                deliveryBO.setOrderId(inventoryDeliveryJiupiOrder.getOrderId().toString());
                deliveryBO.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
                deliveryBO.setValidateSelf(false);
                deliveryBO.setChannel(channel);
                // 设置配送类型,出入库类型.
                deliveryBO.setDeliveryMode(inventoryDeliveryJiupiOrder.getDeliveryMode());

                deliveryBO.setProductSpecificationId(item.getProductSpecification_Id());
                deliveryBO.setSource(ProductSourceType.易酒批);
                deliveryBO.setOutInType(outInType.out);

                deliveryBOList.add(deliveryBO);
            }
        }
        return deliveryBOList;
    }

    // 构建boList,(订单完成变更发货中数量)
    public List<WarehouseInventoryChangeBO>
    createDeliveryBOList(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
        List<WarehouseInventoryChangeBO> deliveryBOList = new ArrayList<>();
        List<InventoryDeliveryJiupiOrderItem> items = inventoryDeliveryJiupiOrder.getItems();
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem item : items) {
            Long productSkuId = item.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            BigDecimal saleSpecQuantity = item.getSaleSpecQuantity();
            Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
            // 用来操作 发货中库存记录
            WarehouseInventoryChangeBO deliveryBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(
                    productSkuId, warehouseId, item.getDeliverCount().multiply(saleSpecQuantity), channel,
                    item.getOwnerId(), item.getSecOwnerId());

            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, deliveryBO);
            // 根据入参的订单类型来设置
            deliveryBO.setOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
            deliveryBO.setOrderId(inventoryDeliveryJiupiOrder.getOrderId().toString());
            deliveryBO.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            deliveryBO.setValidateSelf(false);
            deliveryBO.setChannel(channel);
            // 设置配送类型,出入库类型.
            deliveryBO.setDeliveryMode(inventoryDeliveryJiupiOrder.getDeliveryMode());

            deliveryBO.setProductSpecificationId(item.getProductSpecification_Id());
            deliveryBO.setSource(ProductSourceType.易酒批);
            deliveryBO.setOutInType(outInType.out);

            deliveryBOList.add(deliveryBO);
        }
        return deliveryBOList;
    }

    // 构建boList,(订单完成变更发货中数量)
    public List<WarehouseInventoryChangeBO> createEasyGoDeliveryBOList(OrderStockInMqDTO inventoryDeliveryJiupiOrder) {
        List<WarehouseInventoryChangeBO> deliveryBOList = new ArrayList<>();
        List<OrderItemStockInMqDTO> items = inventoryDeliveryJiupiOrder.getItems();
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (OrderItemStockInMqDTO item : items) {
            Long productSkuId = item.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = ProductChannelType.JIUPI;
            // 用来操作 发货中库存记录
            WarehouseInventoryChangeBO deliveryBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(
                    productSkuId, warehouseId, item.getDeliverCount().multiply(item.getSaleSpecQuantity()), channel,
                    item.getOwnerId(), item.getSecOwnerId());

            setEasyGoWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, deliveryBO);
            // 根据入参的订单类型来设置
            deliveryBO.setOrderType(StoreOrderType.知花知果订单);
            deliveryBO.setOrderId(inventoryDeliveryJiupiOrder.getOrderId().toString());
            deliveryBO.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            deliveryBO.setValidateSelf(false);
            deliveryBO.setChannel(channel);

            deliveryBO.setProductSpecificationId(item.getProductSpecificationId());
            deliveryBO.setSource(ProductSourceType.知花知果);
            deliveryBO.setOutInType(outInType.out);

            deliveryBOList.add(deliveryBO);
        }
        return deliveryBOList;
    }

    // 构建boList,(兑奖订单订单完成变更发货中数量)
    public List<WarehouseInventoryChangeBO> createAwardDeliveryBOList(ErpAwardDeliveryDTO erpAwardDeliveryDTO) {
        List<WarehouseInventoryChangeBO> deliveryBOList = new ArrayList<>();
        List<ErpAwardDeliveryItemDTO> items = erpAwardDeliveryDTO.getItemList();
        Integer warehouseId = erpAwardDeliveryDTO.getWarehouseId();

        for (ErpAwardDeliveryItemDTO item : items) {
            Long productSkuId = item.getProductId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = 0;
            // 用来操作 发货中库存记录
            WarehouseInventoryChangeBO deliveryBO =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId,
                            item.getMinUnitTotalCount(), channel, item.getOwnerId(), item.getSecOwnerId());

            deliveryBO.setCityId(erpAwardDeliveryDTO.getCityId());
            deliveryBO.setOrderId(erpAwardDeliveryDTO.getId());
            deliveryBO.setOrderNo(erpAwardDeliveryDTO.getOrderNo());
            deliveryBO.setCreateUserId(erpAwardDeliveryDTO.getUserId());
            deliveryBO.setCreateUserName(erpAwardDeliveryDTO.getUserId());
            // 根据入参的订单类型来设置
            deliveryBO.setOrderType(
                    com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum.ORDER_TYPE_AWARD);
            deliveryBO.setOrderId(erpAwardDeliveryDTO.getId());
            deliveryBO.setOrderNo(erpAwardDeliveryDTO.getOrderNo());
            deliveryBO.setValidateSelf(false);
            deliveryBO.setChannel(channel);

            deliveryBO.setProductSpecificationId(item.getSpecificationId());
            deliveryBO.setSource(ProductSourceType.易酒批);
            deliveryBO.setOutInType(outInType.out);

            deliveryBOList.add(deliveryBO);
        }
        return deliveryBOList;
    }

    /**
     * 根据jiupiordertype获取库存渠道类型
     *
     * @param jiuPiOrderType
     * @return
     */
    public Integer getChannelByJiuPiOrderType(Integer jiuPiOrderType) {
        return com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum.ORDER_TYPE_WHOLESALE == jiuPiOrderType
                ? ProductChannelType.LARGE : ProductChannelType.JIUPI;
    }

    public void setWarehouseInventoryChangeBO(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder,
                                              ProductInventoryPO productInventoryPO, WarehouseInventoryChangeBO warehouseInventoryChange) {
        warehouseInventoryChange.setCityId(inventoryDeliveryJiupiOrder.getCityId());
        warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getRelationOrderId() == null
                ? String.valueOf(inventoryDeliveryJiupiOrder.getOrderId())
                : String.valueOf(inventoryDeliveryJiupiOrder.getRelationOrderId()));
        warehouseInventoryChange.setOmsOrderId(inventoryDeliveryJiupiOrder.getOmsOrderId());
        warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
        warehouseInventoryChange.setCreateUserId(inventoryDeliveryJiupiOrder.getCreateUserId());
        warehouseInventoryChange.setCreateUserName(inventoryDeliveryJiupiOrder.getCreateUserName());
        if (productInventoryPO != null) {
            warehouseInventoryChange.setOwnType(productInventoryPO.getOwnerType());
            warehouseInventoryChange.setOwnId(productInventoryPO.getOwnerId());
            warehouseInventoryChange.setSecOwnerId(productInventoryPO.getSecOwnerId());
            warehouseInventoryChange.setProductSpecificationId(productInventoryPO.getProductSpecificationId());
        }
    }

    public void setEasyGoWarehouseInventoryChangeBO(OrderStockInMqDTO inventoryDeliveryJiupiOrder,
                                                    ProductInventoryPO productInventoryPO, WarehouseInventoryChangeBO warehouseInventoryChange) {
        warehouseInventoryChange.setCityId(inventoryDeliveryJiupiOrder.getCityId());
        warehouseInventoryChange.setOrderId(String.valueOf(inventoryDeliveryJiupiOrder.getOrderId()));
        warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
        if (productInventoryPO != null) {
            warehouseInventoryChange.setOwnType(productInventoryPO.getOwnerType());
            warehouseInventoryChange.setOwnId(productInventoryPO.getOwnerId());
            warehouseInventoryChange.setSecOwnerId(productInventoryPO.getSecOwnerId());
            warehouseInventoryChange.setProductSpecificationId(productInventoryPO.getProductSpecificationId());
        }
    }

    public void setSellInventoryChangeBO(InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder,
                                         ProductInventoryPO productInventoryPO, SellInventoryChangeBO sellInventoryChangeBO) {
        sellInventoryChangeBO.setCityId(inventoryDeliveryJiupiOrder.getCityId());
        sellInventoryChangeBO.setOrderId(String.valueOf(inventoryDeliveryJiupiOrder.getOrderId()));
        sellInventoryChangeBO.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
        sellInventoryChangeBO.setOmsOrderId(inventoryDeliveryJiupiOrder.getOmsOrderId());
        sellInventoryChangeBO.setCreateUserId(inventoryDeliveryJiupiOrder.getCreateUserId());
        sellInventoryChangeBO.setCreateUserName(inventoryDeliveryJiupiOrder.getCreateUserName());
        if (productInventoryPO != null) {
            sellInventoryChangeBO.setOwnId(productInventoryPO.getOwnerId());
            sellInventoryChangeBO.setSecOwnerId(productInventoryPO.getSecOwnerId());
            sellInventoryChangeBO.setOwnType(productInventoryPO.getOwnerType());
            sellInventoryChangeBO.setProductSpecificationId(productInventoryPO.getProductSpecificationId());
        }
    }

    /**
     * 财务确认收款BO
     *
     * @param warehouseChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processInStockOrderItemToBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
                                            InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

            BigDecimal returnCount = deliveryOrderItem.getTakeCount();
            if (returnCount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, returnCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());

            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(inventoryDeliveryJiupiOrder.getJiupiEventType());
            warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getRelationOrderId() != null
                    ? inventoryDeliveryJiupiOrder.getRelationOrderId().toString()
                    : inventoryDeliveryJiupiOrder.getOrderId().toString());
            warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setValidateSelf(false);
            warehouseInventoryChange.setDescription("出纳确认收款");
            // 根据jiupiordertype来判断是否为大宗产品
            warehouseInventoryChange.setChannel(channel);
            // 需要更新销售库存
            if (warehouseInventoryChange.getChannel().equals(ProductChannelType.LARGE)) {
                warehouseInventoryChange.setHasUpdateOPInventory(false);
            }
            warehouseInventoryChange.setLocationId(deliveryOrderItem.getLocationId());
            warehouseInventoryChange.setLocationName(deliveryOrderItem.getLocationName());
            warehouseInventoryChange.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
            // 产品规格id
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getRelationOrderItemId() != null
                    ? deliveryOrderItem.getRelationOrderItemId() : deliveryOrderItem.getOrderItem_Id());
            if (warehouseInventoryChange.getCount() != null
                    && warehouseInventoryChange.getCount().compareTo(BigDecimal.ZERO) > 0) {
                warehouseInventoryChange.setOutInType(outInType.in);
            } else {
                warehouseInventoryChange.setOutInType(outInType.out);
            }

            // 2022-01-12 给SKUID赋值，后续生上架任务处理销售库存用
            warehouseInventoryChange.setProductSkuId(deliveryOrderItem.getProductSkuId());
            warehouseChangeList.add(warehouseInventoryChange);
        }
    }

    /**
     * 自提订单出库BO
     *
     * @param warehouseChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processZiTiOrderItemToBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
                                         InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

            BigDecimal returnCount = deliveryOrderItem.getTakeCount();
            if (returnCount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, returnCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());

            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(inventoryDeliveryJiupiOrder.getJiupiEventType());
            warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getOrderId().toString());
            warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setOmsOrderId(inventoryDeliveryJiupiOrder.getOmsOrderId());
            warehouseInventoryChange.setValidateSelf(false);
            warehouseInventoryChange.setDescription("自提订单出库扣库存");
            // 根据jiupiordertype来判断是否为大宗产品
            warehouseInventoryChange.setChannel(channel);
            // 需要更新销售库存
            if (warehouseInventoryChange.getChannel().equals(ProductChannelType.LARGE)) {
                warehouseInventoryChange.setHasUpdateOPInventory(false);
            }
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getOrderItem_Id());
            warehouseInventoryChange.setOmsOrderItemId(deliveryOrderItem.getOmsOrderItemId());
            warehouseInventoryChange.setOutInType(outInType.out);
            warehouseChangeList.add(warehouseInventoryChange);
        }
    }

    /**
     * 订单召回BO
     *
     * @param warehouseChangeList
     * @param inventoryDeliveryJiupiOrder
     */
    public void processRecallDeliverOrderItemToBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
                                                  InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder) {
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

            BigDecimal returnCount = deliveryOrderItem.getTakeCount();
            if (returnCount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, returnCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());

            setWarehouseInventoryChangeBO(inventoryDeliveryJiupiOrder, null, warehouseInventoryChange);
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(JiupiEventType.订单召回.getType());
            warehouseInventoryChange.setOrderId(inventoryDeliveryJiupiOrder.getRelationOrderId() != null
                    ? inventoryDeliveryJiupiOrder.getRelationOrderId().toString()
                    : inventoryDeliveryJiupiOrder.getOrderId().toString());
            warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setOmsOrderId(inventoryDeliveryJiupiOrder.getOmsOrderId());
            warehouseInventoryChange.setValidateSelf(false);
            warehouseInventoryChange.setDescription("延迟配送订单召回扣库存");
            // 根据jiupiordertype来判断是否为大宗产品
            warehouseInventoryChange.setChannel(channel);
            // 需要更新销售库存
            if (warehouseInventoryChange.getChannel().equals(ProductChannelType.LARGE)) {
                warehouseInventoryChange.setHasUpdateOPInventory(false);
            }
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getOrderItem_Id());
            warehouseInventoryChange.setOmsOrderItemId(deliveryOrderItem.getOmsOrderItemId());
            warehouseInventoryChange.setOutInType(outInType.out);
            warehouseChangeList.add(warehouseInventoryChange);
        }
    }

    /**
     * 内配单BO
     *
     * @param warehouseChangeList
     * @param inventoryOrder
     */
    public void processOrderItemToOrderInternalDeliveryBO(List<WarehouseInventoryChangeBO> warehouseChangeList,
                                                          InventoryOrderDTO inventoryOrder, Integer toOrgId, Integer toWarehouseId, Boolean isVehicleTransshipment,
                                                          String remark) {
        Long locationId = null;
        String locationName = null;
        for (InventoryOrderItemDTO orderItem : inventoryOrder.getItems()) {
            Long productSkuId = orderItem.getSkuId();
            if (productSkuId == null) {
                continue;
            }
            int orderType = inventoryOrder.getOrderType() == null ? 0 : inventoryOrder.getOrderType();
            Integer channel = getChannelByJiuPiOrderType(orderType);

            WarehouseInventoryChangeBO warehouseInventoryChange = new WarehouseInventoryChangeBO();
            warehouseInventoryChange.setProductSkuId(productSkuId);
            warehouseInventoryChange.setWarehouseId(toWarehouseId);
            warehouseInventoryChange.setChannel(channel);
            warehouseInventoryChange.setOwnId(orderItem.getOwnerId());
            warehouseInventoryChange.setSecOwnerId(orderItem.getSecOwnerId());
            warehouseInventoryChange.setCityId(toOrgId);

            // 根据入参的订单类型来设置
            warehouseInventoryChange.setOrderType(OrderTypeConvert.convertOrderType(orderType));
            warehouseInventoryChange
                    .setOrderId(inventoryOrder.getId() == null ? null : inventoryOrder.getId().toString());
            warehouseInventoryChange.setOrderNo(inventoryOrder.getRefOrderNo());
            warehouseInventoryChange.setOmsOrderId(inventoryOrder.getOmsOrderId());
            warehouseInventoryChange.setValidateSelf(false);
            // 入库总数量
            BigDecimal changeCount = orderItem.getUnitTotalCount() == null
                    ? orderItem.getSaleCount().multiply(orderItem.getSaleSpecQuantity()) : orderItem.getUnitTotalCount();
            // 残次品数量
            BigDecimal defectiveTotalCount =
                    ObjectUtils.defaultIfNull(orderItem.getDefectiveTotalCount(), BigDecimal.ZERO);
            if (changeCount.compareTo(defectiveTotalCount) < 0) {
                throw new BusinessValidateException(String.format("订单 %s 产品 [%s] - [%s] 残次品数量大于入库数量！",
                        inventoryOrder.getRefOrderNo(), orderItem.getProductName(), orderItem.getSkuId()));
            }
            if (defectiveTotalCount.compareTo(BigDecimal.ZERO) > 0) {
                // 有残次品:扣除残次品数量
                changeCount = changeCount.subtract(defectiveTotalCount);
            }
            if (Objects.equals(OrderConstant.ALLOT_TYPE_DELIVERY_RETURN, inventoryOrder.getAllotType())
                    || Objects.equals(OrderConstant.ALLOT_TYPE_ALLOCATION_RETURN, inventoryOrder.getAllotType())
                    || Objects.equals(OrderConstant.ALLOT_TYPE_DELIVERY_RETURN_NPTR, inventoryOrder.getAllotType())) {
                warehouseInventoryChange.setHasUpdateOPInventory(true);
                warehouseInventoryChange.setAllotReturn(true);
                // 查找默认内配退货暂存位
                if (locationId == null) {
                    DefaultLocationConfigDTO defaultLocationConfig = inventoryOrderBizBL
                            .findDefaultLocationConfig(toWarehouseId, DefaultTypeEnum.内配单退货暂存位.getType());
                    locationId = defaultLocationConfig.getTemporaryLocationId();
                    locationName = defaultLocationConfig.getTemporaryLocationName();
                }

                // 内配退入库默认内配单相关暂存位
                orderItem.setLocationId(locationId);
                orderItem.setLocationName(locationName);
            } else {
                warehouseInventoryChange.setHasUpdateOPInventory(false);
                // 普通内配入会传入订单对应周转区，如果没有则查找默认内配收货位,整车转运会由前端传入周转区
                if (orderItem.getLocationId() == null && locationId == null) {
                    DefaultLocationConfigDTO defaultLocationConfig = inventoryOrderBizBL
                            .findDefaultLocationConfig(toWarehouseId, DefaultTypeEnum.内配单收货暂存位.getType());
                    locationId = defaultLocationConfig.getTemporaryLocationId();
                    locationName = defaultLocationConfig.getTemporaryLocationName();
                }
                // 后续更新出库单出库位时使用
                if (orderItem.getLocationId() == null) {
                    orderItem.setLocationId(locationId);
                    orderItem.setLocationName(locationName);
                }
            }
            warehouseInventoryChange.setCount(changeCount);
            warehouseInventoryChange
                    .setLocationId(orderItem.getLocationId() == null ? locationId : orderItem.getLocationId());
            warehouseInventoryChange
                    .setLocationName(orderItem.getLocationName() == null ? locationName : orderItem.getLocationName());
            warehouseInventoryChange.setJiupiEventType(JiupiEventType.内配单入库加库存.getType());
            warehouseInventoryChange.setDescription(remark);

            // 产品规格id
            warehouseInventoryChange.setProductSpecificationId(orderItem.getProductSpecification_Id());
            warehouseInventoryChange.setSource(ProductSourceType.易酒批);
            warehouseInventoryChange.setOrderItemId(orderItem.getId());
            warehouseInventoryChange.setOmsOrderItemId(orderItem.getOmsOrderItemId());

            // 内配单入库需要关联货位
            if (Objects.equals(OrderConstant.ALLOT_TYPE_ALLOCATION, inventoryOrder.getAllotType())) {
                warehouseInventoryChange.setAssociateProductLocation(true);
            }
            warehouseInventoryChange.setOutInType(outInType.in);
            WarehouseInventoryChangeBO defectiveChangeBO =
                    createDefectiveWarehouseInventoryChangeBO(warehouseInventoryChange, orderItem);
            if (defectiveChangeBO != null) {
                warehouseChangeList.add(defectiveChangeBO);
            }

            // 2022-01-12 给SKUID赋值，后续生上架任务处理销售库存用
            warehouseInventoryChange.setProductSkuId(orderItem.getSkuId());
            // 保存生产日期
            if (null != orderItem.getProductionDate()) {
                warehouseInventoryChange.setProductionDate(orderItem.getProductionDate());
            }
            warehouseChangeList.add(warehouseInventoryChange);
        }
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            // 移除 count 为 0 的数据
            warehouseChangeList.removeIf(bo -> bo != null
                    && ObjectUtils.defaultIfNull(bo.getCount(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0);
        }
    }

    private WarehouseInventoryChangeBO createDefectiveWarehouseInventoryChangeBO(
            WarehouseInventoryChangeBO qualityWarehouseInventoryChange, InventoryOrderItemDTO orderItem) {
        if (orderItem.getDefectiveTotalCount() == null
                || orderItem.getDefectiveTotalCount().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        WarehouseInventoryChangeBO defectiveChangeBO = new WarehouseInventoryChangeBO();
        BeanUtils.copyProperties(qualityWarehouseInventoryChange, defectiveChangeBO);
        defectiveChangeBO.setCount(orderItem.getDefectiveTotalCount());
        // 残次品不加销售库存
        defectiveChangeBO.setHasUpdateOPInventory(false);
        if (orderItem.getDefectiveLocationId() != null) {
            defectiveChangeBO.setLocationId(orderItem.getDefectiveLocationId());
            defectiveChangeBO.setLocationName(orderItem.getDefectiveLocationName());
        }
        // 保存生产日期
        if (null != orderItem.getProductionDate()) {
            defectiveChangeBO.setProductionDate(orderItem.getProductionDate());
        }
        return defectiveChangeBO;
    }

    public static List<WarehouseInventoryChangeBO> warehouseInventoryChangeDTOS2WarehouseChangListBO(
            List<WarehouseInventoryChangeDTO> warehouseInventoryChangeDTOS) {
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(warehouseInventoryChangeDTOS)) {
            warehouseInventoryChangeDTOS.forEach(warehouseInventoryChangeDTO -> {
                WarehouseInventoryChangeBO warehouseInventoryChangeBO = new WarehouseInventoryChangeBO();
                BeanUtils.copyProperties(warehouseInventoryChangeDTO, warehouseInventoryChangeBO);

                warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);
            });
        }
        return warehouseInventoryChangeBOS;
    }

    public void directProcessingOrderToOrderDeliveryBO(List<SellInventoryChangeBO> sellChangeList,
                                                       List<WarehouseInventoryChangeBO> warehouseChangeList, InventoryDeliveryJiupiOrder inventoryDeliveryJiupiOrder,
                                                       boolean checkWarehouseInventory) {
        Integer warehouseId = inventoryDeliveryJiupiOrder.getWarehouseId();
        Integer channel = getChannelByJiuPiOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());

        for (InventoryDeliveryJiupiOrderItem deliveryOrderItem : inventoryDeliveryJiupiOrder.getItems()) {
            Long productSkuId = deliveryOrderItem.getProductSkuId();
            if (productSkuId == null) {
                continue;
            }
            byte outIn = outInType.out;
            Integer jiupiEventType = inventoryDeliveryJiupiOrder.getJiupiEventType();
            // 出库--->库存变化数量 负数
            BigDecimal changeCount = deliveryOrderItem.getTakeCount().negate();
            // 入库库--->库存变化数量 正数
            if (jiupiEventType.equals(JiupiEventType.易款便利线下退货单返仓库库存.getType())
                    || jiupiEventType.equals(JiupiEventType.退货单返仓库库存.getType())
                    || jiupiEventType.equals(JiupiEventType.供应链调拨.getType())) {
                outIn = outInType.in;
                changeCount = changeCount.abs();
                SellInventoryChangeBO sellInventoryChangeBO =
                        inventoryChangeFactory.createSellInventoryChangeBO(productSkuId, warehouseId, changeCount);
                // 根据入参的订单类型来设置
                sellInventoryChangeBO.setOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType());
                sellInventoryChangeBO.setJiupiEventType(jiupiEventType);
                sellInventoryChangeBO.setDescription(JiupiEventType.getEnum(jiupiEventType).name());
                sellInventoryChangeBO.setOwnId(deliveryOrderItem.getOwnerId());
                sellInventoryChangeBO.setSecOwnerId(deliveryOrderItem.getSecOwnerId());
                sellInventoryChangeBO.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
                setSellInventoryChangeBO(inventoryDeliveryJiupiOrder, null, sellInventoryChangeBO);
                sellChangeList.add(sellInventoryChangeBO);
            }

            WarehouseInventoryChangeBO warehouseInventoryChange =
                    inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, changeCount, channel,
                            deliveryOrderItem.getOwnerId(), deliveryOrderItem.getSecOwnerId());
            // 根据入参的订单类型来设置
            warehouseInventoryChange
                    .setOrderType(OrderTypeConvert.convertOrderType(inventoryDeliveryJiupiOrder.getJiupiOrderType()));
            warehouseInventoryChange.setJiupiEventType(jiupiEventType);
            warehouseInventoryChange.setCityId(inventoryDeliveryJiupiOrder.getCityId());
            warehouseInventoryChange.setOrderId(String.valueOf(inventoryDeliveryJiupiOrder.getOrderId()));
            warehouseInventoryChange.setOrderNo(inventoryDeliveryJiupiOrder.getOrderNo());
            warehouseInventoryChange.setOmsOrderId(inventoryDeliveryJiupiOrder.getOmsOrderId());
            warehouseInventoryChange.setCreateUserId(inventoryDeliveryJiupiOrder.getCreateUserId());
            warehouseInventoryChange.setCreateUserName(inventoryDeliveryJiupiOrder.getCreateUserName());
            warehouseInventoryChange.setProductSpecificationId(deliveryOrderItem.getProductSpecification_Id());
            warehouseInventoryChange.setValidateSelf(checkWarehouseInventory);
            warehouseInventoryChange.setChannel(channel);
            warehouseInventoryChange.setOrderItemId(deliveryOrderItem.getOrderItem_Id());
            warehouseInventoryChange.setOmsOrderItemId(deliveryOrderItem.getOmsOrderItemId());
            warehouseInventoryChange.setOutInType(outIn);

            // 不需要更新销售库存
            warehouseInventoryChange.setHasUpdateOPInventory(false);
            if(Objects.equals(pushCapabilityTypeEnum.直接出库.getType(), inventoryDeliveryJiupiOrder.getCapabilityType())
                    && Objects.equals(inventoryDeliveryJiupiOrder.getOrderSourceType(), SourceType.WRONG_DELIVERY.getValue())){
                warehouseInventoryChange.setHasUpdateOPInventory(true);
                warehouseInventoryChange.setSendDetailToOMS(false);
            }

            // 记录订单能力类型
            warehouseInventoryChange.setCapabilityType(inventoryDeliveryJiupiOrder.getCapabilityType());
            if (Objects.equals(pushCapabilityTypeEnum.奖券差异出库.getType(), warehouseInventoryChange.getCapabilityType())
                    || Objects.equals(pushCapabilityTypeEnum.奖券出库.getType(),
                    warehouseInventoryChange.getCapabilityType())) {
                warehouseInventoryChange.setSendDetailToOMS(false);
            }

            // 保存生产日期
            if (Objects.nonNull(deliveryOrderItem.getProductionDate())) {
                warehouseInventoryChange.setProductionDate(deliveryOrderItem.getProductionDate());
            }

            warehouseChangeList.add(warehouseInventoryChange);
        }
    }

    public List<WarehouseInventoryChangeBO>
    processOutStockOrderInventoryDTOToWarehouseInventoryChangeBOS(ProcessOutStockOrderInventoryDTO processDTO) {
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(processDTO.getOutStockOrderDTOList())) {
            return warehouseInventoryChangeBOS;
        }

        // 查询残次品陈列品货位
        LocationInfoQueryDTO locationInfoQueryDTO = new LocationInfoQueryDTO();
        List<Byte> subCategoryList = new ArrayList<>();
        subCategoryList.add(LocationEnum.残次品位.getType().byteValue());
        subCategoryList.add(LocationEnum.陈列品位.getType().byteValue());
        locationInfoQueryDTO.setSubcategoryList(subCategoryList);
        locationInfoQueryDTO.setWarehouseId(processDTO.getOutStockOrderDTOList().get(0).getWarehouseId());
        locationInfoQueryDTO.setCityId(processDTO.getOutStockOrderDTOList().get(0).getOrgId());
        PageList<LoactionDTO> locationPageList = iLocationService.pageListLocation(locationInfoQueryDTO);
        List<Long> clLocationIds = new ArrayList<>();
        if (locationPageList != null && CollectionUtils.isNotEmpty(locationPageList.getDataList())) {
            clLocationIds =
                    locationPageList.getDataList().stream().map(LoactionDTO::getId).collect(Collectors.toList());
        }

        // 补齐sku信息
        List<Long> skuIds = processDTO.getOutStockOrderDTOList().stream()
                .flatMap(order -> order.getOutStockOrderItemDTOS().stream()).filter(item -> item.getSkuId() != null)
                .map(OutStockOrderItemDTO::getSkuId).collect(Collectors.toList());
        List<OutStockOrderItemDTO> noSkuIdItems =
                processDTO.getOutStockOrderDTOList().stream().flatMap(order -> order.getOutStockOrderItemDTOS().stream())
                        .filter(item -> item.getSkuId() == null).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noSkuIdItems)) {
            throw new BusinessException("skuId不能为空,数据:" + JSON.toJSONString(noSkuIdItems));
        }

        List<ProductSkuDTO> productSkuDTOS = iProductSkuQueryService.findBySku(skuIds);

        if (CollectionUtils.isEmpty(productSkuDTOS)) {
            throw new BusinessException("产品信息不存在");
        }

        Map<Long, List<ProductSkuDTO>> productSkuMap =
                productSkuDTOS.stream().collect(Collectors.groupingBy(ProductSkuDTO::getProductSkuId));

        List<Long> finalClLocationIds = clLocationIds;
        processDTO.getOutStockOrderDTOList().forEach(outStockOrder -> {
            if (CollectionUtils.isNotEmpty(outStockOrder.getOutStockOrderItemDTOS())) {
                outStockOrder.getOutStockOrderItemDTOS().forEach(item -> {
                    Long productSkuId = item.getSkuId();
                    AssertUtils.notEmpty(productSkuMap.get(productSkuId), "产品信息不存在,skuId:" + productSkuId);

                    ProductSkuDTO productSku = productSkuMap.get(productSkuId).get(0);

                    if (CollectionUtils.isNotEmpty(item.getOutStockOrderItemDetailDTOS())) {
                        item.getOutStockOrderItemDetailDTOS().forEach(detail -> {
                            // 出库扣库存，取反
                            BigDecimal changeCount = detail.getUnitTotalCount().negate();
                            Integer channel = item.getChannel() == null ? ProductChannelType.JIUPI : item.getChannel();

                            // 预防连锁异常数据
                            Long ownerId = detail.getOwnerId();
                            if (ownerId == null) {
                                ownerId = item.getOwnerId();
                            }
                            WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory
                                    .createWarehouseInventoryChangeBO(productSkuId, outStockOrder.getWarehouseId(),
                                            changeCount, channel, ownerId, detail.getSecOwnerId());

                            warehouseInventoryChangeBO.setCityId(outStockOrder.getOrgId());
                            warehouseInventoryChangeBO.setOrderId(outStockOrder.getId());
                            warehouseInventoryChangeBO.setOrderNo(outStockOrder.getRefOrderNo());
                            warehouseInventoryChangeBO.setOrderItemId(detail.getOutStockOrderItemId());
                            warehouseInventoryChangeBO
                                    .setOrderType(outStockOrderType2ErpType(outStockOrder.getOrderType()));
                            warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.供应链库存同步.getType());
                            warehouseInventoryChangeBO.setErpEventType(processDTO.getErpEventType());
                            warehouseInventoryChangeBO.setCreateUserName(outStockOrder.getCreateUser());
                            warehouseInventoryChangeBO.setDescription(processDTO.getDescription() == null
                                    ? getDescriptionByOutOrderType(outStockOrder.getOrderType(),
                                    processDTO.getErpEventType())
                                    : processDTO.getDescription());
                            warehouseInventoryChangeBO.setValidateSelf(outStockOrder.getNeedCheckStoreCount() == null
                                    ? false : outStockOrder.getNeedCheckStoreCount());
                            warehouseInventoryChangeBO.setLocationId(detail.getLocationId());
                            warehouseInventoryChangeBO.setLocationName(detail.getLocationName());
                            warehouseInventoryChangeBO
                                    .setProductSpecificationId(productSku.getProductSpecificationId());
                            warehouseInventoryChangeBO.setOutInType(outInType.out);
                            warehouseInventoryChangeBO
                                    .setAllocationCalculation(outStockOrder.getAllocationCalculation());
                            warehouseInventoryChangeBO.setBusinessType(outStockOrder.getBusinessType());

                            // 计算批属性编号用
                            warehouseInventoryChangeBO.setProductionDate(detail.getProductionDate());
                            warehouseInventoryChangeBO.setBatchTime(detail.getBatchTime());
                            warehouseInventoryChangeBO.setBatchNo(detail.getBatchAttributeInfoNo());

                            // 判断是否处理销售库存
                            warehouseInventoryChangeBO
                                    .setHasUpdateOPInventory(needChangeSaleStore(processDTO.getNeedToChangeSaleStore(),
                                            detail.getLocationId(), outStockOrder.getOrderType(), finalClLocationIds));

                            // 判断是否发送订单明细变更到OMS
                            warehouseInventoryChangeBO.setSendDetailToOMS(processDTO.getSendDetailToOMS());
                            warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);
                        });
                    } else {
                        // 出库扣库存，取反
                        BigDecimal changeCount = item.getUnitTotalCount().negate();
                        Integer channel = item.getChannel() == null ? ProductChannelType.JIUPI : item.getChannel();

                        WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory
                                .createWarehouseInventoryChangeBO(productSkuId, outStockOrder.getWarehouseId(), changeCount,
                                        channel, item.getOwnerId(), item.getSecOwnerId());

                        warehouseInventoryChangeBO.setCityId(outStockOrder.getOrgId());
                        warehouseInventoryChangeBO.setOrderId(outStockOrder.getId());
                        warehouseInventoryChangeBO.setOrderNo(outStockOrder.getRefOrderNo());
                        warehouseInventoryChangeBO
                                .setOrderType(outStockOrderType2ErpType(outStockOrder.getOrderType()));
                        warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.供应链库存同步.getType());
                        warehouseInventoryChangeBO.setErpEventType(processDTO.getErpEventType());
                        warehouseInventoryChangeBO.setCreateUserName(outStockOrder.getCreateUser());
                        warehouseInventoryChangeBO.setDescription(processDTO.getDescription() == null
                                ? getDescriptionByOutOrderType(outStockOrder.getOrderType(), processDTO.getErpEventType())
                                : processDTO.getDescription());
                        warehouseInventoryChangeBO.setValidateSelf(outStockOrder.getNeedCheckStoreCount() == null
                                ? false : outStockOrder.getNeedCheckStoreCount());
                        warehouseInventoryChangeBO.setLocationId(item.getLocationid());
                        warehouseInventoryChangeBO.setLocationName(item.getLocationname());
                        warehouseInventoryChangeBO.setProductSpecificationId(productSku.getProductSpecificationId());
                        warehouseInventoryChangeBO.setOutInType(outInType.out);
                        warehouseInventoryChangeBO.setOrderItemId(Long.valueOf(item.getId()));
                        warehouseInventoryChangeBO.setAllocationCalculation(outStockOrder.getAllocationCalculation());
                        warehouseInventoryChangeBO.setBusinessType(outStockOrder.getBusinessType());

                        // 计算批属性编号用
                        warehouseInventoryChangeBO.setProductionDate(item.getProductionDate());
                        warehouseInventoryChangeBO.setBatchTime(item.getBatchTime());

                        // 判断是否处理销售库存
                        warehouseInventoryChangeBO
                                .setHasUpdateOPInventory(needChangeSaleStore(processDTO.getNeedToChangeSaleStore(),
                                        item.getLocationid(), outStockOrder.getOrderType(), finalClLocationIds));

                        // 判断是否发送订单明细变更到OMS
                        warehouseInventoryChangeBO.setSendDetailToOMS(processDTO.getSendDetailToOMS());
                        warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);
                    }

                });
            }
        });
        return warehouseInventoryChangeBOS;
    }

    public List<WarehouseInventoryChangeBO>
    processInStockOrderInventoryDTOToWarehouseInventoryChangeBOS(ProcessInStockOrderInventoryDTO processDTO) {
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(processDTO.getInStockOrderDTOList())) {
            return warehouseInventoryChangeBOS;
        }
        List<InStockOrderDTO> inStockOrderDTOList = processDTO.getInStockOrderDTOList();
        // 补齐sku信息
        List<Long> skuIds = inStockOrderDTOList.stream()
                .filter(order -> order != null && CollectionUtils.isNotEmpty(order.getInStockOrderItemDTOList()))
                .flatMap(order -> order.getInStockOrderItemDTOList().stream()).filter(item -> item.getSkuId() != null)
                .map(InStockOrderItemDTO::getSkuId).collect(Collectors.toList());
        List<InStockOrderItemDTO> noSkuIdItems = inStockOrderDTOList.stream()
                .filter(order -> order != null && CollectionUtils.isNotEmpty(order.getInStockOrderItemDTOList()))
                .flatMap(order -> order.getInStockOrderItemDTOList().stream()).filter(item -> item.getSkuId() == null)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noSkuIdItems)) {
            throw new BusinessException(String.format("skuId不能为空,数据:%s", JSON.toJSONString(noSkuIdItems)));
        }
        Map<Long, ProductSkuDTO> productSkuMap = iProductSkuQueryService.findBySkuWithMap(skuIds);
        if (productSkuMap == null || productSkuMap.isEmpty()) {
            throw new BusinessException("入库产品信息不存在！");
        }
        inStockOrderDTOList.forEach(inStockOrderDTO -> {
            if (CollectionUtils.isNotEmpty(inStockOrderDTO.getInStockOrderItemDTOList())) {
                inStockOrderDTO.getInStockOrderItemDTOList().forEach(item -> {
                    Long productSkuId = item.getSkuId();
                    AssertUtils.notNull(productSkuMap.get(productSkuId), String.format("产品[%s]信息不存在！skuId : %s",
                            StringUtils.trimToEmpty(item.getProductName()), productSkuId));

                    ProductSkuDTO productSku = productSkuMap.get(productSkuId);

                    // 出库扣库存，取反
                    BigDecimal changeCount = item.getUnitTotalCount();
                    Integer channel =
                            item.getChannel() == null ? ProductChannelType.JIUPI : item.getChannel().intValue();

                    WarehouseInventoryChangeBO warehouseInventoryChangeBO =
                            inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId,
                                    inStockOrderDTO.getWarehouseId(), changeCount, channel, productSku.getCompany_Id());

                    warehouseInventoryChangeBO.setCityId(inStockOrderDTO.getOrgId());
                    warehouseInventoryChangeBO
                            .setOrderId(inStockOrderDTO.getId() == null ? null : inStockOrderDTO.getId().toString());
                    warehouseInventoryChangeBO.setOrderNo(inStockOrderDTO.getRefOrderNo());
                    warehouseInventoryChangeBO.setOrderType(
                            inStockOrderDTO.getOrderType() == null ? null : inStockOrderDTO.getOrderType().intValue());
                    warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.供应链库存同步.getType());
                    warehouseInventoryChangeBO.setErpEventType(processDTO.getErpEventType());
                    warehouseInventoryChangeBO.setCreateUserName(inStockOrderDTO.getCreateuser());
                    warehouseInventoryChangeBO.setDescription(processDTO.getDescription() == null
                            ? getDescriptionByInStockOrderType(inStockOrderDTO.getOrderType(), processDTO.getErpEventType())
                            : processDTO.getDescription());
                    warehouseInventoryChangeBO.setValidateSelf(inStockOrderDTO.getNeedCheckStoreCount() == null ? false
                            : inStockOrderDTO.getNeedCheckStoreCount());
                    warehouseInventoryChangeBO.setLocationId(item.getLocationId());
                    warehouseInventoryChangeBO.setLocationName(item.getLocationName());
                    warehouseInventoryChangeBO.setProductSpecificationId(productSku.getProductSpecificationId());
                    warehouseInventoryChangeBO.setSecOwnerId(item.getSecOwnerId());

                    // 计算批属性编号用
                    warehouseInventoryChangeBO.setProductionDate(item.getProductionDate());
                    warehouseInventoryChangeBO.setBatchTime(item.getBatchTime());
                    warehouseInventoryChangeBO.setExpireTime(item.getExpireTime());
                    warehouseInventoryChangeBO.setOutInType(outInType.in);

                    // 是否在上架任务完成后处理销售库存
                    warehouseInventoryChangeBO.setIsProcessSalesStockAfterPutAway(
                            ObjectUtils.defaultIfNull(item.getIsProcessSalesStockAfterPutAway(), (byte) 0));
                    // 判断是否处理销售库存:1、历史大宗产品不需要处理销售库存 2、本次处理销售库存标识
                    boolean needChangeSaleStore =
                            ObjectUtils.defaultIfNull(processDTO.getNeedToChangeSaleStore(), Boolean.TRUE)
                                    && Objects.equals(ProductChannelType.JIUPI, channel);
                    warehouseInventoryChangeBO.setHasUpdateOPInventory(needChangeSaleStore);
                    warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);
                });
            }
        });
        return warehouseInventoryChangeBOS;
    }

    public List<WarehouseInventoryChangeBO>
    convertInventoryDTO2InventoryChangeBO(List<WarehouseInventoryChangeDTO> changeDTOList) {
        if (CollectionUtils.isEmpty(changeDTOList)) {
            return Collections.emptyList();
        }
        return changeDTOList.stream().filter(Objects::nonNull).map(dto -> {
            WarehouseInventoryChangeBO changeBO = new WarehouseInventoryChangeBO();
            BeanUtils.copyProperties(dto, changeBO);
            return changeBO;
        }).collect(Collectors.toList());
    }

    /**
     * 将outStockOrderType转换为erpType
     *
     * @param orderType
     * @return
     */
    private Integer outStockOrderType2ErpType(Byte orderType) {
        if (orderType == OutStockOrderTypeEnum.销售出库.getType()) {
            return ERPType.销售出库单.getType();
        } else if (orderType == OutStockOrderTypeEnum.调拨出库.getType()) {
            return ERPType.物料调拨单.getType();
        } else if (orderType == OutStockOrderTypeEnum.破损出库.getType()) {
            return ERPType.破损出库单.getType();
        } else if (orderType == OutStockOrderTypeEnum.其他出库.getType()) {
            return ERPType.其他出库单.getType();
        } else if (orderType == OutStockOrderTypeEnum.采购退货.getType()) {
            return ERPType.采购退货单.getType();
        } else if (orderType == OutStockOrderTypeEnum.盘亏出库.getType()) {
            return ERPType.库存盘点单.getType();
        } else if (orderType == OutStockOrderTypeEnum.第三方出库.getType()) {
            return ERPType.第三方出库.getType();
        } else if (orderType == OutStockOrderTypeEnum.同城调拨出库.getType()) {
            return ERPType.同城调拨转出.getType();
        } else if (orderType == OutStockOrderTypeEnum.处理品转入.getType()) {
            return ERPType.处理品转入.getType();
        } else if (orderType == OutStockOrderTypeEnum.陈列品转入.getType()) {
            return ERPType.陈列品转入.getType();
        } else if (orderType == OutStockOrderTypeEnum.自营大客户销售出库.getType()) {
            return ERPType.其他出库单.getType();
        } else {
            return orderType.intValue();
        }
    }

    /**
     * 判断是否变更销售库存
     *
     * @param needToChangeSaleStore
     * @param locationId
     * @param orderType
     * @param locationIds
     * @return
     */
    private boolean needChangeSaleStore(Boolean needToChangeSaleStore, Long locationId, Byte orderType,
                                        List<Long> locationIds) {
        boolean changeSaleStore =
                needToChangeSaleStore && !Objects.equals(OutStockOrderTypeEnum.处理品转入.getType(), orderType)
                        && !Objects.equals(OutStockOrderTypeEnum.陈列品转入.getType(), orderType)
                        && !Objects.equals(OutStockOrderTypeEnum.采购退货.getType(), orderType);
        if (changeSaleStore && locationId != null && CollectionUtils.isNotEmpty(locationIds)) {
            changeSaleStore = !locationIds.contains(locationId);
        }
        return changeSaleStore;
    }

    /**
     * 入库时是否处理销售库存
     */
    private boolean needChangeInStockSaleStore(Boolean needToChangeSaleStore, Long locationId, Byte orderType,
                                               List<Long> locationIds) {
        boolean changeSaleStore =
                needToChangeSaleStore && !Objects.equals(InStockOrderTypeEnum.处理品转出入库.getType(), orderType)
                        && !Objects.equals(InStockOrderTypeEnum.陈列品转出入库.getType(), orderType);
        if (changeSaleStore && locationId != null && CollectionUtils.isNotEmpty(locationIds)) {
            changeSaleStore = !locationIds.contains(locationId);
        }
        return changeSaleStore;
    }

    /**
     * 设置操作描述
     *
     * @param orderType
     * @param erpEventType
     * @return
     */
    private String getDescriptionByOutOrderType(Byte orderType, Integer erpEventType) {
        if (orderType == null && erpEventType == null) {
            return "直接出库";
        } else {
            OutStockOrderTypeEnum typeEnum = orderType == null ? null : OutStockOrderTypeEnum.getEnum(orderType);
            ERPEventType erpEvent = erpEventType == null ? null : ERPEventType.getEnum(erpEventType);
            return String.format("%s-%s", typeEnum != null ? typeEnum.name() : "",
                    erpEventType != null ? erpEvent.name() : "");
        }
    }

    /**
     * 入库设置操作描述
     *
     * @param orderType
     * @param erpEventType
     * @return
     */
    private String getDescriptionByInStockOrderType(Byte orderType, Integer erpEventType) {
        if (orderType == null && erpEventType == null) {
            return "直接入库";
        } else {
            InStockOrderTypeEnum typeEnum = orderType == null ? null : InStockOrderTypeEnum.getEnum(orderType);
            ERPEventType erpEvent = erpEventType == null ? null : ERPEventType.getEnum(erpEventType);
            return String.format("%s-%s", typeEnum != null ? typeEnum.name() : "",
                    erpEventType != null ? erpEvent.name() : "");
        }
    }

    /**
     * 转换成订单明细
     *
     * @param warehouseChangeList
     * @return
     */
    public List<OutStockOrderItemDetailDTO>
    processChangeBOSToOutStockOrderItemDetailDTOS(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return null;
        }
        List<OutStockOrderItemDetailDTO> detailDTOS = new ArrayList<>();
        warehouseChangeList.forEach(change -> {
            OutStockOrderItemDetailDTO detailDTO = new OutStockOrderItemDetailDTO();
            detailDTO.setId(change.getOrderItemDetailId());
            detailDTO.setOrgId(change.getCityId());
            detailDTO.setOutStockOrderItemId(change.getOrderItemId());
            detailDTO.setBusinessItemId(change.getOmsOrderItemId().toString());
            detailDTO.setProductSpecificationId(change.getProductSpecificationId());
            detailDTO.setOwnerId(change.getOwnId());
            detailDTO.setSecOwnerId(change.getSecOwnerId());
            detailDTO.setUnitTotalCount(change.getCount().abs());
            detailDTO.setOutStockUnitTotalCount(change.getCount().abs());

            detailDTOS.add(detailDTO);
        });
        return detailDTOS;
    }

    public List<SecOwnerDetailDTO>
    processBOToOMSSecOwnerDetailDTO(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return null;
        }
        List<SecOwnerDetailDTO> secOwnerDetailDTOS = new ArrayList<>();
        warehouseChangeList.forEach(changeBO -> {
            SecOwnerDetailDTO secOwnerDetailDTO = new SecOwnerDetailDTO();
            secOwnerDetailDTO.setOmsOrderId(changeBO.getOmsOrderId());
            secOwnerDetailDTO.setOmsOrderItemId(changeBO.getOmsOrderItemId());
            secOwnerDetailDTO.setSecOwnerId(changeBO.getSecOwnerId());
            // oms需要正数
            secOwnerDetailDTO.setCount(changeBO.getCount().abs());

            secOwnerDetailDTOS.add(secOwnerDetailDTO);
        });
        return secOwnerDetailDTOS;
    }

    public List<WarehouseInventoryChangeBO> InventoryDeliveryJiupiOrders2WarehouseInventoryChangeBOS(
            List<InventoryDeliveryJiupiOrder> deliveryOrders, boolean checkWarehouseInventory) {
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return warehouseInventoryChangeBOS;
        }
        deliveryOrders.forEach(order -> {
            Integer channel = getChannelByJiuPiOrderType(order.getJiupiOrderType());
            Integer orderType = OrderTypeConvert.convertOrderType(order.getJiupiOrderType());
            Integer jiupiEventType = order.getOutStockMode() == OutStockMode.INTERCITY_DISTRIBUTION
                    ? JiupiEventType.内配单出库减库存.getType() : JiupiEventType.仓库发货扣仓库库存.getType();

            order.getItems().forEach(item -> {

                // 2022-04-22，Item数量为0的，跳过处理
                if (item.getUnitTotalCount().compareTo(BigDecimal.ZERO) <= 0
                        || CollectionUtils.isEmpty(item.getSecOwnerDetail())) {
                    return;
                }
                // 2022-04-22，Item数量与Detail数量不一致的，拦截出库
                BigDecimal detailTotalCount =
                        item.getSecOwnerDetail().stream().filter(dt -> dt != null && dt.getCount() != null)
                                .map(dt -> dt.getCount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (item.getUnitTotalCount().multiply(item.getSaleSpecQuantity()).compareTo(detailTotalCount) != 0) {
                    throw new BusinessException("订单项与Detail数量不一致，请联系技术支持！OrderNo:" + order.getOrderNo());
                }

                item.getSecOwnerDetail().forEach(detail -> {
                    WarehouseInventoryChangeBO changeBO = new WarehouseInventoryChangeBO();
                    changeBO.setOutInType(outInType.out);
                    changeBO.setValidateSelf(checkWarehouseInventory);
                    changeBO.setHasUpdateOPInventory(false);
                    changeBO.setSource(ProductSourceType.易酒批);
                    changeBO.setCityId(order.getCityId());
                    changeBO.setWarehouseId(order.getWarehouseId());
                    changeBO.setOrderId(order.getOrderId().toString());
                    changeBO.setOmsOrderId(order.getOmsOrderId());
                    changeBO.setOrderNo(order.getOrderNo());
                    changeBO.setOrderType(orderType);
                    changeBO.setJiupiEventType(jiupiEventType);
                    changeBO.setDeliveryMode(order.getDeliveryMode());
                    changeBO.setOrderItemId(item.getOrderItem_Id());
                    changeBO.setOmsOrderItemId(item.getOmsOrderItemId());
                    changeBO.setProductSkuId(item.getProductSkuId());
                    changeBO.setProductSpecificationId(item.getProductSpecification_Id());
                    changeBO.setOwnId(detail.getOwnerId());
                    changeBO.setSecOwnerId(detail.getSecOwnerId());
                    changeBO.setChannel(channel);
                    changeBO.setCount(detail.getCount().negate());

                    warehouseInventoryChangeBOS.add(changeBO);
                });
            });
        });
        return warehouseInventoryChangeBOS;
    }

    public static List<WarehouseInventoryChangeDTO>
    warehouseChangListBO2WarehouseInventoryChangeDTOS(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        List<WarehouseInventoryChangeDTO> warehouseInventoryChangeDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(warehouseChangeList)) {
            warehouseChangeList.forEach(warehouseInventoryChangeBO -> {
                WarehouseInventoryChangeDTO warehouseInventoryChange = new WarehouseInventoryChangeDTO();
                BeanUtils.copyProperties(warehouseInventoryChangeBO, warehouseInventoryChange);

                warehouseInventoryChangeDTOS.add(warehouseInventoryChange);
            });
        }
        return warehouseInventoryChangeDTOS;
    }

    public List<WarehouseInventoryChangeBO>
    productInventoryCheckDTOS2BO(List<ProductInventoryCheckDTO> productInventoryCheckDTOS) {
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(productInventoryCheckDTOS)) {
            return warehouseInventoryChangeBOS;
        }
        productInventoryCheckDTOS.forEach(check -> {
            WarehouseInventoryChangeBO bo = new WarehouseInventoryChangeBO();
            bo.setWarehouseId(check.getWarehouseId());
            bo.setProductSkuId(check.getSkuId());
            bo.setProductSpecificationId(check.getSpecId());
            bo.setOwnId(check.getOwnerId());
            bo.setSecOwnerId(check.getSecOwnerId());
            bo.setCount(check.getUnitTotalCount());
            bo.setAllocationCalculation(check.getAllocationCalculation());

            warehouseInventoryChangeBOS.add(bo);
        });
        return warehouseInventoryChangeBOS;
    }
}

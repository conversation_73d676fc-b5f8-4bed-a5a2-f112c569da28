package com.yijiupi.himalaya.supplychain.inventory.domain.bl.biz;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.*;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.InventoryChangeFactory;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.LightAllianceProductInventoryDTO;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.supplychain.serviceutils.constant.owner.OwnerTypeConst;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

@Service
public class LightAllianceInventoryBL {

    private static final Logger LOG = LoggerFactory.getLogger(LightAllianceInventoryBL.class);

    @Autowired
    private TradingThirdUserBL tradingThirdUserBL;

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private InventoryChangeFactory inventoryChangeFactory;
    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private InventoryOrderBizBL inventoryOrderBizBL;
    @Autowired
    private WarehouseInventoryManageBL warehouseInventoryManageBL;
    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;

    /**
     * 获取单个商品轻加盟仓库库存.
     */
    public LightAllianceProductInventoryDTO getProductInventory(Long productSkuId, Integer warehouseId,
        Integer channel) {
        validateLightAllianceWarehouse(warehouseId);
        // 轻加盟产品来源一定是酒批
        ProductInventoryPO productInventoryPO =
            warehouseInventoryQueryBL.getProductInventoryPO(productSkuId, warehouseId, channel, null);
        LightAllianceProductInventoryDTO result = new LightAllianceProductInventoryDTO();
        if (productInventoryPO != null) {
            result.setWarehouseCount(productInventoryPO.getTotalCountMinUnit());
        }
        return result;
    }

    /**
     * 获取多个商品轻加盟仓库库存.
     */
    public Map<Long, LightAllianceProductInventoryDTO> getProductInventoryMap(List<Long> productSkuIds,
        Integer warehouseId, Integer channel) {
        if (CollectionUtils.isEmpty(productSkuIds)) {
            return Collections.emptyMap();
        }
        int size = productSkuIds.size();
        int maxSize = 50;
        if (size > maxSize) {
            throw new BusinessValidateException("产品SKUID个数不得超过50个");
        }
        validateLightAllianceWarehouse(warehouseId);

        Map<Long, LightAllianceProductInventoryDTO> results = new HashMap<>(size);
        for (Long productSkuId : productSkuIds) {
            results.put(productSkuId, getProductInventory(productSkuId, warehouseId, channel));
        }
        return results;
    }

    /**
     * 调整产品库存.
     */
    public void adjustProductInventory(Long productSkuId, Integer warehouseId, BigDecimal count) {
        validateLightAllianceWarehouse(warehouseId);
        Integer channel = ProductChannelType.JIUPI;

        // 通过skuId查询转换系数
        ProductSkuPO productSkuPO = productSkuQueryBL.getProductSkuBySkuId(productSkuId);
        AssertUtils.notNull(productSkuPO, "SkuId不存在,SkuId:" + productSkuId);

        WarehouseInventoryChangeBO changeBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId,
            warehouseId, count, channel, productSkuPO.getCompanyId(), productSkuPO.getSecOwnerId());
        changeBO.setDescription("轻加盟库存调整");
        changeBO.setJiupiEventType(JiupiEventType.手动修改.getType());
        changeBO.setHasUpdateOPInventory(true);// 修改销售库存
        changeBO.setSource(productSkuPO.getSource());
        // inventoryChangeBL.changeInventoryAssociate(changeBO);
        List<WarehouseInventoryChangeBO> boList = new ArrayList<>();

        changeBO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
        changeBO.setCityId(productSkuPO.getCityId());
        changeBO.setSource(ProductSourceType.易酒批);
        boList.add(changeBO);
        // 处理库存变更.库存记录
        warehouseInventoryManageBL.validateAndProcessProductStore(boList, true, false, true, true, false);
        // 发送销售库存变更消息
        warehouseInventoryManageBL.processSellInventory(boList, null);
    }

    private void validateLightAllianceWarehouse(Integer warehouseId) {
        Warehouse warehouse = tradingThirdUserBL.getWarehouse(warehouseId);
        AssertUtils.notNull(warehouse, "仓库不存在: " + warehouseId);
        int lightAllianceWarehouseType = 2;
        if (warehouse.getWarehouseType() != lightAllianceWarehouseType) {
            throw new BusinessValidateException("仓库不是轻加盟仓库: " + warehouseId + "-" + warehouse.getName());
        }
    }

    private void singleAsyncTask(Runnable runnable, String key, String name) {
        BoundValueOperations<String, String> ops = redisTemplate.boundValueOps("singleTask:" + key);
        Boolean absent = ops.setIfAbsent("1");
        if (absent) {
            LOG.info("开始复制" + name);
            ExecutorService cachedThreadPool = new ScheduledThreadPoolExecutor(1,
                new BasicThreadFactory.Builder().namingPattern("copyInventory-pool").daemon(true).build());
            cachedThreadPool.execute(() -> {
                try {
                    LOG.info("开始复制：{}", name);
                    runnable.run();
                    LOG.info("复制结束：{}", name);
                } finally {
                    ops.expire(1, TimeUnit.SECONDS);
                }
            });
        } else {
            throw new BusinessValidateException(name + "正在复制，请勿重复执行");
        }
    }

    /**
     * 复制仓库库存到轻加盟仓库(不复制合作商库存）
     */
    public void syncCopyInventory(Integer cityId, Integer cityWarehouseId, Integer lightWarehouseId, Integer opUserId,
        Integer channel) {
        singleAsyncTask(() -> {
            copyInventoryForLightAlliance(cityId, cityWarehouseId, lightWarehouseId, opUserId, channel);
        }, "syncCopyInventoryForLightAlliance", "轻加盟仓库库存复制");
    }

    private void copyInventoryForLightAlliance(Integer cityId, Integer cityWarehouseId, Integer lightWarehouseId,
        Integer opUserId, Integer channel) {
        AdminUser adminUser = tradingThirdUserBL.getAdminUserWithoutAuthById(opUserId);
        String name = adminUser.getUserName();
        validateLightAllianceWarehouse(lightWarehouseId);
        // 通过仓库id,查询该仓库下的所有库存信息
        List<ProductInventoryPO> productInventoryPOList =
            productInventoryPOMapper.findProductInventoryByWarehouseId(cityWarehouseId, channel);
        productInventoryPOList = productInventoryPOList.stream()
            .filter(p -> Objects.equals(OwnerTypeConst.易酒批, p.getOwnerType())).collect(Collectors.toList());
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOList = new ArrayList<>();
        for (ProductInventoryPO productInventoryPO : productInventoryPOList) {
            Long productSkuId = productInventoryPO.getProductSkuId();
            BigDecimal storeCount = productInventoryPO.getTotalCountMinUnit();
            // Integer productSpecId = productInventoryPO.getProductSpecificationId();
            WarehouseInventoryChangeBO warehouseInventoryChangeBO =
                inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, lightWarehouseId, storeCount,
                    channel, productInventoryPO.getOwnerId(), productInventoryPO.getSecOwnerId());
            warehouseInventoryChangeBO.setCityId(cityId);
            warehouseInventoryChangeBO.setDescription("轻加盟仓库库存复制");
            warehouseInventoryChangeBO.setCreateUserName(name);
            warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.手动修改.getType());
            warehouseInventoryChangeBO.setOwnType(productInventoryPO.getOwnerType());
            warehouseInventoryChangeBO.setProductSpecificationId(productInventoryPO.getProductSpecificationId());
            warehouseInventoryChangeBO.setHasUpdateOPInventory(true);
            warehouseInventoryChangeBO.setSource(ProductSourceType.易酒批);
            warehouseInventoryChangeBOList.add(warehouseInventoryChangeBO);
        }
        // inventoryChangeBL.changeInventoryAssociate(lightWarehouseInventiryChangeBOList);
        // 处理库存变更.库存记录
        warehouseInventoryManageBL.validateAndProcessProductStore(warehouseInventoryChangeBOList, true, false, true,
            true, false);
        // 发送销售库存变更消息
        warehouseInventoryManageBL.processSellInventory(warehouseInventoryChangeBOList, null);
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeRuleRelationMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeRuleRelationPO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="RuleId" property="ruleId" jdbcType="BIGINT"/>
        <result column="RuleType" property="ruleType" jdbcType="TINYINT"/>
        <result column="AttributeValue_Id" property="attributeValueId" jdbcType="VARCHAR"/>
        <result column="AttributeValueName" property="attributeValueName" jdbcType="VARCHAR"/>
    </resultMap>

    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        RuleId,
        RuleType,
        AttributeValue_Id,
        AttributeValueName
    </sql>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="batchAttributeRuleRelationPO.id">
        INSERT INTO batchattributerulerelation (
        id,
        RuleId,
        RuleType,
        AttributeValue_Id,
        AttributeValueName
        ) VALUES (
        #{batchAttributeRuleRelationPO.id,jdbcType=BIGINT},
        #{batchAttributeRuleRelationPO.ruleId,jdbcType=BIGINT},
        #{batchAttributeRuleRelationPO.ruleType,jdbcType=TINYINT},
        #{batchAttributeRuleRelationPO.attributeValueId,jdbcType=VARCHAR},
        #{batchAttributeRuleRelationPO.attributeValueName,jdbcType=VARCHAR}
        )
    </insert>

    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchattributerulerelation (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="batchAttributeRuleRelationPOs" item="batchAttributeRuleRelationPO" index="index"
                 separator=",">
            (
            #{batchAttributeRuleRelationPO.id,jdbcType=BIGINT},
            #{batchAttributeRuleRelationPO.ruleId,jdbcType=BIGINT},
            #{batchAttributeRuleRelationPO.ruleType,jdbcType=TINYINT},
            #{batchAttributeRuleRelationPO.attributeValueId,jdbcType=VARCHAR},
            #{batchAttributeRuleRelationPO.attributeValueName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE batchattributerulerelation
        <set>
            <if test="batchAttributeRuleRelationPO.id != null">id= #{batchAttributeRuleRelationPO.id,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeRuleRelationPO.ruleId != null">RuleId=
                #{batchAttributeRuleRelationPO.ruleId,jdbcType=BIGINT},
            </if>
            <if test="batchAttributeRuleRelationPO.ruleType != null">RuleType=
                #{batchAttributeRuleRelationPO.ruleType,jdbcType=TINYINT},
            </if>
            <if test="batchAttributeRuleRelationPO.attributeValueId != null">AttributeValue_Id=
                #{batchAttributeRuleRelationPO.attributeValueId,jdbcType=VARCHAR}
            </if>
            <if test="batchAttributeRuleRelationPO.attributeValueName != null">AttributeValueName=
                #{batchAttributeRuleRelationPO.attributeValueName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{batchAttributeRuleRelationPO.id,jdbcType=BIGINT}
    </update>

    <!--auto generated by codehelper on 2018-04-10 11:36:47-->
    <delete id="deleteByRuleId">
        delete from batchattributerulerelation
        where RuleId=#{ruleId,jdbcType=BIGINT}
    </delete>
    <select id="selectRuleRelationByRuleTypeAndAttribute"
            parameterType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.RuleRelationQueryDTO"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from batchattributerulerelation
        where RuleType in
        <foreach collection="ruleTypeList" item="ruleType" open="(" close=")" separator=",">
            #{ruleType,jdbcType=TINYINT}
        </foreach>
        <if test="attributeValueIdList != null and attributeValueIdList.size() > 0">
            AND AttributeValue_Id in
            <foreach collection="attributeValueIdList" item="attributeValueId" open="(" close=")" separator=",">
                #{attributeValueId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="attributeValueNameList != null and attributeValueNameList.size() > 0">
            AND AttributeValueName in
            <foreach collection="attributeValueNameList" item="attributeValueName" open="(" close=")" separator=",">
                #{attributeValueName,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>


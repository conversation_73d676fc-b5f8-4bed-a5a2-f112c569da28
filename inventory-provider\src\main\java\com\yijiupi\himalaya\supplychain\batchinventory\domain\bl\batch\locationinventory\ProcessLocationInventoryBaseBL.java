package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.locationinventory;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.BatchInventoryManageBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.ProductStoreBatchBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch.ProductStoreBatchChangeRecordBL;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProcessLocationInventoryBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.bo.locationinventory.ProductStoreBatchShareBO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.BatchInventoryProductStoreBatchMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.inventory.ProductStoreMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductInventoryChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.inventory.WarehouseInventoryTransferPO;
import com.yijiupi.himalaya.supplychain.batchinventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.service.WarehouseConfigService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.LocationReturnDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.IStockAgeStrategyService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.LocationAreaService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
public abstract class ProcessLocationInventoryBaseBL {

    @Reference
    protected WarehouseConfigService warehouseConfigService;
    @Reference
    protected IStockAgeStrategyService iStockAgeStrategyService;
    @Reference
    protected LocationAreaService locationAreaService;

    @Autowired
    protected ProductStoreMapper productStoreMapper;
    @Autowired
    protected BatchInventoryProductStoreBatchMapper batchInventoryProductStoreBatchMapper;
    @Autowired
    private BatchInventoryManageBL batchInventoryManageBL;
    @Autowired
    private ProductStoreBatchChangeRecordBL productStoreBatchChangeRecordBL;
    @Autowired
    private ProductStoreBatchBL productStoreBatchBL;

    protected final static Logger LOGGER = LoggerFactory.getLogger(ProcessLocationInventoryBaseBL.class);

    public boolean support(ProcessLocationInventoryBO bo) {
        return doSupport(bo);
    }

    protected abstract boolean doSupport(ProcessLocationInventoryBO bo);

    protected ProcessLocationInventoryBO rebuildParam(ProcessLocationInventoryBO bo) {
        return bo;
    }

    public ProductInventoryChangeRecordPO processLocationInventory(ProcessLocationInventoryBO bo) {
        bo = rebuildParam(bo);
        ProductInventoryChangeRecordPO productInventoryChangeRecordPO = bo.getProductInventoryChangeRecordPO();
        Integer locationType = bo.getLocationType();
        List<Integer> exSubcategory = bo.getExSubcategory();
        boolean isRandom = bo.isIsRandom();
        BigDecimal changeCount = productInventoryChangeRecordPO.getTotalCount();
        // 是否开启货位组
        boolean isOpenLocationStockGroup =
                warehouseConfigService.isOpenLocationGroup(productInventoryChangeRecordPO.getWarehouseId());
        // 如果待处理数量等于0，不需要处理
        if (changeCount.compareTo(BigDecimal.ZERO) == 0) {
            return productInventoryChangeRecordPO;
        }

        setProductInventoryChangeRecordPOInfo(productInventoryChangeRecordPO);

        String batchAttributeInfoNo = productInventoryChangeRecordPO.getBatchAttributeInfoNo();
        // 查询批次库存
        List<ProductStoreBatchPO> productStoreBatchPOS =
                getProductStoreBatchPOS(productInventoryChangeRecordPO, locationType, exSubcategory);

        productStoreBatchPOS = filterAndInit(productStoreBatchPOS, bo, isOpenLocationStockGroup);
        // 自身的逻辑再过滤一遍
        productStoreBatchPOS = filterSelfProductStoreBatch(productStoreBatchPOS);

        // 入库过滤为数量小于等于0的批次库存
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)
                && productInventoryChangeRecordPO.getTotalCount().compareTo(BigDecimal.ZERO) > 0){
            Predicate<ProductStoreBatchPO> productStoreBatchPOPredicate = (input -> {
                if (input.getTotalCount().compareTo(BigDecimal.ZERO) <= 0) {
                    return false;
                }
                return true;
            });
            productStoreBatchPOS = productStoreBatchPOS.stream().filter(productStoreBatchPOPredicate).collect(Collectors.toList());
            LOGGER.info("批次库存处理-入库过滤为数量小于等于0的批次库存结果：{}", JSON.toJSONString(productStoreBatchPOS));
        }

        // 如果找不到,通过storeid查询仓库id,通过仓库id查询默认暂存位..新增一条批次库存记录
        if (CollectionUtils.isEmpty(productStoreBatchPOS)) {
            LOGGER.info("单据[{}]编号[{}]没有找到对应批次库存信息，新增一条批次记录！", productInventoryChangeRecordPO.getOrderId(),
                    productInventoryChangeRecordPO.getOrderNo());
            ProductStoreBatchPO productStoreBatchPO = createProductStoreBatchIfNotExits(productInventoryChangeRecordPO,
                    locationType, changeCount, batchAttributeInfoNo);
            productInventoryChangeRecordPO.setTotalCount(BigDecimal.ZERO);
            processSelfLogical(bo, Collections.singletonList(productStoreBatchPO));
            return productInventoryChangeRecordPO;
        }

        List<ProductStoreBatchPO> lstProcessProductStores = getFirstPriorityProductStoreBatch(bo, productStoreBatchPOS);

        if (CollectionUtils.isEmpty(lstProcessProductStores)) {
            return productInventoryChangeRecordPO;
        }

        lstProcessProductStores = getProductStoreBatchOrderByProductionDateAndBatchDate(lstProcessProductStores);
        List<ProductStoreBatchPO> lstUpdatePO = shareInventoryToProductStoreBatch(lstProcessProductStores, changeCount);
        if (CollectionUtils.isEmpty(lstUpdatePO)) {
            return productInventoryChangeRecordPO;
        }

        LOGGER.info("变更的库存信息是:{}", JSON.toJSONString(lstUpdatePO));
        // 修改批次库存信息
        batchInventoryProductStoreBatchMapper.updateBatchInventory(lstUpdatePO);
        // 新增批次库存变更记录
        productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(bo.getProductInventoryChangeRecordPO().getId(),
                lstUpdatePO);
        processSelfLogical(bo, lstUpdatePO);

        // 残次品位库存变动数量
        batchInventoryManageBL.defectiveCountChangeSync(lstUpdatePO);

        // 删除促销批次库存
        productStoreBatchBL.deleteByPromotionStoreBatch(productInventoryChangeRecordPO);
        return productInventoryChangeRecordPO;
    }

    protected List<ProductStoreBatchPO> filterSelfProductStoreBatch(List<ProductStoreBatchPO> productStoreBatchPOS) {
        return productStoreBatchPOS;
    }

    /**
     * BatchAttributeInfoNo为空的，设置BatchAttributeInfoNo； 开启了 random和isOpenLocationStockGroup的，清理货位信息，保留货区信息
     *
     * @param productStoreBatchPOS
     * @param bo
     * @param isOpenLocationStockGroup
     * @return
     */
    private List<ProductStoreBatchPO> filterAndInit(List<ProductStoreBatchPO> productStoreBatchPOS,
                                                    ProcessLocationInventoryBO bo, boolean isOpenLocationStockGroup) {
        ProductInventoryChangeRecordPO productInventoryChangeRecordPO = bo.getProductInventoryChangeRecordPO();
        productStoreBatchPOS.stream().filter(n -> Objects.isNull(n.getBatchAttributeInfoNo())).forEach(n -> {
            n.setBatchAttributeInfoNo(productInventoryChangeRecordPO.getBatchAttributeInfoNo());
        });

        LOGGER.info(String.format("开始处理批次库存变更 指定货区：%s,指定货位:%s,排除货区：%s,变更消息：%s,查询库存结果：%s", bo.getLocationType(),
                productInventoryChangeRecordPO.getLocationId(), JSON.toJSONString(bo.getExSubcategory()),
                JSON.toJSONString(productInventoryChangeRecordPO), JSON.toJSONString(productStoreBatchPOS)));

        if (bo.isIsRandom() && isOpenLocationStockGroup
                && productInventoryChangeRecordPO.getTotalCount().compareTo(BigDecimal.ZERO) > 0) {
            // 如果开通货位组 加库存 清除货位信息,保留货区信息
            productStoreBatchPOS = productStoreBatchPOS.stream()
                    .filter(productStoreBatchPO -> Objects.equals(productStoreBatchPO.getLocationCategory(), 1))
                    .collect(Collectors.toList());
            LOGGER.info("开通货位组加库存，清除货位信息,保留货区信息{}", JSON.toJSONString(productStoreBatchPOS));
        }

        return productStoreBatchPOS;
    }

    protected void processSelfLogical(ProcessLocationInventoryBO bo, List<ProductStoreBatchPO> lstUpdatePO) {

    }

    // 分摊转入数量到批次库存
    private List<ProductStoreBatchPO> shareInventoryToProductStoreBatch(List<ProductStoreBatchPO> productStoreBatchPOS,
                                                                        BigDecimal totalChangeCount) {

        BigDecimal transferTotalCount = totalChangeCount.multiply(new BigDecimal(-1));

        List<ProductStoreBatchShareBO> shareBoList =
                productStoreBatchPOS.stream().map(ProductStoreBatchShareBO::convert).collect(Collectors.toList());

        // 分摊逻辑
        resetProductStoreBatchCount(transferTotalCount, shareBoList, 0);

        shareBoList = shareBoList.stream().filter(ProductStoreBatchShareBO::isHasChanged).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(shareBoList)) {
            return Collections.emptyList();
        }

        Map<String, BigDecimal> shareBoMap = shareBoList.stream().collect(
                Collectors.toMap(ProductStoreBatchShareBO::getProductStoreBatchId, ProductStoreBatchShareBO::getCount));

        return productStoreBatchPOS.stream().filter(m -> Objects.nonNull(shareBoMap.get(m.getId()))).map(m -> {
            // m.setChangeCount(shareBoMap.get(m.getId()));
            m.setTotalCount(shareBoMap.get(m.getId()));
            return m;
        }).collect(Collectors.toList());
    }

    // 分摊数量到批次库存
    public void resetProductStoreBatchCount(BigDecimal transferTotalCount, List<ProductStoreBatchShareBO> itemList,
                                            int i) {
        if (transferTotalCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        ProductStoreBatchShareBO processPO = itemList.get(i);
        BigDecimal changeCount = getShouldHandleCount(processPO, transferTotalCount, itemList.size(), i);
        BigDecimal tmpCount = transferTotalCount.subtract(changeCount);
        processPO.setCount(changeCount.multiply(new BigDecimal(-1)));
        processPO.setHasChanged(Boolean.TRUE);

        resetProductStoreBatchCount(tmpCount, itemList, i + 1);
    }

    /**
     * @param processPO
     * @param transferTotalCount
     * @param batchListSize
     * @param i
     * @return 当前批次库存的转移数量
     */
    private BigDecimal getShouldHandleCount(ProductStoreBatchShareBO processPO, BigDecimal transferTotalCount,
                                            int batchListSize, int i) {
        // 当前批次库存的总数量
        BigDecimal batchCount = processPO.getCount();

        // 最后一个，且剩余待移库数量不等于0，如果库存不足，全部扣到最后一个货位上
        if (i == batchListSize) {
            if (transferTotalCount.compareTo(batchCount) != 0) {
                LOGGER.info(String.format("批次ID:%s,库存ID:%s,货位[%s-%s]库存:%s小件,缺%s小件，库存不足强制处理！",
                        processPO.getProductStoreBatchId(), processPO.getProductStoreId(), processPO.getLocationId(),
                        processPO.getLocationName(), batchCount.stripTrailingZeros().toPlainString(),
                        transferTotalCount.stripTrailingZeros().toPlainString()));
                return transferTotalCount;
            }
        }

        // 如果要转移的数量，大于批次库存的数量，本次只转移批次库存数量
        // 如果要转移的数量，小于等于批次库存数量，则全部转移
        // 正正
        // 正负
        // 负负
        // 负正
        if (transferTotalCount.compareTo(batchCount) <= 0) {
            return transferTotalCount;
        }

        // 如果原库存小于0，本次新增库存，则优先补到0，剩余的轮训其他负库存货位
        if (transferTotalCount.compareTo(BigDecimal.ZERO) < 0 && batchCount.compareTo(BigDecimal.ZERO) < 0) {
            BigDecimal changeCount = (transferTotalCount.abs().min(batchCount.abs())).multiply(new BigDecimal(-1));
            LOGGER.info(String.format("[负库存补零]批次ID:%s,库存ID:%s,货位[%s-%s]库存:%s小件，补%s小件",
                    processPO.getProductStoreBatchId(), processPO.getProductStoreId(), processPO.getLocationId(),
                    processPO.getLocationName(), batchCount.stripTrailingZeros().toPlainString(),
                    changeCount.abs().stripTrailingZeros().toPlainString()));

            return changeCount;

        }
        return batchCount;

    }

    protected List<ProductStoreBatchPO> getFirstPriorityProductStoreBatch(ProcessLocationInventoryBO bo,
                                                                          List<ProductStoreBatchPO> productStoreBatchPOS) {
        ProductInventoryChangeRecordPO productInventoryChangeRecordPO = bo.getProductInventoryChangeRecordPO();
        BigDecimal changeCount = productInventoryChangeRecordPO.getTotalCount();
        // 出库扣库存，随机，忽略生产日期属性
        if (bo.isIsRandom()) {

            Predicate<ProductStoreBatchPO> priorityPredicate = getPriority(changeCount);
            List<ProductStoreBatchPO> filterProductStoreBatchList = productStoreBatchPOS.stream()
                    .filter(priorityPredicate).sorted(getComparor(changeCount)).collect(Collectors.toList());
            logPriority(filterProductStoreBatchList);

            return filterProductStoreBatchList;
            // end 随机扣库存
        }

        // 如果扣除数量小于0，优先生产日期匹配的，剩余的：优先扣生产日期为空或者生产日期临期的
        if (Objects.nonNull(productInventoryChangeRecordPO.getProductionDate())) {
            return productStoreBatchPOS.stream()
                    .filter(p -> Objects.equals(p.getProductionDate(), productInventoryChangeRecordPO.getProductionDate()))
                    .collect(Collectors.toList());
        }
        // end 非随机扣库存

        // productStoreBatchPOS.stream().collect(Collectors.groupingBy());

        // 找不到优先处理的，直接处理全部
        return productStoreBatchPOS;
    }

    // private Function<String, List<ProductStoreBatchPO>> getFunction(List<ProductStoreBatchPO> poList) {
    // return new Function<String, List<ProductStoreBatchPO>>() {
    // @Override
    // public List<ProductStoreBatchPO> apply(String s) {
    // return null;
    // }
    // }
    // }

    private Predicate<ProductStoreBatchPO> getPriority(BigDecimal changeCount) {
        // 如果加库存，优先清空负库存
        if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
            return p -> p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) < 0;
        }

        // 如果减库存，优先清空正库存
        return p -> p.getTotalCount() != null && p.getTotalCount().compareTo(BigDecimal.ZERO) > 0;
    }

    private Comparator<ProductStoreBatchPO> getComparor(BigDecimal changeCount) {
        if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
            return Comparator.comparing(ProductStoreBatchPO::getTotalCount).reversed();
        }

        return Comparator.comparing(ProductStoreBatchPO::getTotalCount);
    }

    protected void setProductInventoryChangeRecordPOInfo(ProductInventoryChangeRecordPO changeRecordPO) {
        setRecordPOSkuId(changeRecordPO);
    }

    private void setRecordPOSkuId(ProductInventoryChangeRecordPO changeRecordPO) {
        if (Objects.nonNull(changeRecordPO.getProductSkuId())) {
            return;
        }
        WarehouseInventoryTransferPO skuIdByStoreId =
                productStoreMapper.findSkuIdByStoreId(changeRecordPO.getProductStoreId());
        if (Objects.isNull(skuIdByStoreId)) {
            return;
        }
        changeRecordPO.setProductSkuId(skuIdByStoreId.getProductSkuId());
    }

    private void logPriority(List<ProductStoreBatchPO> filterProductStoreBatchList) {
        List<String> lstLocations = filterProductStoreBatchList.stream()
                .map(p -> String.format("pid:%s,sid:%s,productDate:%s,location:%s-%s,batchStore:%s", p.getId(),
                        p.getProductStoreId(), p.getProductionDate(), p.getLocationId(), p.getLocationName(),
                        p.getTotalCount()))
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstLocations)) {
            LOGGER.info(String.format("随机-优先处理货位集合：%s", JSON.toJSONString(lstLocations)));
        }
    }

    // 直接拷贝
    protected List<ProductStoreBatchPO> getProductStoreBatchPOS(
            ProductInventoryChangeRecordPO productInventoryChangeRecordPO, Integer locationType,
            List<Integer> exSubcategory) {
        // productInventoryChangeRecordPO 中增加 productStoreBatchId(批次库存ID),
        // 如果批次库存ID存在则优先用它查询批次库存信息,不存在则按照原来的方式查
        if (StringUtils.isNotBlank(productInventoryChangeRecordPO.getProductStoreBatchId())) {
            ProductStoreBatchPO productStoreBatchPO = batchInventoryProductStoreBatchMapper
                    .findProductStoreBatchById(productInventoryChangeRecordPO.getProductStoreBatchId());
            if (Objects.nonNull(productStoreBatchPO)) {
                return Lists.newArrayList(productStoreBatchPO);
            }
        }
        // 如果集合为空,要么 productStoreBatchId(批次库存ID) 为空, 要么根据 productStoreBatchId(批次库存ID) 没找到数据则按默认方式查询数据
        if (locationType == null && CollectionUtils.isEmpty(exSubcategory)) {
            return batchInventoryProductStoreBatchMapper.selectTmpLocationInventory(productInventoryChangeRecordPO);
        }

        return batchInventoryProductStoreBatchMapper.selectTmpLocationInventoryBySubCategory(productInventoryChangeRecordPO,
                locationType, exSubcategory);
    }

    // 直接拷过来
    private ProductStoreBatchPO createProductStoreBatchIfNotExits(
            ProductInventoryChangeRecordPO productInventoryChangeRecordPO, Integer locationType, BigDecimal changeCount,
            String batchAttributeInfoNo) {
        LocationReturnDTO locationReturnDTO = null;
        Long locationId = productInventoryChangeRecordPO.getLocationId();
        // 如果有多个出库货位，随机取第一个
        if (locationId == null && CollectionUtils.isNotEmpty(productInventoryChangeRecordPO.getLocationIds())) {
            locationId = productInventoryChangeRecordPO.getLocationIds().get(0);
        }
        if (locationId != null) {
            locationReturnDTO = locationAreaService.findLocationById(String.valueOf(locationId));
        } else {
            Integer warehouseId = productInventoryChangeRecordPO.getWarehouseId();
            Integer cityId = productInventoryChangeRecordPO.getCityId();
            if (warehouseId == null) {
                warehouseId = batchInventoryProductStoreBatchMapper
                        .findWarehouseIdByStoreId(productInventoryChangeRecordPO.getProductStoreId());
            }
            if (cityId == null) {
                cityId =
                        batchInventoryProductStoreBatchMapper.findCityIdByStoreId(productInventoryChangeRecordPO.getProductStoreId());
            }
            AssertUtils.notNull(warehouseId,
                    "该库存记录仓库Id不存在! storeId : " + productInventoryChangeRecordPO.getProductStoreId());
            AssertUtils.notNull(cityId,
                    "该库存记录CityId不存在! storeId : " + productInventoryChangeRecordPO.getProductStoreId());
            // 根据仓库id+类型查询货区
            byte subcategory =
                    locationType == null ? LocationAreaEnum.存储区.getType().byteValue() : locationType.byteValue();
            locationReturnDTO =
                    batchInventoryManageBL.getLoactionByWarehouseId(warehouseId, cityId, subcategory, changeCount);
        }
        AssertUtils.notNull(locationReturnDTO,
                "货位信息不存在！ID：" + locationId + "，名称：" + productInventoryChangeRecordPO.getLocationName());
        ProductStoreBatchPO productStoreBatchPO =
                ProductStoreBatchBL.ProductInventoryChangeRecordPO2ProductStoreBatchPO(productInventoryChangeRecordPO);
        productStoreBatchPO.setId(UUIDUtil.getUUID().replaceAll("-", ""));
        productStoreBatchPO.setLocationId(locationReturnDTO.getId());
        productStoreBatchPO.setLocationName(locationReturnDTO.getName());
        productStoreBatchPO.setSubcategory(Integer.valueOf(locationReturnDTO.getSubcategory()));
        productStoreBatchPO.setBatchAttributeInfoNo(batchAttributeInfoNo);
        batchInventoryProductStoreBatchMapper.addBatchInventory(productStoreBatchPO);
        // 新增批次库存变更记录
        productStoreBatchChangeRecordBL.createStoreBatchChangeRecord(productInventoryChangeRecordPO.getId(),
                Arrays.asList(productStoreBatchPO));

        productStoreBatchPO.setProductSkuId(productInventoryChangeRecordPO.getProductSkuId());

        // 残次品位库存变动数量
        batchInventoryManageBL.defectiveCountChangeSync(Collections.singletonList(productStoreBatchPO));
        return productStoreBatchPO;
    }

    /**
     * 批次库存排序规则-（生产日期+批次时间） 1、生产日期（为空的排最前） 2、批次时间（为空的排最前） 直接拷过来
     *
     * @param productStoreBatchPOS
     * @return
     */
    private List<ProductStoreBatchPO>
    getProductStoreBatchOrderByProductionDateAndBatchDate(List<ProductStoreBatchPO> productStoreBatchPOS) {
        List<ProductStoreBatchPO> lstAllProductStores = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productStoreBatchPOS)) {
            List<Date> lstAllProductionDates = new ArrayList<>();
            lstAllProductionDates.add(null);
            List<Date> lstTmpProductionDates = productStoreBatchPOS.stream().filter(p -> p.getProductionDate() != null)
                    .map(p -> p.getProductionDate()).distinct().sorted().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lstTmpProductionDates)) {
                lstAllProductionDates.addAll(lstTmpProductionDates);
            }

            for (Date dtProducttionDate : lstAllProductionDates) {
                List<ProductStoreBatchPO> lstTmp =
                        getProductStoreBatchPOsOrderByBatchTime(productStoreBatchPOS, dtProducttionDate);
                if (CollectionUtils.isNotEmpty(lstTmp)) {
                    lstAllProductStores.addAll(lstTmp);
                }
            }
            if (lstTmpProductionDates.size() > 1) {
                LOGGER.info(String.format("存在多个生产日期！生产日期：%s，批次库存排序结果：%s", JSON.toJSONString(lstAllProductionDates),
                        JSON.toJSONString(lstAllProductStores)));
            }
        }
        return lstAllProductStores;
    }

    /**
     * 同一生产日期的批次库存排序规则 批次时间（为空的排最前） 直接拷过来
     *
     * @param productStoreBatchPOS
     * @return
     */
    private List<ProductStoreBatchPO> getProductStoreBatchPOsOrderByBatchTime(
            List<ProductStoreBatchPO> productStoreBatchPOS, Date dtProducttionDate) {
        List<ProductStoreBatchPO> lstTmp = productStoreBatchPOS.stream()
                .filter(p -> Objects.equals(dtProducttionDate, p.getProductionDate())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lstTmp)) {
            // 批次为空的数据
            List<ProductStoreBatchPO> lstNoBatchDate =
                    lstTmp.stream().filter(p -> p.getBatchTime() == null).collect(Collectors.toList());
            // 优先扣批次日期为空或者批次临期的
            List<ProductStoreBatchPO> lstHasBatchDate = lstTmp.stream().filter(p -> p.getBatchTime() != null)
                    .sorted(Comparator.nullsFirst(Comparator.comparing(ProductStoreBatchPO::getBatchTime)))
                    .collect(Collectors.toList());
            lstTmp = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lstNoBatchDate)) {
                lstTmp.addAll(lstNoBatchDate);
            }
            if (CollectionUtils.isNotEmpty(lstHasBatchDate)) {
                lstTmp.addAll(lstHasBatchDate);
            }
        }
        return lstTmp;
    }

}

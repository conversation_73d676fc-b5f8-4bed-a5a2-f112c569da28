package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.DeliveryStoreRecordMapper;
import com.yijiupi.himalaya.supplychain.inventory.dto.DeliveryStoreRecordDTO;

/**
 * 已发货记录
 *
 * <AUTHOR>
 * @date 2018/8/29 11:21
 */
@Service
public class DeliveryStoreRecordBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeliveryStoreRecordBL.class);

    @Autowired
    private DeliveryStoreRecordMapper deliveryStoreRecordMapper;

    /**
     * 批量更新已发货总数量
     * 
     * @param list
     */
    public void updateDeliveryStoreRecordBatch(List<DeliveryStoreRecordDTO> list) {
        AssertUtils.notEmpty(list, "批量更新已发货总数量的参数不能为空");
        long startTime = System.currentTimeMillis();
        deliveryStoreRecordMapper.updateDeliveryStoreRecordBatch(list);
        LOGGER.info("更新已发货总数量：{}条，耗时{}ms", list.size(), (System.currentTimeMillis() - startTime));
    }
}

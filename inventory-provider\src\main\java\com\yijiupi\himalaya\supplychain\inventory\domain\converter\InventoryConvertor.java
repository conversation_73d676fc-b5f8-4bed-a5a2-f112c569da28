package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.supplychain.dto.erp.ERPStoreVO;
import com.yijiupi.himalaya.supplychain.dto.erp.ErpPurchaseReturnOutStockDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.dto.ERPCityMergeDTO;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.DataResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.ErpDisposedCountParam;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryCheckDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.InventoryConfigUtil;
import com.yijiupi.himalaya.supplychain.inventory.util.HttpUtil;
import com.yijiupi.himalaya.supplychain.wmsdubbop.dto.WarehouseInventoryModDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 转换类加抽出的方法
 *
 * <AUTHOR> 2017/12/22
 */
public class InventoryConvertor {

    /**
     * 查询ERP处理品数量
     *
     * @return
     */
    public static List<ERPCityMergeDTO> getDisposeProductCount(Integer cityId) {
        DataResult<List<ERPCityMergeDTO>> dataResult =
            HttpUtil.httpGet(InventoryConfigUtil.erpAPIUrl + "DisposedInNoteInfo/GetDisposedOutInfo?cityId=" + cityId,
                new TypeToken<DataResult<List<ERPCityMergeDTO>>>() {}.getType());
        if (dataResult == null) {
            throw new BusinessException("远程调用ERP失败");
        }
        if (!dataResult.getSuccess()) {
            throw new BusinessException(dataResult.getError());
        }
        return dataResult.getData();
    }

    /**
     * 查询ERP处理品数量
     *
     * @return
     */
    public static List<ERPCityMergeDTO> getDisposeProductCount(Integer cityId, Integer warehouseId,
        List<Long> productSkuIds) {
        ErpDisposedCountParam erpDisposedCountParam = new ErpDisposedCountParam();
        erpDisposedCountParam.setCityId(cityId);
        erpDisposedCountParam.setWarehouseId(warehouseId);
        erpDisposedCountParam.setListSkuId(productSkuIds);
        String param = JSON.toJSONString(erpDisposedCountParam);
        DataResult<List<ERPCityMergeDTO>> dataResult =
            HttpUtil.httpPost(InventoryConfigUtil.erpAPIUrl + "/DisposedInNoteInfo/GetProductDisposedCount", param,
                new TypeToken<DataResult<List<ERPCityMergeDTO>>>() {}.getType());
        if (dataResult == null) {
            throw new BusinessException("远程调用ERP失败");
        }
        if (!dataResult.getSuccess()) {
            throw new BusinessException(dataResult.getError());
        }
        return dataResult.getData();
    }

    public static List<ERPStoreVO> getErpStoreCountByCity(Integer cityId) {
        List<ERPStoreVO> dataResult =
            HttpUtil.httpGet(InventoryConfigUtil.erpAPIUrl + "ERPStore/FindERPStoreList?request.cityId=" + cityId,
                new TypeToken<List<ERPStoreVO>>() {}.getType());
        if (dataResult == null) {
            throw new BusinessException("远程调用ERP失败");
        }
        return dataResult;
    }

    public static List<ERPStoreVO> getErpStoreCountByWarehouse(Integer warehouseId, Integer type) {
        List<ERPStoreVO> dataResult =
            HttpUtil.httpGet(InventoryConfigUtil.erpAPIUrl + "ERPStore/FindERPChainStoreListByStoreHouseId?request.storeHouseId="
                + warehouseId + "&request.type=" + type, new TypeToken<List<ERPStoreVO>>() {}.getType());
        if (dataResult == null) {
            throw new BusinessException("远程调用ERP失败");
        }
        return dataResult;
    }

    public static List<WarehouseInventoryModDTO>
        warehouseStoreDTOS2WarehouseInventoryModDTOS(List<WarehouseStoreDTO> warehouseStoreDTOS, Integer opUserId) {
        ArrayList<WarehouseInventoryModDTO> warehouseInventoryModDTOS = new ArrayList<>();
        for (WarehouseStoreDTO warehouseStoreDTO : warehouseStoreDTOS) {
            warehouseInventoryModDTOS.add(warehouseStoreDTO2WarehouseInventoryModDTO(warehouseStoreDTO, opUserId));
        }
        return warehouseInventoryModDTOS;
    }

    public static WarehouseInventoryModDTO
        warehouseStoreDTO2WarehouseInventoryModDTO(WarehouseStoreDTO warehouseStoreDTO, Integer opUserId) {
        if (warehouseStoreDTO == null) {
            return null;
        }
        WarehouseInventoryModDTO warehouseInventoryModDTO = new WarehouseInventoryModDTO();
        warehouseInventoryModDTO.setWarehouseId(warehouseStoreDTO.getWarehouseId());
        warehouseInventoryModDTO.setProductSpecificationId(warehouseStoreDTO.getProductSpecId());
        warehouseInventoryModDTO.setUnitCount(warehouseStoreDTO.getSaleStoreTotalCount());
        warehouseInventoryModDTO.setCityId(warehouseStoreDTO.getCityId());
        warehouseInventoryModDTO.setOwnerId(warehouseStoreDTO.getOwnerId());
        warehouseInventoryModDTO.setOpUserId(opUserId);
        return warehouseInventoryModDTO;
    }

    /**
     * 每次50个
     *
     * @param list
     * @param len
     * @return
     */
    public static List<List<WarehouseStoreDTO>> splitList(List<WarehouseStoreDTO> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<WarehouseStoreDTO>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<WarehouseStoreDTO> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 每次20个
     *
     * @param list
     * @param len
     * @return
     */
    public static List<List<WarehouseInventoryCheckDTO>>
        splitProductInventoryCheckList(List<WarehouseInventoryCheckDTO> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<WarehouseInventoryCheckDTO>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<WarehouseInventoryCheckDTO> subList =
                list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 每次50个
     *
     * @param list
     * @param len
     * @return
     */
    public static List<List<WarehouseInventoryDTO>> splitInventoryList(List<WarehouseInventoryDTO> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<WarehouseInventoryDTO>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<WarehouseInventoryDTO> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 每次50个
     *
     * @param list
     * @param len
     * @return
     */
    public static List<List<InventorySyncRecordDTO>> splitInventorySyncRecordList(List<InventorySyncRecordDTO> list,
        int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<InventorySyncRecordDTO>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<InventorySyncRecordDTO> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    public static List<com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryModDTO>
        warehouseInventoryDTOS2WarehouseInventoryModDTOS(List<WarehouseInventoryDTO> warehouseStoreDTOS) {
        ArrayList<com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryModDTO> warehouseInventoryModDTOS =
            new ArrayList<>();
        for (WarehouseInventoryDTO warehouseStoreDTO : warehouseStoreDTOS) {

            if (warehouseStoreDTO == null) {
                return null;
            }
            com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryModDTO warehouseInventoryModDTO =
                new com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryModDTO();
            warehouseInventoryModDTO.setWarehouseId(warehouseStoreDTO.getWarehouseId());
            warehouseInventoryModDTO.setProductSkuId(warehouseStoreDTO.getProductSkuId());
            warehouseInventoryModDTO.setUnitCount(warehouseStoreDTO.getDiffTotalCount());
            warehouseInventoryModDTO.setPackageCount(BigDecimal.ZERO);
            warehouseInventoryModDTO.setHasUpdateOPInventory(0);
            warehouseInventoryModDTO.setChannel(0);
            warehouseInventoryModDTOS.add(warehouseInventoryModDTO);
        }
        return warehouseInventoryModDTOS;
    }

    /**
     * 拆分list
     * 
     * @param list
     * @param len
     * @return
     */
    public static <T> List<List<T>> splitListNew(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return new ArrayList<>();
        }

        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 查询ERP采购退预占库存数据
     */
    public static List<ErpPurchaseReturnOutStockDTO> getErpPurchaseReturnOutStock(Integer warehouseId) {
        DataResult<List<ErpPurchaseReturnOutStockDTO>> dataResult = HttpUtil.httpGet(
            InventoryConfigUtil.erpAPIUrl + "/PurchaseOutStock/GetRequisitionItemsByWarehouseId/?warehouseId=" + warehouseId,
            new TypeToken<DataResult<List<ErpPurchaseReturnOutStockDTO>>>() {}.getType());
        if (dataResult == null) {
            throw new BusinessException("远程调用ERP失败");
        }
        return dataResult.getData();
    }

}

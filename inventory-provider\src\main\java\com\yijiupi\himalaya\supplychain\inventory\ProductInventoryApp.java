/*
 * Copyright © 2016 北京易酒批电子商务有限公司. All rights reserved.
 */
package com.yijiupi.himalaya.supplychain.inventory;

import com.yijiupi.himalaya.distributedlock.annotation.EnableDistributedLock;
import com.yijiupi.himalaya.distributedlock.enums.DistributedLockType;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

@EnableAsync
@EnableCaching
@EnableDistributedLock(lockType = DistributedLockType.Redis)
@MapperScan(basePackages = {"com.yijiupi.himalaya.supplychain.inventory","com.yijiupi.himalaya.supplychain.batchinventory"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class}, scanBasePackages = "com.yijiupi.himalaya")
public class ProductInventoryApp {
    private static final Logger LOG = LoggerFactory.getLogger(ProductInventoryApp.class);

    public static void main(String[] args) throws Exception {
        SpringApplication app = new SpringApplication(ProductInventoryApp.class);
        // app.setWebEnvironment(false);// 不启动WEB 环境
        app.run(args);
        LOG.info("项目启动成功！");
        CountDownLatch latch = new CountDownLatch(1);
        latch.await();
    }

    @Bean(name = "inventoryTaskExecutor")
    public Executor outStockOrderSyncTaskExecutor() {
        int coreSize = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize * 2 + 1);
        executor.setMaxPoolSize(coreSize * 3);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("InventoryTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        return executor;
    }

    @Bean(name = "batchInventoryTaskExecutor")
    public ExecutorService batchInventoryTaskExecutor() {
        int coreSize = Runtime.getRuntime().availableProcessors();
        return new ThreadPoolExecutor(coreSize, coreSize + 80, 120, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(1000),
                new BasicThreadFactory.Builder().namingPattern("BatchInventoryTaskExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}

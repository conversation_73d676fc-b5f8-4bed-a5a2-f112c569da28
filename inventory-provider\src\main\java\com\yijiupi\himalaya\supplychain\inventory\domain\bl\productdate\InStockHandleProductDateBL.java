package com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
@Service
public class InStockHandleProductDateBL {

    @Autowired
    private List<InStockHandleProductDateBaseBL> handleList;
    private static final Logger LOG = LoggerFactory.getLogger(InStockHandleProductDateBL.class);

    public void processOrderProductDate(List<OrderDTO> orderList) {
        LOG.info("查询原单生产日期并赋值，入参：{}", JSON.toJSONString(orderList));
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        handleList.forEach(service -> service.fillProductDate(orderList));

        LOG.info("处理后的结果为：{}", JSON.toJSONString(orderList));
    }

}

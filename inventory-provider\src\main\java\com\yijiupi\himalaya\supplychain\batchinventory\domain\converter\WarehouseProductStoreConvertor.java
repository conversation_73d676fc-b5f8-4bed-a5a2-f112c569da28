package com.yijiupi.himalaya.supplychain.batchinventory.domain.converter;

import java.util.List;
import java.util.stream.Collectors;

import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSpecAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseProductStoreQueryDTO;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
public class WarehouseProductStoreConvertor {

    public static WarehouseProductStoreQueryDTO
        convertWarehouseProductStoreQueryDTO(List<ProductionDateDTO> productionDateDTOS) {
        List<ProductSpecAndOwnerIdDTO> tmpQueryDTOList = productionDateDTOS.stream().map(m -> {
            ProductSpecAndOwnerIdDTO specAndOwnerIdDTO = new ProductSpecAndOwnerIdDTO();
            specAndOwnerIdDTO.setProductSpecId(m.getProductSpecificationId());
            specAndOwnerIdDTO.setOwnerId(m.getOwnerId());
            specAndOwnerIdDTO.setSecOwnerId(m.getSecOwnerId());

            return specAndOwnerIdDTO;
        }).collect(Collectors.toList());

        // 通过仓库id+规格id+货主id+二级货主id 获取销售库存
        WarehouseProductStoreQueryDTO queryDTO = new WarehouseProductStoreQueryDTO();
        queryDTO.setCityId(productionDateDTOS.get(0).getOrgId());
        queryDTO.setWarehouseId(productionDateDTOS.get(0).getWarehouseId());
        queryDTO.setSpecAndOwnerIdAndSecOwnerIds(tmpQueryDTOList);

        return queryDTO;
    }

}

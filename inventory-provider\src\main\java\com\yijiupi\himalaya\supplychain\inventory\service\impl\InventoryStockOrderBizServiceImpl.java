package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.aspect.InventorySendFaildMQ;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.InventoryStockOrderBizBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.StockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryChangeDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.instock.ProcessInStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.outstock.ProcessOutStockOrderInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IInventoryStockOrderBizService;

/**
 * 仓库单据库存处理服务
 */
@Service(timeout = 60000)
public class InventoryStockOrderBizServiceImpl implements IInventoryStockOrderBizService {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryStockOrderBizServiceImpl.class);

    @Autowired
    private InventoryStockOrderBizBL inventoryStockOrderBizBL;

    @Autowired
    private InventorySendFaildMQ inventorySendFaildMQ;

    /**
     * 处理出库单库存
     * 
     * @param processDTO
     */
    @Override
    public List<WarehouseInventoryChangeDTO>
        processOutStockOrderInventory(ProcessOutStockOrderInventoryDTO processDTO) {
        try {
            return inventoryStockOrderBizBL.processOutStockOrderInventory(processDTO);
        } catch (Exception e) {
            if (processDTO.getAllowThrowException()) {
                throw e;
            }
            LOG.error("出库单据库存操作失败,参数:{}", JSON.toJSONString(processDTO), e);
            inventorySendFaildMQ.mqSendFaild(JSON.toJSONString(processDTO), "processInventoryByOutStock", e);
        }

        return new ArrayList<>();
    }

    /**
     * 处理出库单库存(异常消息重试)
     * 
     * @param message
     */
    @Override
    public void retriesOutStockOrderInventoryByMessage(String message) {
        inventoryStockOrderBizBL.retriesOutStockOrderInventoryByMessage(message);
    }

    /**
     * 处理入库单库存
     */
    @Override
    public void processInStockOrderInventory(ProcessInStockOrderInventoryDTO processDTO) {
        inventoryStockOrderBizBL.processInStockOrderInventory(processDTO);
    }

    /**
     * 处理入库单库存(异常消息重试)
     */
    @Override
    public void retriesInStockOrderInventoryByMessage(String message) {
        inventoryStockOrderBizBL.retriesInStockOrderInventoryByMessage(message);
    }

    /**
     * 处理 WMS 不存在单据库存：ERP某些单据不存在WMS但是删除的时候需要处理库存
     */
    @Override
    public void processWmsNotExitsOrderInventory(List<WarehouseInventoryChangeDTO> changeDTOList) {
        inventoryStockOrderBizBL.processWmsNotExitsOrderInventory(changeDTOList, false, null);
    }

    /**
     * SCM2-15295 虚仓实配库存管理 不生成出库单据，只处理库存
     *
     * @param stockOrderInventoryDTO
     */
    @Override
    public void processOutStockOrderInventoryDYY(StockOrderInventoryDTO stockOrderInventoryDTO) {
        inventoryStockOrderBizBL.processOutStockOrderInventoryDYY(stockOrderInventoryDTO);
    }

    /**
     * 易经销出入库处理
     *
     * @param stockOrderInventoryDTO
     */
    @Override
    public void processOutStockOrderInventoryWithEasySell(StockOrderInventoryDTO stockOrderInventoryDTO) {
        inventoryStockOrderBizBL.processOutStockOrderInventoryWithEasySell(stockOrderInventoryDTO);
    }
}

package com.yijiupi.himalaya.supplychain.inventory.domain.converter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.ordercenter.OrderCenterSaleInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSecOwnerIdQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSpecAndOwnerIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductSecOwnerService;

/**
 * Copyright © 2024 北京易酒批电子商务有限公司. All rights reserved.
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Component
public class SaleInventoryQueryConvertor {

    @Reference
    private IProductSecOwnerService iProductSecOwnerService;
    private static final Logger LOGGER = LoggerFactory.getLogger(SaleInventoryQueryConvertor.class);

    /**
     * 
     * @param warehouseStoreDTOS 入参只有 规格id 和 ownerId可用
     * @return
     */
    public List<OrderCenterSaleInventoryQueryDTO> convertQueryList(List<WarehouseStoreDTO> warehouseStoreDTOS,
        Integer orgId, Integer warehouseId) {
        List<ProductSpecAndOwnerIdDTO> queryList = warehouseStoreDTOS.stream().map(t -> {
            ProductSpecAndOwnerIdDTO productSpecAndOwnerIdDTO = new ProductSpecAndOwnerIdDTO();
            productSpecAndOwnerIdDTO.setProductSpecId(t.getProductSpecId());
            productSpecAndOwnerIdDTO.setOwnerId(Objects.isNull(t.getOwnerId()) ? null : t.getOwnerId());
            return productSpecAndOwnerIdDTO;
        }).collect(Collectors.toList());

        ProductSecOwnerIdQueryDTO productSecOwnerIdQueryDTO = new ProductSecOwnerIdQueryDTO();

        productSecOwnerIdQueryDTO.setSpecAndOwnerIds(queryList);
        productSecOwnerIdQueryDTO.setWarehouseId(warehouseId);
        // key : 规格id-一级货主id
        Map<String, List<Long>> secOwners = iProductSecOwnerService.getSecOwnerIdMap(productSecOwnerIdQueryDTO);

        // LOGGER.info("查询二级货主为:{}", JSON.toJSONString(secOwners));
        List<OrderCenterSaleInventoryQueryDTO> saleInventoryQueryDTOList = new ArrayList<>();
        secOwners.forEach((m, n) -> {
            String[] split = m.split("-");
            n.forEach(secOwnerId -> {
                OrderCenterSaleInventoryQueryDTO saleInventoryQueryDTO = new OrderCenterSaleInventoryQueryDTO();
                saleInventoryQueryDTO.setCityId(orgId);
                saleInventoryQueryDTO.setWarehouseId(warehouseId);
                saleInventoryQueryDTO.setProductSpecId(Long.valueOf(split[0]));
                saleInventoryQueryDTO.setOwnerId(getOwnerId(split[1]));
                saleInventoryQueryDTO.setSecOwnerId(secOwnerId);
                saleInventoryQueryDTOList.add(saleInventoryQueryDTO);
            });
        });

        return saleInventoryQueryDTOList;
    }

    private Long getOwnerId(String ownerId) {
        if ("null".equals(ownerId)) {
            return null;
        }
        if (StringUtils.isBlank(ownerId)) {
            return null;
        }

        return Long.valueOf(ownerId);
    }

}

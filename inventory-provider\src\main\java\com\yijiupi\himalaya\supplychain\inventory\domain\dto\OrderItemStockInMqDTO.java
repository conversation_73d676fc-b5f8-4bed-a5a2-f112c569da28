package com.yijiupi.himalaya.supplychain.inventory.domain.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderItemStockInMqDTO implements Serializable {
    private static final long serialVersionUID = -8430146644914370378L;
    /**
     * 购买数量
     */
    private BigDecimal buyCount;
    /**
     * 发货数量
     */
    private BigDecimal deliverCount;
    /**
     * 退货数量
     */
    private BigDecimal returnCount;
    /**
     * 配送数量
     */
    private BigDecimal takeCount;
    /**
     * 销售规格
     */
    private BigDecimal saleSpecQuantity;
    /**
     * id
     */
    private Long orderItemId;
    /**
     * skuid
     */
    private Long productSkuId;

    // 货主Id
    private Long ownerId;

    // 规格Id
    private Long productSpecificationId;

    // 二级货主Id
    private Long secOwnerId;

    @Override
    public String toString() {
        return "OrderItemCompleteMqDTO [buyCount=" + buyCount + ", deliverCount=" + deliverCount + ", returnCount="
            + returnCount + ", takeCount=" + takeCount + ", saleSpecQuantity=" + saleSpecQuantity + ", orderItemId="
            + orderItemId + ", productSkuId=" + productSkuId + "]";
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getProductSpecificationId() {
        return productSpecificationId;
    }

    public void setProductSpecificationId(Long productSpecificationId) {
        this.productSpecificationId = productSpecificationId;
    }

    public BigDecimal getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(BigDecimal buyCount) {
        this.buyCount = buyCount;
    }

    public BigDecimal getDeliverCount() {
        return deliverCount;
    }

    public void setDeliverCount(BigDecimal deliverCount) {
        this.deliverCount = deliverCount;
    }

    public BigDecimal getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }

    public BigDecimal getTakeCount() {
        return takeCount;
    }

    public void setTakeCount(BigDecimal takeCount) {
        this.takeCount = takeCount;
    }

    public BigDecimal getSaleSpecQuantity() {
        return saleSpecQuantity;
    }

    public void setSaleSpecQuantity(BigDecimal saleSpecQuantity) {
        this.saleSpecQuantity = saleSpecQuantity;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    /**
     * 获取orderItemId
     * 
     * @return orderItemId orderItemId
     */
    public Long getOrderItemId() {
        return orderItemId;
    }

    /**
     * 设置orderItemId
     * 
     * @param orderItemId orderItemId
     */
    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Long getSecOwnerId() {
        return secOwnerId;
    }

    public void setSecOwnerId(Long secOwnerId) {
        this.secOwnerId = secOwnerId;
    }
}

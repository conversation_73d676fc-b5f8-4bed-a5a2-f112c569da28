<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute.BatchAttributeTemplateMapper">
    <!--auto generated Code-->
    <resultMap id="BaseResultMap"
               type="com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplatePO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="TemplateName" property="templateName" jdbcType="VARCHAR"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="IsEnable" property="enable" jdbcType="BIT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!--auto generated Code-->
    <sql id="Base_Column_List">
        id,
        TemplateName,
        Remark,
        IsEnable,
        CreateUser
    </sql>

    <!--auto generated Code-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="batchAttributeTemplatePO.id">
        INSERT INTO batchattributetemplate (
        id,
        TemplateName,
        Remark,
        IsEnable,
        CreateUser
        ) VALUES (
        #{batchAttributeTemplatePO.id},
        #{batchAttributeTemplatePO.templateName,jdbcType=VARCHAR},
        #{batchAttributeTemplatePO.remark,jdbcType=VARCHAR},
        #{batchAttributeTemplatePO.enable,jdbcType=BIT},
        #{batchAttributeTemplatePO.createUser,jdbcType=VARCHAR}
        )
    </insert>


    <!--auto generated Code-->
    <insert id="insertList">
        INSERT INTO batchattributetemplate (
        <include refid="Base_Column_List"/>
        )VALUES
        <foreach collection="batchAttributeTemplatePOs" item="batchAttributeTemplatePO" index="index" separator=",">
            (
            #{batchAttributeTemplatePO.id,jdbcType=BIGINT},
            #{batchAttributeTemplatePO.templateName,jdbcType=VARCHAR},
            #{batchAttributeTemplatePO.remark,jdbcType=VARCHAR},
            #{batchAttributeTemplatePO.enable,jdbcType=BIT},
            #{batchAttributeTemplatePO.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--auto generated Code-->
    <update id="update">
        UPDATE batchattributetemplate
        <set>
            <if test="batchAttributeTemplatePO.templateName != null">TemplateName=
                #{batchAttributeTemplatePO.templateName,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeTemplatePO.remark != null">Remark=
                #{batchAttributeTemplatePO.remark,jdbcType=VARCHAR},
            </if>
            <if test="batchAttributeTemplatePO.enable != null">IsEnable=
                #{batchAttributeTemplatePO.enable,jdbcType=BIT},
            </if>
            <if test="batchAttributeTemplatePO.createUser != null">CreateUser=
                #{batchAttributeTemplatePO.createUser,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{batchAttributeTemplatePO.id,jdbcType=BIGINT}
    </update>

    <resultMap id="BaseResultMap1"
               type="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateReturnDTO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="Remark" property="remark" jdbcType="VARCHAR"/>
        <result column="TemplateName" property="templateName" jdbcType="VARCHAR"/>
        <result column="IsEnable" property="enable" jdbcType="BIT"/>
        <result column="CreateUser" property="createUser" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="VARCHAR"/>
        <collection property="relationList"
                    ofType="com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationReturnDTO">
            <id column="brid" property="id"/>
            <result column="AttributeId" property="dicId"/>
            <result column="AttributeName" property="attributeName"/>
            <result column="AttributeType" property="attributeType"/>
            <result column="EffectiveDigit" property="effectiveDigit"/>
            <result column="IsRequired" property="required"/>
            <result column="IsCalculation" property="isCalculation"/>
            <result column="IsStoreCheck" property="isStoreCheck"/>
        </collection>
    </resultMap>
    <!--auto generated by codehelper on 2018-04-09 21:37:51-->
    <select id="findBatchAttributeTemplateList" resultMap="BaseResultMap1">
        select
        b.id,
        b.TemplateName,
        b.Remark,
        b.IsEnable,
        b.CreateUser,
        DATE_FORMAT(b.CreateTime,'%Y-%m-%d %H:%i:%s') as CreateTime,
        br.id as brid,
        br.AttributeId,
        br.AttributeName,
        br.AttributeType,
        br.EffectiveDigit,
        br.IsRequired,
        br.IsCalculation,
        br.IsStoreCheck
        from batchattributetemplate b
        INNER JOIN batchattributetemplaterelation br on b.id = br.Template_id
        where 1=1
        <if test="dto.templateName != null and dto.templateName!=''">
            and TemplateName like concat(concat('%',#{dto.templateName,jdbcType=VARCHAR}),'%')
        </if>
    </select>

    <update id="updateIsEnable">
        UPDATE batchattributetemplate set
        IsEnable= #{batchAttributeTemplatePO.enable,jdbcType=BIT}
        WHERE id = #{batchAttributeTemplatePO.id,jdbcType=BIGINT}
    </update>

    <!--auto generated by codehelper on 2018-04-09 21:53:53-->
    <delete id="deleteById">
        delete from batchattributetemplate
        where id=#{id,jdbcType=BIGINT}
    </delete>

</mapper>


package com.yijiupi.himalaya.supplychain.inventory.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.dubbo.config.annotation.Service;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.DeliveryStoreRecordBL;
import com.yijiupi.himalaya.supplychain.inventory.dto.DeliveryStoreRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.IDeliveryStoreRecordService;

/**
 * 已发货记录
 *
 * <AUTHOR>
 * @date 2018/8/29 11:25
 */
@Service
public class DeliveryStoreRecordServiceImpl implements IDeliveryStoreRecordService {

    @Autowired
    private DeliveryStoreRecordBL deliveryStoreRecordBL;

    /**
     * 批量更新已发货总数量
     * 
     * @param list
     */
    @Override
    public void updateDeliveryStoreRecordBatch(List<DeliveryStoreRecordDTO> list) {
        deliveryStoreRecordBL.updateDeliveryStoreRecordBatch(list);
    }
}

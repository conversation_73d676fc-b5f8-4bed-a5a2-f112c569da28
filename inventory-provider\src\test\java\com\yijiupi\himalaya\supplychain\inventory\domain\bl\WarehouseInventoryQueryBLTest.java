package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryBySkuIdCityIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSkuForInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WareHoseInventoryQueryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseStoreDTO;

/**
 * <AUTHOR> 2017/12/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class WarehouseInventoryQueryBLTest {

    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;

    @Test
    public void getProductInventoryBySkuIdCityId() {
        ArrayList<ProductSkuForInventoryDTO> list = new ArrayList<>();
        ProductSkuForInventoryDTO dto1 = new ProductSkuForInventoryDTO();
        dto1.setCityId(100);
        dto1.setProductSkuId(10000000008878L);
        ProductSkuForInventoryDTO dto2 = new ProductSkuForInventoryDTO();
        dto2.setCityId(103);
        dto2.setProductSkuId(10300000010587L);
        list.add(dto1);
        list.add(dto2);

        List<InventoryBySkuIdCityIdDTO> bySkuIdCityId =
            warehouseInventoryQueryBL.getProductInventoryBySkuIdCityId(list);
    }

    @Test
    public void getDisposedProductInventoriesTest() {
        WareHoseInventoryQueryDTO wareHoseInventoryQueryDTO = new WareHoseInventoryQueryDTO();
        wareHoseInventoryQueryDTO.setProductSkuIds(Collections.singletonList(4824939808117091717L));
        wareHoseInventoryQueryDTO.setCityId(998);
        wareHoseInventoryQueryDTO.setWarehouseId(9981);
        List<WarehouseStoreDTO> resultList = warehouseInventoryQueryBL.getDisposedProductInventories(wareHoseInventoryQueryDTO);

        Assertions.assertThat(resultList).isNotNull();
    }

}

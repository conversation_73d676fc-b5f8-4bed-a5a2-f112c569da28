package com.yijiupi.himalaya.supplychain.inventory.domain.aspect;

import java.io.PrintWriter;
import java.io.StringWriter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @date 2018/7/9 16:21
 */
@Component
public class InventorySendFaildMQ {

    private static final Logger LOG = LoggerFactory.getLogger(InventorySendFaildMQ.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${ex.supplychain.mq.sendFaild}")
    private String sendFaildEx;

    /**
     * 发送失败消息
     * 
     * @param json （方法参数）
     * @param requestType
     * @param e
     */
    public void mqSendFaild(String json, String requestType, Throwable e) {
        RequestErrorTrackingDTO dto = new RequestErrorTrackingDTO();
        dto.setRequestType(requestType);
        dto.setRequestContent(json);
        dto.setResponseContent(e.toString());
        dto.setProductLine((byte)20);

        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw));
        try {
            LOG.info("{}发送失败消息:{},异常详细信息:{}", sendFaildEx, JSON.toJSONString(dto), sw.toString());
            rabbitTemplate.convertAndSend(sendFaildEx, null, dto);
        } catch (Exception ex) {
            LOG.info("{}发送失败消息异常:{}", sendFaildEx, ex.toString());
        }
    }
}

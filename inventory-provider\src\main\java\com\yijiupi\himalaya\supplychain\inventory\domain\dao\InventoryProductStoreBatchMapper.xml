<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventoryProductStoreBatchMapper">

    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductStoreBatchPO">
        <id column="Id" property="id" jdbcType="VARCHAR"/>
        <result column="productstore_id" property="productStoreId" jdbcType="VARCHAR"/>
        <result column="totalcount_minunit" property="totalCount" jdbcType="VARCHAR"/>
        <result column="productiondate" property="productionDate" jdbcType="TIMESTAMP"/>
        <result column="location_id" property="locationId" jdbcType="BIGINT"/>
        <result column="location_name" property="locationName" jdbcType="VARCHAR"/>
        <result column="locationCategory" property="locationCategory" jdbcType="TINYINT"/>
        <result column="subcategory" property="subcategory" jdbcType="TINYINT"/>
        <result column="expiretime" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="batchtime" property="batchTime" jdbcType="TIMESTAMP"/>
        <result column="createuserId" property="createUserId" jdbcType="INTEGER"/>
        <result column="batchProperty" property="batchProperty" jdbcType="TINYINT"/>
        <result column="businessType" property="businessType" jdbcType="TINYINT"/>
    </resultMap>

    <select id="findProductStoreBatch" resultMap="BaseResultMap">
        SELECT id,
        totalcount_minunit,
        productstore_id,
        batchtime,
        productiondate,
        expiretime, batchProperty, businessType
        from productstorebatch
        where productstore_id = #{productStoreId,jdbcType=VARCHAR} and totalcount_minunit &gt; 0
        order by createtime asc
    </select>

</mapper>
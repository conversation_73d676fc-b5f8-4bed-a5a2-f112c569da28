package com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.attribute;

import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.attribute.BatchAttributeTemplateRelationPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeEnableQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRelationReturnDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeTemplateRuleTypeQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.attribute.BatchAttributeValueQueryDTO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.BatchProductInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatchAttributeTemplateRelationMapper {
    int insert(
        @Param("batchAttributeTemplateRelationPO") BatchAttributeTemplateRelationPO batchAttributeTemplateRelationPO);

    int insertSelective(
        @Param("batchAttributeTemplateRelationPO") BatchAttributeTemplateRelationPO batchAttributeTemplateRelationPO);

    int insertList(@Param("batchAttributeTemplateRelationPOs") List<
        BatchAttributeTemplateRelationPO> batchAttributeTemplateRelationPOs);

    int update(
        @Param("batchAttributeTemplateRelationPO") BatchAttributeTemplateRelationPO batchAttributeTemplateRelationPO);

    /**
     * 根据关联Template_id删除
     *
     * @param templateId
     * @return
     */
    int deleteByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据产品信息查询批属性应填属性
     *
     * @param batchProductInfoDTO
     * @return
     */
    List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeTemplateRelation(@Param("dto") BatchProductInfoDTO batchProductInfoDTO);

    /**
     * 查询品牌和仓库绑定的批属性字典
     *
     * @param batchAttributeEnableQueryDTO
     * @return
     */
    List<BatchAttributeTemplateRelationReturnDTO>
        findBatchAttributeEnable(@Param("dto") BatchAttributeEnableQueryDTO batchAttributeEnableQueryDTO);

    /**
     * 根据配置类型和属性值Id查询批属性字典
     */
    List<BatchAttributeTemplateRelationReturnDTO>
        findAttributeTemplateByRuleTypeAndValueId(BatchAttributeTemplateRuleTypeQueryDTO ruleTypeQueryDTO);

    /**
     * 根据批次编号和适用服务商查询属性值
     */
    List<BatchAttributeTemplateRelationReturnDTO> findAttributeTemplateValue(BatchAttributeValueQueryDTO valueQueryDTO);
}

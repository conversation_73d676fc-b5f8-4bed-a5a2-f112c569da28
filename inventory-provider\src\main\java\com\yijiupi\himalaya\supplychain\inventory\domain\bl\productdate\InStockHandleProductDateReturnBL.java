package com.yijiupi.himalaya.supplychain.inventory.domain.bl.productdate;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.ProductStoreBatchChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.instockorder.dto.instock.InStockOrderDTO;
import com.yijiupi.himalaya.supplychain.instockorder.query.InStockOrderQueryCondition;
import com.yijiupi.himalaya.supplychain.instockorder.service.IInStockQueryService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OrderItemDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.order.OutStockOrderDTO;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockCommQueryService;

/**
 * Copyright © 2023 北京易酒批电子商务有限公司. All rights reserved. 处理客退单
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
@Service
public class InStockHandleProductDateReturnBL extends InStockHandleProductDateBaseBL {

    // @Reference
    // private IOrderQueryService iOrderQueryService;
    @Reference
    private IInStockQueryService inStockQueryService;
    @Reference
    private IOutStockCommQueryService iOutStockCommQueryService;

    @Override
    protected List<OrderDTO> doSupport(List<OrderDTO> orderList) {
        return orderList.stream().filter(order -> isReturnOrder(order.getOrderType(), order.getBusinessType())
            && !isNotNumberOrder(order.getRefOrderNo())).collect(Collectors.toList());
    }

    @Override
    protected void setProductionDate(Map<String, List<ProductStoreBatchChangeRecordDTO>> ownerIdAndSecOwnerIdOrderNoMap,
        OrderDTO order, OrderItemDTO d, Map<String, String> orderNoMap) {
        List<ProductStoreBatchChangeRecordDTO> orderStoreChangeRecordDTOList =
            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecificationId() + "_" + d.getOwnerId() + "_"
                + d.getSecOwnerId() + "_" + order.getRefOrderNo());
        List<ProductStoreBatchChangeRecordDTO> replaceOrderStorechangeRecordDTOList =
            ownerIdAndSecOwnerIdOrderNoMap.get(d.getProductSpecificationId() + "_" + d.getOwnerId() + "_"
                + d.getSecOwnerId() + "_" + orderNoMap.get(order.getRefOrderNo()));
        LOG.info("查询原单生产日期并赋值，获取orderStoreChangeRecordDTOList：{},获取replaceOrderStorechangeRecordDTOList：{}",
            JSON.toJSONString(orderStoreChangeRecordDTOList), JSON.toJSONString(replaceOrderStorechangeRecordDTOList));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(replaceOrderStorechangeRecordDTOList)) {
            d.setProductionDate(replaceOrderStorechangeRecordDTOList.get(0).getProductionDate());
            return;
        }
        if (CollectionUtils.isEmpty(orderStoreChangeRecordDTOList)) {
            return;
        }

        d.setProductionDate(orderStoreChangeRecordDTOList.get(0).getProductionDate());
    }

    @Override
    protected Map<String, String> getOrderNoMap(List<OrderDTO> orderList) {
        LOG.info("匹配订单为：{}", JSON.toJSONString(orderList));
        OrderDTO orderDTO = orderList.get(0);
        List<String> returnOrderNoList =
            orderList.stream().map(OrderDTO::getRefOrderNo).distinct().collect(Collectors.toList());
        InStockOrderQueryCondition queryDTO = new InStockOrderQueryCondition();
        queryDTO.setRefOrderNoList(returnOrderNoList);
        queryDTO.setOrgId(orderDTO.getOrgId());
        queryDTO.setWarehouseId(orderDTO.getWarehouseId());
        // queryDTO.setWarehouseId();

        List<InStockOrderDTO> inStockOrderDTOList = inStockQueryService.listOrderByCondition(queryDTO);
        if (CollectionUtils.isEmpty(inStockOrderDTOList)) {
            return Collections.emptyMap();
        }
        List<String> businessIds =
            inStockOrderDTOList.stream().map(this::getRelatedId).distinct().collect(Collectors.toList());
        // FIXME SCM-12065_【WMS_阶段二】OMS依赖服务下线-Core包 - 未处理
        // key是入库单的businessId，value是对应的出库单的businessId
        // Map<Long, Long> omsOrderIdMap =
        // iOrderQueryService.queryReturnToOrder(businessIds.stream().map(Long::valueOf).collect(Collectors.toList()));
        //
        // OutStockOrderQueryConditionDTO conditionDTO = new OutStockOrderQueryConditionDTO();
        // Integer warehouseId = orderList.get(0).getWarehouseId();
        // conditionDTO.setWarehouseId(warehouseId);
        // conditionDTO.setBusinessIds(omsOrderIdMap.values().stream().map(String::valueOf).collect(Collectors.toList()));
        // List<OutStockOrderDTO> outStockOrderDTOList =
        // iOutStockCommQueryService.findOutStockOrderBriefInfoByCondition(conditionDTO);
        //
        // return convertOrderNoMap(inStockOrderDTOList, omsOrderIdMap, outStockOrderDTOList);
        return new HashMap<>();
    }

    /**
     * @param inStockOrderDTOList 入库单
     * @param omsOrderIdMap key：入库单的businessId，value：出库单的businessId
     * @param outStockOrderDTOList 入库单对应的出库单
     * @return
     */
    private Map<String, String> convertOrderNoMap(List<InStockOrderDTO> inStockOrderDTOList,
        Map<Long, Long> omsOrderIdMap, List<OutStockOrderDTO> outStockOrderDTOList) {
        Map<String, OutStockOrderDTO> outStockOrderMap =
            outStockOrderDTOList.stream().collect(Collectors.toMap(OutStockOrderDTO::getBusinessId, v -> v));
        return inStockOrderDTOList.stream().collect(Collectors.toMap(InStockOrderDTO::getRefOrderNo,
            inStockOrder -> getRelatedRefOrderNo(inStockOrder, omsOrderIdMap, outStockOrderMap)));
    }

    private String getRelatedRefOrderNo(InStockOrderDTO inStockOrder, Map<Long, Long> omsOrderIdMap,
        Map<String, OutStockOrderDTO> outStockOrderMap) {
        Long outStockOrderBusinessId = omsOrderIdMap.get(Long.valueOf(getRelatedId(inStockOrder)));
        OutStockOrderDTO outStockOrderDTO = outStockOrderMap.get(outStockOrderBusinessId.toString());
        if (Objects.isNull(outStockOrderDTO)) {
            return inStockOrder.getRefOrderNo();
        }
        return outStockOrderDTO.getRefOrderNo();
    }

    private String getRelatedId(InStockOrderDTO m) {
        if (Objects.nonNull(m.getRefOrderId())) {
            return m.getRefOrderId();
        }
        return m.getBusinessId();
    }

}

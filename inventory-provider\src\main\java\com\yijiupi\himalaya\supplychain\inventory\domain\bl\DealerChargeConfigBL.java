package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.DealerChargeConfigConvert;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.DealerChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.WarehouseChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.easysell.ProductStoreRecordPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.DealerChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OrgDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.service.ISubOrgService;
import com.yijiupi.himalaya.uuid.UUIDGenerator;

/**
 * 经销商费用配置BL
 *
 * @author: lidengfeng
 * @date 2018/9/26 19:25
 */
@Service
public class DealerChargeConfigBL {

    private static final Logger LOG = LoggerFactory.getLogger(DealerChargeConfigBL.class);

    @Autowired
    private DealerChargeConfigPOMapper dealerChargeConfigPOMapper;

    @Autowired
    private DealerChargeConfigConvert dealerChargeConfigConvert;

    @Autowired
    private WarehouseChargeConfigPOMapper warehouseChargeConfigPOMapper;

    @Autowired
    private ProductStoreRecordPOMapper productStoreRecordPOMapper;

    @Reference
    private ISubOrgService iSubOrgService;

    /**
     * 新增或修改经销商费用
     *
     * @param dto
     * @return: void
     */
    public void saveOrUpdateDealerChargeConfig(DealerChargeConfigDTO dto) {
        LOG.info("新增或修改经销商费用：" + JSON.toJSONString(dto));
        DealerChargeConfigPO po = dealerChargeConfigConvert.reverseConvert(dto);
        DealerCountQuery dealerCountQuery = new DealerCountQuery();
        dealerCountQuery.setDealerId(Long.valueOf(dto.getDealerId()));
        dealerCountQuery.setFacilitatorId(dto.getFacilitatorId());
        int count = dealerChargeConfigPOMapper.selectCountByDealerId(dealerCountQuery);
        if (count > 0) {
            dealerChargeConfigPOMapper.updateDealerChargeConfig(po);
        } else {
            po.setId(UUIDGenerator.getUUID(DealerChargeConfigPO.class.getName()));
            dealerChargeConfigPOMapper.insertDealerChargeConfig(po);
        }
    }

    /**
     * 根据经销商id集合查询经销商费用配置 仓配服务首页
     * 
     * @param
     * @return
     */
    public PageList<DealerChargeConfigDTO> selectDealerChargeList(DealerChargeConfigQuery dealerChargeConfigQuery) {
        int facilitatorId = Math.toIntExact(dealerChargeConfigQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(dealerChargeConfigQuery.getFacilitatorId());
        dealerChargeConfigQuery.setFacilitatorIdList(facilitatorIdList);
        PageList<DealerChargeConfigDTO> pageList = new PageList<>();
        PageResult<DealerChargeConfigDTO> dealerChargeConfigList =
            dealerChargeConfigPOMapper.selectDealerChargeList(dealerChargeConfigQuery);
        pageList.setDataList(dealerChargeConfigList);
        pageList.setPager(dealerChargeConfigList.getPager());
        return pageList;
    }

    /**
     * 根据经销商id集合查询经销商费用配置 仓配服务首页
     * 
     * @param
     * @return
     */
    public PageList<DealerChargeConfigDTO> selectDealerList(DealerChargeConfigQuery dealerChargeConfigQuery) {
        int facilitatorId = Math.toIntExact(dealerChargeConfigQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(dealerChargeConfigQuery.getFacilitatorId());
        dealerChargeConfigQuery.setFacilitatorIdList(facilitatorIdList);
        PageList<DealerChargeConfigDTO> pageList = new PageList<>();
        PageResult<DealerChargeConfigDTO> dealerChargeConfigList =
            dealerChargeConfigPOMapper.selectDealerList(dealerChargeConfigQuery);
        pageList.setDataList(dealerChargeConfigList);
        pageList.setPager(dealerChargeConfigList.getPager());
        return pageList;
    }

    /**
     * 得到经销商是否存在
     * 
     * @param dealerCountQuery
     * @return
     */
    public Boolean selectCountByDealerId(DealerCountQuery dealerCountQuery) {
        Boolean flag = true;
        int count = dealerChargeConfigPOMapper.selectCountByDealerId(dealerCountQuery);
        if (count > 0) {
            flag = false;
        }
        return flag;
    }

    /**
     * 经销商费用配置明细查询
     *
     * @param dealerCountQuery
     * @return
     */
    public DealerChargeConfigDTO selectDealerChargeConfigById(DealerCountQuery dealerCountQuery) {
        DealerChargeConfigDTO dealerChargeConfigDTO =
            dealerChargeConfigPOMapper.selectDealerChargeConfigById(dealerCountQuery);
        return dealerChargeConfigDTO;
    }

    /**
     * 启用停用经销商费用配置
     *
     * @param dto
     * @return
     */
    public void updateDealerConfigStatus(DealerChargeConfigDTO dto) {
        DealerChargeConfigPO dealerChargeConfigPO = dealerChargeConfigConvert.reverseConvert(dto);
        dealerChargeConfigPOMapper.updateDealerConfigStatus(dealerChargeConfigPO);

    }

    /**
     * 根据经销商id，城市id查询仓库信息
     *
     * @param dealerWarehouseQuery
     * @return
     */
    public PageList<DealerWarehouseDTO> selectDealerWarehouseList(DealerWarehouseQuery dealerWarehouseQuery) {
        int facilitatorId = Math.toIntExact(dealerWarehouseQuery.getFacilitatorId());
        List<OrgDTO> cityMsg = iSubOrgService.findCityMsg(facilitatorId);
        List<Long> facilitatorIdList =
            cityMsg.stream().map(k -> Long.valueOf(k.getId())).distinct().collect(Collectors.toList());
        facilitatorIdList.add(dealerWarehouseQuery.getFacilitatorId());
        dealerWarehouseQuery.setFacilitatorIdList(facilitatorIdList);
        PageList<DealerWarehouseDTO> pageList = new PageList<>();
        PageResult<DealerWarehouseDTO> dealerWarehouseList =
            dealerChargeConfigPOMapper.selectDealerWarehouseList(dealerWarehouseQuery);
        pageList.setDataList(dealerWarehouseList);
        pageList.setPager(dealerWarehouseList.getPager());
        return pageList;
    }
}

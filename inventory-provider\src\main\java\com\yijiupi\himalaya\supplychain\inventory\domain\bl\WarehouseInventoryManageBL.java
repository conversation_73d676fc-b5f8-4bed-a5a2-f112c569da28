package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.exception.BusinessException;
import com.yijiupi.himalaya.base.exception.BusinessValidateException;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.dto.OrderProcessRuleConfigDTO;
import com.yijiupi.himalaya.supplychain.enums.InventoryChangeTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.enums.ProcessOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.enums.StoreOrderType;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.InventoryChangeEventFireBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.InventoryChangeFactory;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.SellInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change.WarehouseInventoryChangeBO;
import com.yijiupi.himalaya.supplychain.inventory.domain.bl.notify.PdaMessageNotifyBL;
import com.yijiupi.himalaya.supplychain.inventory.domain.converter.WarehouseChangListBOConverter;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.ProductInventoryPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.dao.WarehouseChargeConfigPOMapper;
import com.yijiupi.himalaya.supplychain.inventory.domain.model.SellInventoryChangeMessage;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductInventoryPO;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.ProductSkuPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductInventoryCheckDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryCheckRecordDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryModDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.WarehouseInventoryTransfersDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockOperateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockProductDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.WJStockOperateDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.WJStockProductDTO;
import com.yijiupi.himalaya.supplychain.inventory.util.StreamUtils;
import com.yijiupi.himalaya.supplychain.inventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPEventType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.ERPType;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.outInType;
import com.yijiupi.himalaya.supplychain.productsync.dto.product.ProductSkuConfigDTO;
import com.yijiupi.himalaya.supplychain.productsync.service.IProductSkuConfigService;
import com.yijiupi.himalaya.supplychain.service.IOrderProcessRuleConfigService;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.util.SecOwnerIdComparator;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;

/**
 * 仓库库存BL
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WarehouseInventoryManageBL {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseInventoryManageBL.class);

    @Autowired
    private WarehouseChargeConfigPOMapper warehouseChargeConfigPOMapper;

    @Autowired
    private ProductSkuQueryBL productSkuQueryBL;
    @Autowired
    private TradingThirdUserBL tradingThirdUserBL;
    @Autowired
    private InventoryChangeFactory inventoryChangeFactory;
    @Autowired
    private InventoryChangeEventFireBL inventoryChangeEventFireBL;
    @Autowired
    private ProductInventoryPOMapper productInventoryPOMapper;
    @Autowired
    private ProductStoreChangeRecordBL productStoreChangeRecordBL;
    @Autowired
    private WarehouseInventoryQueryBL warehouseInventoryQueryBL;
    @Autowired
    private OwnerTypeBL ownerTypeBL;
    @Autowired
    private ProductSecOwnerServiceBL productSecOwnerServiceBL;

    @Resource
    private PdaMessageNotifyBL pdaMessageNotifyBL;

    @Reference
    private IOrderProcessRuleConfigService iOrderProcessRuleConfigService;

    @Autowired
    private WarehouseInventoryAtomicBL warehouseInventoryAtomicBL;

    @Autowired
    private WarehouseChangListBOConverter warehouseChangListBOConverter;

    @Reference
    private IProductSkuConfigService iProductSkuConfigService;

    @Reference
    private IWarehouseQueryService warehouseQueryService;

    @Autowired
    private WarehouseInventorySyncBL warehouseInventorySyncBL;

    /**
     * 修改仓库库存数（根据库存对账记录变更，可以自定义库存变更明细）
     */
    public void modWarehouseInventoryByCheckRecord(WarehouseInventoryCheckRecordDTO warehouseInventoryCheckRecordDTO) {
        LOG.info("根据库存对账记录变更库存参数：{}", JSON.toJSONString(warehouseInventoryCheckRecordDTO));

        List<WarehouseInventoryChangeBO> lstBO = new ArrayList<>();
        Long productSkuId = warehouseInventoryCheckRecordDTO.getProductSkuId();
        Long specId = warehouseInventoryCheckRecordDTO.getProductSpecificationId();
        Long ownerId = warehouseInventoryCheckRecordDTO.getOwnerId();
        Long secOwnerId = warehouseInventoryCheckRecordDTO.getSecOwnerId();
        Integer cityId = warehouseInventoryCheckRecordDTO.getOrgId();
        BigDecimal packageQuantity = warehouseInventoryCheckRecordDTO.getPackageQuantity();

        BigDecimal packageCount =
            ObjectUtils.defaultIfNull(warehouseInventoryCheckRecordDTO.getPackageCount(), BigDecimal.ZERO);
        BigDecimal changeCount =
            ObjectUtils.defaultIfNull(warehouseInventoryCheckRecordDTO.getUnitCount(), BigDecimal.ZERO);

        if (specId == null) {
            // 通过skuId查询转换系数
            ProductSkuPO productSkuPO = productSkuQueryBL.getProductSkuBySkuId(productSkuId);
            AssertUtils.notNull(productSkuPO, "SkuId不存在,SkuId:" + productSkuId);

            specId = productSkuPO.getProductSpecificationId();
            ownerId = productSkuPO.getCompanyId();
            cityId = productSkuPO.getCityId();

            // 获取库存变更数量 = 大单位*转换系数+小单位 //处理大单位
            changeCount = ProcessPackageCount(productSkuPO, packageCount, changeCount);
        } else {
            // 获取库存变更数量 = 大单位*转换系数+小单位 //处理大单位
            changeCount = ProcessPackageCount(packageQuantity, packageCount, changeCount);
        }

        WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(
            productSkuId, warehouseInventoryCheckRecordDTO.getWarehouseId(), changeCount,
            warehouseInventoryCheckRecordDTO.getChannel(), ownerId, secOwnerId);
        warehouseInventoryChangeBO.setProductSpecificationId(specId);
        // 是否修改交易平台库存 op用到这个功能.
        warehouseInventoryChangeBO
            .setHasUpdateOPInventory(warehouseInventoryCheckRecordDTO.getHasUpdateOPInventory() == 1);
        warehouseInventoryChangeBO.setOrderType(warehouseInventoryCheckRecordDTO.getOrderType());
        warehouseInventoryChangeBO.setOrderNo(warehouseInventoryCheckRecordDTO.getOrderNo());
        warehouseInventoryChangeBO.setJiupiEventType(warehouseInventoryCheckRecordDTO.getJiupiEventType());
        warehouseInventoryChangeBO.setErpEventType(warehouseInventoryCheckRecordDTO.getErpEventType());
        warehouseInventoryChangeBO.setCreateUserName(warehouseInventoryCheckRecordDTO.getCreateUser());
        warehouseInventoryChangeBO.setDescription(warehouseInventoryCheckRecordDTO.getDescription());
        warehouseInventoryChangeBO.setSource(ProductSourceType.易酒批);
        warehouseInventoryChangeBO.setCityId(cityId);
        lstBO.add(warehouseInventoryChangeBO);

        // 处理库存相关
        processInventory(lstBO, false);
    }

    /**
     * 设置仓库库存数量
     *
     * @param lstDTO 设置仓库库存数量DTO
     * @param opUserId 操作人Id
     * @param inventoryChangeTypeEnum 库存变更类型
     */
    public void modWarehouseInventory(List<WarehouseInventoryModDTO> lstDTO, Integer opUserId,
        InventoryChangeTypeEnum inventoryChangeTypeEnum) {
        AssertUtils.notNull(inventoryChangeTypeEnum, "库存变更类型不能为空");

        List<WarehouseInventoryChangeBO> lstBO = new ArrayList<>();
        // 是否校验库存为空，默认false不校验
        boolean isCheckWarehouseInventory = false;
        // 操作人名称
        String trueName = "";
        if (InventoryChangeTypeEnum.设置仓库库存数量 == inventoryChangeTypeEnum) {
            // LOG.info("设置仓库库存数量...");
            AdminUser adminUser = tradingThirdUserBL.getAdminUserWithoutAuthById(opUserId);
            if (adminUser != null) {
                trueName = adminUser.getTrueName();
                if (StringUtils.isEmpty(trueName)) {
                    trueName = adminUser.getUserName();
                }
            }
        } else if (InventoryChangeTypeEnum.设置经销商库存 == inventoryChangeTypeEnum) {
            // LOG.info("设置经销商库存...");
            // 操作人名称不一样,这里是经销商的名称
            OwnerDTO shopAdminUser = tradingThirdUserBL.getShopAdminUserById(opUserId);
            if (shopAdminUser != null) {
                trueName = shopAdminUser.getOwnerName();
            }
            // 需要校验库存
            isCheckWarehouseInventory = true;
        }

        for (WarehouseInventoryModDTO warehouseInventoryModDTO : lstDTO) {
            WarehouseInventoryChangeBO warehouseInventoryChangeBO = null;
            if (warehouseInventoryModDTO.getProductStoreId() != null) {
                warehouseInventoryChangeBO =
                    getWarehouseInventoryChangeBOByProductStoreId(opUserId, trueName, warehouseInventoryModDTO);
            } else {
                warehouseInventoryChangeBO =
                    getWarehouseInventoryChangeBOByProductSku(opUserId, trueName, warehouseInventoryModDTO);
            }
            if (InventoryChangeTypeEnum.SAAS变更库存 == inventoryChangeTypeEnum
                || InventoryChangeTypeEnum.设置仓库库存数量 == inventoryChangeTypeEnum) {
                warehouseInventoryChangeBO.setDescription(warehouseInventoryModDTO.getDescription());
                warehouseInventoryChangeBO.setJiupiEventType(warehouseInventoryModDTO.getJiupiEventType());
                warehouseInventoryChangeBO.setErpEventType(warehouseInventoryModDTO.getErpEventType());
                warehouseInventoryChangeBO.setOrderType(warehouseInventoryModDTO.getOrderType());
                warehouseInventoryChangeBO.setOrderId(warehouseInventoryModDTO.getOrderId());
                warehouseInventoryChangeBO.setOrderNo(warehouseInventoryModDTO.getOrderNo());
                warehouseInventoryChangeBO.setCreateUserName(
                    warehouseInventoryModDTO.getOpUser() == null ? "" : warehouseInventoryModDTO.getOpUser());
            } else {
                warehouseInventoryChangeBO.setDescription(inventoryChangeTypeEnum.name());
                warehouseInventoryChangeBO.setJiupiEventType(InventoryChangeTypeEnum.ERP库存同步 == inventoryChangeTypeEnum
                    ? JiupiEventType.erp库存同步.getType() : JiupiEventType.手动修改.getType());
                warehouseInventoryChangeBO.setOrderType(InventoryChangeTypeEnum.ERP库存同步 == inventoryChangeTypeEnum
                    ? StoreOrderType.矫正库存 : StoreOrderType.OP_MANUAL_ORDER);
            }
            warehouseInventoryChangeBO.setAllocationCalculation(false);
            lstBO.add(warehouseInventoryChangeBO);
        }

        LOG.info(String.format("%s变更库存参数：%s", inventoryChangeTypeEnum.name(), JSON.toJSONString(lstBO)));
        // 处理库存相关
        processInventory(lstBO, isCheckWarehouseInventory);
    }

    private WarehouseInventoryChangeBO getWarehouseInventoryChangeBOByProductStoreId(Integer opUserId, String trueName,
        WarehouseInventoryModDTO warehouseInventoryModDTO) {
        String productStoreId = warehouseInventoryModDTO.getProductStoreId();
        List<ProductInventoryPO> productInventoryPOS =
            warehouseInventoryQueryBL.selectInventoryListByPrimaryKey(Collections.singletonList(productStoreId));
        AssertUtils.notEmpty(productInventoryPOS, "库存信息不存在,Id:" + productStoreId);
        ProductInventoryPO productInventoryPO = productInventoryPOS.get(0);

        BigDecimal packageCount =
            ObjectUtils.defaultIfNull(warehouseInventoryModDTO.getPackageCount(), BigDecimal.ZERO);
        BigDecimal changeCount = ObjectUtils.defaultIfNull(warehouseInventoryModDTO.getUnitCount(), BigDecimal.ZERO);// 将小单位数量赋给变更数量

        // 获取库存变更数量 = 大单位*转换系数+小单位 //处理大单位
        changeCount = ProcessPackageCount(productInventoryPO.getPackageQuantity(), packageCount, changeCount);

        WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(
            warehouseInventoryModDTO.getProductSkuId(), productInventoryPO.getWarehouseId(), changeCount,
            productInventoryPO.getChannel(), productInventoryPO.getOwnerId(), productInventoryPO.getSecOwnerId());

        warehouseInventoryChangeBO.setCreateUserName(trueName);
        warehouseInventoryChangeBO.setCreateUserId(opUserId + "");
        // ownerType,ownerId后面操作库存的时候会设置
        warehouseInventoryChangeBO.setHasUpdateOPInventory(warehouseInventoryModDTO.getHasUpdateOPInventory() == 1);// 是否修改交易平台库存
                                                                                                                    // op用到这个功能.
        // warehouseInventoryChangeBO.setJiupiEventType(InventoryChangeTypeEnum.ERP库存同步 == inventoryChangeTypeEnum ?
        // JiupiEventType.erp库存同步.getType() : JiupiEventType.手动修改.getType());
        // warehouseInventoryChangeBO.setOrderType(InventoryChangeTypeEnum.ERP库存同步 == inventoryChangeTypeEnum ?
        // StoreOrderType.矫正库存 : StoreOrderType.OP_MANUAL_ORDER);
        warehouseInventoryChangeBO.setProductSpecificationId(productInventoryPO.getProductSpecificationId());
        warehouseInventoryChangeBO.setSource(productInventoryPO.getSource());
        warehouseInventoryChangeBO.setCityId(productInventoryPO.getCityId());
        warehouseInventoryChangeBO.setSecOwnerId(productInventoryPO.getSecOwnerId());
        return warehouseInventoryChangeBO;
    }

    private WarehouseInventoryChangeBO getWarehouseInventoryChangeBOByProductSku(Integer opUserId, String trueName,
        WarehouseInventoryModDTO warehouseInventoryModDTO) {
        Long productSkuId = warehouseInventoryModDTO.getProductSkuId();
        BigDecimal packageCount =
            ObjectUtils.defaultIfNull(warehouseInventoryModDTO.getPackageCount(), BigDecimal.ZERO);
        BigDecimal changeCount = ObjectUtils.defaultIfNull(warehouseInventoryModDTO.getUnitCount(), BigDecimal.ZERO);// 将小单位数量赋给变更数量

        // 通过skuId查询转换系数
        ProductSkuPO productSkuPO = productSkuQueryBL.getProductSkuBySkuId(productSkuId);
        AssertUtils.notNull(productSkuPO, "SkuId不存在,SkuId:" + productSkuId);
        // 获取库存变更数量 = 大单位*转换系数+小单位 //处理大单位
        changeCount = ProcessPackageCount(productSkuPO, packageCount, changeCount);

        WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory.createWarehouseInventoryChangeBO(
            productSkuId, warehouseInventoryModDTO.getWarehouseId(), changeCount, warehouseInventoryModDTO.getChannel(),
            productSkuPO.getCompanyId(), productSkuPO.getSecOwnerId());

        warehouseInventoryChangeBO.setCreateUserName(trueName);
        warehouseInventoryChangeBO.setCreateUserId(opUserId + "");
        // ownerType,ownerId后面操作库存的时候会设置
        warehouseInventoryChangeBO.setHasUpdateOPInventory(warehouseInventoryModDTO.getHasUpdateOPInventory() == 1);// 是否修改交易平台库存
                                                                                                                    // op用到这个功能.
        warehouseInventoryChangeBO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
        warehouseInventoryChangeBO.setSource(productSkuPO.getSource());
        warehouseInventoryChangeBO.setCityId(productSkuPO.getCityId());
        warehouseInventoryChangeBO.setSecOwnerId(warehouseInventoryModDTO.getSecOwnerId());
        return warehouseInventoryChangeBO;
    }

    // 处理大单位,如果有大单位,则全部转换成小单位.
    private BigDecimal ProcessPackageCount(BigDecimal packageQuantity, BigDecimal packageCount,
        BigDecimal changeCount) {
        if (packageCount.abs().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal quantity = packageQuantity;// 转换系数
            changeCount = changeCount.add(packageCount.multiply(quantity));// 小单位+转换系数*大单位
        }
        return changeCount;
    }

    // 处理大单位,如果有大单位,则全部转换成小单位.
    private BigDecimal ProcessPackageCount(ProductSkuPO productSkuPO, BigDecimal packageCount, BigDecimal changeCount) {
        return ProcessPackageCount(productSkuPO.getPackageQuantity(), packageCount, changeCount);
    }

    /**
     * 处理配送完成后的已发货数量 todo
     *
     * @param deliveryBOList
     */
    public void processOrderDeliveryCount(List<WarehouseInventoryChangeBO> deliveryBOList) {
        Map<Long, BigDecimal> errSkuIdList = new HashMap<>(16);
        // 创建发货数量记录(如果有就进行更新)
        ArrayList<ProductInventoryPO> deliveryProductInventoryPOS = new ArrayList<>();
        validateOrderDeliveryProductStore(deliveryBOList, errSkuIdList, deliveryProductInventoryPOS, false, false);
        // 创建发货数量记录(如果有就进行更新)
        deliveryProductInventoryPOS.forEach(n -> n.setChangeCount(n.getChangeCount().multiply(new BigDecimal(-1))));
        // DeliveryStoreRecordBL.instance.addStoreRecordMsg(deliveryProductInventoryPOS);
        // inventoryChangeEventFireBL.deliveryCountInventoryChangeEvent(deliveryProductInventoryPOS);
        // deliveryStoreRecordMapper.insertOrUpdateDeliveryRecordBatch(deliveryProductInventoryPOS);
    }

    // 处理库存相关
    private void processInventory(WarehouseInventoryChangeBO warehouseInventoryChangeBO,
        boolean isCheckWarehouseInventory) {
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);
        processInventory(warehouseInventoryChangeBOS, isCheckWarehouseInventory);
    }

    // 处理库存相关
    public void processInventory(List<WarehouseInventoryChangeBO> boList, boolean isCheckWarehouseInventory) {
        // 处理库存变更.库存记录
        validateAndProcessProductStore(boList, isCheckWarehouseInventory, false, true, true, false);
        // 发送销售库存变更消息
        processSellInventory(boList, null);
    }

    /**
     * 处理销售库存
     *
     * @param sellChangeList
     * @param warehouseChangeList
     */
    public void processSellInventory(List<WarehouseInventoryChangeBO> warehouseChangeList,
        List<SellInventoryChangeBO> sellChangeList) {
        // 单独处理销售库存逻辑
        List<SellInventoryChangeMessage> lstSellInventoryChangMessage = new ArrayList<>();

        // 需要发销售库存变更消息的BO处理
        List<WarehouseInventoryChangeBO> lstProcessSellMsgBO = warehouseChangeList.stream()
            .filter(WarehouseInventoryChangeBO::getHasUpdateOPInventory).collect(Collectors.toList());
        for (WarehouseInventoryChangeBO sellBO : lstProcessSellMsgBO) {
            lstSellInventoryChangMessage.add(sellBO.createSellInventoryChangeMessage());
        }

        // 如果存在部分配送发送消息
        if (!CollectionUtils.isEmpty(sellChangeList)) {
            for (SellInventoryChangeBO sellInventoryChangeBO : sellChangeList) {
                if (sellInventoryChangeBO.getProductSpecificationId() != null) {
                    lstSellInventoryChangMessage.add(sellInventoryChangeBO.createMessage());
                    continue;
                }
                Long productSkuId = sellInventoryChangeBO.getProductSkuId();
                String skuSign = sellInventoryChangeBO.getSkuSign();
                Optional<WarehouseInventoryChangeBO> productChangeBO =
                    warehouseChangeList.stream().filter(p -> p.getSkuSign().equals(skuSign)).findFirst();
                if (productChangeBO.isPresent()) {
                    // 从库存BO中把销售库存变更需要的字段加上
                    WarehouseInventoryChangeBO warehouseInventoryChangeBO = productChangeBO.get();
                    sellInventoryChangeBO
                        .setProductSpecificationId(warehouseInventoryChangeBO.getProductSpecificationId());
                    sellInventoryChangeBO.setOwnType(warehouseInventoryChangeBO.getOwnType());
                    sellInventoryChangeBO.setCreateUserId(warehouseInventoryChangeBO.getCreateUserId());
                    sellInventoryChangeBO.setCreateUserName(warehouseInventoryChangeBO.getCreateUserName());
                } else {
                    // 根据SKU查询产品库存信息
                    ProductInventoryPO productInventoryPO = warehouseInventoryQueryBL.getProductInventoryPO(
                        productSkuId, sellInventoryChangeBO.getWarehouseId(), sellInventoryChangeBO.getChannel(),
                        sellInventoryChangeBO.getSecOwnerId());
                    if (productInventoryPO != null) {
                        sellInventoryChangeBO.setProductSpecificationId(productInventoryPO.getProductSpecificationId());
                        sellInventoryChangeBO.setOwnType(productInventoryPO.getOwnerType());
                    } else {
                        // 如果没找到库存，找对应的产品信息，把规格Id赋值
                        ProductSkuPO productSkuBySkuId =
                            productSkuQueryBL.getProductSkuBySkuId(sellInventoryChangeBO.getProductSkuId());
                        if (productSkuBySkuId != null) {
                            sellInventoryChangeBO
                                .setProductSpecificationId(productSkuBySkuId.getProductSpecificationId());
                            sellInventoryChangeBO.setSource(productSkuBySkuId.getSource());
                        }
                    }
                }
                lstSellInventoryChangMessage.add(sellInventoryChangeBO.createMessage());
            }
        }
        inventoryChangeEventFireBL.sellInventoryChangeEvent(lstSellInventoryChangMessage);
    }

    // 处理仓库库存
    public void validateAndProcessProductStore(List<WarehouseInventoryChangeBO> warehouseChangeList,
        boolean checkWarehouseInventory, boolean isUpdateDeliveryCount, boolean isUpdateProductStore,
        boolean isUpdateProductBatchStore, boolean isSkipNotExitsSku) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return;
        }
        Map<Long, BigDecimal> errSkuIdList = new HashMap<>(16);
        ArrayList<ProductInventoryPO> productInventoryPOS = new ArrayList<>();
        // 校验库存
        validateOrderDeliveryProductStore(warehouseChangeList, errSkuIdList, productInventoryPOS,
            checkWarehouseInventory, isSkipNotExitsSku);

        // LOG.info(String.format("查询库存结果：%s", JSON.toJSONString(productInventoryPOS)));
        // 换成批量 批量skuid查询交易平台
        if (!errSkuIdList.isEmpty()) {
            throw new BusinessValidateException(createErrorMessage(warehouseChangeList, errSkuIdList));
        }

        productInventoryPOS.removeIf(p -> p.getChangeCount().compareTo(BigDecimal.ZERO) == 0);
        // LOG.info("库存变更数据productInventoryPOS:{}", JSON.toJSONString(productInventoryPOS));

        if (CollectionUtils.isEmpty(productInventoryPOS)) {
            LOG.info(String.format(
                "没有找到库存！参数 checkWarehouseInventory:%s,isUpdateDeliveryCount:%s,isUpdateProductStore:%s,isUpdateProductBatchStore:%s, warehouseChangeList:%s,",
                checkWarehouseInventory, isUpdateDeliveryCount, isUpdateProductStore, isUpdateProductBatchStore,
                JSON.toJSONString(warehouseChangeList)));
        } else {

            if (isUpdateProductStore) {
                // 批量扣库存（以订单项为维度）
                productInventoryPOMapper.increaseWarehouseCountBatchById(productInventoryPOS);
                // 总库存变更消息
                inventoryChangeEventFireBL.warehouseInventoryChangeEvent(warehouseChangeList);
                // // 库存转移到replaceSku
                // inventoryChangeEventFireBL.storeTransferToReplaceSku(productInventoryPOS);
            }

            // 创建库存扣减记录（以订单项为维度）
            productStoreChangeRecordBL.processProductStoreChangeRecord(warehouseChangeList, productInventoryPOS,
                isUpdateProductBatchStore, isUpdateProductStore);

            // if (isUpdateDeliveryCount) {
            // //创建发货数量记录(如果有就进行更新)
            // productInventoryPOS.forEach(n -> n.setChangeCount(n.getChangeCount().multiply(new BigDecimal(-1))));
            // inventoryChangeEventFireBL.deliveryCountInventoryChangeEvent(productInventoryPOS);
            // }

            // 处理完之后修改出入库单detail数据并同步给oms
            syncOrders(warehouseChangeList);
        }
        // SCM-15219 2.5+实施优化，产品入库后如果未关联货位，给仓管pda推送通知
        pdaMessageNotifyBL.notifyWhenInStock(warehouseChangeList);
        // 加库存批量更新SKU上架状态
        warehouseInventorySyncBL.updateProductSkuState(warehouseChangeList, isUpdateProductStore);
    }

    /**
     * 同步订单明细
     * 
     * @param warehouseChangeList
     */
    private void syncOrders(List<WarehouseInventoryChangeBO> warehouseChangeList) {
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return;
        }
        List<WarehouseInventoryChangeBO> syncBOS = warehouseChangeList.stream()
            .filter(bo -> !Objects.equals(bo.getErpEventType(), ERPEventType.单据明细删除.getType())
                && !Objects.equals(bo.getErpEventType(), ERPEventType.单据删除.getType())
                && !Objects.equals(bo.getOrderType(), ERPType.库存盘点单.getType()) && bo.getOrderItemId() != null)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncBOS)) {
            return;
        }
        syncBOS.forEach(bo -> {
            long detailId = UUIDUtil.getUUIDLong(UUIDUtil.OUT_STOCK_ORDER_ITEM_DETAIL);
            if (bo.getOutInType() != null && bo.getOutInType() == outInType.in) {
                detailId = UUIDUtil.getUUIDLong(UUIDUtil.IN_STOCK_ORDER_ITEM_DETAIL);
            }
            bo.setOrderItemDetailId(detailId);
        });
        inventoryChangeEventFireBL.syncOrderDetails(syncBOS);
    }

    /**
     * 根据skuId获取specId
     *
     * @param productSkuIds
     * @return
     */
    private Map<Long, ProductSkuPO> getSpecMap(List<Long> productSkuIds) {
        Map<Long, ProductSkuPO> productSpecMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(productSkuIds)) {
            // 查询specId
            List<ProductSkuPO> productSkuPOS = productSkuQueryBL.getProductSkuListByIds(productSkuIds);
            if (!CollectionUtils.isEmpty(productSkuPOS)) {
                for (ProductSkuPO productSkuPO : productSkuPOS) {
                    productSpecMap.put(productSkuPO.getProductSkuId(), productSkuPO);
                }
            }
        }
        return productSpecMap;
    }

    /**
     * 校验批量发货库存
     *
     * @param warehouseChangeList bolist
     * @param errSkuIdList 不存在的skuidlist
     * @param productInventoryPOS
     */
    public void validateOrderDeliveryProductStore(List<WarehouseInventoryChangeBO> warehouseChangeList,
        Map<Long, BigDecimal> errSkuIdList, ArrayList<ProductInventoryPO> productInventoryPOS,
        boolean isCheckProductStore, boolean isSkipNotExitsSku) {
        // k(warehouseId+channel)->v(BO)
        Map<String, List<WarehouseInventoryChangeBO>> boMapperByChannelAndWareHouseId =
            warehouseChangeList.stream().filter(d -> d.getWarehouseIdAndChannelAndOwnerId() != null)
                .collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getWarehouseIdAndChannelAndOwnerId));
        LOG.info("warehouseChangeList={}", JSON.toJSONString(warehouseChangeList));
        // 后续warehouseChangeList有可能会变化，先清空、后续添加
        warehouseChangeList.clear();

        for (String key : boMapperByChannelAndWareHouseId.keySet()) {

            // 未入过库的需新增记录
            List<ProductInventoryPO> insertProductInventoryPOS = new ArrayList<>();

            List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOBySkuANdChannel =
                boMapperByChannelAndWareHouseId.get(key);

            // LOG.info("仓库库存开始处理WarehouseInventoryChangeBO:{}",
            // JSON.toJSONString(warehouseInventoryChangeBOBySkuANdChannel));

            warehouseInventoryChangeBOBySkuANdChannel
                .removeIf(p -> p.getProductSkuId() == null && p.getProductSpecificationId() == null);

            List<Long> lstNoSpecSkuIds = warehouseInventoryChangeBOBySkuANdChannel.stream()
                .filter(p -> p.getProductSkuId() != null && p.getProductSpecificationId() == null)
                .map(WarehouseInventoryChangeBO::getProductSkuId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lstNoSpecSkuIds)) {
                LOG.info(String.format("发现规格Id为空的Sku，开始处理：%s", JSON.toJSONString(lstNoSpecSkuIds)));
                Map<Long, ProductSkuPO> specMap = getSpecMap(lstNoSpecSkuIds);
                warehouseInventoryChangeBOBySkuANdChannel.forEach(p -> {
                    if (p.getProductSkuId() != null && p.getProductSpecificationId() == null) {
                        ProductSkuPO skuPO = specMap.get(p.getProductSkuId());
                        if (skuPO != null) {
                            p.setProductSpecificationId(skuPO.getProductSpecificationId());
                            p.setOwnId(skuPO.getCompanyId());
                            lstNoSpecSkuIds.remove(p.getProductSkuId());
                        }
                    }
                });
            }

            if (!CollectionUtils.isEmpty(lstNoSpecSkuIds)) {
                // 跳过规格不存在的SKU
                if (isSkipNotExitsSku) {
                    LOG.warn(String.format("发现规格Id为空且不存在的Sku，跳过不处理：%s", JSON.toJSONString(lstNoSpecSkuIds)));
                    warehouseInventoryChangeBOBySkuANdChannel
                        .removeIf(p -> p.getProductSkuId() != null && p.getProductSpecificationId() == null);
                } else {
                    throw new BusinessException("SkuId不存在：" + JSON.toJSONString(lstNoSpecSkuIds));
                }
            }

            // 如果没有有效数据，跳过
            if (CollectionUtils.isEmpty(warehouseInventoryChangeBOBySkuANdChannel)) {
                continue;
            }

            // 整合以SKU为维度的list（以SKU为维度），skuId->扣除的总数量
            Map<Long,
                BigDecimal> productStoreMap = warehouseInventoryChangeBOBySkuANdChannel.stream()
                    .filter(d -> d.getProductSpecificationId() != null)
                    .collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getProductSpecificationId,
                        Collectors.reducing(BigDecimal.ZERO, WarehouseInventoryChangeBO::getCount, BigDecimal::add)));
            Map<Long, Long> specSkuMap = new HashMap<>(16);
            warehouseInventoryChangeBOBySkuANdChannel.forEach(p -> {
                if (!specSkuMap.containsKey(p.getProductSpecificationId())) {
                    specSkuMap.put(p.getProductSpecificationId(), p.getProductSkuId());
                }
            });

            WarehouseInventoryChangeBO warehouseInventoryChangeBO = warehouseInventoryChangeBOBySkuANdChannel.get(0);
            Integer cityId = warehouseInventoryChangeBO.getCityId();
            Integer warehouseId = warehouseInventoryChangeBO.getWarehouseId();
            Integer channel = warehouseInventoryChangeBO.getChannel();
            Long ownerId = warehouseInventoryChangeBO.getOwnId();
            // Long secOwnerID = ownerId == null ? null : warehouseInventoryChangeBO.getSecOwnerId();

            // SCM2-8596 店仓多货主调整: 店仓不抹货主
            // if (ownerId != null) {
            // Boolean ownerInfoNotAutomaticFillFlag =
            // inStockQueryService.findOwnerInfoEraseWarehouseConfig(warehouseId);
            // if (ownerInfoNotAutomaticFillFlag) {
            // LOG.info(String.format("当前仓库：%s,需要清除货主：%s", warehouseId, ownerId));
            // warehouseInventoryChangeBOBySkuANdChannel.forEach(p -> {
            // p.setOwnId(null);
            // });
            // ownerId = null;
            // }
            // }

            // **批量获取仓库库存
            Map<Long, List<ProductInventoryPO>> longProductInventoryPOMap =
                getLongProductInventoryPOMapAndCreateNoExits(specSkuMap, cityId, warehouseId, ownerId, channel, null);
            List<ProductInventoryPO> productInventoryPOList = new ArrayList<>();
            // 将不存在的创建出一条记录出来
            insertProductInventoryPOS.addAll(createProductStoreByChangeBOS(warehouseInventoryChangeBOBySkuANdChannel,
                longProductInventoryPOMap, productInventoryPOList));

            if (!CollectionUtils.isEmpty(insertProductInventoryPOS)) {
                insertProductInventoryPOS.stream().collect(Collectors.groupingBy(ProductInventoryPO::getWarehouseId))
                    .forEach((lstWarehouseId, list) -> {
                        warehouseInventoryAtomicBL.insertInventoryPOList(lstWarehouseId, list);
                    });
            }

            // 后续计算使用
            productInventoryPOList.forEach(inventory -> {
                if (inventory.getChangeCount() == null) {
                    // 避免影响变更记录中的原库存数量
                    inventory.setCalcTotalCountMinUnit(inventory.getTotalCountMinUnit());
                    // 避免后面计算报错
                    inventory.setChangeCount(BigDecimal.ZERO);
                }
            });

            longProductInventoryPOMap = productInventoryPOList.stream()
                .collect(Collectors.groupingBy(ProductInventoryPO::getProductSpecificationId));
            LOG.info("仓库库存处理查询到的仓库库存longProductInventoryPOMap:{}", JSON.toJSONString(longProductInventoryPOMap));

            productSecOwnerServiceBL.processSecOwnerIdRelateInfo(productInventoryPOList);

            // 校验库存,根据库存信息.进行比对.
            warehouseInventoryChangeBOBySkuANdChannel.forEach(bo -> bo.setResultCount(bo.getCount()));
            Map<Long, List<WarehouseInventoryChangeBO>> changeBOMap = warehouseInventoryChangeBOBySkuANdChannel.stream()
                .collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getProductSpecificationId));

            for (Map.Entry<Long, List<ProductInventoryPO>> inventoryEntry : longProductInventoryPOMap.entrySet()) {
                List<ProductInventoryPO> inventories = inventoryEntry.getValue();

                List<WarehouseInventoryChangeBO> changeBOS = changeBOMap.get(inventoryEntry.getKey());

                BigDecimal changeTotalCount = productStoreMap.get(inventoryEntry.getKey());
                BigDecimal warehouseTotalCount = inventories.stream()
                    .filter(inventory -> inventory.getCalcTotalCountMinUnit().compareTo(BigDecimal.ZERO) > 0)
                    .map(ProductInventoryPO::getCalcTotalCountMinUnit).reduce(BigDecimal.ZERO, BigDecimal::add);
                Long productSkuId = inventories.get(0).getProductSkuId();

                // 校验库存且仓库大于0的库存总数量比要处理的数量少时，需要记录下来后续提示
                if (isCheckProductStore) {
                    // LOG.info("errSkuIdList:{}", JSON.toJSONString(errSkuIdList));
                    checkProductStore(changeBOS, inventories, errSkuIdList);
                    if (errSkuIdList.get(productSkuId) != null) {
                        warehouseChangeList.addAll(changeBOS);
                        continue;
                    }
                }

                // if (ownerId != null) {
                // // 非久批业务需要规格货主一致,且只有一条库存记录
                // ProductInventoryPO productInventoryPO = inventories.get(0);
                // productInventoryPO.setChangeCount(changeTotalCount);
                // productInventoryPOS.add(productInventoryPO);
                //
                // warehouseChangeList.addAll(warehouseInventoryChangeBOBySkuANdChannel);
                // } else {
                //
                // }

                // 久批业务可能存在多供应商，需循环计算分配
                Map<String, ProductInventoryPO> signWarehouseInventoryMap = inventories.stream().collect(
                    Collectors.toMap(ProductInventoryPO::getSkuSign, Function.identity(), (key1, key2) -> key1));
                // LOG.info("仓库库存开始处理changeBOS:{},signWarehouseInventoryMap:{}", JSON.toJSONString(changeBOS),
                // JSON.toJSONString(signWarehouseInventoryMap));
                processSupplierInventory(productInventoryPOS, signWarehouseInventoryMap, changeBOS, changeTotalCount);
                // LOG.info("仓库库存处理结果changeBOS:{}", JSON.toJSONString(changeBOS));
                warehouseChangeList.addAll(changeBOS);
            }

            if (CollectionUtils.isEmpty(productInventoryPOS)) {
                LOG.info(String.format("查找库存项为空！  warehouseChangeList:%s,longProductInventoryPOMap:%s",
                    JSON.toJSONString(warehouseChangeList), JSON.toJSONString(longProductInventoryPOMap)));
            }

            warehouseChangeList.removeIf(i -> i.getCount().compareTo(BigDecimal.ZERO) == 0);
        }

        // 将库存PO中的信息回写到BOList中，下边会用到
        if (CollectionUtils.isEmpty(errSkuIdList)) {
            for (ProductInventoryPO productInventoryPO : productInventoryPOS) {
                List<WarehouseInventoryChangeBO> lstWarehouseChangListBySkuAndWareIdAndChannel =
                    warehouseChangeList.stream()
                        .filter(p -> Objects.equals(p.getWarehouseId(), productInventoryPO.getWarehouseId())
                            && Objects.equals(p.getChannel(), productInventoryPO.getChannel())
                            && (p.getProductSpecificationId() == null || Objects.equals(p.getProductSpecificationId(),
                                productInventoryPO.getProductSpecificationId()))
                            && (p.getProductSkuId() == null
                                || Objects.equals(p.getProductSkuId(), productInventoryPO.getProductSkuId()))
                            && Objects.equals(p.getOwnId(), productInventoryPO.getOwnerId())
                            && Objects.equals(p.getSecOwnerId(), productInventoryPO.getSecOwnerId()))
                        .collect(Collectors.toList());
                lstWarehouseChangListBySkuAndWareIdAndChannel.forEach(warehouseInventoryChangeBO -> {
                    warehouseInventoryChangeBO.setOwnId(productInventoryPO.getOwnerId());
                    warehouseInventoryChangeBO.setOwnType(productInventoryPO.getOwnerType());
                    warehouseInventoryChangeBO.setChannel(productInventoryPO.getChannel());
                    warehouseInventoryChangeBO.setSecOwnerId(productInventoryPO.getSecOwnerId());
                    warehouseInventoryChangeBO
                        .setProductSpecificationId(productInventoryPO.getProductSpecificationId());
                    warehouseInventoryChangeBO.setCityId(productInventoryPO.getCityId());
                    warehouseInventoryChangeBO.setSource(productInventoryPO.getSource());
                });
            }
        }
    }

    /**
     * 库存校验(同规格id)
     */
    private void checkProductStore(List<WarehouseInventoryChangeBO> changeBOS, List<ProductInventoryPO> inventories,
        Map<Long, BigDecimal> errSkuIdList) {
        if (CollectionUtils.isEmpty(changeBOS)) {
            return;
        }
        List<WarehouseInventoryChangeBO> bos =
            changeBOS.stream().filter(bo -> bo.getCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        // 加库存默认不校验
        if (CollectionUtils.isEmpty(bos)) {
            return;
        }

        Long productSkuId = bos.get(0).getProductSkuId();
        BigDecimal errCount = errSkuIdList.get(productSkuId);
        // LOG.info("errCount:{},changeBOS:{}", errCount, JSON.toJSONString(bos));

        // 查不到库存的话，以总数为准
        if (CollectionUtils.isEmpty(inventories)) {
            BigDecimal changeTotalCount =
                bos.stream().map(WarehouseInventoryChangeBO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (errCount != null) {
                errCount = errCount.add(changeTotalCount);
            } else {
                errCount = changeTotalCount;
            }
            if (errCount.compareTo(BigDecimal.ZERO) < 0) {
                errSkuIdList.put(productSkuId, errCount);
            }
            return;
        }

        Map<String, BigDecimal> inventoryMap = new HashMap<>(16);
        inventories.forEach(inventory -> {
            inventoryMap.put(inventory.getSkuSign(), inventory.getTotalCountMinUnit());
        });

        List<WarehouseInventoryChangeBO> mandatoryCheckBOS =
            bos.stream().filter(bo -> !bo.getAllocationCalculation()).collect(Collectors.toList());
        List<WarehouseInventoryChangeBO> checkBOS =
            bos.stream().filter(WarehouseInventoryChangeBO::getAllocationCalculation).collect(Collectors.toList());

        // 强校验的需要完全匹配供应商库存
        if (!CollectionUtils.isEmpty(mandatoryCheckBOS)) {
            for (WarehouseInventoryChangeBO bo : mandatoryCheckBOS) {
                BigDecimal storeCount = inventoryMap.get(bo.getSkuSign());
                BigDecimal resultCount = storeCount.add(bo.getCount());
                if (resultCount.compareTo(BigDecimal.ZERO) < 0) {
                    // LOG.info(String.format("mandatoryCheckBOS storeCount:%s,resultCount:%s,errorCount:%s",
                    // storeCount, resultCount, errCount));
                    if (errCount != null) {
                        errCount = errCount.add(resultCount);
                    } else {
                        errCount = resultCount;
                    }
                }
                inventoryMap.put(bo.getSkuSign(), resultCount);
            }
        }

        // 不需要强校验的匹配一级货主库存
        if (!CollectionUtils.isEmpty(checkBOS)) {
            BigDecimal checkCount =
                checkBOS.stream().map(WarehouseInventoryChangeBO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal storeCount = inventoryMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal resultCount = checkCount.add(storeCount);
            if (resultCount.compareTo(BigDecimal.ZERO) < 0) {
                // LOG.info(String.format("changeBOS storeCount:%s,resultCount:%s,errorCount:%s", storeCount,
                // resultCount, errCount));
                if (errCount != null) {
                    errCount = errCount.add(resultCount);
                } else {
                    errCount = resultCount;
                }
            }
        }

        if (errCount != null) {
            errSkuIdList.put(productSkuId, errCount);
        }
    }

    /**
     * 通过不存在的bo数据创建库存记录
     * 
     * @return
     */
    private List<ProductInventoryPO> createProductStoreByChangeBOS(
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS,
        Map<Long, List<ProductInventoryPO>> productInventoryMap, List<ProductInventoryPO> productInventoryPOS) {
        List<ProductInventoryPO> insertProductInventoryPOS = new ArrayList<>();
        productInventoryMap.forEach((specId, inventoryList) -> productInventoryPOS.addAll(inventoryList));

        // 扣减库存的只有当该规格无库存记录时才创建
        warehouseInventoryChangeBOS.stream().filter(bo -> bo.getCount().compareTo(BigDecimal.ZERO) < 0)
            .collect(Collectors.groupingBy(WarehouseInventoryChangeBO::getProductSpecificationId))
            .forEach((specId, changeBOS) -> {
                List<ProductInventoryPO> productInventoryPOList = productInventoryMap.get(specId);
                if (CollectionUtils.isEmpty(productInventoryPOList)) {
                    changeBOS.forEach(bo -> {
                        processInsertInventoryPOS(insertProductInventoryPOS, specId, bo);
                    });
                }
            });

        // 加库存的，如果强一致匹配不存在的，需要创建
        warehouseInventoryChangeBOS.stream()
            .filter(bo -> bo.getCount().compareTo(BigDecimal.ZERO) > 0 || !bo.getAllocationCalculation())
            .collect(
                Collectors.toMap(WarehouseInventoryChangeBO::getSkuSign, Function.identity(), (key1, key2) -> key1))
            .forEach((skuSign, bo) -> {
                List<ProductInventoryPO> productInventoryPOList = productInventoryPOS.stream()
                    .filter(inventory -> skuSign.equals(inventory.getSkuSign())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(productInventoryPOList)) {
                    processInsertInventoryPOS(insertProductInventoryPOS, bo.getProductSpecificationId(), bo);
                }
            });
        if (!CollectionUtils.isEmpty(insertProductInventoryPOS)) {
            LOG.info(String.format(
                "新增库存参数：warehouseInventoryChangeBOS:%s=======productInventoryPOS:%s,=======insertProductInventoryPOS:%s",
                JSON.toJSONString(warehouseInventoryChangeBOS), JSON.toJSONString(productInventoryPOS),
                JSON.toJSONString(insertProductInventoryPOS)));
        }
        productInventoryPOS.addAll(insertProductInventoryPOS);
        return insertProductInventoryPOS;
    }

    private void processInsertInventoryPOS(List<ProductInventoryPO> insertProductInventoryPOS, Long specId,
        WarehouseInventoryChangeBO bo) {
        Long ownerId = bo.getOwnId();
        Integer ownerType = ownerTypeBL.getOwnerTypeByOwnerAndCityId(bo.getCityId(), ownerId);
        ProductInventoryPO productInventoryPO = insertProductInventory(specId, bo.getWarehouseId(), ownerType,
            bo.getOwnId(), bo.getCityId(), bo.getSecOwnerId(), bo.getChannel());
        productInventoryPO.setProductSkuId(bo.getProductSkuId());
        // 新增中台SkuId
        productInventoryPO.setUnifySkuId(bo.getUnifySkuId());
        if (!insertProductInventoryPOS.stream()
            .anyMatch(q -> Objects.equals(q.getWarehouseId(), productInventoryPO.getWarehouseId())
                && Objects.equals(q.getProductSpecificationId(), productInventoryPO.getProductSpecificationId())
                && Objects.equals(q.getOwnerId(), productInventoryPO.getOwnerId())
                && Objects.equals(q.getSecOwnerId(), productInventoryPO.getSecOwnerId()))) {
            insertProductInventoryPOS.add(productInventoryPO);
        }
    }

    /**
     * 同规格处理包含供应商的库存(方法内bo都是同规格数据)
     * 
     * @param productInventoryPOS 最终计算结果
     * @param signWarehouseInventoryMap 仓库库存
     * @param changeBOS 变更bo
     * @param changeTotalCount 变更总数量
     */
    private void processSupplierInventory(ArrayList<ProductInventoryPO> productInventoryPOS,
        Map<String, ProductInventoryPO> signWarehouseInventoryMap, List<WarehouseInventoryChangeBO> changeBOS,
        BigDecimal changeTotalCount) {
        if (changeTotalCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 降序，出现同一供应商产品一减一加时，优先处理加
        changeBOS.sort(Comparator.comparing(WarehouseInventoryChangeBO::getCount));

        for (WarehouseInventoryChangeBO bo : changeBOS) {
            ProductInventoryPO productInventory = signWarehouseInventoryMap.get(bo.getSkuSign());
            if (productInventory == null) {
                bo.setCount(BigDecimal.ZERO);
                continue;
            }

            BigDecimal warehouseCount = productInventory.getCalcTotalCountMinUnit();
            BigDecimal changeCount = bo.getCount();
            BigDecimal warehouseChangeCount = changeCount;

            // 不需要计算的则强一致匹配处理库存
            if (!bo.getAllocationCalculation()) {
                bo.setResultCount(BigDecimal.ZERO);
            } else {
                if (changeCount.compareTo(BigDecimal.ZERO) > 0) {
                    // 标记处理结果
                    bo.setResultCount(BigDecimal.ZERO);
                } else {
                    // 扣库存的需要循环计算处理
                    if (warehouseCount.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal resultCount = warehouseCount.add(changeCount);

                        if (resultCount.compareTo(BigDecimal.ZERO) < 0) {
                            // 仓库库存不够扣的则扣除仓库库存
                            warehouseChangeCount = warehouseCount.negate();
                            // 重置实际扣减数量
                            bo.setCount(warehouseCount.negate());
                            // 标记处理结果
                            bo.setResultCount(resultCount);
                        } else {
                            // 标记处理结果
                            bo.setResultCount(BigDecimal.ZERO);
                        }
                    } else {
                        // 仓库库存为负数的数据最后处理
                        bo.setCount(BigDecimal.ZERO);
                        continue;
                    }

                }
            }

            // changeTotalCount = changeTotalCount.subtract(productInventory.getChangeCount());
            productInventory.setChangeCount(productInventory.getChangeCount().add(warehouseChangeCount));
            productInventory.setCalcTotalCountMinUnit(warehouseCount.add(warehouseChangeCount));
            productInventoryPOS.add(productInventory);
        }

        // 没有处理完的继续计算
        List<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = changeBOS.stream()
            .filter(bo -> bo.getResultCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(warehouseInventoryChangeBOS)) {
            LOG.info("第一次未处理完成的数据:{}", JSON.toJSONString(warehouseInventoryChangeBOS));
            // 未处理完的changeCount需要进行下一步计算
            // List<String> skuSigns =
            // changeBOS.stream().map(WarehouseInventoryChangeBO::getSkuSign).collect(Collectors.toList());

            // // changeBOS未处理完，变更数量未处理完，需要将符合BO条件且有多余库存的参与下一步计
            // List<ProductInventoryPO> unfinishedWarehouseInventoryList =
            // signWarehouseInventoryMap.values().stream().filter(inventory -> skuSigns.contains(inventory.getSkuSign())
            // && inventory.getTotalCountMinUnit().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            //
            // unprocessedSupplierInventory(productInventoryPOS, unfinishedWarehouseInventoryList, changeTotalCount);

            // changeBOS处理完，变更数量未处理完，需要将不符合BO条件且有库存的数据参与下一步计算
            // List<ProductInventoryPO> surplusWarehouseInventoryList =
            // signWarehouseInventoryMap.values().stream().filter(inventory ->
            // !skuSigns.contains(inventory.getSkuSign()) && inventory.getTotalCountMinUnit().compareTo(BigDecimal.ZERO)
            // > 0).collect(Collectors.toList());

            // 没处理完的，拿其他同规格库存数大于0的数据进行处理
            List<ProductInventoryPO> surplusWarehouseInventoryList = signWarehouseInventoryMap.values().stream()
                .filter(inventory -> inventory.getCalcTotalCountMinUnit().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(surplusWarehouseInventoryList)) {
                for (WarehouseInventoryChangeBO bo : warehouseInventoryChangeBOS) {
                    processSurplusInventory(productInventoryPOS, surplusWarehouseInventoryList, changeBOS, bo);
                }
            }

            // 最后还未处理完的，需要将仓库库存扣成负的，先将规格货主一致的扣成负的，如没有，则随机扣成负的
            warehouseInventoryChangeBOS = changeBOS.stream()
                .filter(bo -> bo.getResultCount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
            LOG.info("未处理完成的数据:{}", JSON.toJSONString(warehouseInventoryChangeBOS));
            for (WarehouseInventoryChangeBO bo : warehouseInventoryChangeBOS) {
                ProductInventoryPO productInventoryPO = signWarehouseInventoryMap.get(bo.getSkuSign());
                if (productInventoryPO == null) {
                    productInventoryPO = signWarehouseInventoryMap.values().stream().findFirst().get();
                }
                productInventoryPO.setChangeCount(productInventoryPO.getChangeCount().add(bo.getResultCount()));
                productInventoryPO
                    .setCalcTotalCountMinUnit(productInventoryPO.getCalcTotalCountMinUnit().add(bo.getResultCount()));
                bo.setSecOwnerId(productInventoryPO.getSecOwnerId());
                bo.setCount(bo.getCount().add(bo.getResultCount()));

                productInventoryPOS.add(productInventoryPO);
            }
        }
        // 去重
        ArrayList<ProductInventoryPO> productInventoryPOArrayList =
            productInventoryPOS.stream().filter(StreamUtils.distinctByKey(ProductInventoryPO::getId))
                .collect(Collectors.toCollection(ArrayList::new));
        productInventoryPOS.clear();
        productInventoryPOS.addAll(productInventoryPOArrayList);
    }

    /**
     * 扣除未处理完的数量
     * 
     * @param productInventoryPOS 最终计算结果
     * @param warehouseInventoryList 符合BO条件的仓库库存数据
     * @param changeTotalCount 需要扣减的数量(结束标示)
     */
    private void unprocessedSupplierInventory(ArrayList<ProductInventoryPO> productInventoryPOS,
        List<ProductInventoryPO> warehouseInventoryList, BigDecimal changeTotalCount) {
        if (changeTotalCount.compareTo(BigDecimal.ZERO) >= 0 || CollectionUtils.isEmpty(warehouseInventoryList)) {
            return;
        }

        // 优先将久批的扣完
        warehouseInventoryList.sort(Comparator.nullsFirst(Comparator.comparing(ProductInventoryPO::getSecOwnerId)));
        for (ProductInventoryPO productInventory : warehouseInventoryList) {
            BigDecimal warehouseCount = productInventory.getTotalCountMinUnit();
            BigDecimal resultCount = warehouseCount.add(changeTotalCount);
            if (resultCount.compareTo(BigDecimal.ZERO) < 0) {
                changeTotalCount = changeTotalCount.add(warehouseCount);
                productInventory.setChangeCount(warehouseCount.negate());

                productInventoryPOS.add(productInventory);
            } else {
                changeTotalCount = BigDecimal.ZERO;
                productInventory.setChangeCount(changeTotalCount);

                productInventoryPOS.add(productInventory);
                break;
            }
        }
    }

    /**
     * 剩余库存处理
     * 
     * @param productInventoryPOS 最近计算结果
     * @param surplusWarehouseInventoryList 不符合BO条件的仓库库存数据
     * @param changeBOS 原BO集合
     * @param changeBO bo拷贝源对象
     */
    private void processSurplusInventory(ArrayList<ProductInventoryPO> productInventoryPOS,
        List<ProductInventoryPO> surplusWarehouseInventoryList, List<WarehouseInventoryChangeBO> changeBOS,
        WarehouseInventoryChangeBO changeBO) {
        BigDecimal changeTotalCount = changeBO.getResultCount();
        surplusWarehouseInventoryList = surplusWarehouseInventoryList.stream()
            .filter(inventory -> inventory.getCalcTotalCountMinUnit().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(surplusWarehouseInventoryList)
            || changeTotalCount.compareTo(BigDecimal.ZERO) >= 0) {
            return;
        }

        OrderProcessRuleConfigDTO orderProcessRuleConfigDTO =
            iOrderProcessRuleConfigService.findByOrgIdAndOrderType(changeBO.getCityId(), changeBO.getOrderType(), true);
        if (orderProcessRuleConfigDTO != null) {
            ProcessOrderTypeEnum orderTypeEnum =
                ProcessOrderTypeEnum.getEnum(orderProcessRuleConfigDTO.getProcessType());
            // 按规则排序处理
            surplusWarehouseInventoryList = surplusWarehouseInventoryList.stream()
                .sorted(Comparator.nullsFirst(
                    Comparator.comparing(ProductInventoryPO::getSecOwnerId, new SecOwnerIdComparator(orderTypeEnum))))
                .collect(Collectors.toList());
            LOG.info("surplusWarehouseInventoryList按规则排序结果:{}", JSON.toJSONString(surplusWarehouseInventoryList));
        }

        // 循环将未处理完的数量处理完，并添加新的bo
        for (ProductInventoryPO productInventory : surplusWarehouseInventoryList) {
            WarehouseInventoryChangeBO bo = new WarehouseInventoryChangeBO();
            BeanUtils.copyProperties(changeBO, bo);
            bo.setResultCount(BigDecimal.ZERO);
            bo.setSecOwnerId(productInventory.getSecOwnerId());

            BigDecimal warehouseCount = productInventory.getCalcTotalCountMinUnit();
            BigDecimal resultCount = warehouseCount.add(changeTotalCount);
            if (resultCount.compareTo(BigDecimal.ZERO) >= 0) {
                productInventory.setCalcTotalCountMinUnit(resultCount);
                productInventory.setChangeCount(changeTotalCount.add(productInventory.getChangeCount()));

                bo.setCount(changeTotalCount);
                changeTotalCount = BigDecimal.ZERO;
            } else {
                productInventory.setCalcTotalCountMinUnit(BigDecimal.ZERO);
                productInventory.setChangeCount(warehouseCount.negate().add(productInventory.getChangeCount()));

                bo.setCount(warehouseCount.negate());
                changeTotalCount = changeTotalCount.add(warehouseCount);
            }

            productInventoryPOS.add(productInventory);
            changeBOS.add(bo);
            if (changeTotalCount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
        }

        changeBO.setResultCount(changeTotalCount);
    }

    /**
     * 库存转移
     *
     * @param warehouseInventoryTransfersDTO
     * @param opUserId
     */
    public void modTransfersWarehouseInventory(WarehouseInventoryTransfersDTO warehouseInventoryTransfersDTO,
        Integer opUserId) {
        Long productSkuId = warehouseInventoryTransfersDTO.getProductSkuId();
        Integer warehouseId = warehouseInventoryTransfersDTO.getWarehouseId();
        BigDecimal packageCount =
            ObjectUtils.defaultIfNull(warehouseInventoryTransfersDTO.getPackageCount(), BigDecimal.ZERO);
        BigDecimal changeCount =
            ObjectUtils.defaultIfNull(warehouseInventoryTransfersDTO.getUnitCount(), BigDecimal.ZERO);// 将小单位数量赋给变更数量
        // 通过skuId查询转换系数
        ProductSkuPO productSkuPO = productSkuQueryBL.getProductSkuBySkuId(productSkuId);
        AssertUtils.notNull(productSkuPO, "SkuId不存在,SkuId:" + productSkuId);
        // 默认为酒批
        Long ownerId = productSkuPO.getCompanyId();
        // 取被转移库存信息
        if (packageCount.compareTo(BigDecimal.ZERO) > 0) {
            // 获取库存变更数量 = 大单位*转换系数+小单位 //处理大单位
            changeCount = ProcessPackageCount(productSkuPO, packageCount, changeCount);
        }
        // 构建被转移库存BO,进行库存操作.
        WarehouseInventoryChangeBO warehouseInventoryChangeOutBO =
            inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId,
                changeCount.multiply(new BigDecimal(-1)), warehouseInventoryTransfersDTO.getBeChannel(),
                warehouseInventoryTransfersDTO.getOwnerId(), warehouseInventoryTransfersDTO.getSecOwnerId());
        warehouseInventoryChangeOutBO.setLocationId(warehouseInventoryTransfersDTO.getFromLocationId());
        warehouseInventoryChangeOutBO.setProductionDate(warehouseInventoryTransfersDTO.getProductionDate());
        warehouseInventoryChangeOutBO.setBatchTime(warehouseInventoryTransfersDTO.getBatchTime());
        warehouseInventoryChangeOutBO.setExpireTime(warehouseInventoryTransfersDTO.getExpireTime());
        warehouseInventoryChangeOutBO.setOwnId(ownerId);
        warehouseInventoryChangeOutBO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
        warehouseInventoryChangeOutBO.setSource(productSkuPO.getSource());
        warehouseInventoryChangeOutBO.setCityId(productSkuPO.getCityId());
        getWarehouseInventoryChangeBO(warehouseInventoryChangeOutBO, opUserId);
        processInventory(warehouseInventoryChangeOutBO, true);

        // 去被转移渠道的库存信息,如果没有加插入一条库存记录.
        WarehouseInventoryChangeBO warehouseInventoryChangeInBO =
            inventoryChangeFactory.createWarehouseInventoryChangeBO(productSkuId, warehouseId, changeCount,
                warehouseInventoryTransfersDTO.getChannel(), warehouseInventoryTransfersDTO.getOwnerId(),
                warehouseInventoryTransfersDTO.getSecOwnerId());
        warehouseInventoryChangeInBO.setLocationId(warehouseInventoryTransfersDTO.getToLocationId());
        warehouseInventoryChangeInBO.setProductionDate(warehouseInventoryTransfersDTO.getProductionDate());
        warehouseInventoryChangeInBO.setBatchTime(warehouseInventoryTransfersDTO.getBatchTime());
        warehouseInventoryChangeInBO.setExpireTime(warehouseInventoryTransfersDTO.getExpireTime());
        warehouseInventoryChangeInBO.setOwnId(ownerId);
        warehouseInventoryChangeInBO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
        warehouseInventoryChangeInBO.setSource(productSkuPO.getSource());
        warehouseInventoryChangeInBO.setCityId(productSkuPO.getCityId());
        getWarehouseInventoryChangeBO(warehouseInventoryChangeInBO, opUserId);
        processInventory(warehouseInventoryChangeInBO, true);

    }

    // 构建bo
    private WarehouseInventoryChangeBO
        getWarehouseInventoryChangeBO(WarehouseInventoryChangeBO warehouseInventoryChangeBO, Integer opUserId) {
        OwnerDTO shopAdminUser = tradingThirdUserBL.getShopAdminUserById(opUserId);
        if (shopAdminUser != null) {
            warehouseInventoryChangeBO.setCreateUserName(shopAdminUser.getOwnerName());
        }
        // warehouseInventoryChangeBO.setCityId(productInventoryPO.getCityId());
        warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.手动修改.getType());
        warehouseInventoryChangeBO.setOrderType(StoreOrderType.OP_MANUAL_ORDER);
        warehouseInventoryChangeBO.setDescription("库存转移");
        warehouseInventoryChangeBO.setCreateUserId(opUserId + "");
        AdminUser adminUser = tradingThirdUserBL.getAdminUserWithoutAuthById(opUserId);
        if (adminUser != null) {
            String trueName = adminUser.getTrueName();
            if (StringUtils.isEmpty(trueName)) {
                trueName = adminUser.getUserName();
            }
            warehouseInventoryChangeBO.setCreateUserName(trueName);
        }
        // 大宗业务已废除，后边不需要处理大宗业务
        // if (warehouseInventoryChangeBO.getChannel() == ProductChannelType.LARGE) {//如果是大宗商品,就设置为不需要修改销售库存
        warehouseInventoryChangeBO.setHasUpdateOPInventory(false);
        return warehouseInventoryChangeBO;
    }

    /**
     * 根据warehouseId和SKUIDlist批量查询库存，并处理异常库存
     *
     * @param warehouseId
     * @return
     */
    public Map<Long, List<ProductInventoryPO>> getLongProductInventoryPOMapAndCreateNoExits(Map<Long, Long> specSkuMap,
        Integer cityId, Integer warehouseId, Long ownerId, Integer channel, Long secOwnerId) {
        List<Long> productSpecIdList = new ArrayList<>(specSkuMap.keySet());
        // 如果存在多条记录，比对是否库存信息异常
        Map<Long, List<ProductInventoryPO>> longProductInventoryPOMap = warehouseInventoryQueryBL
            .getLongProductInventoryPOMap(warehouseId, productSpecIdList, ownerId, channel, secOwnerId);
        // LOG.info(String.format("查询库存信息参数-2：warehouseId:%s,specIds:%s,ownerId:%s,channel:%s,secOnwerId:%s,Map:{}",
        // warehouseId, JSON.toJSONString(productSpecIdList), ownerId, channel, secOwnerId,
        // JSON.toJSONString(longProductInventoryPOMap)));

        // (入参数量>返回数量 说明有查不到的库存记录)查询库存，如果不存在创建一条记录
        // if (productSpecIdList.size() > longProductInventoryPOMap.keySet().size()) {
        //
        // List<Long> notExitsProductSpecIdList = productSpecIdList.stream().filter(p ->
        // !longProductInventoryPOMap.containsKey(p)).distinct().collect(Collectors.toList());
        //
        // if (!CollectionUtils.isEmpty(notExitsProductSpecIdList)) {
        // Long finalOwnerId = ownerId;
        // if (secOwnerId != null) {
        // finalOwnerId = secOwnerId;
        // }
        // Integer ownerTypeFromCity = ownerTypeBL.getOwnerTypeByOwnerAndCityId(cityId, finalOwnerId);
        //
        // LOG.info(String.format("库存信息不存在，需要创建，规格Id：%s,City:%s,WarehouseId:%s,OwnerId:%s,OwnerType:%s"
        // , JSON.toJSONString(notExitsProductSpecIdList), cityId, warehouseId, ownerId, ownerTypeFromCity));
        //
        // List<ProductInventoryPO> lstInventoryPO = new ArrayList<>();
        // //全部都有skuid记录,但是没有库存记录,插入库存记录.
        // for (Long specId : notExitsProductSpecIdList) {
        // ProductInventoryPO productInventoryPO = insertProductInventory(
        // specId, warehouseId, ownerTypeFromCity, ownerId, cityId, secOwnerId, channel);
        // longProductInventoryPOMap.put(productInventoryPO.getProductSpecificationId(),
        // Arrays.asList(productInventoryPO));
        // lstInventoryPO.add(productInventoryPO);
        // }
        // if (!CollectionUtils.isEmpty(lstInventoryPO)) {
        // productInventoryPOMapper.insertInventoryPOList(lstInventoryPO);
        // }
        // }
        // }
        longProductInventoryPOMap.values().stream().flatMap(Collection::stream).forEach(p -> {
            Long skuId = specSkuMap.get(p.getProductSpecificationId());
            if (skuId != null) {
                p.setProductSkuId(skuId);
            }
        });
        return longProductInventoryPOMap;
    }

    /**
     * 新增仓库库存
     */
    private ProductInventoryPO insertProductInventory(Long specificationId, Integer warehouseId, Integer ownerType,
        Long ownerId, Integer cityId, Long secOwnerId, Integer channel) {
        if (cityId == null) {
            cityId = 0;
            LOG.warn(String.format("城市Id为空！仓库Id:%s,OwnerType:%s,OwnerId:%s", warehouseId, ownerType, ownerId));
        }
        ProductInventoryPO po = new ProductInventoryPO();
        po.setId(UUIDUtil.getUUID());
        po.setCityId(cityId);
        po.setProductSpecificationId(specificationId);
        po.setWarehouseId(warehouseId);
        po.setOwnerType(ownerType);
        po.setOwnerId(ownerId);
        po.setSecOwnerId(secOwnerId);
        po.setChannel(channel);
        po.setTotalCountMinUnit(BigDecimal.ZERO);
        po.setCreateTime(new Date());
        po.setLastUpdateTime(new Date());
        return po;
    }

    /**
     * 构建库存不足报错提示.
     *
     * @param errSkuIdList 库存不足的skuid->取货小单位数量
     * @return
     */
    private String createErrorMessage(List<WarehouseInventoryChangeBO> warehouseChangeList,
        Map<Long, BigDecimal> errSkuIdList) {
        List<Long> productSkuIdSet = new ArrayList<>(errSkuIdList.keySet());
        List<ProductSkuPO> productSkuPOList = productSkuQueryBL.getProductSkuListByIds(productSkuIdSet);
        Map<Long, ProductSkuPO> map = productSkuPOList.stream().collect(Collectors.toMap(ProductSkuPO::getProductSkuId,
            Function.identity(), (key1, key2) -> key2 != null ? key2 : key1));
        LOG.info("createErrorMessage errSkuIdList:{}, map: {}", JSON.toJSONString(errSkuIdList),
            JSON.toJSONString(map));
        StringBuilder errorMessage = new StringBuilder("仓库库存不足!\n");
        for (Long productSkuId : errSkuIdList.keySet()) {
            ProductSkuPO productSkuPO = map.get(productSkuId);
            BigDecimal lackCount = errSkuIdList.get(productSkuId);// 缺少的小单位数量

            List<String> lstTmpOrderNos =
                warehouseChangeList.stream().filter(p -> Objects.equals(p.getProductSkuId(), productSkuId))
                    .map(p -> p.getOrderNo()).distinct().collect(Collectors.toList());

            if (productSkuPO != null) {
                BigDecimal[] remainder = lackCount.divideAndRemainder(productSkuPO.getPackageQuantity());
                BigDecimal packageCount = remainder[0]; // 52/6=8件
                BigDecimal unitCount = remainder[1]; // 52%6=4瓶
                String packageName = productSkuPO.getPackageName();// 大单位名称
                String unitName = productSkuPO.getUnitName();// 小单位名称
                errorMessage.append(String.format("SkuId :%s,产品名称:%s,缺货%s%s%s%s,单号:%s %n", productSkuId,
                    productSkuPO.getName(), packageCount.stripTrailingZeros().toPlainString(), packageName,
                    unitCount.stripTrailingZeros().toPlainString(), unitName, JSON.toJSONString(lstTmpOrderNos)));
            } else {
                errorMessage.append(String.format("SkuId :%s,缺货%s%s,单号:%s %n", productSkuId,
                    lackCount.stripTrailingZeros().toPlainString(), "小件", JSON.toJSONString(lstTmpOrderNos)));
            }
        }
        return errorMessage.toString();
    }
    //
    // /**
    // * 经销商调拨(确认出库)供应链调拨发送销售库存变更
    // *
    // * @param list
    // */
    // public void sendShopWarehouseInventoryRecord(List<ShopWarehouseInventoryRecordDTO> list) {
    // List<WarehouseInventoryChangeBO> boList = new ArrayList<>();
    // for (ShopWarehouseInventoryRecordDTO shopWarehouseInventoryRecordDTO : list) {
    // AssertUtils.notNull(shopWarehouseInventoryRecordDTO.getProductSkuId(), "skuid不能为null");
    // AssertUtils.notNull(shopWarehouseInventoryRecordDTO.getWarehouseId(), "仓库d不能为null");
    // AssertUtils.notNull(shopWarehouseInventoryRecordDTO.getChannel(), "渠道不能为null");
    //
    // Long productSkuId = shopWarehouseInventoryRecordDTO.getProductSkuId();
    // //通过skuId查询转换系数
    // ProductSkuPO productSkuPO = productSkuQueryBL.getProductSkuBySkuId(productSkuId, ProductSourceType.易酒批);
    // AssertUtils.notNull(productSkuPO, "SkuId不存在,SkuId:" + productSkuId);
    // WarehouseInventoryChangeBO warehouseInventoryChangeBO = inventoryChangeFactory
    // .createWarehouseInventoryChangeBO(productSkuId, shopWarehouseInventoryRecordDTO.getWarehouseId(),
    // shopWarehouseInventoryRecordDTO.getChangCount(), shopWarehouseInventoryRecordDTO.getChannel(),
    // productSkuPO.getCompanyId());
    //
    // warehouseInventoryChangeBO.setHasUpdateOPInventory(shopWarehouseInventoryRecordDTO.getHasUpdateOPInventory() ==
    // 1);
    // warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.供应链调拨.getType());
    // warehouseInventoryChangeBO.setOrderType(StoreOrderType.TRANSFER_OUT_ORDER);
    // warehouseInventoryChangeBO.setErpEventType(ERPEventType.单据录入.getType());
    // warehouseInventoryChangeBO.setSystemSource(SystemSource.易经销.getType());
    // warehouseInventoryChangeBO.setProductSpecificationId(productSkuPO.getProductSpecificationId());
    // boList.add(warehouseInventoryChangeBO);
    // }
    // validateOrderDeliveryProductStore(boList, new HashMap<>(16), new ArrayList<>(), true, false);
    // processSellInventory(boList, null);
    // }

    /**
     * 销商调拨(确认出入库)
     *
     * @param agencyStockOperateDTO
     */
    public void agencyStockOperate(AgencyStockOperateDTO agencyStockOperateDTO) {
        Integer warehouseId = agencyStockOperateDTO.getWarehouseId();
        Warehouse warehouseDto = warehouseQueryService.findWarehouseById(warehouseId);
        String createUser = agencyStockOperateDTO.getCreateUser();
        Integer yiJingXiaoEventType = agencyStockOperateDTO.getYiJingXiaoEventType();
        Integer orderType = agencyStockOperateDTO.getOrderType();
        ArrayList<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();

        for (AgencyStockProductDTO agencyStockProductDTO : agencyStockOperateDTO.getItems()) {
            WarehouseInventoryChangeBO warehouseInventoryChangeBO =
                inventoryChangeFactory.createWarehouseInventoryChangeBO(agencyStockProductDTO.getProductSkuId(),
                    warehouseId, agencyStockProductDTO.getChangeCount(), agencyStockProductDTO.getChannel(),
                    agencyStockProductDTO.getOwnerId(), agencyStockProductDTO.getSecOwnerId());

            if (StoreOrderType.TRANSFER_IN_ORDER == orderType) {
                warehouseInventoryChangeBO.setDescription("经销商确认入库");
            } else if (StoreOrderType.TRANSFER_OUT_ORDER == orderType) {
                warehouseInventoryChangeBO.setDescription("经销商确认出库");
            } else if (StoreOrderType.库存盘点单 == orderType) {
                warehouseInventoryChangeBO.setDescription("经销商库存盘点");
            }

            if (warehouseDto == null) {
                ProductSkuPO productSkuBySkuId =
                    productSkuQueryBL.getProductSkuBySkuId(agencyStockProductDTO.getProductSkuId());
                if (productSkuBySkuId != null) {
                    warehouseInventoryChangeBO.setCityId(productSkuBySkuId.getCityId());
                } else {
                    warehouseInventoryChangeBO.setCityId(agencyStockOperateDTO.getCityId());
                }
            } else {
                warehouseInventoryChangeBO.setCityId(warehouseDto.getCityId());
            }
            warehouseInventoryChangeBO.setOrderType(orderType);
            warehouseInventoryChangeBO.setHasUpdateOPInventory(agencyStockOperateDTO.getHasUpdateOPInventory());
            warehouseInventoryChangeBO.setCreateUserName(createUser);
            warehouseInventoryChangeBO.setSource(agencyStockProductDTO.getSource());
            warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.供应链调拨.getType());
            warehouseInventoryChangeBO.setErpEventType(yiJingXiaoEventType);
            warehouseInventoryChangeBO.setOrderId(agencyStockProductDTO.getOrderId());
            warehouseInventoryChangeBO.setOrderNo(agencyStockProductDTO.getOrderNo());
            // 计算批属性编号用
            warehouseInventoryChangeBO.setProductionDate(agencyStockProductDTO.getProductionDate());
            warehouseInventoryChangeBO.setBatchTime(agencyStockProductDTO.getBatchTime());
            warehouseInventoryChangeBO.setExpireTime(agencyStockProductDTO.getExpireTime());
            // todo 这个暂时没用
            warehouseInventoryChangeBO.setAttributeList(agencyStockProductDTO.getAttributeList());
            warehouseInventoryChangeBO.setLocationId(agencyStockProductDTO.getLocationId());

            warehouseInventoryChangeBO.setProductSpecificationId(agencyStockProductDTO.getProductSpecificationId());
            warehouseInventoryChangeBO.setOwnId(agencyStockProductDTO.getOwnerId());
            warehouseInventoryChangeBO.setSource(ProductSourceType.易酒批);

            // 微酒及经销商，库存不足时，不需要自动计算二级货主
            warehouseInventoryChangeBO.setAllocationCalculation(false);

            warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);
        }

        boolean checkWarehouseInventory = agencyStockOperateDTO.getCheckWarehouseInventory() == null
            || agencyStockOperateDTO.getCheckWarehouseInventory();
        // 处理库存变更.库存记录
        validateAndProcessProductStore(warehouseInventoryChangeBOS, checkWarehouseInventory, false, true, true, false);
        // 发送销售库存变更消息
        processSellInventory(warehouseInventoryChangeBOS, null);
    }

    /**
     * 微酒(确认出入库)
     *
     * @param wjStockOperateDTO
     */
    public void wjStockOperate(WJStockOperateDTO wjStockOperateDTO) {
        Integer warehouseId = wjStockOperateDTO.getWarehouseId();
        String createUser = wjStockOperateDTO.getCreateUser();
        Integer eventType = wjStockOperateDTO.getEventType();
        Integer orderType = wjStockOperateDTO.getOrderType();
        ArrayList<WarehouseInventoryChangeBO> warehouseInventoryChangeBOS = new ArrayList<>();
        // 声明批量同步skuConfig对象
        List<ProductSkuConfigDTO> configDTOS = new ArrayList<>();

        for (WJStockProductDTO productDTO : wjStockOperateDTO.getItems()) {
            WarehouseInventoryChangeBO warehouseInventoryChangeBO =
                inventoryChangeFactory.createWarehouseInventoryChangeBO(productDTO.getProductSkuId(), warehouseId,
                    productDTO.getChangeCount(), productDTO.getChannel(), productDTO.getOwnerId(),
                    productDTO.getSecOwnerId());

            if (StoreOrderType.WJ_TRANSFER_IN_ORDER == orderType.intValue()) {
                warehouseInventoryChangeBO.setDescription("微酒确认入库");
            } else if (StoreOrderType.WJ_TRANSFER_OUT_ORDER == orderType.intValue()) {
                warehouseInventoryChangeBO.setDescription("微酒确认出库");
            }

            ProductSkuPO productSkuBySkuId = productSkuQueryBL.getProductSkuBySkuId(productDTO.getProductSkuId());
            AssertUtils.notNull(productSkuBySkuId, "商品SKU信息不存在！");
            warehouseInventoryChangeBO.setCityId(productSkuBySkuId.getCityId());

            warehouseInventoryChangeBO.setOrderType(orderType);
            warehouseInventoryChangeBO.setHasUpdateOPInventory(wjStockOperateDTO.getHasUpdateOPInventory());
            warehouseInventoryChangeBO.setCreateUserName(createUser);
            warehouseInventoryChangeBO.setSource(productDTO.getSource());
            warehouseInventoryChangeBO.setJiupiEventType(JiupiEventType.供应链调拨.getType());
            warehouseInventoryChangeBO.setErpEventType(eventType);
            warehouseInventoryChangeBO.setOrderId(productDTO.getOrderId());
            warehouseInventoryChangeBO.setOrderNo(productDTO.getOrderNo());
            // 计算批属性编号用
            warehouseInventoryChangeBO.setProductionDate(productDTO.getProductionDate());
            warehouseInventoryChangeBO.setBatchTime(productDTO.getBatchTime());
            warehouseInventoryChangeBO.setExpireTime(productDTO.getExpireTime());

            warehouseInventoryChangeBO.setAttributeList(productDTO.getAttributeList());
            warehouseInventoryChangeBO.setLocationId(productDTO.getLocationId());

            warehouseInventoryChangeBO.setProductSpecificationId(productDTO.getProductSpecificationId());
            warehouseInventoryChangeBO.setSource(ProductSourceType.微酒);

            // 微酒及经销商，库存不足时，不需要自动计算二级货主
            warehouseInventoryChangeBO.setAllocationCalculation(false);

            warehouseInventoryChangeBOS.add(warehouseInventoryChangeBO);

            // 给批量同步skuConfig入参对象赋值
            ProductSkuConfigDTO configDTO = new ProductSkuConfigDTO();
            configDTO.setWarehouseId(warehouseId);
            configDTO.setProductSkuId(productDTO.getProductSkuId());
            configDTOS.add(configDTO);
        }
        // 处理库存变更.库存记录
        validateAndProcessProductStore(warehouseInventoryChangeBOS, true, false, true, true, false);
        // 发送销售库存变更消息
        processSellInventory(warehouseInventoryChangeBOS, null);

        // 调用批量同步skuConfig方法
        iProductSkuConfigService.saveProductSkuConfigBatch(configDTOS);
    }

    /**
     * 根据仓库id获取 城市id
     *
     * @param warehouseId
     * @return
     */
    public Integer getCityIdByWarehouseId(Integer warehouseId) {
        return warehouseChargeConfigPOMapper.getCityIdByWarehouseId(warehouseId);
    }

    /**
     * 根据规格ID和货主获取准确的库存信息(库存信息不存在则会自动创建为0库存记录)
     *
     */
    public Map<Long, List<ProductInventoryPO>> findExactProductInventoryBySpecAndOwner(Map<Long, Long> specSkuMap,
        Integer cityId, Integer warehouseId, Long ownerId, Integer channel, Long secOwnerId, Boolean createInventory) {
        AssertUtils.notNull(cityId, "城市ID不能为空");
        AssertUtils.notNull(warehouseId, "仓库ID不能为空");
        AssertUtils.notEmpty(specSkuMap, "规格信息不能为空");
        List<Long> productSpecIdList = new ArrayList<>(specSkuMap.keySet());
        // 如果存在多条记录，比对是否库存信息异常
        Map<Long, List<ProductInventoryPO>> longProductInventoryPOMap = warehouseInventoryQueryBL
            .getLongProductInventoryPOMap(warehouseId, productSpecIdList, ownerId, channel, secOwnerId);
        if (createInventory == null || createInventory) {
            Integer ownerTypeFromCity = ownerTypeBL.getOwnerTypeByOwnerAndCityId(cityId, ownerId);
            List<ProductInventoryPO> lstInventoryPO = new ArrayList<>();
            // (入参数量>返回数量 说明有查不到的库存记录)查询库存，如果不存在创建一条记录
            if (productSpecIdList.size() > longProductInventoryPOMap.keySet().size()) {
                List<Long> notExitsProductSpecIdList = productSpecIdList.stream()
                    .filter(p -> !longProductInventoryPOMap.containsKey(p)).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(notExitsProductSpecIdList)) {
                    LOG.info(String.format(
                        "库存信息不存在，需要创建，规格Id：%s,City:%s,WarehouseId:%s,OwnerId:%s,OwnerType:%s,SecOwnerId:%s",
                        JSON.toJSONString(notExitsProductSpecIdList), cityId, warehouseId, ownerId, ownerTypeFromCity,
                        secOwnerId));
                    // 全部都有skuid记录,但是没有库存记录,插入库存记录.
                    for (Long specId : notExitsProductSpecIdList) {
                        ProductInventoryPO productInventoryPO = insertProductInventory(specId, warehouseId,
                            ownerTypeFromCity, ownerId, cityId, secOwnerId, channel);
                        longProductInventoryPOMap.put(productInventoryPO.getProductSpecificationId(),
                            Arrays.asList(productInventoryPO));
                        lstInventoryPO.add(productInventoryPO);
                    }
                }
            }
            // 强匹配规格ID + 货主 + 二级货主
            List<String> existSpecIdKeys = longProductInventoryPOMap.values().stream()
                .filter(pos -> !CollectionUtils.isEmpty(pos)).flatMap(pos -> pos.stream()).filter(Objects::nonNull)
                .map(po -> String.format("%s%s%s", po.getProductSpecificationId(), po.getOwnerId(), po.getSecOwnerId()))
                .distinct().collect(Collectors.toList());
            for (Long specId : productSpecIdList) {
                if (!existSpecIdKeys.contains(String.format("%s%s%s", specId, ownerId, secOwnerId))) {
                    ProductInventoryPO productInventoryPO = insertProductInventory(specId, warehouseId,
                        ownerTypeFromCity, ownerId, cityId, secOwnerId, channel);
                    List<ProductInventoryPO> inventoryPOList = longProductInventoryPOMap.get(specId);
                    if (CollectionUtils.isEmpty(inventoryPOList)) {
                        longProductInventoryPOMap.put(productInventoryPO.getProductSpecificationId(),
                            Arrays.asList(productInventoryPO));
                    } else {
                        // specId 存在其它货主库存则将新创建的加入其中
                        inventoryPOList.add(productInventoryPO);
                    }
                    lstInventoryPO.add(productInventoryPO);
                }
            }
            if (!CollectionUtils.isEmpty(lstInventoryPO)) {
                productInventoryPOMapper.insertInventoryPOList(lstInventoryPO);
            }
        }
        longProductInventoryPOMap.values().stream().flatMap(Collection::stream).filter(Objects::nonNull).forEach(p -> {
            Long skuId = specSkuMap.get(p.getProductSpecificationId());
            if (skuId != null) {
                p.setProductSkuId(skuId);
            }
        });
        return longProductInventoryPOMap;
    }

    /**
     * 库存校验接口
     */
    public Map<Long, BigDecimal> checkProductInventory(List<ProductInventoryCheckDTO> productInventoryCheckDTOS) {
        Map<Long, BigDecimal> errSkuIdList = new HashMap<>(16);
        List<WarehouseInventoryChangeBO> warehouseChangeList =
            warehouseChangListBOConverter.productInventoryCheckDTOS2BO(productInventoryCheckDTOS);
        ArrayList<ProductInventoryPO> productInventoryPOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(warehouseChangeList)) {
            return errSkuIdList;
        }
        validateOrderDeliveryProductStore(warehouseChangeList, errSkuIdList, productInventoryPOS, true, false);
        return errSkuIdList;
    }
}

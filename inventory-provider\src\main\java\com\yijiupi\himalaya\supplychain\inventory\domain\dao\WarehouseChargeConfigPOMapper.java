package com.yijiupi.himalaya.supplychain.inventory.domain.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.inventory.domain.po.WarehouseChargeConfigPO;
import com.yijiupi.himalaya.supplychain.inventory.dto.*;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWareHouseDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.agency.AgencyStockWarehouseQuery;

/**
 * <AUTHOR>
 */
public interface WarehouseChargeConfigPOMapper {

    /**
     * 仓库标准费率新增
     *
     * @param po
     * @return
     */
    int insertWarehouseChargeConfig(@Param("po") WarehouseChargeConfigPO po);

    /**
     * 仓库标准费率明细查询
     *
     * @param id
     * @return
     */
    WarehouseChargeConfigDTO selectWarehouseChargeConfigById(@Param("warehouseId") Integer warehouseId);

    /**
     * 仓库标准费率修改
     *
     * @param po
     * @return
     */
    int updateWarehouseChargeConfig(@Param("po") WarehouseChargeConfigPO po);

    /**
     * 启用停用仓库标准费率
     *
     * @param po
     * @return
     */
    int updateChargeConfigStatus(@Param("po") WarehouseChargeConfigPO po);

    /**
     * 根据仓库id集合查询标准费率
     *
     * @param list
     * @return
     */
    @MapKey("warehouseId")
    Map<Integer, WarehouseChargeConfigDTO> selectWarehouseChargeList(@Param("list") List<Integer> list);

    /**
     * 根据仓库id查询个数
     *
     * @param
     * @return
     */
    int selectCountByWarehouseId(@Param("warehouseId") Integer warehouseId);

    /**
     * 服务商id，城市查询仓库信息及费用
     *
     * @param agencyStockWarehouseQuery
     * @return
     */
    PageResult<AgencyStockWareHouseDTO> findWarehouseChargeList(AgencyStockWarehouseQuery agencyStockWarehouseQuery);

    /**
     * 查询自有仓库
     */
    PageResult<WarehouseChooseReturnDTO> findOwnerWarehouseList(WarehouseChooseDTO warehouseChooseDTO);

    /**
     * 查询所有仓库
     *
     * @param warehouseChooseDTO
     * @return
     */
    PageResult<WarehouseChooseReturnDTO> findWarehouseList(WarehouseChooseDTO warehouseChooseDTO);

    /**
     * 查询托管仓库
     *
     * @param warehouseChooseDTO
     * @return
     */
    PageResult<WarehouseChooseReturnDTO> findDepositWarehouseList(WarehouseChooseDTO warehouseChooseDTO);

    /**
     * 查询申请出库仓库
     *
     * @param warehouseChooseDTO
     * @return
     */
    PageResult<WarehouseChooseReturnDTO> findDealerWarehouseList(WarehouseChooseDTO warehouseChooseDTO);

    /**
     * 城市下服务商的仓库查询
     *
     * @param cityWarehouseQuery
     * @return
     */
    PageResult<WarehouseChargeConfigDTO> findWarehouseChargeDetailList(CityWarehouseQuery cityWarehouseQuery);

    /**
     * 根据仓库id得到经销商id的集合
     *
     * @param warehouseId
     * @return
     */
    List<Long> selectShopIdList(Integer warehouseId);

    /**
     * 根据服务商id查询仓库id的集合
     *
     * @param facilitatorId
     * @return
     */
    List<Integer> selectWarehouseIdList(Long facilitatorId);

    /**
     * 根据仓库id获取城市id
     *
     * @param warehouseId
     * @return
     */
    Integer getCityIdByWarehouseId(Integer warehouseId);

    /**
     * 仓库管理 仓库库存
     *
     * @param warehouseServiceStoreQuery
     * @return
     */
    PageResult<WarehouseServiceStoreDTO>
        findWarehouseServiceStoreList(WarehouseServiceStoreQuery warehouseServiceStoreQuery);

    /**
     * 根据仓库id查询仓库信息
     */
    WarehouseReturnDTO findByWarehouseId(CityWarehouseQuery cityWarehouseQuery);

    /**
     * 根据仓库id集合查询仓库信息
     *
     * @param warehouseIdList
     * @return
     */
    PageResult<WarehouseReturnDTO> findByWarehouseList(@Param("list") List<Integer> warehouseIdList);

}
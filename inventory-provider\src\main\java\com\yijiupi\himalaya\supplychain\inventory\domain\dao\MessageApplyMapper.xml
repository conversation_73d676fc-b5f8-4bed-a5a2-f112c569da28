<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.MessageApplyMapper">

    <insert id="insertMessageApply">
        INSERT INTO InventoryMessageApply (messageId) VALUES (#{messageId,jdbcType=VARCHAR});
    </insert>

    <insert id="insertMessageApplyBatch" parameterType="java.util.List">
        INSERT INTO InventoryMessageApply (messageId)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item})
        </foreach>
    </insert>

    <insert id="deleteMessagesBatch" parameterType="java.util.List">
        DELETE FROM InventoryMessageApply
        where messageId in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </insert>

    <select id="countMessageApply" resultType="int">
        SELECT count(messageId)
        FROM InventoryMessageApply
        WHERE messageId = #{messageId,jdbcType=VARCHAR}
    </select>

    <select id="getProcessedMessage" resultType="java.lang.String">
        SELECT DISTINCT messageId
        FROM InventoryMessageApply
        where messageId in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
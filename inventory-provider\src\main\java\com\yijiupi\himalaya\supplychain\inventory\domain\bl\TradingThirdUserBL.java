package com.yijiupi.himalaya.supplychain.inventory.domain.bl;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.user.dto.AdminUser;
import com.yijiupi.himalaya.supplychain.user.service.IAdminUserService;
import com.yijiupi.himalaya.supplychain.warehouse.dto.Warehouse;
import com.yijiupi.himalaya.supplychain.warehouse.service.IWarehouseQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.service.OwnerService;

/**
 * 三方用户及公用信息查询BL
 *
 * <AUTHOR> 2017/12/9
 */
@Service
public class TradingThirdUserBL {

    @Reference
    private IAdminUserService adminUserService;
    @Reference
    private OwnerService ownerService;
    @Reference
    private IWarehouseQueryService iWarhouseService;

    /**
     * 通过id获取经销商运营人员
     *
     * @param userId
     * @return
     */
    public OwnerDTO getShopAdminUserById(Integer userId) {
        return ownerService.getOwnerById(Long.valueOf(userId));
    }

    /**
     * 查出酒批城市下的所有可用的仓库
     *
     * @param warehouseClass 仓库类型 null 查出所有 0 查出实仓 1 查出虚仓
     */
    public List<Warehouse> listAllEnableWarehouseByCityId(Integer cityId, Integer warehouseClass) {
        return iWarhouseService.listAllEnableWarehouseByCityId(cityId, warehouseClass);
    }

    /**
     * 获取仓库详情
     *
     * @param warehouseId
     * @return
     */
    public Warehouse getWarehouse(Integer warehouseId) {
        List<Warehouse> lstWarehouse = iWarhouseService.listWarehouseByIds(Collections.singletonList(warehouseId));
        return CollectionUtils.isEmpty(lstWarehouse) ? null : lstWarehouse.get(0);
    }

    /**
     * 通过用户Id查询单个用户信息
     *
     * @param userId
     * @return
     */
    public AdminUser getAdminUserWithoutAuthById(Integer userId) {
        return adminUserService.getAdminUserWithoutAuthById(userId);
    }

}

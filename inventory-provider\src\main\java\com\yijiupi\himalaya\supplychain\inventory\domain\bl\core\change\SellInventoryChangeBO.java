package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core.change;

import java.math.BigDecimal;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.yijiupi.himalaya.supplychain.inventory.domain.model.SellInventoryChangeMessage;

/**
 * 销售库存变更BO. Created by Lifeng on 2017/7/18.
 */
public class SellInventoryChangeBO extends BaseInventoryChangeBO {

    private static final Logger LOG = LoggerFactory.getLogger(SellInventoryChangeBO.class);

    private Long productSkuId;

    private Integer warehouseId;

    private BigDecimal count;

    /**
     * 关联仓库Id
     */
    private Integer relateWarehouseId;

    public void validateSelf() {}

    /**
     * 变更自身.
     */
    // @Override
    // public void changeOnlyself() {
    // }

    /**
     * 关联变更: 如变更DB库存时会关联变更Redis库存，变更仓库库存会关联变更销售库存，返回Redis库存应变更条目，批量扣除【Redis库存由返回值统一变更】.
     */
    // @Override
    // public void changeAssociate() {
    // //发送销售库存变更消息
    // cityInventoryBL.sellInventoryChangeEvent(createMessage());
    // }

    public Long getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public Integer getRelateWarehouseId() {
        return relateWarehouseId;
    }

    public void setRelateWarehouseId(Integer relateWarehouseId) {
        this.relateWarehouseId = relateWarehouseId;
    }

    /**
     * 构建sku唯一标示
     */
    public String getSkuSign() {
        // String skuSign = getProductSkuId() != null ? getProductSkuId().toString() : null;
        // if (getOwnId() != null) {
        // skuSign += getOwnId();
        // }
        return String.format("%s-%s-%s-%s", getWarehouseId(), getProductSpecificationId(), getOwnId(), getSecOwnerId());
    }

    /**
     * 构建mq消息体.
     */
    public SellInventoryChangeMessage createMessage() {
        SellInventoryChangeMessage message = new SellInventoryChangeMessage();
        message.setCityId(getCityId());
        message.setWarehouseId(warehouseId);
        message.setProductSpecificationId(getProductSpecificationId());
        message.setOwnerType(getOwnType());
        message.setJiupiEventType(getJiupiEventType());
        message.setTotalCount(count);
        message.setCreateTime(new Date());

        // 非必填消息
        message.setOwnerId(getOwnId());
        message.setOrderNo(getOrderNo());
        message.setOrderId(getOrderId());
        message.setOrderType(getOrderType());
        message.setCreateUserId(getCreateUserId());
        message.setCreateUserName(getCreateUserName());
        message.setDescription(getDescription());
        message.setErpEventType(getErpEventType());
        message.setSecOwnerId(getSecOwnerId());
        message.setRelateWarehouseId(relateWarehouseId);

        message.setSendMsgToOms(isSendMsgToOms());

        return message;
    }
}

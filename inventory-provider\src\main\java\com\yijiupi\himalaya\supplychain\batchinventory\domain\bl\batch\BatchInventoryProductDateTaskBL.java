package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckParam;
import com.yijiupi.himalaya.supplychain.orgsettings.dto.OutStockConfigCheckResultDTO;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.CategoryPeriodConfigTypeEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.enums.InStockAlarmEnum;
import com.yijiupi.himalaya.supplychain.orgsettings.service.IInStockConfigService;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductChannelType;
import com.yijiupi.supplychain.serviceutils.constant.product.ProductSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.event.ProductionDateTaskEventBL;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductDateTaskMessage;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.product.ProductionDateTaskDTO;
import com.yijiupi.himalaya.supplychain.dto.ReportBatchLocationInfoDTO;
import com.yijiupi.himalaya.supplychain.dto.ReportBatchLocationInfoQueryDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;

/**
 * 批次库存过期产品任务
 *
 * <AUTHOR>
 * @date 2025/2/8
 */
@Service
public class BatchInventoryProductDateTaskBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(BatchInventoryProductDateTaskBL.class);

    @Autowired
    private BatchInventoryQueryBL batchInventoryQueryBL;

    @Autowired
    private ProductionDateTaskEventBL productionDateTaskEventBL;

    @Reference(timeout = 120000)
    private IInStockConfigService iInStockConfigService;
    /**
     * 是否能够完成生成日期治理任务检查
     */
    public Map<String, Boolean> checkCompleteProductDateTask(List<ProductionDateTaskDTO> taskDTOS) {
        AssertUtils.notEmpty(taskDTOS, "生产日志治理任务不能为空");
        taskDTOS.stream().forEach(checkDTO -> {
            AssertUtils.notNull(checkDTO.getWarehouseId(), "仓库id不能为空");
            AssertUtils.notNull(checkDTO.getSkuId(), "skuId不能为空");
            AssertUtils.hasText(checkDTO.getStoreBatchId(), "批次日期id不能为空");
        });

        LOGGER.info("是否能够完成生成日期治理任务检查 入参：{}", JSON.toJSONString(taskDTOS));
        Map<String, Boolean> completeFlagMap = new HashMap<>();
        List<Byte> excludeSubcategoryList =
            Arrays.asList(LocationEnum.残次品位.getType().byteValue(), LocationAreaEnum.残次品区.getType().byteValue());
        taskDTOS.stream().collect(Collectors.groupingBy(p -> p.getWarehouseId()))
            .forEach((warehouseId, checkTaskDTOS) -> {

                List<OutStockConfigCheckDTO> checkProductDTOList = new ArrayList<>();
                checkTaskDTOS.forEach(taskDTO -> {
                    OutStockConfigCheckDTO checkElem = new OutStockConfigCheckDTO();
                    checkElem.setSkuId(taskDTO.getSkuId());
                    checkElem.setChannel(ProductChannelType.JIUPI);
                    checkElem.setSource(ProductSourceType.易酒批);
                    checkProductDTOList.add(checkElem);
                });

                OutStockConfigCheckParam configCheckDTO = new OutStockConfigCheckParam();
                configCheckDTO.setWarehouseId(warehouseId);
                configCheckDTO.setCheckProductDTOList(checkProductDTOList);
                configCheckDTO.setExcludeSubcategoryList(excludeSubcategoryList);
                configCheckDTO.setCheckAllStoreFlag(true);
                configCheckDTO.setCategoryPeriodConfigType(CategoryPeriodConfigTypeEnum.NEAR_EXPIRY_PERIOD.getValue());
                List<OutStockConfigCheckResultDTO> results =
                        iInStockConfigService.checkByCategoryPeriodConfigType(configCheckDTO).stream()
                                .filter(elem -> elem != null && Objects.equals(elem.getAlarm(), InStockAlarmEnum.入库禁止.getType()) && elem.getStorageAttribute() != null)
                                .collect(Collectors.toList());
                Map<String, OutStockConfigCheckResultDTO> resultMap = results.stream().collect(Collectors.toMap(OutStockConfigCheckResultDTO::getStoreBatchId, it -> it));
                Map<String, List<ProductionDateTaskDTO>> taskMap = checkTaskDTOS.stream().collect(Collectors.groupingBy(ProductionDateTaskDTO::getStoreBatchId));
                taskMap.forEach((taskKey, taskDTOList) -> {
                    // 结果入参包含则为静止
                    if (!resultMap.containsKey(taskKey)) {
                        completeFlagMap.put(taskKey, true);
                    } else {
                        completeFlagMap.put(taskKey, false);
                    }
                });
            });

        LOGGER.info("是否能够完成生成日期治理任务检查 结果：{}", JSON.toJSONString(completeFlagMap));
        return completeFlagMap;
    }

    /**
     * 是否能够完成生成日期治理任务检查
     */
    public void completeProductDateTaskNotify(List<ProductDateTaskMessage> taskMessageList) {
        productionDateTaskEventBL.notifyProductionDateTaskCompleteBatch(taskMessageList);
    }
}

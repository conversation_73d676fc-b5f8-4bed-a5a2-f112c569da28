package com.yijiupi.himalaya.supplychain.inventory.domain.bl.core;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.supplychain.inventory.domain.configuration.InventoryMQProperties;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventorySyncRecordDTO;
import com.yijiupi.himalaya.supplychain.warehouseproduct.dto.OwnerDTO;

@Service
public class InventorySyncEventFireBL {

    private static final Logger LOG = LoggerFactory.getLogger(InventorySyncEventFireBL.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 库存差异同步
     */
    public void inventorySyncEvent(List<InventorySyncRecordDTO> syncRecordDTOList, List<OwnerDTO> ownerDTOS) {
        // 移除无差异的数据
        // 移除货主不为空的数据
        List<InventorySyncRecordDTO> syncRecordDTOS = syncRecordDTOList.stream()
            .filter(p -> p.getDiffTotalCount().compareTo(BigDecimal.ZERO) != 0 && p.getOwnerId() == null)
            .filter(p -> StringUtils.isEmpty(p.getRemark()) || !p.getRemark().startsWith("易款库存对账"))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncRecordDTOS)) {
            return;
        }
        syncRecordDTOS.forEach(record -> {
            if (record.getSecOwnerId() == null) {
                return;
            }
            Optional<OwnerDTO> ownerDto =
                ownerDTOS.stream().filter(p -> Objects.equals(p.getId(), record.getSecOwnerId())).findAny();
            if (!ownerDto.isPresent()) {
                return;
            }
            record.setRefSecOwnerId(ownerDto.get().getRefPartnerId());
        });
        LOG.info("库存差异同步:{}", JSON.toJSONString(syncRecordDTOS));
        rabbitTemplate.convertAndSend(InventoryMQProperties.INVENTORY_SYNC_CHANGE_EXCHANGE, null, syncRecordDTOS);
    }
}

package com.yijiupi.himalaya.supplychain.batchinventory.domain.bl.batch;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.base.utils.AssertUtils;
import com.yijiupi.himalaya.ibatis.type.PageResult;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.dao.batch.ProductStoreBatchChangeRecordMapper;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchChangeRecordPO;
import com.yijiupi.himalaya.supplychain.batchinventory.domain.po.batch.ProductStoreBatchPO;
import com.yijiupi.himalaya.supplychain.batchinventory.dto.batch.*;
import com.yijiupi.himalaya.supplychain.batchinventory.util.UUIDUtil;
import com.yijiupi.himalaya.supplychain.dto.product.ProductInventoryChangeRecordDTO;
import com.yijiupi.himalaya.supplychain.enums.JiupiEventType;
import com.yijiupi.himalaya.supplychain.enums.StoreOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.inventory.constant.InventoryChangeTypes;
import com.yijiupi.himalaya.supplychain.inventory.service.IProductInventoryRecordManagerService;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.enums.JiupiOrderTypeEnum;
import com.yijiupi.himalaya.supplychain.outstockordersync.dto.service.IOutStockQueryService;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.CategoryEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationAreaEnum;
import com.yijiupi.himalaya.supplychain.warehouseproduct.enums.LocationEnum;
import com.yijiupi.himalaya.uuid.UUIDGenerator;
import com.yijiupi.supplychain.serviceutils.constant.OrderConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 批次库存变更
 *
 * <AUTHOR> 2018/1/29
 */
@Service
public class ProductStoreBatchChangeRecordBL {

    private final static Logger LOGGER = LoggerFactory.getLogger(ProductStoreBatchChangeRecordBL.class);

    @Autowired
    private ProductStoreBatchChangeRecordMapper productStoreBatchChangeRecordMapper;

    @Reference
    private IProductInventoryRecordManagerService iProductInventoryRecordManagerService;

    @Reference
    private IOutStockQueryService outStockQueryService;

    /**
     * 新增批次库存变更记录
     *
     * @param changeRecordId 仓库库存变更记录id
     * @param productStoreBatchPOS 批次库存信息
     */
    public void createStoreBatchChangeRecord(String changeRecordId, List<ProductStoreBatchPO> productStoreBatchPOS) {
        ArrayList<ProductStoreBatchChangeRecordPO> productStoreBatchChangeRecordPOS = new ArrayList<>();
        for (ProductStoreBatchPO productStoreBatchPO : productStoreBatchPOS) {
            ProductStoreBatchChangeRecordPO po = new ProductStoreBatchChangeRecordPO();
            po.setChangeCount(productStoreBatchPO.getTotalCount());
            po.setBatchId(productStoreBatchPO.getId());
            po.setChangeRecordId(StringUtils.isNotEmpty(changeRecordId) ? changeRecordId : "");
            po.setId(UUIDGenerator.getUUID(po.getClass().getName()) + "");
            productStoreBatchChangeRecordPOS.add(po);
        }
        productStoreBatchChangeRecordMapper.insertProductStoreBatchChangeRecord(productStoreBatchChangeRecordPOS);
    }

    /**
     * 新增批次库存变更记录（没有关联仓库库存变更记录）
     */
    public void createStoreBatchChangeRecordByPickUp(PickUpChangeRecordDTO pickUpChangeRecordDTO,
        List<ProductStoreBatchPO> productStoreBatchPOS) {
        AssertUtils.notNull(pickUpChangeRecordDTO, "新增批次库存变更记录参数不能为空");
        AssertUtils.notNull(pickUpChangeRecordDTO.getDescription(), "（新增批次库存变更记录）描述不能为空");

        // LOGGER.info("移库变更记录参数:{}", JSON.toJSONString(pickUpChangeRecordDTO));
        // 1、新增仓库库存变更记录
        ProductInventoryChangeRecordDTO productInventoryChangeRecordDTO =
            createProductInventoryChangeRecordDTO(pickUpChangeRecordDTO);
        iProductInventoryRecordManagerService
            .saveProductStoreChangeRecord(Arrays.asList(productInventoryChangeRecordDTO));

        // 2、新增批次库存变更记录
        createStoreBatchChangeRecord(productInventoryChangeRecordDTO.getId(), productStoreBatchPOS);
    }

    private ProductInventoryChangeRecordDTO
        createProductInventoryChangeRecordDTO(PickUpChangeRecordDTO pickUpChangeRecordDTO) {
        ProductInventoryChangeRecordDTO productInventoryChangeRecordDTO = new ProductInventoryChangeRecordDTO();
        productInventoryChangeRecordDTO.setId(UUIDUtil.getUUID());
        productInventoryChangeRecordDTO.setProductStoreId("");
        productInventoryChangeRecordDTO
            .setCityId(pickUpChangeRecordDTO.getCityId() == null ? 0 : pickUpChangeRecordDTO.getCityId());
        productInventoryChangeRecordDTO.setOrderType(pickUpChangeRecordDTO.getOrderType() == null
            ? StoreOrderTypeEnum.移库单.getType() : pickUpChangeRecordDTO.getOrderType());
        productInventoryChangeRecordDTO.setOrderId(pickUpChangeRecordDTO.getOrderId());
        productInventoryChangeRecordDTO.setOrderNo(pickUpChangeRecordDTO.getOrderNo());
        productInventoryChangeRecordDTO.setJiupiEventType(pickUpChangeRecordDTO.getJiupiEventType() == null
            ? JiupiOrderTypeEnum.ORDER_TYPE_NORMAL : pickUpChangeRecordDTO.getJiupiEventType());
        productInventoryChangeRecordDTO.setCountMaxUnit(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setCountMinUnit(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setTotalCount(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setSourceTotalCount(BigDecimal.ZERO);
        productInventoryChangeRecordDTO.setDescription(pickUpChangeRecordDTO.getDescription());
        productInventoryChangeRecordDTO.setCreateTime(new Date());
        productInventoryChangeRecordDTO.setCreateUser(pickUpChangeRecordDTO.getCreateUser());
        productInventoryChangeRecordDTO.setStoreType(InventoryChangeTypes.WAREHOUSE);
        return productInventoryChangeRecordDTO;
    }

    /**
     * 获取批次库存变更记录
     *
     * @return
     */
    public PageList<ProductStoreBatchChangeRecordDTO>
        listProductStoreBatchChangeRecord(ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO) {
        // LOGGER.info("获取批次库存变更记录参数:{}", JSON.toJSONString(productStoreBatchChangeRecordQueryDTO));
        PageResult<ProductStoreBatchChangeRecordDTO> pageResult = productStoreBatchChangeRecordMapper
            .listProductStoreBatchChangeRecord(productStoreBatchChangeRecordQueryDTO);
        // 设置变更类型和事件类型名称
        if (CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            pageResult.toPageList().getDataList().forEach(p -> {
                if (p.getOrderType() != null) {
                    p.setOrderTypeName(OrderConstant.getOrderTypeName(Byte.valueOf(p.getOrderType().toString())));
                }
                if (p.getJiupiEventType() != null && JiupiEventType.getEnum(p.getJiupiEventType()) != null) {
                    p.setJiupiEventTypeName(JiupiEventType.getEnum(p.getJiupiEventType()).name());
                }
                // 货区/货位类型名称
                if (Objects.equals(p.getLocationCategory(), CategoryEnum.CARGO_LOCATION.getValue())) {
                    p.setLocationSubcategoryName(LocationEnum.getEnumStr(p.getLocationSubcategory()));
                } else if (Objects.equals(p.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue())) {
                    p.setLocationSubcategoryName(LocationAreaEnum.getEnumStr(p.getLocationSubcategory()));
                }
            });
        }
        return pageResult.toPageList();
    }

    /**
     * 获取批次库存变更流水
     *
     * @return
     */
    public PageList<ProductStoreBatchChangeFlowDTO>
        listProductStoreBatchChangeFlow(ProductStoreBatchChangFlowQueryDTO productStoreBatchChangeRecordQueryDTO) {
        if (StringUtils.isNotEmpty(productStoreBatchChangeRecordQueryDTO.getOrderNo())) {
            List<String> refNosByOrderNo =
                outStockQueryService.findRefNosByOrderNo(productStoreBatchChangeRecordQueryDTO.getOrderNo());
            productStoreBatchChangeRecordQueryDTO.setOrderNos(refNosByOrderNo);
        }
        PageResult<ProductStoreBatchChangeFlowDTO> pageResult =
            productStoreBatchChangeRecordMapper.listProductStoreBatchChangeFlow(productStoreBatchChangeRecordQueryDTO);
        // 设置变更类型和事件类型名称
        if (CollectionUtils.isNotEmpty(pageResult.toPageList().getDataList())) {
            pageResult.toPageList().getDataList().forEach(p -> {
                if (p.getOrderType() != null && StoreOrderTypeEnum.getEnum(p.getOrderType()) != null) {
                    p.setOrderTypeName(StoreOrderTypeEnum.getEnum(p.getOrderType()).name());
                }
                if (p.getJiupiEventType() != null && JiupiEventType.getEnum(p.getJiupiEventType()) != null) {
                    p.setJiupiEventTypeName(JiupiEventType.getEnum(p.getJiupiEventType()).name());
                }
                // 货区/货位类型名称
                if (Objects.equals(p.getLocationCategory(), CategoryEnum.CARGO_LOCATION.getValue())) {
                    p.setLocationSubcategoryName(LocationEnum.getEnumStr(p.getLocationSubcategory()));
                } else if (Objects.equals(p.getLocationCategory(), CategoryEnum.CARGO_AREA.getValue())) {
                    p.setLocationSubcategoryName(LocationAreaEnum.getEnumStr(p.getLocationSubcategory()));
                }
            });
        }
        return pageResult.toPageList();
    }

    // /**
    // * 新增批次库存变更记录
    // *
    // * @param productInventoryChangeRecordPO
    // */
    // public void createStoreBatchChangeRecord(ProductInventoryChangeRecordPO productInventoryChangeRecordPO,
    // ProductStoreBatchPO productStoreBatchPO) {
    // List<ProductStoreBatchPO> productStoreBatchPOS = new ArrayList<>();
    // productStoreBatchPOS.add(productStoreBatchPO);
    // createStoreBatchChangeRecord(productInventoryChangeRecordPO, productStoreBatchPOS);
    // }
    //
    /**
     * 新增批次库存变更记录
     *
     * @param productStoreBatchChangeRecordPOS
     */
    public void addStoreBatchChangeRecordS(List<ProductStoreBatchChangeRecordPO> productStoreBatchChangeRecordPOS) {
        productStoreBatchChangeRecordPOS.forEach(p -> p.setId(UUIDGenerator.getUUID(p.getClass().getName()) + ""));
        productStoreBatchChangeRecordMapper.insertProductStoreBatchChangeRecord(productStoreBatchChangeRecordPOS);
    }
    //
    // /**
    // * 根据库存变更历史storeId,orderNo查找上一次的批次库存变更历史
    // */
    // public List<ProductStoreBatchChangeRecordPO> selectBatchInventoryByChangeRecordId(String productStoreId, String
    // orderNo) {
    // return productStoreBatchChangeRecordMapper.selectBatchInventoryByChangeRecordId(productStoreId, orderNo);
    // }

    public List<ProductStoreBatchChangeRecordDTO> selectProductStoreBatchChangeRecords(
        ProductStoreBatchChangeRecordQueryDTO productStoreBatchChangeRecordQueryDTO) {
        return productStoreBatchChangeRecordMapper
            .selectProductStoreBatchChangeRecords(productStoreBatchChangeRecordQueryDTO);
    }

    public List<ProductStoreBatchChangeInfoResultDTO> findChangeRecordInfoByOrderInfo(ProductStoreBatchChangeInfoQueryDTO queryDTO) {
//        AssertUtils.notNull(queryDTO.getOrderNo(), "订单信息不能为空！");
        List<ProductStoreBatchChangeInfoResultDTO> resultDTOList = productStoreBatchChangeRecordMapper.findChangeRecordInfoByOrderInfo(queryDTO);

        return resultDTOList;
    }


}

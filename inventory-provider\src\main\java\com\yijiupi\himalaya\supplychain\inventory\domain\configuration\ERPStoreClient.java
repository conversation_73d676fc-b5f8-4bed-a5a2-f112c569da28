package com.yijiupi.himalaya.supplychain.inventory.domain.configuration;

import org.springframework.stereotype.Service;

/**
 * ERP库存客户端. Created by Lifeng on 2017/6/12.
 */
@Service
public class ERPStoreClient {

    // private static final Logger LOG = LoggerFactory.getLogger(ERPStoreClient.class);

    // private RestTemplate restTemplate;

    // private String erpStoreUrl;

    // public ERPStoreClient(RestTemplate restTemplate, String erpStoreUrl) {
    // this.restTemplate = restTemplate;
    // this.erpStoreUrl = erpStoreUrl;
    // }

    // public List<ERPStoreDTO> getStoresByCity(Integer cityId) {
    // ResponseEntity<String> resp = restTemplate.getForEntity(erpStoreUrl, String.class,
    // Collections.singletonMap("cityId", cityId));
    // AssertUtils.isTrue(resp.getStatusCode() == HttpStatus.OK, "查询ERP库存异常");
    // String json = resp.getBody();
    // List<ERPStore> list = JSON.parseArray(json, ERPStore.class);
    // List<ERPStoreDTO> result = new ArrayList<>(list.size());
    // for (ERPStore erpStore : list) {
    // try {
    //
    // Long productSkuId = Long.valueOf(erpStore.getProductSkuId());
    // Integer warehouseId = Integer.valueOf(erpStore.getWarehouseId());
    //
    // ERPStoreDTO store = new ERPStoreDTO();
    // store.setProductSkuId(productSkuId);
    // store.setWarehouseId(warehouseId);
    // store.setErpDisplayCount(erpStore.getErpDisplayCount());
    // store.setBuyCount(erpStore.getBuyCount());
    // store.setBuyReturnCount(erpStore.getBuyReturnCount());
    // store.setSaleCount(erpStore.getSaleCount());
    // store.setSaleReturnCount(erpStore.getSaleReturnCount());
    // store.setErpRealCount(erpStore.getErpRealCount());
    // result.add(store);
    //
    // } catch (Exception e) {
    // LOG.error("ERP库存数据转换失败: " + JSON.toJSONString(erpStore), e);
    // }
    // }
    // return result;
    // }

}

package com.yijiupi.himalaya.supplychain.inventory.service;

import java.util.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.yijiupi.himalaya.base.search.PageList;
import com.yijiupi.himalaya.supplychain.dto.product.WarehouseInventoryYJXDTO;
import com.yijiupi.himalaya.supplychain.dto.product.WarehouseInventoryYJXQueryDTO;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductSpecCityStoreDTO;
import com.yijiupi.himalaya.supplychain.dto.standard.ProductWarehouseStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.ProductInventoryApp;
import com.yijiupi.himalaya.supplychain.inventory.dto.InventoryBySkuIdCityIdDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductSkuForInventoryDTO;
import com.yijiupi.himalaya.supplychain.inventory.dto.ProductStoreDTO;
import com.yijiupi.himalaya.supplychain.inventory.service.impl.WarehouseInventoryQueryServiceImpl;
import com.yijiupi.himalaya.supplychain.search.standard.ProductSpecCityListStoreSO;
import com.yijiupi.himalaya.supplychain.search.standard.ProductWarehouseListStoreItemSO;

/**
 * <AUTHOR> 2017/11/30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductInventoryApp.class)
public class WarehouseInventoryQueryServiceImplTest {

    @Autowired
    private WarehouseInventoryQueryServiceImpl warehouseInventoryQueryService;

    @Test
    public void listWarehouseInventoryYJX() {
        WarehouseInventoryYJXQueryDTO warehouseInventoryYJXQueryDTO = new WarehouseInventoryYJXQueryDTO();
        warehouseInventoryYJXQueryDTO.setCityId(999);
        warehouseInventoryYJXQueryDTO.setWarehouseId(9991);
        warehouseInventoryYJXQueryDTO.setPageNum(1);
        warehouseInventoryYJXQueryDTO.setPageSize(20);
        PageList<WarehouseInventoryYJXDTO> pageList =
            warehouseInventoryQueryService.listWarehouseInventoryYJX(warehouseInventoryYJXQueryDTO);
        System.out.println(JSON.toJSONString(pageList));
    }

    /**
     * 查询单个商品仓库库存数. 1/19通过
     */
    @Test
    public void getWarehouseInventory() {
        Map<Long, ProductStoreDTO> map = warehouseInventoryQueryService.listProductInventoryBySkuIds(
            Arrays.asList(Long.valueOf("99700109404600"), Long.valueOf("99700020408575")), 9971);
        System.out.println(JSON.toJSONString(map));
        // final Integer warehouseInventory = warehouseInventoryQueryService.
        // getWarehouseInventory(99900000307814L, 9991, 1, null, 0);
    }

    // @Test
    // public void getProductInventoryStoreId() {
    // ProductSkuForInventoryShopDTO productSkuForInventoryShopDTO = new ProductSkuForInventoryShopDTO();
    // List<Long> longs = new ArrayList<>();
    // productSkuForInventoryShopDTO.setProductSkuIdList(longs);
    // productSkuForInventoryShopDTO.setWarehouseId(9991);
    // productSkuForInventoryShopDTO.setOwnerId(Long.valueOf(2));
    // productSkuForInventoryShopDTO.setPageSize(20);
    // productSkuForInventoryShopDTO.setCurrentPage(1);
    // productSkuForInventoryShopDTO.setSearchKey("山庄老酒");
    // System.out.println(warehouseInventoryQueryService.getProductInventoryStoreId(productSkuForInventoryShopDTO));
    //
    // }

    /**
     * 查询多个商品仓库库存数. 1/19通过
     */
    @Test
    public void getWarehouseInventory2() {
        ArrayList<Long> productSkuIdList = new ArrayList<>();
        // productSkuIdList.add(10000000001495L);
        productSkuIdList.add(99900050716521L);
        // Map<Long, Integer> warehouseInventory =
        // warehouseInventoryQueryService.getWarehouseInventoryList(productSkuIdList, 9991, 0, 1, 0);
    }

    /**
     * 查询一个产品在多个仓库下的库存数量 k(仓库id)-v(库存数量) 1/19通过
     */
    @Test
    public void getWarehouseInventory3() {
        // Map<Integer, Integer> map = warehouseInventoryQueryService.getWarehouseInventoryMap(99900050716521L,
        // Collections.singletonList(9991), 0, 1, 0);
    }

    /**
     * 查询单个商品在对应城市下所有仓库库存数总和 1/19通过
     */
    @Test
    public void getSumWarehouseInventory() {
        // Integer sumWarehouseInventory = warehouseInventoryQueryService.
        // getSumWarehouseInventory(99900049216014L, 999, 0, 1, 0);
    }

    /**
     * 查询多个商品在对应城市下的仓库库存数总和(sku就是在某一个城市下) 1/19通过
     */
    @Test
    public void getCityWarehouseInventory() {
        // Map<Long, Integer> map =
        // warehouseInventoryQueryService.getCityWarehouseInventory(Collections.singleton(99900049216014L), 0, 1, 0);
    }

    /**
     * 获取产品信息对城市库存 1/19通过
     */
    @Test
    public void findProductSpecCityListStore() {
        ProductSpecCityListStoreSO so = new ProductSpecCityListStoreSO();
        so.setCityIds(Collections.singletonList(999));
        so.setProductSpecId(49216L);
        so.setChannel(0);
        so.setSecOwnerId(Long.valueOf(1));
        List<ProductSpecCityStoreDTO> list = warehouseInventoryQueryService.findProductSpecCityListStore(so);
    }

    /**
     * 获取产品SKU信息对应仓库库存 1/19通过 todo 关联发货中数量的都要进行修改.
     */
    @Test
    public void findProductWarehouseListStore() {
        ArrayList<ProductWarehouseListStoreItemSO> productWarehouseListStoreItemSOS = new ArrayList<>();
        ProductWarehouseListStoreItemSO so = new ProductWarehouseListStoreItemSO();
        so.setWarehouseId(9991);
        so.setProductSkuId(99900040884324L);
        so.setChannel(0);
        so.setSecOwnerId(Long.valueOf(1));
        so.setSource(0);
        productWarehouseListStoreItemSOS.add(so);
        List<ProductWarehouseStoreDTO> list =
            warehouseInventoryQueryService.findProductWarehouseListStore(productWarehouseListStoreItemSOS);
    }

    /**
     * 根据skuId、仓库Id、渠道、二级货主id、产品来源获取库存信息 1/22通过
     */
    @Test
    public void getProductInventoryDetail() {
        // WarehouseStoreDTO productInventoryDetail = warehouseInventoryQueryService.
        // getProductInventoryDetail(99900049216014L, 9991, 0, null, 0);
    }

    /**
     * 根据skuId（必填）、cityId(必填)、产品名称、渠道、二级货主id、产品来源获取库存信 1/22通过
     */
    @Test
    public void getProductInventoryBySkuIdCityId() {
        ArrayList<ProductSkuForInventoryDTO> productSkuForInventoryDTOS = new ArrayList<>();
        ProductSkuForInventoryDTO productSkuForInventoryDTO = new ProductSkuForInventoryDTO();
        productSkuForInventoryDTO.setProductSkuId(99900049216014L);
        productSkuForInventoryDTO.setCityId(999);
        // productSkuForInventoryDTO.setSecOwnerId(1);
        productSkuForInventoryDTO.setSource(1);
        productSkuForInventoryDTO.setChannel(0);
        productSkuForInventoryDTOS.add(productSkuForInventoryDTO);
        List<InventoryBySkuIdCityIdDTO> productInventoryBySkuIdCityId =
            warehouseInventoryQueryService.getProductInventoryBySkuIdCityId(productSkuForInventoryDTOS);
    }

    @Test
    public void process2ZhaoShangSku() {
        Map<Long, Long> longLongMap =
            warehouseInventoryQueryService.process2ZhaoShangSku(Collections.singletonList(99900002440223L), 898);
    }

}

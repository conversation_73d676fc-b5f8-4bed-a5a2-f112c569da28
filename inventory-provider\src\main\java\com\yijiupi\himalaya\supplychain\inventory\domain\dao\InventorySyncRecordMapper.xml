<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yijiupi.himalaya.supplychain.inventory.domain.dao.InventorySyncRecordMapper">
    <resultMap id="BaseResultMap" type="com.yijiupi.himalaya.supplychain.inventory.domain.po.InventorySyncRecordPO">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="Org_Id" jdbcType="INTEGER" property="orgId"/>
        <result column="Warehouse_Id" jdbcType="INTEGER" property="warehouseId"/>
        <result column="TMSDeliveryedCount" jdbcType="DECIMAL" property="tmsDeliveryedCount"/>
        <result column="WMSDeliveryedCount" jdbcType="DECIMAL" property="wmsDeliveryedCount"/>
        <result column="ErpRealCount" jdbcType="INTEGER" property="erpRealCount"/>
        <result column="StoreCount_MinUnit" jdbcType="INTEGER" property="storeCountMinUnit"/>
        <result column="DiffTotalCount" jdbcType="DECIMAL" property="diffTotalCount"/>
        <result column="DiffMaxCount" jdbcType="DECIMAL" property="diffMaxCount"/>
        <result column="DiffMinCount" jdbcType="DECIMAL" property="diffMinCount"/>
        <result column="ProductSku_Id" jdbcType="BIGINT" property="productSkuId"/>
        <result column="ProductSpecification_Id" jdbcType="BIGINT" property="productSpecificationId"/>
        <result column="ProductName" jdbcType="VARCHAR" property="productName"/>
        <result column="SpecName" jdbcType="VARCHAR" property="specName"/>
        <result column="packageQuantity" jdbcType="DECIMAL" property="packageQuantity"/>
        <result column="Owner_Id" jdbcType="BIGINT" property="ownerId"/>
        <result column="SecOwner_Id" jdbcType="BIGINT" property="secOwnerId"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="StoreType" jdbcType="TINYINT" property="storeType"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser_Id" jdbcType="INTEGER" property="createUserId"/>
        <result column="IsDiff" jdbcType="TINYINT" property="diff"/>
        <result column="State" jdbcType="TINYINT" property="state"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, Org_Id, Warehouse_Id, TMSDeliveryedCount, WMSDeliveryedCount, ErpRealCount, StoreCount_MinUnit,
        DiffTotalCount, DiffMaxCount, DiffMinCount, ProductSku_Id, ProductSpecification_Id,
        ProductName, SpecName, packageQuantity, Owner_Id, Remark, StoreType, CreateTime,
        CreateUser_Id, IsDiff, State, SecOwner_Id
    </sql>

    <insert id="insertList" parameterType="java.util.List">
        insert into inventorysyncrecord (Id, Org_Id, Warehouse_Id,
        TMSDeliveryedCount, WMSDeliveryedCount,
        ErpRealCount, StoreCount_MinUnit, DiffTotalCount,
        DiffMaxCount, DiffMinCount, ProductSku_Id,
        ProductSpecification_Id, ProductName, SpecName,
        packageQuantity, Owner_Id, Remark,
        StoreType, CreateTime, CreateUser_Id, IsDiff, State, SecOwner_Id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.orgId,jdbcType=INTEGER},
            #{item.warehouseId,jdbcType=INTEGER},
            #{item.tmsDeliveryedCount,jdbcType=DECIMAL},
            #{item.wmsDeliveryedCount,jdbcType=DECIMAL},
            #{item.erpRealCount,jdbcType=INTEGER},
            #{item.storeCountMinUnit,jdbcType=DECIMAL},
            #{item.diffTotalCount,jdbcType=DECIMAL},
            #{item.diffMaxCount,jdbcType=DECIMAL},
            #{item.diffMinCount,jdbcType=DECIMAL},
            #{item.productSkuId,jdbcType=BIGINT},
            #{item.productSpecificationId,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR},
            #{item.specName,jdbcType=VARCHAR},
            #{item.packageQuantity,jdbcType=DECIMAL},
            #{item.ownerId,jdbcType=BIGINT},
            #{item.remark,jdbcType=VARCHAR},
            #{item.storeType,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=INTEGER},
            #{item.diff,jdbcType=TINYINT},
            #{item.state,jdbcType=TINYINT},
            #{item.secOwnerId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <select id="listInventorySyncRecord" resultMap="BaseResultMap">
        select
        record.Id, record.Org_Id, record.Warehouse_Id, record.TMSDeliveryedCount, record.WMSDeliveryedCount,
        record.ErpRealCount, record.StoreCount_MinUnit,
        record.DiffTotalCount, record.DiffMaxCount, record.DiffMinCount, record.ProductSku_Id,
        record.ProductSpecification_Id,
        record.ProductName, record.SpecName, record.packageQuantity, record.Owner_Id, record.Remark, record.StoreType,
        record.CreateTime,
        record.CreateUser_Id, record.IsDiff, record.State, record.SecOwner_Id,own.OwnerName as
        ownerName,secOwn.OwnerName as secOwnerName
        , if(record.State=1, 9, record.State) as flag
        from inventorysyncrecord record
        left join owner own on record.Owner_Id is not null and own.id = record.Owner_Id
        left join owner secOwn on record.SecOwner_Id is not null and secOwn.id = record.SecOwner_Id
        <where>
            <if test="orgId != null">
                and record.Org_Id = #{orgId,jdbcType=INTEGER}
            </if>
            <if test="warehouseId != null">
                and record.Warehouse_Id = #{warehouseId,jdbcType=INTEGER}
            </if>
            <if test="productSkuId != null">
                and record.ProductSku_Id = #{productSkuId,jdbcType=BIGINT}
            </if>
            <if test="productName != null">
                and record.ProductName like concat('%', #{productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="storeType != null">
                and record.StoreType = #{storeType,jdbcType=TINYINT}
            </if>
            <if test="diff == null">
                and record.IsDiff in (0,1)
            </if>
            <if test="diff != null">
                and record.IsDiff = #{diff,jdbcType=TINYINT}
            </if>
            <if test="state != null">
                and record.State = #{state,jdbcType=TINYINT}
            </if>
            <if test="startTime != null">
                and <![CDATA[ record.CreateTime >=  #{startTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="endTime != null">
                and <![CDATA[ record.CreateTime <=  #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="remark != null">
                and record.Remark like concat('%', #{remark,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        order by record.Org_Id, record.Warehouse_Id, record.ProductSku_Id, record.IsDiff desc, flag
        desc,record.CreateTime desc, record.Id
    </select>

    <update id="updateInventorySyncRecord"
            parameterType="com.yijiupi.himalaya.supplychain.inventory.domain.po.InventorySyncRecordPO">
        update inventorysyncrecord
        <set>
            <if test="state != null">
                State = #{state,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateRecordState">
        update inventorysyncrecord set State = #{state,jdbcType=TINYINT}
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteTodaySyncRecordByCityId">
        delete from inventorysyncrecord
        where Org_Id = #{orgId,jdbcType=TINYINT}
        and CreateTime >= date_format(now(),'%Y-%m-%d')
    </update>
</mapper>